// middleware/auth.js

// Middleware to check if user is logged in
exports.ensureAuthenticated = (req, res, next) => {
  console.log('Auth check:', {
    isAuthenticated: req.isAuthenticated && req.isAuthenticated(),
    user: req.user ? {
      id: req.user._id,
      username: req.user.username,
      role: req.user.role
    } : null
  });

  if (req.isAuthenticated && req.isAuthenticated()) {
    return next();
  }
  res.status(401).json({ error: 'Not authenticated' });
};

// Middleware to check if user has set up a username
exports.ensureUsernameDefined = (req, res, next) => {
  if (req.user && req.user.isUsernameDefined) {
    return next();
  }
  res.status(403).json({ error: 'Username setup required' });
};

// Middleware to check if user is an admin
exports.isAdmin = (req, res, next) => {
  if (req.user && req.user.role === 'admin') {
    return next();
  }
  res.status(403).json({ error: 'Admin access required' });
};
