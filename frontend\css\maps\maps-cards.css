/* ===== CONTENT SECTIONS ===== */
.game-content {
  display: none;
  margin-top: 2rem;
}

.game-content.active {
  display: block;
}

/* ===== WAR2 ACTIVE SECTION ===== */
.war2-section {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  margin: 2rem auto;
  position: relative;
  overflow: hidden;
}

.war2-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #ffd700, #ffed4e, #ff6b35);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.section-title {
  font-family: 'Cinzel', serif;
  font-size: 2rem;
  font-weight: 700;
  color: #ffd700;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.section-title i {
  font-size: 1.8rem;
}

.section-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

/* ===== SEARCH AND FILTERS ===== */
.maps-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.maps-search {
  display: flex;
  gap: 0.5rem;
  flex: 1;
  max-width: 400px;
}

.maps-search input {
  flex: 1;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 25px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.maps-search input:focus {
  outline: none;
  border-color: rgba(255, 215, 0, 0.5);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.1);
}

.maps-search input::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

.maps-tabs {
  display: flex;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.03);
  padding: 0.5rem;
  border-radius: 15px;
  margin-bottom: 2rem;
}

.tab-btn {
  flex: 1;
  padding: 0.75rem 1rem;
  background: transparent;
  border: none;
  border-radius: 10px;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 100px;
}

.tab-btn.active {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #000;
  font-weight: 600;
}

.tab-btn:hover:not(.active) {
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.8);
}

/* ===== MAP CARDS STYLING ===== */
#maps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 15px;
  margin: 1rem 0;
}

.map-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  backdrop-filter: blur(10px);
  height: auto;
  transform-origin: center;
}

.map-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.1), transparent);
  transition: left 0.6s;
  z-index: 1;
}

.map-card:hover {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.08), rgba(255, 215, 0, 0.04));
  border-color: rgba(255, 215, 0, 0.3);
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 12px 25px rgba(255, 215, 0, 0.2);
}

.map-card:hover::before {
  left: 100%;
}

.map-thumbnail-wrapper {
  position: relative;
  width: 100%;
  height: 140px;
  overflow: hidden;
  border-radius: 12px 12px 0 0;
}

.map-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.map-card:hover .map-thumbnail {
  transform: scale(1.05);
}

.map-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0.7) 100%);
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 0.75rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.map-card:hover .map-overlay {
  opacity: 1;
}

.fullscreen-icon {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background: rgba(0, 0, 0, 0.7);
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  opacity: 0;
  font-size: 0.8rem;
}

.map-card:hover .fullscreen-icon {
  opacity: 1;
}

.fullscreen-icon:hover {
  background: rgba(255, 215, 0, 0.8);
  color: black;
  transform: scale(1.1);
}

.map-stats {
  display: flex;
  gap: 0.5rem;
}

.downloads-badge,
.matches-badge,
.rating-badge {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.2rem 0.4rem;
  border-radius: 10px;
  font-size: 0.65rem;
  display: flex;
  align-items: center;
  gap: 0.2rem;
  font-weight: 500;
}

.matches-badge {
  background: rgba(34, 139, 34, 0.9);
  color: white;
}

.rating-badge {
  background: rgba(255, 215, 0, 0.9);
  color: black;
}

.map-info {
  padding: 0.75rem;
  position: relative;
  z-index: 2;
}

.map-name {
  font-family: 'Cinzel', serif;
  font-size: 0.9rem;
  font-weight: 600;
  color: #ffd700;
  margin: 0 0 0.4rem 0;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.map-description {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.75rem;
  line-height: 1.3;
  margin: 0 0 0.6rem 0;
  height: 2.4rem;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.map-meta {
  margin-bottom: 0.6rem;
}

.map-rating {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  margin-bottom: 0.4rem;
}

.stars {
  color: #ffd700;
  font-size: 0.7rem;
}

.rating-count {
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.65rem;
}

.map-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.6rem;
}

.map-size,
.player-count {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  padding: 0.2rem 0.4rem;
  border-radius: 5px;
  font-size: 0.65rem;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.player-count {
  background: rgba(255, 215, 0, 0.2);
  color: #ffd700;
  border-color: rgba(255, 215, 0, 0.3);
}

.map-actions {
  display: flex;
  gap: 0.4rem;
  margin-bottom: 0.6rem;
}

.map-actions .btn {
  flex: 1;
  padding: 0.4rem 0.6rem;
  border: none;
  border-radius: 5px;
  font-size: 0.7rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.3rem;
  text-decoration: none;
}

.map-actions .btn-primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.map-actions .btn-primary:hover {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(59, 130, 246, 0.4);
}

.map-actions .btn-success {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.map-actions .btn-success:hover {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(16, 185, 129, 0.4);
}

.map-actions .btn-danger {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.map-actions .btn-danger:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(239, 68, 68, 0.4);
}

.map-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.3rem;
  margin-top: 0.5rem;
}

.tag {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  padding: 0.2rem 0.4rem;
  border-radius: 10px;
  font-size: 0.6rem;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.tag-more {
  background: rgba(255, 215, 0, 0.2);
  color: #ffd700;
  padding: 0.2rem 0.4rem;
  border-radius: 10px;
  font-size: 0.6rem;
  font-weight: 500;
  border: 1px solid rgba(255, 215, 0, 0.3);
}

/* No maps state */
.no-data {
  text-align: center;
  padding: 3rem 2rem;
  color: rgba(255, 255, 255, 0.6);
  font-size: 1.1rem;
  grid-column: 1 / -1;
}

/* ===== LOADING STATES ===== */
.loading {
  text-align: center;
  padding: 3rem;
  color: rgba(255, 255, 255, 0.6);
  font-size: 1.1rem;
  position: relative;
}

.loading::after {
  content: '';
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 215, 0, 0.2);
  border-top: 3px solid #ffd700;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 1rem auto 0;
  display: block;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== PAGINATION ===== */
.pagination {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 2rem;
}

.pagination button {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
}

.pagination button:hover {
  background: rgba(255, 215, 0, 0.1);
  border-color: rgba(255, 215, 0, 0.3);
  color: #ffd700;
}

.pagination button.active {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #000;
  border-color: #ffd700;
}

/* ==========================================================================
   MAP CARDS ENHANCED STYLING
   ========================================================================== */

/* Strategic Info Section */
.strategic-info {
  background: rgba(255, 215, 0, 0.05);
  border: 1px solid rgba(255, 215, 0, 0.2);
  border-radius: 8px;
  padding: 12px;
  margin: 12px 0;
}

.strategic-info h4 {
  color: #ffd700;
  font-family: 'Cinzel', serif;
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 4px;
}

.strategic-items {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6px;
}

.strategic-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  line-height: 1.2;
}

.strategic-icon {
  font-size: 12px;
  width: 16px;
  text-align: center;
}

.strategic-label {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.strategic-value {
  color: #ffd700;
  font-weight: 600;
  margin-left: auto;
}

/* Strategic value colors based on content */
.strategic-value:contains("Yes"),
.strategic-value:contains("Easy") {
  color: #4caf50;
}

.strategic-value:contains("High"),
.strategic-value:contains("Long") {
  color: #ff9800;
}

.strategic-value:contains("Hard"),
.strategic-value:contains("No") {
  color: #f44336;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .strategic-items {
    grid-template-columns: 1fr;
    gap: 4px;
  }
  
  .strategic-item {
    font-size: 10px;
  }
  
  .strategic-icon {
    font-size: 11px;
    width: 14px;
  }
}

/* ===== WC3 SPECIFIC STYLING ===== */
.war3-section {
  background: rgba(138, 43, 226, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(138, 43, 226, 0.1);
  border-radius: 20px;
  padding: 2rem;
  margin: 2rem auto;
  position: relative;
  overflow: hidden;
}

.war3-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #8a2be2, #9370db, #ff6347);
}

.section-title-with-search {
  display: flex;
  align-items: center;
  gap: 2rem;
  flex: 1;
}

.maps-search-inline {
  flex: 1;
  max-width: 400px;
}

.search-input-container {
  display: flex;
  gap: 0.5rem;
}

.search-input-container input {
  flex: 1;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 25px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.search-input-container input:focus {
  outline: none;
  border-color: rgba(138, 43, 226, 0.5);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 2px rgba(138, 43, 226, 0.1);
}

.search-btn-modern {
  padding: 0.75rem 1rem;
  background: linear-gradient(45deg, #8a2be2, #9370db);
  border: none;
  border-radius: 25px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.search-btn-modern:hover {
  background: linear-gradient(45deg, #9370db, #8a2be2);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(138, 43, 226, 0.3);
}

.maps-tabs-section {
  margin-bottom: 2rem;
}

.war3-filters {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  flex-wrap: wrap;
}

.filter-select {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
  min-width: 150px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-select:focus {
  outline: none;
  border-color: rgba(138, 43, 226, 0.5);
  background: rgba(255, 255, 255, 0.08);
}

.filter-select option {
  background: rgba(30, 30, 30, 0.95);
  color: white;
}

.war3-maps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 15px;
  margin: 1rem 0;
}

.war3-map-card {
  background: linear-gradient(135deg, rgba(138, 43, 226, 0.08), rgba(138, 43, 226, 0.03));
  border: 1px solid rgba(138, 43, 226, 0.2);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  backdrop-filter: blur(10px);
  transform-origin: center;
}

.war3-map-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(138, 43, 226, 0.1), transparent);
  transition: left 0.6s;
  z-index: 1;
}

.war3-map-card:hover {
  background: linear-gradient(135deg, rgba(138, 43, 226, 0.15), rgba(138, 43, 226, 0.08));
  border-color: rgba(138, 43, 226, 0.4);
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 12px 25px rgba(138, 43, 226, 0.3);
}

.war3-map-card:hover::before {
  left: 100%;
}

.war3-map-card .map-thumbnail {
  position: relative;
  width: 100%;
  height: 160px;
  overflow: hidden;
}

.war3-map-card .map-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.war3-map-card:hover .map-thumbnail img {
  transform: scale(1.05);
}

.war3-map-card .map-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0.2) 0%, rgba(0,0,0,0.7) 100%);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0.75rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.war3-map-card:hover .map-overlay {
  opacity: 1;
}

.map-expansion {
  background: rgba(0, 0, 0, 0.7);
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
}

.map-expansion.roc {
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  color: white;
}

.map-expansion.tft {
  background: linear-gradient(45deg, #4a90e2, #7b68ee);
  color: white;
}

.map-players {
  background: rgba(138, 43, 226, 0.8);
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  color: white;
}

.war3-map-card .map-info {
  padding: 1rem;
  position: relative;
  z-index: 2;
}

.war3-map-card .map-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  margin-bottom: 0.5rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.3;
}

.war3-map-card .map-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 0.75rem;
  font-size: 0.85rem;
}

.war3-map-card .map-details > div {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.7);
}

.war3-map-card .map-details i {
  width: 12px;
  color: rgba(138, 43, 226, 0.8);
}

.war3-map-card .map-category {
  font-weight: 500;
  color: rgba(138, 43, 226, 0.9);
}

.war3-map-card .map-description {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.4;
  margin-bottom: 0.75rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.war3-map-card .map-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin-bottom: 1rem;
}

.war3-map-card .map-tag {
  background: rgba(138, 43, 226, 0.2);
  color: rgba(255, 255, 255, 0.8);
  padding: 0.2rem 0.5rem;
  border-radius: 8px;
  font-size: 0.7rem;
  font-weight: 500;
}

.war3-map-card .map-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: auto;
}

.map-action-btn {
  flex: 1;
  padding: 0.5rem 0.75rem;
  border: none;
  border-radius: 8px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
}

.map-action-btn:first-child {
  background: rgba(138, 43, 226, 0.2);
  color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(138, 43, 226, 0.3);
}

.map-action-btn:first-child:hover {
  background: rgba(138, 43, 226, 0.3);
  border-color: rgba(138, 43, 226, 0.5);
}

.map-action-btn:last-child {
  background: rgba(76, 175, 80, 0.2);
  color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.map-action-btn:last-child:hover {
  background: rgba(76, 175, 80, 0.3);
  border-color: rgba(76, 175, 80, 0.5);
}

.no-maps-state,
.error-state {
  text-align: center;
  padding: 3rem 2rem;
  color: rgba(255, 255, 255, 0.7);
}

.no-maps-state i,
.error-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: rgba(138, 43, 226, 0.6);
}

.no-maps-state h3,
.error-state h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: rgba(255, 255, 255, 0.9);
}

.no-maps-state p,
.error-state p {
  margin-bottom: 1.5rem;
  font-size: 1rem;
}

/* Admin only button */
.admin-only {
  display: none;
}

.user-admin .admin-only {
  display: inline-flex;
}

/* Responsive design for WC3 maps */
@media (max-width: 768px) {
  .section-title-with-search {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  
  .maps-search-inline {
    max-width: none;
  }
  
  .war3-filters {
    flex-direction: column;
  }
  
  .filter-select {
    min-width: auto;
  }
  
  .war3-maps-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
  }
  
  .section-actions {
    justify-content: center;
  }
}

/* Enhanced War3 Map Card Styling */
.war3-map-card .map-header {
  margin-bottom: 0.75rem;
}

.war3-map-card .map-author {
  font-size: 0.75rem;
  color: rgba(138, 43, 226, 0.8);
  font-weight: 500;
  margin-top: 0.25rem;
}

.war3-indicators {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  z-index: 10;
}

.expansion-badge,
.custom-content-badge,
.protected-badge {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.expansion-badge.tft {
  background: linear-gradient(135deg, #4a90e2, #7b68ee);
  color: white;
}

.custom-content-badge {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: white;
}

.protected-badge {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
}

.war3-metadata {
  margin: 0.75rem 0;
  padding: 0.75rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  border: 1px solid rgba(138, 43, 226, 0.2);
}

.metadata-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
  font-size: 0.8rem;
}

.metadata-row:last-child {
  margin-bottom: 0;
}

.metadata-label {
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.metadata-value {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
}

.map-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin: 0.75rem 0;
}

.map-tag {
  background: rgba(138, 43, 226, 0.2);
  color: rgba(255, 255, 255, 0.85);
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-size: 0.65rem;
  font-weight: 500;
  border: 1px solid rgba(138, 43, 226, 0.3);
  transition: all 0.2s ease;
}

.map-tag:hover {
  background: rgba(138, 43, 226, 0.3);
  border-color: rgba(138, 43, 226, 0.5);
}

.map-tag.more {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.6);
  border-color: rgba(255, 255, 255, 0.2);
}

/* Improved map card hover effects */
.war3-map-card:hover .war3-indicators .expansion-badge,
.war3-map-card:hover .war3-indicators .custom-content-badge,
.war3-map-card:hover .war3-indicators .protected-badge {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}

.war3-map-card:hover .war3-metadata {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(138, 43, 226, 0.4);
}

/* Enhanced responsive design for War3 cards */
@media (max-width: 480px) {
  .war3-metadata {
    padding: 0.5rem;
  }
  
  .metadata-row {
    font-size: 0.75rem;
  }
  
  .war3-indicators {
    top: 0.25rem;
    right: 0.25rem;
    gap: 0.2rem;
  }
  
  .expansion-badge,
  .custom-content-badge,
  .protected-badge {
    width: 20px;
    height: 20px;
    font-size: 0.6rem;
  }
  
  .map-tag {
    font-size: 0.6rem;
    padding: 0.15rem 0.4rem;
  }
}
