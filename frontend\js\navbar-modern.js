/**
 * Modern Navbar System
 * Handles navigation, mobile menu, and user interactions
 */

console.log('🚀 Modern Navbar JS loading...');

class ModernNavbar {
  constructor() {
    console.log('🚀 Initializing Modern Navbar...');
    this.isMenuOpen = false;
    this.eventListeners = [];
    this.domElements = {};
    this.soundEnabled = false; // Disabled audio functionality
    this.musicSystem = null;
  }

  async init() {
    try {
      console.log('⚙️ Starting navbar initialization...');
      
      // Wait for the navbar to be loaded in the DOM
      await this.waitForNavbar();
      
      // Cache DOM elements
      this.cacheDOMElements();
      
      // Setup functionality
      this.setupMobileMenu();
      this.setupDropdowns();
      this.setupKeyboardNavigation();
      
      // Load user data and initialize additional features
      await this.loadUserData();
      this.setupMusicSystem();
      
      // Initialize notifications
      await this.initNotifications();
      
      // Update current page
      this.updateCurrentPageMenuItem();
      
      console.log('✅ Modern Navbar initialized successfully');
    } catch (error) {
      console.error('❌ Error initializing navbar:', error);
    }
  }

  async waitForNavbar() {
    console.log('⏳ Waiting for navbar to be available...');
    let attempts = 0;
    const maxAttempts = 50;
    
    while (attempts < maxAttempts) {
      const navbar = document.querySelector('.navbar, .navbar-modern');
      if (navbar) {
        console.log('✅ Navbar found in DOM');
        return;
      }
      await new Promise(resolve => setTimeout(resolve, 100));
      attempts++;
    }
    
    throw new Error('Navbar not found in DOM after waiting');
  }

  cacheDOMElements() {
    console.log('📦 Caching DOM elements...');
    this.domElements = {
      mobileMenuToggle: document.getElementById('mobile-menu-toggle'),
      mobileNav: document.querySelector('.mobile-nav'),
      mobileOverlay: document.querySelector('.mobile-nav-overlay'),
      dropdownToggles: document.querySelectorAll('.nav-dropdown > button:not(#notifications-indicator)'),
      profileImage: document.getElementById('profile-image'),
      profileImageMobile: document.getElementById('profile-image-mobile'),
      navbarUsername: document.getElementById('navbar-username'),
      navbarUsernameMobile: document.getElementById('navbar-username-mobile'),
      adminLink: document.getElementById('admin-link')
    };
    console.log('✅ DOM elements cached');
  }

  setupMobileMenu() {
    console.log('📱 Setting up mobile menu...');
    const { mobileMenuToggle, mobileNav, mobileOverlay } = this.domElements;

    if (mobileMenuToggle && mobileNav && mobileOverlay) {
      // Toggle button click
      const toggleHandler = (e) => {
        e.preventDefault();
        this.toggleMobileMenu();
      };
      mobileMenuToggle.addEventListener('click', toggleHandler);
      this.eventListeners.push({ element: mobileMenuToggle, event: 'click', handler: toggleHandler });

      // Overlay click
      const overlayHandler = () => this.closeMobileMenu();
      mobileOverlay.addEventListener('click', overlayHandler);
      this.eventListeners.push({ element: mobileOverlay, event: 'click', handler: overlayHandler });

      // Escape key
      const escapeHandler = (e) => {
        if (e.key === 'Escape' && this.isMenuOpen) {
          this.closeMobileMenu();
        }
      };
      document.addEventListener('keydown', escapeHandler);
      this.eventListeners.push({ element: document, event: 'keydown', handler: escapeHandler });
      
      console.log('✅ Mobile menu setup complete');
    } else {
      console.log('⚠️ Mobile menu elements not found');
    }
  }

  toggleMobileMenu() {
    this.isMenuOpen = !this.isMenuOpen;
    const { mobileNav, mobileOverlay, mobileMenuToggle } = this.domElements;

    if (this.isMenuOpen) {
      mobileNav?.classList.add('active');
      mobileOverlay?.classList.add('active');
      mobileMenuToggle?.classList.add('active');
      mobileMenuToggle?.setAttribute('aria-expanded', 'true');
      document.body.classList.add('mobile-menu-open');
    } else {
      this.closeMobileMenu();
    }
  }

  closeMobileMenu() {
    this.isMenuOpen = false;
    const { mobileNav, mobileOverlay, mobileMenuToggle } = this.domElements;

    mobileNav?.classList.remove('active');
    mobileOverlay?.classList.remove('active');
    mobileMenuToggle?.classList.remove('active');
    mobileMenuToggle?.setAttribute('aria-expanded', 'false');
    document.body.classList.remove('mobile-menu-open');
  }

  setupDropdowns() {
    console.log('📋 Setting up dropdowns...');
    const { dropdownToggles } = this.domElements;
    
    console.log(`🔍 Found ${dropdownToggles.length} dropdown toggle(s)`);
    
    // Add notifications toggle to the existing dropdowns
    const notificationsToggle = document.getElementById('notifications-indicator');
    const notificationsDropdown = document.getElementById('notifications-dropdown');
    
    dropdownToggles.forEach((toggle, index) => {
      console.log(`🔧 Setting up dropdown toggle ${index + 1}:`, toggle);
      const dropdown = toggle.closest('.nav-dropdown, .profile-dropdown');
      
      if (!dropdown) {
        console.warn(`⚠️ No dropdown container found for toggle ${index + 1}`);
        return;
      }
      
      const toggleHandler = (e) => {
        console.log('🔄 Dropdown toggle clicked!', toggle);
        e.preventDefault();
        e.stopPropagation();
        
        // Close other dropdowns
        dropdownToggles.forEach(otherToggle => {
          const otherDropdown = otherToggle.closest('.nav-dropdown, .profile-dropdown');
          if (otherDropdown !== dropdown) {
            otherDropdown.classList.remove('active');
            otherToggle.setAttribute('aria-expanded', 'false');
          }
        });
        
        // Close notifications dropdown if it's not the current one
        if (notificationsDropdown && dropdown !== notificationsDropdown.parentElement) {
          notificationsDropdown.classList.remove('show');
          if (notificationsToggle) notificationsToggle.setAttribute('aria-expanded', 'false');
        }
        
        // Toggle current dropdown
        const isActive = dropdown.classList.toggle('active');
        toggle.setAttribute('aria-expanded', isActive.toString());
        
        console.log(`📋 Dropdown ${isActive ? 'opened' : 'closed'}`);
        
        // Special handling for profile dropdown
        if (dropdown.classList.contains('profile-dropdown')) {
          console.log('👤 Profile dropdown toggled');
        }
      };
      
      toggle.addEventListener('click', toggleHandler);
      this.eventListeners.push({ element: toggle, event: 'click', handler: toggleHandler });
    });

    // Handle notifications dropdown separately
    if (notificationsToggle && notificationsDropdown) {
      const notificationToggleHandler = (e) => {
        e.preventDefault();
        e.stopPropagation();
        
        // Close other dropdowns
        dropdownToggles.forEach(otherToggle => {
          const otherDropdown = otherToggle.closest('.nav-dropdown, .profile-dropdown');
          otherDropdown.classList.remove('active');
          otherToggle.setAttribute('aria-expanded', 'false');
        });
        
        // Toggle notifications dropdown
        const isActive = notificationsDropdown.classList.toggle('show');
        notificationsToggle.setAttribute('aria-expanded', isActive.toString());
        
        console.log('🔔 Notifications dropdown toggled:', isActive);
      };
      
      notificationsToggle.addEventListener('click', notificationToggleHandler);
      this.eventListeners.push({ element: notificationsToggle, event: 'click', handler: notificationToggleHandler });
    }

    // Close dropdowns when clicking outside
    const outsideClickHandler = () => {
      dropdownToggles.forEach(toggle => {
        const dropdown = toggle.closest('.nav-dropdown, .profile-dropdown');
        dropdown.classList.remove('active');
        toggle.setAttribute('aria-expanded', 'false');
      });
      
      // Close notifications dropdown
      if (notificationsDropdown && notificationsToggle) {
        notificationsDropdown.classList.remove('show');
        notificationsToggle.setAttribute('aria-expanded', 'false');
      }
    };
    document.addEventListener('click', outsideClickHandler);
    this.eventListeners.push({ element: document, event: 'click', handler: outsideClickHandler });

    // Escape key handling
    const escapeHandler = (e) => {
      if (e.key === 'Escape') {
        dropdownToggles.forEach(toggle => {
          const dropdown = toggle.closest('.nav-dropdown, .profile-dropdown');
          dropdown.classList.remove('active');
          toggle.setAttribute('aria-expanded', 'false');
        });
        
        // Close notifications dropdown
        if (notificationsDropdown && notificationsToggle) {
          notificationsDropdown.classList.remove('show');
          notificationsToggle.setAttribute('aria-expanded', 'false');
        }
      }
    };
    document.addEventListener('keydown', escapeHandler);
    this.eventListeners.push({ element: document, event: 'keydown', handler: escapeHandler });
    
    console.log('✅ Dropdowns setup complete');
  }

  setupKeyboardNavigation() {
    console.log('⌨️ Setting up keyboard navigation...');
    
    // Combined handler for all keyboard navigation
    const keyboardHandler = (e) => {
      const activeDropdown = document.querySelector('.nav-dropdown.active, .profile-dropdown.active');
      if (!activeDropdown) return;
      
      const menuItems = activeDropdown.querySelectorAll('.nav-dropdown-item');
      const currentFocus = document.activeElement;
      const currentIndex = Array.from(menuItems).indexOf(currentFocus);
      
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          const nextIndex = currentIndex < menuItems.length - 1 ? currentIndex + 1 : 0;
          menuItems[nextIndex]?.focus();
          break;
        case 'ArrowUp':
          e.preventDefault();
          const prevIndex = currentIndex > 0 ? currentIndex - 1 : menuItems.length - 1;
          menuItems[prevIndex]?.focus();
          break;
        case 'Home':
          e.preventDefault();
          menuItems[0]?.focus();
          break;
        case 'End':
          e.preventDefault();
          menuItems[menuItems.length - 1]?.focus();
          break;
      }
    };
    
    document.addEventListener('keydown', keyboardHandler);
    this.eventListeners.push({ element: document, event: 'keydown', handler: keyboardHandler });
    
    console.log('✅ Keyboard navigation setup complete');
  }

  setupMusicSystem() {
    console.log('🎵 Setting up music system integration...');
    // Initialize music system
    if (typeof window.initializeMusic === 'function') {
      window.initializeMusic();
    } else {
      // Wait for music functions
      setTimeout(() => {
        if (typeof window.initializeMusic === 'function') {
          window.initializeMusic();
        }
      }, 500);
    }
  }

  async loadUserData() {
    console.log('👤 Loading user data...');
    
    // Wait for loadUser function
    let attempts = 0;
    while (typeof window.loadUser !== 'function' && attempts < 10) {
      await new Promise(resolve => setTimeout(resolve, 200));
      attempts++;
    }
    
    if (typeof window.loadUser === 'function') {
      try {
        await window.loadUser();
        console.log('✅ User data loaded successfully');
      } catch (error) {
        console.error('❌ Error loading user data:', error);
      }
    }
  }

  async updateUserDisplay(user) {
    console.log('👤 Updating user display...');
    const { profileImage, profileImageMobile, navbarUsername, navbarUsernameMobile, adminLink } = this.domElements;

    console.log('🔍 Admin link debug:', {
      user: user ? { username: user.username, role: user.role } : null,
      adminLink: adminLink ? 'found' : 'not found',
      adminLinkDisplay: adminLink ? adminLink.style.display : 'N/A',
      adminLinkElement: adminLink
    });

    if (user) {
      // Update usernames
      if (navbarUsername) navbarUsername.textContent = (user.username || 'User').toUpperCase();
      if (navbarUsernameMobile) navbarUsernameMobile.textContent = (user.username || 'User').toUpperCase();

      // Show admin link if user is admin
      const isAdmin = user.role === 'admin';
      console.log('🔍 Admin check:', { role: user.role, username: user.username, isAdmin });

      if (isAdmin) {
        // Try multiple ways to show the admin link
        if (adminLink) {
          adminLink.style.display = 'flex';
          adminLink.style.visibility = 'visible';
          console.log('✅ Admin link shown for admin user via cached element');
        }

        // Always try fallback as well
        const adminLinkFallback = document.getElementById('admin-link');
        if (adminLinkFallback) {
          adminLinkFallback.style.display = 'flex';
          adminLinkFallback.style.visibility = 'visible';
          console.log('✅ Admin link shown via fallback element');
        } else {
          console.error('❌ Admin link element not found in DOM at all!');
        }

        // Force update the cached element
        this.domElements.adminLink = adminLinkFallback;

      } else {
        console.log('ℹ️ User is not admin, hiding admin link');
        if (adminLink) adminLink.style.display = 'none';
        const adminLinkFallback = document.getElementById('admin-link');
        if (adminLinkFallback) adminLinkFallback.style.display = 'none';
      }

      // Use user's database avatar directly (managed by AvatarService)
      const avatarUrl = user.avatar || '/assets/img/ranks/emblem.png';
      console.log('👤 Using database avatar for navbar:', avatarUrl);
      
      this.setRankImages(profileImage, profileImageMobile, avatarUrl, 'User Avatar');
      
      console.log('✅ User display updated successfully');
    } else {
      // Reset to defaults when no user
      if (navbarUsername) navbarUsername.textContent = 'GUEST';
      if (navbarUsernameMobile) navbarUsernameMobile.textContent = 'GUEST';
      this.setDefaultImages(profileImage, profileImageMobile);
      if (adminLink) adminLink.style.display = 'none';
    }
  }

  // Helper method to set rank images
  setRankImages(profileImage, profileImageMobile, rankImageUrl, rankName) {
    if (profileImage) {
      profileImage.src = rankImageUrl;
      profileImage.alt = `${rankName} rank`;
      console.log('✅ Updated desktop profile image');
    }
    if (profileImageMobile) {
      profileImageMobile.src = rankImageUrl;
      profileImageMobile.alt = `${rankName} rank`;
      console.log('✅ Updated mobile profile image');
    }
  }

  // Cache management methods removed - using database avatar directly

  setDefaultImages(profileImage, profileImageMobile) {
    const defaultImageUrl = '/assets/img/ranks/emblem.png';
    if (profileImage) {
      profileImage.src = defaultImageUrl;
      profileImage.alt = 'Default profile';
    }
    if (profileImageMobile) {
      profileImageMobile.src = defaultImageUrl;
      profileImageMobile.alt = 'Default profile';
    }
  }

  async initNotifications() {
    console.log('🔔 Initializing notifications system...');
    
    // Wait for NotificationsManager to be available
    let attempts = 0;
    const maxAttempts = 50;
    
    while (attempts < maxAttempts && !window.notificationsManager) {
      await new Promise(resolve => setTimeout(resolve, 100));
      attempts++;
    }
    
    if (!window.notificationsManager) {
      console.warn('⚠️ NotificationsManager not available after waiting');
      return;
    }
    
    // Initialize the notifications manager if not already initialized
    if (!window.notificationsManager.isInitialized) {
      try {
        await window.notificationsManager.init();
        console.log('✅ NotificationsManager initialized');
      } catch (error) {
        console.error('❌ Error initializing NotificationsManager:', error);
      }
    }
    
    console.log('✅ Notification system initialized');
  }

  updateCurrentPageMenuItem() {
    console.log('📍 Updating current page menu item...');
    
    // Get current page path
    const currentPath = window.location.pathname;
    console.log('Current path:', currentPath);
    
    // Define page mappings
    const pageMapping = {
      '/views/myprofile.html': 'My Profile',
      '/views/campaigns.html': 'War Table', 
      '/views/forum.html': 'Forum',
      '/views/chat.html': 'Live Chat',
      '/download': 'Download App'
    };
    
    // Find all dropdown menu items
    const dropdownItems = document.querySelectorAll('.nav-dropdown-item:not(.logout-item)');
    const mobileNavItems = document.querySelectorAll('.mobile-nav-item:not(.logout-item)');
    
    // Check both desktop and mobile menu items
    [...dropdownItems, ...mobileNavItems].forEach(item => {
      const href = item.getAttribute('href');
      
      if (href && currentPath.includes(href)) {
        // Add current page styling
        item.classList.add('current-page');
        item.style.pointerEvents = 'none';
        
        // Add "Currently viewing" text
        const textContent = item.textContent.trim();
        if (!textContent.includes('Currently viewing')) {
          const textSpan = item.querySelector('span') || item;
          if (textSpan.textContent) {
            textSpan.textContent = `${textSpan.textContent} (Current)`;
          }
        }
        
        console.log(`✅ Marked as current page: ${href}`);
      } else {
        // Remove current page styling if it exists
        item.classList.remove('current-page');
        item.style.pointerEvents = '';
        
        // Remove "Current" text if it exists
        const textSpan = item.querySelector('span') || item;
        if (textSpan.textContent && textSpan.textContent.includes(' (Current)')) {
          textSpan.textContent = textSpan.textContent.replace(' (Current)', '');
        }
      }
    });
  }

  // Cleanup method to prevent memory leaks
  destroy() {
    console.log('🧹 Cleaning up navbar event listeners...');
    this.eventListeners.forEach(({ element, event, handler }) => {
      element.removeEventListener(event, handler);
    });
    this.eventListeners = [];
    
    console.log('✅ Navbar cleanup complete');
  }

  /**
   * Refresh notifications (called by achievement system)
   */
  async refreshNotifications() {
    console.log('🔔 ModernNavbar: Refreshing notifications...');
    
    if (window.notificationsManager && typeof window.notificationsManager.loadNotifications === 'function') {
      await window.notificationsManager.loadNotifications();
      console.log('✅ ModernNavbar: Notifications refreshed');
    }
  }
}

function initModernNavbar() {
  console.log('🚀 Initializing Modern Navbar...');
  
  // Prevent multiple instances
  if (window.modernNavbar) {
    console.log('⚠️ Navbar already exists, cleaning up first...');
    window.modernNavbar.destroy();
    window.modernNavbar = null;
  }
  
  // Create new navbar instance
  window.modernNavbar = new ModernNavbar();
  window.modernNavbar.init();
}

console.log('✅ Modern navbar script loaded'); 