<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Enter the Arena - WC Arena</title>
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-brands-400.woff2" as="font" type="font/woff2" crossorigin>
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>
  <link rel="stylesheet" href="/css/warcraft-app-modern.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700;800&family=Uncial+Antiqua&display=swap" rel="stylesheet">
  
  <style>
    /* ===== EPIC WARCRAFT LOGIN STYLING ===== */
    body {
      margin: 0;
      padding: 0;
      min-height: 100vh;
      background: linear-gradient(
        135deg,
        #0a0a0a 0%,
        #1a1a2e 15%,
        #16213e 30%,
        #0f3460 45%,
        #8b4513 60%,
        #2d1810 75%,
        #0a0a0a 100%
      );
      background-size: 400% 400%;
      animation: epicGradientShift 20s ease infinite;
      font-family: 'Cinzel', serif;
      overflow-x: hidden;
      position: relative;
    }

    @keyframes epicGradientShift {
      0% { background-position: 0% 50%; }
      25% { background-position: 100% 50%; }
      50% { background-position: 100% 100%; }
      75% { background-position: 0% 100%; }
      100% { background-position: 0% 50%; }
    }

    /* Animated Background Elements */
    body::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: 
        radial-gradient(3px 3px at 20px 30px, rgba(255, 215, 0, 0.6), transparent),
        radial-gradient(3px 3px at 40px 70px, rgba(255, 255, 255, 0.4), transparent),
        radial-gradient(2px 2px at 90px 40px, rgba(255, 215, 0, 0.8), transparent),
        radial-gradient(2px 2px at 130px 80px, rgba(255, 255, 255, 0.3), transparent),
        radial-gradient(1px 1px at 160px 30px, rgba(255, 215, 0, 0.9), transparent);
      background-repeat: repeat;
      background-size: 250px 150px;
      animation: magicalSparkle 6s linear infinite;
      pointer-events: none;
      z-index: 1;
    }

    @keyframes magicalSparkle {
      0% { transform: translateY(0) rotate(0deg) scale(1); opacity: 1; }
      100% { transform: translateY(-100vh) rotate(360deg) scale(0.5); opacity: 0; }
    }

    /* Floating Runes */
    body::after {
      content: '⚔️ 🛡️ 🏰 ⚡ 🔥 💎 👑 🗡️';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      font-size: clamp(20px, 3vw, 40px);
      letter-spacing: 100px;
      line-height: 200px;
      color: rgba(255, 215, 0, 0.2);
      animation: floatingRunes 30s linear infinite;
      pointer-events: none;
      z-index: 1;
    }

    @keyframes floatingRunes {
      0% { transform: translateY(100vh) rotate(0deg); }
      100% { transform: translateY(-100vh) rotate(360deg); }
    }

    /* Main Login Container */
    .login-container {
      position: relative;
      z-index: 10;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 2rem;
      background: rgba(0, 0, 0, 0.3);
      backdrop-filter: blur(10px);
    }

    /* Epic Login Card */
    .login-card {
      position: relative;
      background: rgba(0, 0, 0, 0.8);
      backdrop-filter: blur(20px);
      border-radius: 20px;
      padding: 3rem 2.5rem;
      box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.6),
        0 0 100px rgba(255, 215, 0, 0.1);
      border: 2px solid transparent;
      background-clip: padding-box;
      position: relative;
      z-index: 3;
      width: 100%;
      max-width: 450px;
      margin: 2rem auto 8rem auto;
      animation: cardPulse 6s ease-in-out infinite, cardEntrance 1s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }

    @keyframes cardEntrance {
      0% {
        transform: translateY(100px) scale(0.9);
        opacity: 0;
        filter: blur(10px);
      }
      100% {
        transform: translateY(0) scale(1);
        opacity: 1;
        filter: blur(0);
      }
    }

    /* Animated Border */
    .login-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 25px;
      background: linear-gradient(45deg, 
        #ffd700, #ff6b35, #8b4513, #d4af37, 
        #ffd700, #ff6b35, #8b4513, #d4af37);
      background-size: 400% 400%;
      animation: borderGlow 4s ease infinite;
      z-index: -1;
      filter: blur(2px);
    }

    @keyframes borderGlow {
      0%, 100% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
    }

    /* Logo Section */
    .login-logo {
      text-align: center;
      margin-bottom: 2rem;
      position: relative;
    }

    .logo-emblem {
      width: 100px;
      height: 100px;
      margin: 0 auto 1.5rem;
      position: relative;
      animation: logoFloat 3s ease-in-out infinite;
    }

    @keyframes logoFloat {
      0%, 100% { transform: translateY(0) rotate(0deg); }
      50% { transform: translateY(-10px) rotate(5deg); }
    }

    .logo-emblem img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.8));
    }

    /* Floating Decorations */
    .logo-decoration {
      position: absolute;
      width: 20px;
      height: 20px;
      background: radial-gradient(circle, #ffd700, transparent);
      border-radius: 50%;
      animation: decoration-float 4s ease-in-out infinite;
    }

    .logo-decoration:nth-child(2) { top: 10px; left: -30px; animation-delay: -1s; }
    .logo-decoration:nth-child(3) { top: 10px; right: -30px; animation-delay: -2s; }
    .logo-decoration:nth-child(4) { bottom: 10px; left: -20px; animation-delay: -3s; }
    .logo-decoration:nth-child(5) { bottom: 10px; right: -20px; animation-delay: -4s; }

    @keyframes decoration-float {
      0%, 100% { transform: translateY(0) scale(0.8); opacity: 0.6; }
      50% { transform: translateY(-15px) scale(1.2); opacity: 1; }
    }

    /* Title Styling */
    .login-title {
      font-family: 'Uncial Antiqua', cursive;
      font-size: clamp(2.2rem, 6vw, 3.5rem);
      font-weight: 800;
      background: linear-gradient(45deg, #ffd700, #ffed4e, #ffd700, #ff6b35);
      background-size: 300% 300%;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      animation: titleShimmer 3s ease-in-out infinite;
      text-align: center;
      margin: 0 0 0.5rem 0;
      letter-spacing: 2px;
      position: relative;
      z-index: 5;
      /* Crisp, bold text shadows for strong contrast */
      -webkit-text-stroke: 2px rgba(0, 0, 0, 0.8);
      text-shadow: 
        0 0 20px rgba(255, 215, 0, 0.9),
        0 4px 8px rgba(0, 0, 0, 0.9),
        0 8px 16px rgba(0, 0, 0, 0.6);
      filter: drop-shadow(0 0 15px rgba(255, 215, 0, 0.7));
    }

    @keyframes titleShimmer {
      0%, 100% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
    }

    .login-subtitle {
      font-size: 1.1rem;
      color: rgba(255, 255, 255, 0.95);
      text-align: center;
      margin-bottom: 2.5rem;
      font-weight: 400;
      text-shadow: 
        0 2px 8px rgba(0, 0, 0, 0.9),
        0 4px 16px rgba(0, 0, 0, 0.7);
      line-height: 1.4;
      background: rgba(0, 0, 0, 0.3);
      padding: 1rem 1.5rem;
      border-radius: 15px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    /* Login Buttons */
    .login-buttons {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .login-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 1rem;
      padding: 1rem 1.5rem;
      border: 2px solid transparent;
      border-radius: 15px;
      background: rgba(0, 0, 0, 0.6);
      backdrop-filter: blur(20px);
      color: rgba(255, 255, 255, 0.95);
      font-family: 'Cinzel', serif;
      font-size: 1rem;
      font-weight: 600;
      text-decoration: none;
      transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      position: relative;
      overflow: hidden;
      text-shadow: 
        0 2px 6px rgba(0, 0, 0, 0.8),
        0 4px 12px rgba(0, 0, 0, 0.6);
    }

    .login-btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      transition: left 0.6s;
    }

    .login-btn:hover::before {
      left: 100%;
    }

    .login-btn:hover {
      transform: translateY(-3px) scale(1.02);
      box-shadow: 
        0 15px 35px rgba(0, 0, 0, 0.6),
        0 0 30px rgba(255, 215, 0, 0.3);
      border-color: rgba(255, 215, 0, 0.6);
    }

    .login-btn img {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.5));
    }

    .btn-google:hover {
      background: rgba(219, 68, 55, 0.2);
      border-color: #db4437;
      box-shadow: 
        0 15px 35px rgba(219, 68, 55, 0.3),
        0 0 30px rgba(219, 68, 55, 0.4);
    }

    .btn-discord:hover {
      background: rgba(114, 137, 218, 0.2);
      border-color: #7289da;
      box-shadow: 
        0 15px 35px rgba(114, 137, 218, 0.3),
        0 0 30px rgba(114, 137, 218, 0.4);
    }

    .btn-twitch:hover {
      background: rgba(145, 70, 255, 0.2);
      border-color: #9146ff;
      box-shadow: 
        0 15px 35px rgba(145, 70, 255, 0.3),
        0 0 30px rgba(145, 70, 255, 0.4);
    }

    /* Footer */
    .login-footer {
      text-align: center;
      color: rgba(255, 255, 255, 0.9);
      font-size: 0.85rem;
      line-height: 1.6;
      background: rgba(0, 0, 0, 0.4);
      padding: 1rem 1.5rem;
      border-radius: 12px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      text-shadow: 
        0 2px 6px rgba(0, 0, 0, 0.8),
        0 4px 12px rgba(0, 0, 0, 0.6);
    }

    .login-footer a {
      color: #ffd700;
      text-decoration: none;
      transition: all 0.3s ease;
      font-weight: 600;
      text-shadow: 
        0 2px 6px rgba(0, 0, 0, 0.9),
        0 0 10px rgba(255, 215, 0, 0.5);
    }

    .login-footer a:hover {
      color: #ffed4e;
      text-shadow: 
        0 2px 6px rgba(0, 0, 0, 0.9),
        0 0 15px rgba(255, 215, 0, 0.8);
    }

    /* Epic Quote */
    .epic-quote {
      position: absolute;
      bottom: 4rem;
      left: 50%;
      transform: translateX(-50%);
      text-align: center;
      color: rgba(255, 255, 255, 0.85);
      font-style: italic;
      font-size: 0.9rem;
      max-width: 400px;
      animation: quoteGlow 4s ease-in-out infinite;
      background: rgba(0, 0, 0, 0.5);
      padding: 1rem 1.5rem;
      border-radius: 15px;
      backdrop-filter: blur(15px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      text-shadow: 
        0 2px 8px rgba(0, 0, 0, 0.9),
        0 4px 16px rgba(0, 0, 0, 0.7);
      z-index: 10;
    }

    @keyframes quoteGlow {
      0%, 100% { text-shadow: 0 0 5px rgba(255, 215, 0, 0.3); }
      50% { text-shadow: 0 0 15px rgba(255, 215, 0, 0.6); }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .login-card {
        margin: 1rem auto 6rem auto;
        padding: 2rem 1.5rem;
      }
      
      .login-title {
        font-size: 2.2rem;
      }
      
      .epic-quote {
        bottom: 2rem;
        font-size: 0.8rem;
        padding: 0.8rem 1rem;
        max-width: 320px;
      }
    }

    /* Loading Animation */
    .login-btn.loading {
      pointer-events: none;
      opacity: 0.8;
    }

    .login-btn.loading::after {
      content: '';
      position: absolute;
      right: 15px;
      width: 20px;
      height: 20px;
      border: 2px solid transparent;
      border-top: 2px solid currentColor;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="login-container">
    <div class="login-card">
      <div class="login-logo">
        <div class="logo-emblem">
          <img src="/assets/img/ranks/emblem.png" alt="WC Arena Logo" />
          <div class="logo-decoration"></div>
          <div class="logo-decoration"></div>
          <div class="logo-decoration"></div>
          <div class="logo-decoration"></div>
        </div>
      </div>
      
      <h1 class="login-title">WC Arena</h1>
      <p class="login-subtitle">Enter the legendary battlegrounds where champions are forged and legends are born</p>

      <div class="login-buttons">
        <a href="/auth/google" class="login-btn btn-google" onclick="this.classList.add('loading')">
          <img src="/assets/img/socials/google.png" alt="Google" />
          <span>Forge Your Legacy with Google</span>
        </a>
        <a href="/auth/discord" class="login-btn btn-discord" onclick="this.classList.add('loading')">
          <img src="/assets/img/socials/discord.png" alt="Discord" />
          <span>Join the Alliance via Discord</span>
        </a>
        <a href="/auth/twitch" class="login-btn btn-twitch" onclick="this.classList.add('loading')">
          <img src="/assets/img/socials/twitch.png" alt="Twitch" />
          <span>Stream to Glory with Twitch</span>
        </a>
      </div>

      <div class="login-footer">
        <p>By entering the arena, you pledge to uphold our <a href="#" onclick="showTerms()">Sacred Code</a> and <a href="#" onclick="showPrivacy()">Honor's Promise</a></p>
      </div>
    </div>
    
    <div class="epic-quote">
      "Victory is reserved for those who are willing to pay its price."<br>
      <em>— Sun Tzu, The Art of War</em>
    </div>
  </div>

  <script>
    // Epic entrance animations
    document.addEventListener('DOMContentLoaded', function() {
      // Stagger button animations
      const buttons = document.querySelectorAll('.login-btn');
      buttons.forEach((button, index) => {
        button.style.animationDelay = `${0.6 + index * 0.2}s`;
        button.style.animation = 'cardEntrance 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275) both';
      });

      // Add click sound effect (if available)
      buttons.forEach(button => {
        button.addEventListener('click', function() {
          // Add epic click effect
          this.style.transform = 'translateY(-1px) scale(0.98)';
          setTimeout(() => {
            this.style.transform = '';
          }, 150);
        });
      });

      // Easter egg: Konami code
      let konamiCode = [];
      const targetCode = ['ArrowUp', 'ArrowUp', 'ArrowDown', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'ArrowLeft', 'ArrowRight', 'KeyB', 'KeyA'];
      
      document.addEventListener('keydown', function(e) {
        konamiCode.push(e.code);
        if (konamiCode.length > targetCode.length) {
          konamiCode.shift();
        }
        
        if (konamiCode.join(',') === targetCode.join(',')) {
          activateEasterEgg();
        }
      });

      function activateEasterEgg() {
        document.body.style.animation = 'epicGradientShift 1s ease infinite';
        const title = document.querySelector('.login-title');
        title.textContent = 'LEGENDARY WARRIOR DETECTED';
        title.style.fontSize = '2rem';
        
        setTimeout(() => {
          title.textContent = 'WC Arena';
          title.style.fontSize = '';
          document.body.style.animation = '';
        }, 3000);
      }
    });

    function showTerms() {
      showSimpleAlert(
        'Sacred Code of Honor ⚔️',
        `🏆 WARRIOR'S CREED:\n\n• Fight with honor and respect thy opponents\n• Victory through skill, not through deception\n• Protect the realm from trolls and griefers\n• Share knowledge and lift up fellow warriors\n\n⚖️ ARENA LAWS:\n\n• No cheating, hacking, or exploits\n• Respect match results and sportsmanship\n• Keep discussions civil and constructive\n• Report misconduct to maintain honor\n\n"May the best warrior win, and may all battles be legendary!"`
      );
    }

    function showPrivacy() {
      showSimpleAlert(
        'Honor\'s Promise 🛡️',
        `🔒 GUARDIAN'S OATH:\n\nYour battle secrets and personal data are protected by the strongest magical wards.\n\n🛡️ WHAT WE GUARD:\n\n• Match histories and strategic preferences\n• Account information and social connections\n• Chat logs (encrypted and secure)\n• Performance analytics and rankings\n\n⚡ HOW WE PROTECT:\n\n• End-to-end encryption for sensitive data\n• No sharing with third-party merchants\n• Secure authentication through trusted allies\n• Regular security audits by our best mages\n\n"We guard your data as fiercely as our own fortress!"`
      );
    }

    function showSimpleAlert(title, message) {
      // Create a simple, reliable alert overlay
      const overlay = document.createElement('div');
      overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        font-family: Arial, sans-serif;
      `;

      const alertBox = document.createElement('div');
      alertBox.style.cssText = `
        background: #1a1a2e;
        border: 3px solid #ffd700;
        border-radius: 15px;
        padding: 2rem;
        max-width: 500px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.6);
      `;

      const titleElement = document.createElement('h2');
      titleElement.textContent = title;
      titleElement.style.cssText = `
        color: #ffd700;
        margin: 0 0 1rem 0;
        font-size: 1.5rem;
        font-weight: bold;
        text-align: center;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 1);
      `;

      const messageElement = document.createElement('pre');
      messageElement.textContent = message;
      messageElement.style.cssText = `
        color: #ffffff;
        font-size: 1rem;
        line-height: 1.6;
        margin: 0 0 2rem 0;
        white-space: pre-wrap;
        font-family: Arial, sans-serif;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 1);
      `;

      const closeButton = document.createElement('button');
      closeButton.textContent = 'I Understand';
      closeButton.style.cssText = `
        background: linear-gradient(135deg, #ffd700, #ffed4e);
        color: #1a1a2e;
        border: none;
        padding: 0.8rem 2rem;
        border-radius: 10px;
        font-weight: bold;
        font-size: 1rem;
        cursor: pointer;
        display: block;
        margin: 0 auto;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      `;

      closeButton.onclick = () => {
        document.body.removeChild(overlay);
      };

      // Close on overlay click
      overlay.onclick = (e) => {
        if (e.target === overlay) {
          document.body.removeChild(overlay);
        }
      };

      // Close on escape key
      const handleEscape = (e) => {
        if (e.key === 'Escape') {
          document.body.removeChild(overlay);
          document.removeEventListener('keydown', handleEscape);
        }
      };
      document.addEventListener('keydown', handleEscape);

      alertBox.appendChild(titleElement);
      alertBox.appendChild(messageElement);
      alertBox.appendChild(closeButton);
      overlay.appendChild(alertBox);
      document.body.appendChild(overlay);
    }

    // Add subtle mouse parallax effect
    document.addEventListener('mousemove', function(e) {
      const card = document.querySelector('.login-card');
      const x = (e.clientX / window.innerWidth) * 2 - 1;
      const y = (e.clientY / window.innerHeight) * 2 - 1;
      
      card.style.transform = `perspective(1000px) rotateY(${x * 5}deg) rotateX(${-y * 5}deg)`;
    });

    document.addEventListener('mouseleave', function() {
      const card = document.querySelector('.login-card');
      card.style.transform = '';
    });
  </script>
</body>
</html>
