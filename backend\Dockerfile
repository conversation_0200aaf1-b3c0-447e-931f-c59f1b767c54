# Use an official Node runtime as the base image
FROM node:16

# Set working directory in the container
WORKDIR /app

# Install canvas dependencies and build tools
RUN apt-get update && apt-get install -y \
    build-essential \
    libcairo2-dev \
    libpango1.0-dev \
    libjpeg-dev \
    libgif-dev \
    librsvg2-dev \
    pkg-config \
    python3 \
    g++ \
    make \
    && rm -rf /var/lib/apt/lists/*

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies with canvas rebuild
RUN npm install --build-from-source

# Copy the rest of the backend code
COPY . .

# Create required directories and set permissions
RUN mkdir -p /app/uploads/maps \
    /app/uploads/thumbnails \
    /app/frontend/assets/AUDIO/ORC \
    /app/frontend/assets/AUDIO/HUMAN \
    /app/frontend/assets/img && \
    chmod -R 777 /app/uploads /app/frontend/assets

# Expose the port the app runs on
EXPOSE 3000

# Command to run the application
CMD ["npm", "start"]
