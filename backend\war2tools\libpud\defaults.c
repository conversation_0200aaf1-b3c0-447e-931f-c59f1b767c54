/*
 * Copyright (c) 2014-2016 <PERSON>h
 *
 * Permission is hereby granted, free of charge, to any person obtaining a
 * copy of this software and associated documentation files (the "Software"),
 * to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.
 */

#include "pud_private.h"

static void _udta_defaults_set(Pud *pud);
static void _ugrd_defaults_set(Pud *pud);

PUDAPI Pud_Bool
pud_defaults_set(Pud *pud)
{
   PUD_SANITY_CHECK(pud, PUD_OPEN_MODE_W, PUD_FALSE);

   int i;

   /* SGLD */
   for (i = 0; i < 8; i++) pud->sgld.players[i] = 2000;
   for (i = 0; i < 7; i++) pud->sgld.unusable[i] = 2000;
   pud->sgld.neutral = 2000;

   /* SLBR */
   for (i = 0; i < 8; i++) pud->slbr.players[i] = 1000;
   for (i = 0; i < 7; i++) pud->slbr.unusable[i] = 1000;
   pud->slbr.neutral = 1000;

   /* SOIL */
   for (i = 0; i < 8; i++) pud->soil.players[i] = 1000;
   for (i = 0; i < 7; i++) pud->soil.unusable[i] = 1000;
   pud->soil.neutral = 1000;

   pud->private_data->default_udta = PUD_TRUE;
   pud->private_data->default_ugrd = PUD_TRUE;

   /* AI */
   for (i = 0; i < 8; i++) pud->ai.players[i] = 0x00;
   for (i = 0; i < 7; i++) pud->ai.unusable[i] = 0x00;
   pud->ai.neutral = 0x00;

   /* SIDE */
   for (i = 0; i < 8; i++)
     pud->side.players[i] = (i % 2 == 0) ? PUD_SIDE_HUMAN : PUD_SIDE_ORC;
   pud->side.neutral = PUD_SIDE_NEUTRAL;

   /* OWNR */
   pud->owner.players[0] = PUD_OWNER_HUMAN;
   for (i = 1; i < 8; i++) pud->owner.players[i] = PUD_OWNER_COMPUTER;
   for (i = 0; i < 7; i++) pud->owner.unusable[i] = PUD_OWNER_NOBODY;
   pud->owner.neutral = PUD_OWNER_PASSIVE_COMPUTER;

   _udta_defaults_set(pud);
   _ugrd_defaults_set(pud);
   pud_alow_defaults_set(pud);

   /* Most of the fields are assumed valid */
   pud->private_data->init = PUD_TRUE;

   return PUD_TRUE;
}


/* UGRD defaults are generated with tools/defaults_gen */

static void
_ugrd_defaults_set(Pud *pud)
{
   const Pud_Upgrade_Description ugrd[52] = {
      [0] = {
         .time = 200,
         .gold = 800,
         .lumber = 0,
         .oil = 0,
         .icon = 117,
         .group = 1,
         .flags = 4
      },
      [1] = {
         .time = 250,
         .gold = 2400,
         .lumber = 0,
         .oil = 0,
         .icon = 118,
         .group = 1,
         .flags = 8
      },
      [2] = {
         .time = 200,
         .gold = 500,
         .lumber = 100,
         .oil = 0,
         .icon = 120,
         .group = 1,
         .flags = 4
      },
      [3] = {
         .time = 250,
         .gold = 1500,
         .lumber = 300,
         .oil = 0,
         .icon = 121,
         .group = 1,
         .flags = 8
      },
      [4] = {
         .time = 200,
         .gold = 300,
         .lumber = 300,
         .oil = 0,
         .icon = 125,
         .group = 0,
         .flags = 1
      },
      [5] = {
         .time = 250,
         .gold = 900,
         .lumber = 500,
         .oil = 0,
         .icon = 126,
         .group = 0,
         .flags = 1
      },
      [6] = {
         .time = 200,
         .gold = 300,
         .lumber = 300,
         .oil = 0,
         .icon = 128,
         .group = 0,
         .flags = 1
      },
      [7] = {
         .time = 250,
         .gold = 900,
         .lumber = 500,
         .oil = 0,
         .icon = 129,
         .group = 0,
         .flags = 2
      },
      [8] = {
         .time = 200,
         .gold = 300,
         .lumber = 300,
         .oil = 0,
         .icon = 165,
         .group = 2,
         .flags = 16
      },
      [9] = {
         .time = 250,
         .gold = 900,
         .lumber = 500,
         .oil = 0,
         .icon = 166,
         .group = 2,
         .flags = 32
      },
      [10] = {
         .time = 200,
         .gold = 300,
         .lumber = 300,
         .oil = 0,
         .icon = 168,
         .group = 2,
         .flags = 16
      },
      [11] = {
         .time = 250,
         .gold = 900,
         .lumber = 500,
         .oil = 0,
         .icon = 169,
         .group = 2,
         .flags = 32
      },
      [12] = {
         .time = 200,
         .gold = 700,
         .lumber = 100,
         .oil = 1000,
         .icon = 145,
         .group = 3,
         .flags = 64
      },
      [13] = {
         .time = 250,
         .gold = 2000,
         .lumber = 250,
         .oil = 3000,
         .icon = 146,
         .group = 3,
         .flags = 128
      },
      [14] = {
         .time = 200,
         .gold = 700,
         .lumber = 100,
         .oil = 1000,
         .icon = 148,
         .group = 3,
         .flags = 64
      },
      [15] = {
         .time = 250,
         .gold = 2000,
         .lumber = 250,
         .oil = 3000,
         .icon = 149,
         .group = 3,
         .flags = 128
      },
      [16] = {
         .time = 200,
         .gold = 500,
         .lumber = 500,
         .oil = 0,
         .icon = 154,
         .group = 4,
         .flags = 256
      },
      [17] = {
         .time = 250,
         .gold = 1500,
         .lumber = 900,
         .oil = 0,
         .icon = 155,
         .group = 4,
         .flags = 512
      },
      [18] = {
         .time = 200,
         .gold = 500,
         .lumber = 500,
         .oil = 0,
         .icon = 151,
         .group = 4,
         .flags = 256
      },
      [19] = {
         .time = 250,
         .gold = 1500,
         .lumber = 900,
         .oil = 0,
         .icon = 152,
         .group = 4,
         .flags = 512
      },
      [20] = {
         .time = 250,
         .gold = 1500,
         .lumber = 0,
         .oil = 0,
         .icon = 138,
         .group = 6,
         .flags = 4096
      },
      [21] = {
         .time = 250,
         .gold = 4000,
         .lumber = 0,
         .oil = 0,
         .icon = 139,
         .group = 6,
         .flags = 8192
      },
      [22] = {
         .time = 250,
         .gold = 1500,
         .lumber = 0,
         .oil = 0,
         .icon = 140,
         .group = 6,
         .flags = 4096
      },
      [23] = {
         .time = 250,
         .gold = 4000,
         .lumber = 0,
         .oil = 0,
         .icon = 141,
         .group = 6,
         .flags = 8192
      },
      [24] = {
         .time = 250,
         .gold = 1500,
         .lumber = 0,
         .oil = 0,
         .icon = 6,
         .group = 7,
         .flags = 65536
      },
      [25] = {
         .time = 250,
         .gold = 2000,
         .lumber = 0,
         .oil = 0,
         .icon = 132,
         .group = 8,
         .flags = 131072
      },
      [26] = {
         .time = 250,
         .gold = 1500,
         .lumber = 0,
         .oil = 0,
         .icon = 133,
         .group = 9,
         .flags = 262144
      },
      [27] = {
         .time = 250,
         .gold = 2500,
         .lumber = 0,
         .oil = 0,
         .icon = 134,
         .group = 10,
         .flags = 524288
      },
      [28] = {
         .time = 250,
         .gold = 1500,
         .lumber = 0,
         .oil = 0,
         .icon = 7,
         .group = 7,
         .flags = 65536
      },
      [29] = {
         .time = 250,
         .gold = 2000,
         .lumber = 0,
         .oil = 0,
         .icon = 135,
         .group = 8,
         .flags = 131072
      },
      [30] = {
         .time = 250,
         .gold = 1500,
         .lumber = 0,
         .oil = 0,
         .icon = 136,
         .group = 9,
         .flags = 262144
      },
      [31] = {
         .time = 250,
         .gold = 3000,
         .lumber = 0,
         .oil = 0,
         .icon = 137,
         .group = 10,
         .flags = 524288
      },
      [32] = {
         .time = 250,
         .gold = 1000,
         .lumber = 0,
         .oil = 0,
         .icon = 11,
         .group = 20,
         .flags = 1048576
      },
      [33] = {
         .time = 250,
         .gold = 1000,
         .lumber = 0,
         .oil = 0,
         .icon = 10,
         .group = 20,
         .flags = 1048576
      },
      [34] = {
         .time = 0,
         .gold = 0,
         .lumber = 0,
         .oil = 0,
         .icon = 106,
         .group = 0,
         .flags = 1
      },
      [35] = {
         .time = 200,
         .gold = 1000,
         .lumber = 0,
         .oil = 0,
         .icon = 107,
         .group = 1,
         .flags = 2
      },
      [36] = {
         .time = 200,
         .gold = 2000,
         .lumber = 0,
         .oil = 0,
         .icon = 110,
         .group = 3,
         .flags = 8
      },
      [37] = {
         .time = 100,
         .gold = 1000,
         .lumber = 0,
         .oil = 0,
         .icon = 100,
         .group = 4,
         .flags = 16
      },
      [38] = {
         .time = 0,
         .gold = 0,
         .lumber = 0,
         .oil = 0,
         .icon = 101,
         .group = 5,
         .flags = 32
      },
      [39] = {
         .time = 100,
         .gold = 500,
         .lumber = 0,
         .oil = 0,
         .icon = 94,
         .group = 6,
         .flags = 64
      },
      [40] = {
         .time = 200,
         .gold = 2500,
         .lumber = 0,
         .oil = 0,
         .icon = 95,
         .group = 7,
         .flags = 128
      },
      [41] = {
         .time = 200,
         .gold = 2000,
         .lumber = 0,
         .oil = 0,
         .icon = 115,
         .group = 8,
         .flags = 256
      },
      [42] = {
         .time = 200,
         .gold = 2000,
         .lumber = 0,
         .oil = 0,
         .icon = 105,
         .group = 9,
         .flags = 512
      },
      [43] = {
         .time = 0,
         .gold = 0,
         .lumber = 0,
         .oil = 0,
         .icon = 111,
         .group = 10,
         .flags = 1024
      },
      [44] = {
         .time = 100,
         .gold = 1000,
         .lumber = 0,
         .oil = 0,
         .icon = 112,
         .group = 11,
         .flags = 2048
      },
      [45] = {
         .time = 100,
         .gold = 1500,
         .lumber = 0,
         .oil = 0,
         .icon = 114,
         .group = 13,
         .flags = 8192
      },
      [46] = {
         .time = 100,
         .gold = 0,
         .lumber = 0,
         .oil = 0,
         .icon = 103,
         .group = 14,
         .flags = 16384
      },
      [47] = {
         .time = 150,
         .gold = 1500,
         .lumber = 0,
         .oil = 0,
         .icon = 104,
         .group = 15,
         .flags = 32768
      },
      [48] = {
         .time = 100,
         .gold = 500,
         .lumber = 0,
         .oil = 0,
         .icon = 96,
         .group = 16,
         .flags = 65536
      },
      [49] = {
         .time = 200,
         .gold = 2500,
         .lumber = 0,
         .oil = 0,
         .icon = 98,
         .group = 17,
         .flags = 131072
      },
      [50] = {
         .time = 150,
         .gold = 1000,
         .lumber = 0,
         .oil = 0,
         .icon = 97,
         .group = 18,
         .flags = 262144
      },
      [51] = {
         .time = 200,
         .gold = 2000,
         .lumber = 0,
         .oil = 0,
         .icon = 108,
         .group = 19,
         .flags = 524288
      }
   };

   memcpy(&(pud->upgrades[0]), &(ugrd[0]), sizeof(ugrd));
}

PUDAPI void
pud_alow_defaults_set(Pud *pud)
{
   unsigned int i;

   PUD_SANITY_CHECK(pud, PUD_OPEN_MODE_W, VOID);

   pud->private_data->default_allow = PUD_TRUE;

   /* Everything is allowed */
   memset(&pud->unit_alow, 0xff, sizeof(pud->unit_alow));
   memset(&pud->spell_alow, 0xff, sizeof(pud->spell_alow));
   memset(&pud->up_alow, 0xff, sizeof(pud->up_alow));

   /* We start with nothing, expect fireball and death coil */
   memset(&pud->spell_start, 0x00, sizeof(pud->spell_start));
   for (i = 0; i < 8; i++)
     {
        pud->spell_start.players[i] |= PUD_ALLOW_SPELL_FIREBALL;
        pud->spell_start.players[i] |= PUD_ALLOW_SPELL_DEATH_COIL;
     }
   for (i = 0; i < 7; i++)
     {
        pud->spell_start.unusable[i] |= PUD_ALLOW_SPELL_FIREBALL;
        pud->spell_start.unusable[i] |= PUD_ALLOW_SPELL_DEATH_COIL;
     }
   pud->spell_start.neutral |= PUD_ALLOW_SPELL_FIREBALL;
   pud->spell_start.neutral |= PUD_ALLOW_SPELL_DEATH_COIL;

   /* We are not researching anything */
   memset(&pud->spell_acq, 0x00, sizeof(pud->spell_acq));
   memset(&pud->up_acq, 0x00, sizeof(pud->up_acq));
}

/* UDTA defaults are generated with tools/defaults_gen */

static void
_udta_defaults_set(Pud *pud)
{
   const Pud_Unit_Description udata[110] = {
      [0] = {
         .overlap_frames = 0,
         .sight = 4,
         .hp = 60,
         .build_time = 60,
         .gold_cost = 60,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 31,
         .box_h = 31,
         .range = 1,
         .computer_react_range = 6,
         .human_react_range = 4,
         .armor = 2,
         .priority = 60,
         .basic_damage = 6,
         .piercing_damage = 3,
         .missile_weapon = 29,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 50,
         .can_target = 1,
         .flags = 134742017,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 1,
         .armor_upgradable = 1
      },
      [1] = {
         .overlap_frames = 0,
         .sight = 4,
         .hp = 60,
         .build_time = 60,
         .gold_cost = 60,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 31,
         .box_h = 31,
         .range = 1,
         .computer_react_range = 6,
         .human_react_range = 4,
         .armor = 2,
         .priority = 60,
         .basic_damage = 6,
         .piercing_damage = 3,
         .missile_weapon = 29,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 50,
         .can_target = 1,
         .flags = 134742017,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 1,
         .armor_upgradable = 1
      },
      [2] = {
         .overlap_frames = 0,
         .sight = 4,
         .hp = 30,
         .build_time = 45,
         .gold_cost = 40,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 31,
         .box_h = 31,
         .range = 1,
         .computer_react_range = 6,
         .human_react_range = 4,
         .armor = 0,
         .priority = 50,
         .basic_damage = 3,
         .piercing_damage = 2,
         .missile_weapon = 29,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 3,
         .point_value = 30,
         .can_target = 1,
         .flags = 134742273,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [3] = {
         .overlap_frames = 0,
         .sight = 4,
         .hp = 30,
         .build_time = 45,
         .gold_cost = 40,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 31,
         .box_h = 31,
         .range = 1,
         .computer_react_range = 6,
         .human_react_range = 4,
         .armor = 0,
         .priority = 50,
         .basic_damage = 3,
         .piercing_damage = 2,
         .missile_weapon = 29,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 3,
         .point_value = 30,
         .can_target = 1,
         .flags = 134742273,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [4] = {
         .overlap_frames = 0,
         .sight = 9,
         .hp = 110,
         .build_time = 250,
         .gold_cost = 90,
         .lumber_cost = 30,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 63,
         .box_h = 63,
         .range = 8,
         .computer_react_range = 11,
         .human_react_range = 9,
         .armor = 0,
         .priority = 70,
         .basic_damage = 80,
         .piercing_damage = 0,
         .missile_weapon = 14,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 100,
         .can_target = 3,
         .flags = 540676,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 1,
         .armor_upgradable = 0
      },
      [5] = {
         .overlap_frames = 0,
         .sight = 9,
         .hp = 110,
         .build_time = 250,
         .gold_cost = 90,
         .lumber_cost = 30,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 63,
         .box_h = 63,
         .range = 8,
         .computer_react_range = 11,
         .human_react_range = 9,
         .armor = 0,
         .priority = 70,
         .basic_damage = 80,
         .piercing_damage = 0,
         .missile_weapon = 13,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 100,
         .can_target = 3,
         .flags = 540676,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 1,
         .armor_upgradable = 0
      },
      [6] = {
         .overlap_frames = 0,
         .sight = 4,
         .hp = 90,
         .build_time = 90,
         .gold_cost = 80,
         .lumber_cost = 10,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 42,
         .box_h = 42,
         .range = 1,
         .computer_react_range = 6,
         .human_react_range = 4,
         .armor = 4,
         .priority = 63,
         .basic_damage = 8,
         .piercing_damage = 4,
         .missile_weapon = 29,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 100,
         .can_target = 1,
         .flags = 134742017,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 1,
         .armor_upgradable = 1
      },
      [7] = {
         .overlap_frames = 0,
         .sight = 4,
         .hp = 90,
         .build_time = 90,
         .gold_cost = 80,
         .lumber_cost = 10,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 42,
         .box_h = 42,
         .range = 1,
         .computer_react_range = 6,
         .human_react_range = 4,
         .armor = 4,
         .priority = 63,
         .basic_damage = 8,
         .piercing_damage = 4,
         .missile_weapon = 29,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 100,
         .can_target = 1,
         .flags = 134742017,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 1,
         .armor_upgradable = 1
      },
      [8] = {
         .overlap_frames = 0,
         .sight = 5,
         .hp = 40,
         .build_time = 70,
         .gold_cost = 50,
         .lumber_cost = 5,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 33,
         .box_h = 33,
         .range = 4,
         .computer_react_range = 7,
         .human_react_range = 5,
         .armor = 0,
         .priority = 55,
         .basic_damage = 3,
         .piercing_damage = 6,
         .missile_weapon = 15,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 60,
         .can_target = 7,
         .flags = 134742017,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 1,
         .armor_upgradable = 0
      },
      [9] = {
         .overlap_frames = 0,
         .sight = 5,
         .hp = 40,
         .build_time = 70,
         .gold_cost = 50,
         .lumber_cost = 5,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 36,
         .box_h = 36,
         .range = 4,
         .computer_react_range = 7,
         .human_react_range = 5,
         .armor = 0,
         .priority = 55,
         .basic_damage = 3,
         .piercing_damage = 6,
         .missile_weapon = 16,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 60,
         .can_target = 7,
         .flags = 134742017,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 1,
         .armor_upgradable = 0
      },
      [10] = {
         .overlap_frames = 0,
         .sight = 9,
         .hp = 60,
         .build_time = 120,
         .gold_cost = 120,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 33,
         .box_h = 33,
         .range = 2,
         .computer_react_range = 11,
         .human_react_range = 9,
         .armor = 0,
         .priority = 70,
         .basic_damage = 0,
         .piercing_damage = 9,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 100,
         .can_target = 7,
         .flags = 201981953,
         .rect_sel = 1,
         .has_magic = 1,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [11] = {
         .overlap_frames = 0,
         .sight = 9,
         .hp = 60,
         .build_time = 120,
         .gold_cost = 120,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 39,
         .box_h = 39,
         .range = 3,
         .computer_react_range = 11,
         .human_react_range = 9,
         .armor = 0,
         .priority = 70,
         .basic_damage = 0,
         .piercing_damage = 9,
         .missile_weapon = 10,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 100,
         .can_target = 7,
         .flags = 202014721,
         .rect_sel = 1,
         .has_magic = 1,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [12] = {
         .overlap_frames = 0,
         .sight = 5,
         .hp = 90,
         .build_time = 90,
         .gold_cost = 80,
         .lumber_cost = 10,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 42,
         .box_h = 42,
         .range = 1,
         .computer_react_range = 7,
         .human_react_range = 5,
         .armor = 4,
         .priority = 65,
         .basic_damage = 8,
         .piercing_damage = 4,
         .missile_weapon = 29,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 110,
         .can_target = 1,
         .flags = 134873089,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 1,
         .armor_upgradable = 1
      },
      [13] = {
         .overlap_frames = 0,
         .sight = 5,
         .hp = 90,
         .build_time = 90,
         .gold_cost = 80,
         .lumber_cost = 10,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 42,
         .box_h = 42,
         .range = 1,
         .computer_react_range = 7,
         .human_react_range = 5,
         .armor = 4,
         .priority = 65,
         .basic_damage = 8,
         .piercing_damage = 4,
         .missile_weapon = 29,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 110,
         .can_target = 1,
         .flags = 134873089,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 1,
         .armor_upgradable = 1
      },
      [14] = {
         .overlap_frames = 0,
         .sight = 4,
         .hp = 40,
         .build_time = 200,
         .gold_cost = 70,
         .lumber_cost = 25,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 32,
         .box_h = 32,
         .range = 1,
         .computer_react_range = 4,
         .human_react_range = 2,
         .armor = 0,
         .priority = 55,
         .basic_damage = 4,
         .piercing_damage = 2,
         .missile_weapon = 29,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 5,
         .point_value = 100,
         .can_target = 1,
         .flags = 168296449,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 1,
         .armor_upgradable = 0
      },
      [15] = {
         .overlap_frames = 0,
         .sight = 4,
         .hp = 40,
         .build_time = 200,
         .gold_cost = 70,
         .lumber_cost = 25,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 37,
         .box_h = 37,
         .range = 1,
         .computer_react_range = 4,
         .human_react_range = 2,
         .armor = 0,
         .priority = 55,
         .basic_damage = 4,
         .piercing_damage = 2,
         .missile_weapon = 29,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 5,
         .point_value = 100,
         .can_target = 1,
         .flags = 168296449,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 1,
         .armor_upgradable = 0
      },
      [16] = {
         .overlap_frames = 0,
         .sight = 4,
         .hp = 30,
         .build_time = 45,
         .gold_cost = 40,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 31,
         .box_h = 31,
         .range = 1,
         .computer_react_range = 6,
         .human_react_range = 4,
         .armor = 0,
         .priority = 50,
         .basic_damage = 3,
         .piercing_damage = 2,
         .missile_weapon = 29,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 30,
         .can_target = 1,
         .flags = 134742017,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [17] = {
         .overlap_frames = 0,
         .sight = 4,
         .hp = 30,
         .build_time = 45,
         .gold_cost = 40,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 31,
         .box_h = 31,
         .range = 1,
         .computer_react_range = 6,
         .human_react_range = 4,
         .armor = 0,
         .priority = 50,
         .basic_damage = 3,
         .piercing_damage = 2,
         .missile_weapon = 29,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 30,
         .can_target = 1,
         .flags = 134742017,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [18] = {
         .overlap_frames = 0,
         .sight = 6,
         .hp = 50,
         .build_time = 70,
         .gold_cost = 50,
         .lumber_cost = 5,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 33,
         .box_h = 33,
         .range = 4,
         .computer_react_range = 9,
         .human_react_range = 6,
         .armor = 0,
         .priority = 57,
         .basic_damage = 3,
         .piercing_damage = 6,
         .missile_weapon = 15,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 70,
         .can_target = 7,
         .flags = 134742017,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 1,
         .armor_upgradable = 0
      },
      [19] = {
         .overlap_frames = 0,
         .sight = 6,
         .hp = 50,
         .build_time = 70,
         .gold_cost = 50,
         .lumber_cost = 5,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 36,
         .box_h = 36,
         .range = 4,
         .computer_react_range = 9,
         .human_react_range = 6,
         .armor = 0,
         .priority = 57,
         .basic_damage = 3,
         .piercing_damage = 6,
         .missile_weapon = 16,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 70,
         .can_target = 7,
         .flags = 134742017,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 1,
         .armor_upgradable = 0
      },
      [20] = {
         .overlap_frames = 0,
         .sight = 9,
         .hp = 120,
         .build_time = 70,
         .gold_cost = 50,
         .lumber_cost = 5,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 33,
         .box_h = 33,
         .range = 7,
         .computer_react_range = 7,
         .human_react_range = 5,
         .armor = 5,
         .priority = 55,
         .basic_damage = 10,
         .piercing_damage = 18,
         .missile_weapon = 15,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 60,
         .can_target = 7,
         .flags = 134742017,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 1,
         .armor_upgradable = 0
      },
      [21] = {
         .overlap_frames = 0,
         .sight = 9,
         .hp = 180,
         .build_time = 120,
         .gold_cost = 120,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 39,
         .box_h = 39,
         .range = 4,
         .computer_react_range = 11,
         .human_react_range = 9,
         .armor = 2,
         .priority = 70,
         .basic_damage = 0,
         .piercing_damage = 16,
         .missile_weapon = 10,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 100,
         .can_target = 7,
         .flags = 202014721,
         .rect_sel = 1,
         .has_magic = 1,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [22] = {
         .overlap_frames = 0,
         .sight = 9,
         .hp = 250,
         .build_time = 250,
         .gold_cost = 250,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 63,
         .box_h = 63,
         .range = 5,
         .computer_react_range = 8,
         .human_react_range = 6,
         .armor = 6,
         .priority = 65,
         .basic_damage = 0,
         .piercing_damage = 25,
         .missile_weapon = 1,
         .type = 1,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 150,
         .can_target = 7,
         .flags = 134742146,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [23] = {
         .overlap_frames = 0,
         .sight = 6,
         .hp = 300,
         .build_time = 90,
         .gold_cost = 80,
         .lumber_cost = 10,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 42,
         .box_h = 42,
         .range = 1,
         .computer_react_range = 6,
         .human_react_range = 4,
         .armor = 8,
         .priority = 63,
         .basic_damage = 18,
         .piercing_damage = 6,
         .missile_weapon = 29,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 100,
         .can_target = 1,
         .flags = 134873089,
         .rect_sel = 1,
         .has_magic = 1,
         .weapons_upgradable = 1,
         .armor_upgradable = 1
      },
      [24] = {
         .overlap_frames = 0,
         .sight = 9,
         .hp = 120,
         .build_time = 120,
         .gold_cost = 120,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 33,
         .box_h = 33,
         .range = 6,
         .computer_react_range = 11,
         .human_react_range = 9,
         .armor = 3,
         .priority = 70,
         .basic_damage = 0,
         .piercing_damage = 16,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 100,
         .can_target = 7,
         .flags = 201981953,
         .rect_sel = 1,
         .has_magic = 1,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [25] = {
         .overlap_frames = 0,
         .sight = 5,
         .hp = 240,
         .build_time = 60,
         .gold_cost = 60,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 31,
         .box_h = 31,
         .range = 1,
         .computer_react_range = 6,
         .human_react_range = 4,
         .armor = 8,
         .priority = 60,
         .basic_damage = 16,
         .piercing_damage = 6,
         .missile_weapon = 29,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 50,
         .can_target = 1,
         .flags = 134742017,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 1,
         .armor_upgradable = 1
      },
      [26] = {
         .overlap_frames = 0,
         .sight = 4,
         .hp = 90,
         .build_time = 50,
         .gold_cost = 40,
         .lumber_cost = 20,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 63,
         .box_h = 63,
         .range = 1,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 10,
         .priority = 50,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 29,
         .type = 2,
         .decay_rate = 0,
         .annoy = 10,
         .mouse_right_btn = 4,
         .point_value = 40,
         .can_target = 0,
         .flags = 520,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [27] = {
         .overlap_frames = 0,
         .sight = 4,
         .hp = 90,
         .build_time = 50,
         .gold_cost = 40,
         .lumber_cost = 20,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 63,
         .box_h = 63,
         .range = 1,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 10,
         .priority = 50,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 29,
         .type = 2,
         .decay_rate = 0,
         .annoy = 10,
         .mouse_right_btn = 4,
         .point_value = 40,
         .can_target = 0,
         .flags = 520,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [28] = {
         .overlap_frames = 0,
         .sight = 4,
         .hp = 150,
         .build_time = 70,
         .gold_cost = 60,
         .lumber_cost = 20,
         .oil_cost = 50,
         .size_w = 1,
         .size_h = 1,
         .box_w = 63,
         .box_h = 63,
         .range = 1,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 0,
         .priority = 70,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 29,
         .type = 2,
         .decay_rate = 0,
         .annoy = 15,
         .mouse_right_btn = 6,
         .point_value = 50,
         .can_target = 0,
         .flags = 1032,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 1
      },
      [29] = {
         .overlap_frames = 0,
         .sight = 4,
         .hp = 150,
         .build_time = 70,
         .gold_cost = 60,
         .lumber_cost = 20,
         .oil_cost = 50,
         .size_w = 1,
         .size_h = 1,
         .box_w = 63,
         .box_h = 63,
         .range = 1,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 0,
         .priority = 70,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 29,
         .type = 2,
         .decay_rate = 0,
         .annoy = 15,
         .mouse_right_btn = 6,
         .point_value = 50,
         .can_target = 0,
         .flags = 1032,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 1
      },
      [30] = {
         .overlap_frames = 0,
         .sight = 8,
         .hp = 100,
         .build_time = 90,
         .gold_cost = 70,
         .lumber_cost = 35,
         .oil_cost = 70,
         .size_w = 1,
         .size_h = 1,
         .box_w = 63,
         .box_h = 63,
         .range = 4,
         .computer_react_range = 10,
         .human_react_range = 8,
         .armor = 10,
         .priority = 65,
         .basic_damage = 35,
         .piercing_damage = 0,
         .missile_weapon = 24,
         .type = 2,
         .decay_rate = 0,
         .annoy = 20,
         .mouse_right_btn = 1,
         .point_value = 150,
         .can_target = 7,
         .flags = 524296,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 1,
         .armor_upgradable = 1
      },
      [31] = {
         .overlap_frames = 0,
         .sight = 8,
         .hp = 100,
         .build_time = 90,
         .gold_cost = 70,
         .lumber_cost = 35,
         .oil_cost = 70,
         .size_w = 1,
         .size_h = 1,
         .box_w = 63,
         .box_h = 63,
         .range = 4,
         .computer_react_range = 10,
         .human_react_range = 8,
         .armor = 10,
         .priority = 65,
         .basic_damage = 35,
         .piercing_damage = 0,
         .missile_weapon = 24,
         .type = 2,
         .decay_rate = 0,
         .annoy = 20,
         .mouse_right_btn = 1,
         .point_value = 150,
         .can_target = 7,
         .flags = 524296,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 1,
         .armor_upgradable = 1
      },
      [32] = {
         .overlap_frames = 0,
         .sight = 8,
         .hp = 150,
         .build_time = 140,
         .gold_cost = 100,
         .lumber_cost = 50,
         .oil_cost = 100,
         .size_w = 1,
         .size_h = 1,
         .box_w = 70,
         .box_h = 70,
         .range = 6,
         .computer_react_range = 10,
         .human_react_range = 8,
         .armor = 15,
         .priority = 63,
         .basic_damage = 130,
         .piercing_damage = 0,
         .missile_weapon = 7,
         .type = 2,
         .decay_rate = 0,
         .annoy = 25,
         .mouse_right_btn = 1,
         .point_value = 300,
         .can_target = 3,
         .flags = 540680,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 1,
         .armor_upgradable = 1
      },
      [33] = {
         .overlap_frames = 0,
         .sight = 8,
         .hp = 150,
         .build_time = 140,
         .gold_cost = 100,
         .lumber_cost = 50,
         .oil_cost = 100,
         .size_w = 1,
         .size_h = 1,
         .box_w = 70,
         .box_h = 70,
         .range = 6,
         .computer_react_range = 10,
         .human_react_range = 8,
         .armor = 15,
         .priority = 63,
         .basic_damage = 130,
         .piercing_damage = 0,
         .missile_weapon = 7,
         .type = 2,
         .decay_rate = 0,
         .annoy = 25,
         .mouse_right_btn = 1,
         .point_value = 300,
         .can_target = 3,
         .flags = 540680,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 1,
         .armor_upgradable = 1
      },
      [34] = {
         .overlap_frames = 0,
         .sight = 0,
         .hp = 0,
         .build_time = 0,
         .gold_cost = 0,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 0,
         .size_h = 0,
         .box_w = 0,
         .box_h = 0,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 0,
         .priority = 0,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 0,
         .point_value = 0,
         .can_target = 0,
         .flags = 0,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [35] = {
         .overlap_frames = 0,
         .sight = 9,
         .hp = 800,
         .build_time = 250,
         .gold_cost = 250,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 71,
         .box_h = 71,
         .range = 5,
         .computer_react_range = 8,
         .human_react_range = 6,
         .armor = 10,
         .priority = 65,
         .basic_damage = 10,
         .piercing_damage = 25,
         .missile_weapon = 2,
         .type = 1,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 150,
         .can_target = 7,
         .flags = 134742146,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [36] = {
         .overlap_frames = 0,
         .sight = 4,
         .hp = 60,
         .build_time = 60,
         .gold_cost = 40,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 63,
         .box_h = 63,
         .range = 1,
         .computer_react_range = 20,
         .human_react_range = 10,
         .armor = 2,
         .priority = 40,
         .basic_damage = 9,
         .piercing_damage = 1,
         .missile_weapon = 29,
         .type = 2,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 0,
         .can_target = 0,
         .flags = 8,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [37] = {
         .overlap_frames = 0,
         .sight = 4,
         .hp = 60,
         .build_time = 60,
         .gold_cost = 40,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 63,
         .box_h = 63,
         .range = 1,
         .computer_react_range = 20,
         .human_react_range = 10,
         .armor = 2,
         .priority = 40,
         .basic_damage = 9,
         .piercing_damage = 1,
         .missile_weapon = 29,
         .type = 2,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 0,
         .can_target = 0,
         .flags = 8,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [38] = {
         .overlap_frames = 0,
         .sight = 5,
         .hp = 60,
         .build_time = 100,
         .gold_cost = 80,
         .lumber_cost = 15,
         .oil_cost = 90,
         .size_w = 1,
         .size_h = 1,
         .box_w = 63,
         .box_h = 63,
         .range = 4,
         .computer_react_range = 7,
         .human_react_range = 5,
         .armor = 0,
         .priority = 60,
         .basic_damage = 50,
         .piercing_damage = 0,
         .missile_weapon = 17,
         .type = 2,
         .decay_rate = 0,
         .annoy = 20,
         .mouse_right_btn = 1,
         .point_value = 120,
         .can_target = 2,
         .flags = 524488,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [39] = {
         .overlap_frames = 0,
         .sight = 5,
         .hp = 60,
         .build_time = 100,
         .gold_cost = 80,
         .lumber_cost = 15,
         .oil_cost = 90,
         .size_w = 1,
         .size_h = 1,
         .box_w = 63,
         .box_h = 63,
         .range = 4,
         .computer_react_range = 7,
         .human_react_range = 5,
         .armor = 0,
         .priority = 60,
         .basic_damage = 50,
         .piercing_damage = 0,
         .missile_weapon = 18,
         .type = 2,
         .decay_rate = 0,
         .annoy = 20,
         .mouse_right_btn = 1,
         .point_value = 120,
         .can_target = 2,
         .flags = 524488,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [40] = {
         .overlap_frames = 0,
         .sight = 9,
         .hp = 150,
         .build_time = 65,
         .gold_cost = 50,
         .lumber_cost = 10,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 63,
         .box_h = 63,
         .range = 1,
         .computer_react_range = 19,
         .human_react_range = 15,
         .armor = 2,
         .priority = 40,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 29,
         .type = 1,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 2,
         .point_value = 40,
         .can_target = 0,
         .flags = 130,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [41] = {
         .overlap_frames = 0,
         .sight = 9,
         .hp = 150,
         .build_time = 65,
         .gold_cost = 50,
         .lumber_cost = 10,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 63,
         .box_h = 63,
         .range = 1,
         .computer_react_range = 19,
         .human_react_range = 15,
         .armor = 2,
         .priority = 40,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 29,
         .type = 1,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 2,
         .point_value = 40,
         .can_target = 0,
         .flags = 130,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [42] = {
         .overlap_frames = 0,
         .sight = 6,
         .hp = 100,
         .build_time = 250,
         .gold_cost = 250,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 63,
         .box_h = 63,
         .range = 4,
         .computer_react_range = 8,
         .human_react_range = 6,
         .armor = 5,
         .priority = 65,
         .basic_damage = 0,
         .piercing_damage = 16,
         .missile_weapon = 1,
         .type = 1,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 150,
         .can_target = 7,
         .flags = 134742146,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [43] = {
         .overlap_frames = 0,
         .sight = 6,
         .hp = 100,
         .build_time = 250,
         .gold_cost = 250,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 71,
         .box_h = 71,
         .range = 4,
         .computer_react_range = 8,
         .human_react_range = 6,
         .armor = 5,
         .priority = 65,
         .basic_damage = 0,
         .piercing_damage = 16,
         .missile_weapon = 2,
         .type = 1,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 150,
         .can_target = 7,
         .flags = 134742146,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [44] = {
         .overlap_frames = 0,
         .sight = 6,
         .hp = 180,
         .build_time = 90,
         .gold_cost = 80,
         .lumber_cost = 10,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 42,
         .box_h = 42,
         .range = 1,
         .computer_react_range = 7,
         .human_react_range = 5,
         .armor = 10,
         .priority = 65,
         .basic_damage = 14,
         .piercing_damage = 5,
         .missile_weapon = 29,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 110,
         .can_target = 1,
         .flags = 134873089,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 1,
         .armor_upgradable = 1
      },
      [45] = {
         .overlap_frames = 0,
         .sight = 3,
         .hp = 100,
         .build_time = 0,
         .gold_cost = 0,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 31,
         .box_h = 31,
         .range = 1,
         .computer_react_range = 20,
         .human_react_range = 10,
         .armor = 0,
         .priority = 0,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 29,
         .type = 1,
         .decay_rate = 3,
         .annoy = 0,
         .mouse_right_btn = 2,
         .point_value = 0,
         .can_target = 0,
         .flags = 130,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [46] = {
         .overlap_frames = 0,
         .sight = 6,
         .hp = 220,
         .build_time = 60,
         .gold_cost = 60,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 31,
         .box_h = 31,
         .range = 1,
         .computer_react_range = 6,
         .human_react_range = 4,
         .armor = 8,
         .priority = 60,
         .basic_damage = 15,
         .piercing_damage = 8,
         .missile_weapon = 29,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 50,
         .can_target = 1,
         .flags = 134742017,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 1,
         .armor_upgradable = 1
      },
      [47] = {
         .overlap_frames = 0,
         .sight = 5,
         .hp = 240,
         .build_time = 60,
         .gold_cost = 60,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 31,
         .box_h = 31,
         .range = 1,
         .computer_react_range = 6,
         .human_react_range = 4,
         .armor = 8,
         .priority = 60,
         .basic_damage = 16,
         .piercing_damage = 6,
         .missile_weapon = 29,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 50,
         .can_target = 1,
         .flags = 134742017,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 1,
         .armor_upgradable = 1
      },
      [48] = {
         .overlap_frames = 0,
         .sight = 0,
         .hp = 0,
         .build_time = 0,
         .gold_cost = 0,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 0,
         .size_h = 0,
         .box_w = 0,
         .box_h = 0,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 0,
         .priority = 0,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 0,
         .point_value = 0,
         .can_target = 0,
         .flags = 0,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [49] = {
         .overlap_frames = 0,
         .sight = 5,
         .hp = 100,
         .build_time = 100,
         .gold_cost = 110,
         .lumber_cost = 5,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 42,
         .box_h = 42,
         .range = 1,
         .computer_react_range = 7,
         .human_react_range = 5,
         .armor = 0,
         .priority = 65,
         .basic_damage = 10,
         .piercing_damage = 5,
         .missile_weapon = 29,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 120,
         .can_target = 1,
         .flags = 143261697,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 1,
         .armor_upgradable = 1
      },
      [50] = {
         .overlap_frames = 0,
         .sight = 5,
         .hp = 90,
         .build_time = 100,
         .gold_cost = 90,
         .lumber_cost = 10,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 42,
         .box_h = 42,
         .range = 1,
         .computer_react_range = 7,
         .human_react_range = 5,
         .armor = 4,
         .priority = 65,
         .basic_damage = 8,
         .piercing_damage = 4,
         .missile_weapon = 29,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 120,
         .can_target = 1,
         .flags = 143130625,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 1,
         .armor_upgradable = 1
      },
      [51] = {
         .overlap_frames = 0,
         .sight = 8,
         .hp = 40,
         .build_time = 120,
         .gold_cost = 120,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 33,
         .box_h = 33,
         .range = 3,
         .computer_react_range = 10,
         .human_react_range = 8,
         .armor = 0,
         .priority = 70,
         .basic_damage = 0,
         .piercing_damage = 3,
         .missile_weapon = 10,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 120,
         .can_target = 7,
         .flags = 143294465,
         .rect_sel = 1,
         .has_magic = 1,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [52] = {
         .overlap_frames = 0,
         .sight = 5,
         .hp = 90,
         .build_time = 100,
         .gold_cost = 90,
         .lumber_cost = 10,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 42,
         .box_h = 42,
         .range = 1,
         .computer_react_range = 7,
         .human_react_range = 5,
         .armor = 4,
         .priority = 65,
         .basic_damage = 8,
         .piercing_damage = 4,
         .missile_weapon = 29,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 120,
         .can_target = 1,
         .flags = 143261697,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 1,
         .armor_upgradable = 1
      },
      [53] = {
         .overlap_frames = 0,
         .sight = 6,
         .hp = 40,
         .build_time = 70,
         .gold_cost = 50,
         .lumber_cost = 5,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 36,
         .box_h = 36,
         .range = 5,
         .computer_react_range = 8,
         .human_react_range = 6,
         .armor = 0,
         .priority = 55,
         .basic_damage = 3,
         .piercing_damage = 6,
         .missile_weapon = 16,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 120,
         .can_target = 7,
         .flags = 143130625,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 1,
         .armor_upgradable = 0
      },
      [54] = {
         .overlap_frames = 0,
         .sight = 0,
         .hp = 0,
         .build_time = 0,
         .gold_cost = 0,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 0,
         .size_h = 0,
         .box_w = 0,
         .box_h = 0,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 0,
         .priority = 0,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 0,
         .point_value = 0,
         .can_target = 0,
         .flags = 0,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [55] = {
         .overlap_frames = 0,
         .sight = 3,
         .hp = 40,
         .build_time = 0,
         .gold_cost = 0,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 31,
         .box_h = 31,
         .range = 1,
         .computer_react_range = 4,
         .human_react_range = 2,
         .armor = 0,
         .priority = 55,
         .basic_damage = 6,
         .piercing_damage = 3,
         .missile_weapon = 29,
         .type = 0,
         .decay_rate = 100,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 0,
         .can_target = 1,
         .flags = 134774801,
         .rect_sel = 1,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [56] = {
         .overlap_frames = 0,
         .sight = 5,
         .hp = 60,
         .build_time = 0,
         .gold_cost = 0,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 31,
         .box_h = 31,
         .range = 2,
         .computer_react_range = 7,
         .human_react_range = 5,
         .armor = 2,
         .priority = 63,
         .basic_damage = 9,
         .piercing_damage = 1,
         .missile_weapon = 27,
         .type = 1,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 1,
         .point_value = 100,
         .can_target = 7,
         .flags = 134742162,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [57] = {
         .overlap_frames = 0,
         .sight = 2,
         .hp = 5,
         .build_time = 0,
         .gold_cost = 0,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 31,
         .box_h = 31,
         .range = 1,
         .computer_react_range = 20,
         .human_react_range = 10,
         .armor = 0,
         .priority = 37,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 29,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 2,
         .point_value = 1,
         .can_target = 0,
         .flags = 134217745,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [58] = {
         .overlap_frames = 6,
         .sight = 3,
         .hp = 400,
         .build_time = 100,
         .gold_cost = 50,
         .lumber_cost = 25,
         .oil_cost = 0,
         .size_w = 2,
         .size_h = 2,
         .box_w = 63,
         .box_h = 63,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 20,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 45,
         .mouse_right_btn = 255,
         .point_value = 100,
         .can_target = 0,
         .flags = 32,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [59] = {
         .overlap_frames = 6,
         .sight = 3,
         .hp = 400,
         .build_time = 100,
         .gold_cost = 50,
         .lumber_cost = 25,
         .oil_cost = 0,
         .size_w = 2,
         .size_h = 2,
         .box_w = 63,
         .box_h = 63,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 20,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 45,
         .mouse_right_btn = 255,
         .point_value = 100,
         .can_target = 0,
         .flags = 32,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [60] = {
         .overlap_frames = 6,
         .sight = 3,
         .hp = 800,
         .build_time = 200,
         .gold_cost = 70,
         .lumber_cost = 45,
         .oil_cost = 0,
         .size_w = 3,
         .size_h = 3,
         .box_w = 95,
         .box_h = 95,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 30,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 35,
         .mouse_right_btn = 255,
         .point_value = 160,
         .can_target = 0,
         .flags = 32,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [61] = {
         .overlap_frames = 6,
         .sight = 3,
         .hp = 800,
         .build_time = 200,
         .gold_cost = 70,
         .lumber_cost = 45,
         .oil_cost = 0,
         .size_w = 3,
         .size_h = 3,
         .box_w = 95,
         .box_h = 95,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 30,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 35,
         .mouse_right_btn = 255,
         .point_value = 160,
         .can_target = 0,
         .flags = 32,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [62] = {
         .overlap_frames = 6,
         .sight = 3,
         .hp = 700,
         .build_time = 175,
         .gold_cost = 90,
         .lumber_cost = 50,
         .oil_cost = 0,
         .size_w = 3,
         .size_h = 3,
         .box_w = 95,
         .box_h = 95,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 15,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 35,
         .mouse_right_btn = 255,
         .point_value = 240,
         .can_target = 0,
         .flags = 32,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [63] = {
         .overlap_frames = 6,
         .sight = 3,
         .hp = 700,
         .build_time = 175,
         .gold_cost = 90,
         .lumber_cost = 50,
         .oil_cost = 0,
         .size_w = 3,
         .size_h = 3,
         .box_w = 95,
         .box_h = 95,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 15,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 35,
         .mouse_right_btn = 255,
         .point_value = 240,
         .can_target = 0,
         .flags = 32,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [64] = {
         .overlap_frames = 6,
         .sight = 9,
         .hp = 100,
         .build_time = 60,
         .gold_cost = 55,
         .lumber_cost = 20,
         .oil_cost = 0,
         .size_w = 2,
         .size_h = 2,
         .box_w = 63,
         .box_h = 63,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 55,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 29,
         .type = 0,
         .decay_rate = 0,
         .annoy = 50,
         .mouse_right_btn = 255,
         .point_value = 95,
         .can_target = 7,
         .flags = 160,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [65] = {
         .overlap_frames = 6,
         .sight = 9,
         .hp = 100,
         .build_time = 60,
         .gold_cost = 55,
         .lumber_cost = 20,
         .oil_cost = 0,
         .size_w = 2,
         .size_h = 2,
         .box_w = 63,
         .box_h = 63,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 55,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 29,
         .type = 0,
         .decay_rate = 0,
         .annoy = 50,
         .mouse_right_btn = 255,
         .point_value = 95,
         .can_target = 7,
         .flags = 160,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [66] = {
         .overlap_frames = 6,
         .sight = 3,
         .hp = 500,
         .build_time = 150,
         .gold_cost = 100,
         .lumber_cost = 30,
         .oil_cost = 0,
         .size_w = 3,
         .size_h = 3,
         .box_w = 95,
         .box_h = 95,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 15,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 15,
         .mouse_right_btn = 255,
         .point_value = 210,
         .can_target = 0,
         .flags = 32,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [67] = {
         .overlap_frames = 6,
         .sight = 3,
         .hp = 500,
         .build_time = 150,
         .gold_cost = 100,
         .lumber_cost = 30,
         .oil_cost = 0,
         .size_w = 3,
         .size_h = 3,
         .box_w = 95,
         .box_h = 95,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 15,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 15,
         .mouse_right_btn = 255,
         .point_value = 210,
         .can_target = 0,
         .flags = 32,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [68] = {
         .overlap_frames = 6,
         .sight = 3,
         .hp = 500,
         .build_time = 150,
         .gold_cost = 100,
         .lumber_cost = 40,
         .oil_cost = 0,
         .size_w = 3,
         .size_h = 3,
         .box_w = 95,
         .box_h = 95,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 15,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 20,
         .mouse_right_btn = 255,
         .point_value = 230,
         .can_target = 0,
         .flags = 32,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [69] = {
         .overlap_frames = 6,
         .sight = 3,
         .hp = 500,
         .build_time = 150,
         .gold_cost = 100,
         .lumber_cost = 40,
         .oil_cost = 0,
         .size_w = 3,
         .size_h = 3,
         .box_w = 95,
         .box_h = 95,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 15,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 20,
         .mouse_right_btn = 255,
         .point_value = 230,
         .can_target = 0,
         .flags = 32,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [70] = {
         .overlap_frames = 6,
         .sight = 3,
         .hp = 500,
         .build_time = 150,
         .gold_cost = 100,
         .lumber_cost = 40,
         .oil_cost = 0,
         .size_w = 3,
         .size_h = 3,
         .box_w = 95,
         .box_h = 95,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 15,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 20,
         .mouse_right_btn = 255,
         .point_value = 280,
         .can_target = 0,
         .flags = 32,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [71] = {
         .overlap_frames = 6,
         .sight = 3,
         .hp = 500,
         .build_time = 150,
         .gold_cost = 100,
         .lumber_cost = 40,
         .oil_cost = 0,
         .size_w = 3,
         .size_h = 3,
         .box_w = 95,
         .box_h = 95,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 15,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 20,
         .mouse_right_btn = 255,
         .point_value = 280,
         .can_target = 0,
         .flags = 32,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [72] = {
         .overlap_frames = 7,
         .sight = 3,
         .hp = 1100,
         .build_time = 200,
         .gold_cost = 80,
         .lumber_cost = 45,
         .oil_cost = 0,
         .size_w = 3,
         .size_h = 3,
         .box_w = 95,
         .box_h = 95,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 30,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 20,
         .mouse_right_btn = 255,
         .point_value = 170,
         .can_target = 0,
         .flags = 16842784,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [73] = {
         .overlap_frames = 8,
         .sight = 3,
         .hp = 1100,
         .build_time = 200,
         .gold_cost = 80,
         .lumber_cost = 45,
         .oil_cost = 0,
         .size_w = 3,
         .size_h = 3,
         .box_w = 95,
         .box_h = 95,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 30,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 20,
         .mouse_right_btn = 255,
         .point_value = 170,
         .can_target = 0,
         .flags = 16842784,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [74] = {
         .overlap_frames = 6,
         .sight = 4,
         .hp = 1200,
         .build_time = 255,
         .gold_cost = 120,
         .lumber_cost = 80,
         .oil_cost = 0,
         .size_w = 4,
         .size_h = 4,
         .box_w = 126,
         .box_h = 126,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 35,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 45,
         .mouse_right_btn = 255,
         .point_value = 200,
         .can_target = 0,
         .flags = 4128,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [75] = {
         .overlap_frames = 6,
         .sight = 4,
         .hp = 1200,
         .build_time = 255,
         .gold_cost = 120,
         .lumber_cost = 80,
         .oil_cost = 0,
         .size_w = 4,
         .size_h = 4,
         .box_w = 127,
         .box_h = 127,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 35,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 45,
         .mouse_right_btn = 255,
         .point_value = 200,
         .can_target = 0,
         .flags = 4128,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [76] = {
         .overlap_frames = 6,
         .sight = 3,
         .hp = 600,
         .build_time = 150,
         .gold_cost = 60,
         .lumber_cost = 45,
         .oil_cost = 0,
         .size_w = 3,
         .size_h = 3,
         .box_w = 95,
         .box_h = 95,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 25,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 15,
         .mouse_right_btn = 255,
         .point_value = 150,
         .can_target = 0,
         .flags = 262176,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [77] = {
         .overlap_frames = 6,
         .sight = 3,
         .hp = 600,
         .build_time = 150,
         .gold_cost = 60,
         .lumber_cost = 45,
         .oil_cost = 0,
         .size_w = 3,
         .size_h = 3,
         .box_w = 95,
         .box_h = 95,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 25,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 15,
         .mouse_right_btn = 255,
         .point_value = 150,
         .can_target = 0,
         .flags = 262176,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [78] = {
         .overlap_frames = 13,
         .sight = 3,
         .hp = 750,
         .build_time = 175,
         .gold_cost = 70,
         .lumber_cost = 40,
         .oil_cost = 40,
         .size_w = 3,
         .size_h = 3,
         .box_w = 95,
         .box_h = 95,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 15,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 20,
         .mouse_right_btn = 255,
         .point_value = 200,
         .can_target = 0,
         .flags = 65568,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [79] = {
         .overlap_frames = 14,
         .sight = 3,
         .hp = 750,
         .build_time = 175,
         .gold_cost = 70,
         .lumber_cost = 40,
         .oil_cost = 40,
         .size_w = 3,
         .size_h = 3,
         .box_w = 95,
         .box_h = 95,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 15,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 20,
         .mouse_right_btn = 255,
         .point_value = 200,
         .can_target = 0,
         .flags = 65568,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [80] = {
         .overlap_frames = 6,
         .sight = 3,
         .hp = 500,
         .build_time = 125,
         .gold_cost = 100,
         .lumber_cost = 20,
         .oil_cost = 0,
         .size_w = 3,
         .size_h = 3,
         .box_w = 95,
         .box_h = 95,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 35,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 20,
         .mouse_right_btn = 255,
         .point_value = 240,
         .can_target = 0,
         .flags = 32,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [81] = {
         .overlap_frames = 6,
         .sight = 3,
         .hp = 500,
         .build_time = 125,
         .gold_cost = 100,
         .lumber_cost = 20,
         .oil_cost = 0,
         .size_w = 3,
         .size_h = 3,
         .box_w = 95,
         .box_h = 95,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 35,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 20,
         .mouse_right_btn = 255,
         .point_value = 240,
         .can_target = 0,
         .flags = 32,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [82] = {
         .overlap_frames = 6,
         .sight = 3,
         .hp = 775,
         .build_time = 200,
         .gold_cost = 80,
         .lumber_cost = 45,
         .oil_cost = 10,
         .size_w = 3,
         .size_h = 3,
         .box_w = 95,
         .box_h = 95,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 15,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 20,
         .mouse_right_btn = 255,
         .point_value = 170,
         .can_target = 0,
         .flags = 32,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [83] = {
         .overlap_frames = 6,
         .sight = 3,
         .hp = 775,
         .build_time = 200,
         .gold_cost = 80,
         .lumber_cost = 45,
         .oil_cost = 10,
         .size_w = 3,
         .size_h = 3,
         .box_w = 95,
         .box_h = 95,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 15,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 20,
         .mouse_right_btn = 255,
         .point_value = 170,
         .can_target = 0,
         .flags = 32,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [84] = {
         .overlap_frames = 11,
         .sight = 3,
         .hp = 600,
         .build_time = 225,
         .gold_cost = 80,
         .lumber_cost = 35,
         .oil_cost = 20,
         .size_w = 3,
         .size_h = 3,
         .box_w = 95,
         .box_h = 95,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 25,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 20,
         .mouse_right_btn = 255,
         .point_value = 200,
         .can_target = 0,
         .flags = 16842784,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [85] = {
         .overlap_frames = 12,
         .sight = 3,
         .hp = 600,
         .build_time = 225,
         .gold_cost = 80,
         .lumber_cost = 35,
         .oil_cost = 20,
         .size_w = 3,
         .size_h = 3,
         .box_w = 95,
         .box_h = 95,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 25,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 20,
         .mouse_right_btn = 255,
         .point_value = 200,
         .can_target = 0,
         .flags = 16842784,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [86] = {
         .overlap_frames = 9,
         .sight = 3,
         .hp = 650,
         .build_time = 200,
         .gold_cost = 70,
         .lumber_cost = 45,
         .oil_cost = 0,
         .size_w = 3,
         .size_h = 3,
         .box_w = 95,
         .box_h = 95,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 20,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 2,
         .decay_rate = 0,
         .annoy = 20,
         .mouse_right_btn = 255,
         .point_value = 160,
         .can_target = 0,
         .flags = 2080,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [87] = {
         .overlap_frames = 10,
         .sight = 3,
         .hp = 650,
         .build_time = 200,
         .gold_cost = 70,
         .lumber_cost = 45,
         .oil_cost = 0,
         .size_w = 3,
         .size_h = 3,
         .box_w = 95,
         .box_h = 95,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 20,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 2,
         .decay_rate = 0,
         .annoy = 20,
         .mouse_right_btn = 255,
         .point_value = 160,
         .can_target = 0,
         .flags = 2080,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [88] = {
         .overlap_frames = 6,
         .sight = 6,
         .hp = 1400,
         .build_time = 200,
         .gold_cost = 200,
         .lumber_cost = 100,
         .oil_cost = 20,
         .size_w = 4,
         .size_h = 4,
         .box_w = 127,
         .box_h = 127,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 37,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 40,
         .mouse_right_btn = 255,
         .point_value = 600,
         .can_target = 0,
         .flags = 4128,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [89] = {
         .overlap_frames = 6,
         .sight = 6,
         .hp = 1400,
         .build_time = 200,
         .gold_cost = 200,
         .lumber_cost = 100,
         .oil_cost = 20,
         .size_w = 4,
         .size_h = 4,
         .box_w = 127,
         .box_h = 127,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 37,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 40,
         .mouse_right_btn = 255,
         .point_value = 600,
         .can_target = 0,
         .flags = 4128,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [90] = {
         .overlap_frames = 6,
         .sight = 9,
         .hp = 1600,
         .build_time = 200,
         .gold_cost = 250,
         .lumber_cost = 120,
         .oil_cost = 50,
         .size_w = 4,
         .size_h = 4,
         .box_w = 127,
         .box_h = 127,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 40,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 50,
         .mouse_right_btn = 255,
         .point_value = 1500,
         .can_target = 0,
         .flags = 4128,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [91] = {
         .overlap_frames = 6,
         .sight = 9,
         .hp = 1600,
         .build_time = 200,
         .gold_cost = 250,
         .lumber_cost = 120,
         .oil_cost = 50,
         .size_w = 4,
         .size_h = 4,
         .box_w = 127,
         .box_h = 127,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 40,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 50,
         .mouse_right_btn = 255,
         .point_value = 1500,
         .can_target = 0,
         .flags = 4128,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [92] = {
         .overlap_frames = 6,
         .sight = 3,
         .hp = 25500,
         .build_time = 150,
         .gold_cost = 0,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 3,
         .size_h = 3,
         .box_w = 95,
         .box_h = 95,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 0,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 255,
         .point_value = 0,
         .can_target = 0,
         .flags = 4194336,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [93] = {
         .overlap_frames = 0,
         .sight = 0,
         .hp = 0,
         .build_time = 0,
         .gold_cost = 0,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 3,
         .size_h = 3,
         .box_w = 95,
         .box_h = 95,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 0,
         .priority = 0,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 2,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 255,
         .point_value = 0,
         .can_target = 0,
         .flags = 2097184,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [94] = {
         .overlap_frames = 0,
         .sight = 0,
         .hp = 0,
         .build_time = 0,
         .gold_cost = 0,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 31,
         .box_h = 31,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 0,
         .priority = 0,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 255,
         .point_value = 0,
         .can_target = 0,
         .flags = 0,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [95] = {
         .overlap_frames = 0,
         .sight = 0,
         .hp = 0,
         .build_time = 0,
         .gold_cost = 0,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 31,
         .box_h = 31,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 0,
         .priority = 0,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 255,
         .point_value = 0,
         .can_target = 0,
         .flags = 0,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [96] = {
         .overlap_frames = 6,
         .sight = 9,
         .hp = 130,
         .build_time = 140,
         .gold_cost = 50,
         .lumber_cost = 15,
         .oil_cost = 0,
         .size_w = 2,
         .size_h = 2,
         .box_w = 63,
         .box_h = 63,
         .range = 6,
         .computer_react_range = 6,
         .human_react_range = 6,
         .armor = 20,
         .priority = 40,
         .basic_damage = 4,
         .piercing_damage = 12,
         .missile_weapon = 15,
         .type = 0,
         .decay_rate = 0,
         .annoy = 50,
         .mouse_right_btn = 255,
         .point_value = 200,
         .can_target = 7,
         .flags = 1573024,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [97] = {
         .overlap_frames = 6,
         .sight = 9,
         .hp = 130,
         .build_time = 140,
         .gold_cost = 50,
         .lumber_cost = 15,
         .oil_cost = 0,
         .size_w = 2,
         .size_h = 2,
         .box_w = 63,
         .box_h = 63,
         .range = 6,
         .computer_react_range = 6,
         .human_react_range = 6,
         .armor = 20,
         .priority = 40,
         .basic_damage = 4,
         .piercing_damage = 12,
         .missile_weapon = 15,
         .type = 0,
         .decay_rate = 0,
         .annoy = 50,
         .mouse_right_btn = 255,
         .point_value = 200,
         .can_target = 7,
         .flags = 1573024,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [98] = {
         .overlap_frames = 6,
         .sight = 9,
         .hp = 160,
         .build_time = 190,
         .gold_cost = 100,
         .lumber_cost = 30,
         .oil_cost = 0,
         .size_w = 2,
         .size_h = 2,
         .box_w = 63,
         .box_h = 63,
         .range = 7,
         .computer_react_range = 7,
         .human_react_range = 7,
         .armor = 20,
         .priority = 40,
         .basic_damage = 50,
         .piercing_damage = 0,
         .missile_weapon = 24,
         .type = 0,
         .decay_rate = 0,
         .annoy = 50,
         .mouse_right_btn = 255,
         .point_value = 250,
         .can_target = 3,
         .flags = 1573024,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [99] = {
         .overlap_frames = 6,
         .sight = 9,
         .hp = 160,
         .build_time = 190,
         .gold_cost = 100,
         .lumber_cost = 30,
         .oil_cost = 0,
         .size_w = 2,
         .size_h = 2,
         .box_w = 63,
         .box_h = 63,
         .range = 7,
         .computer_react_range = 7,
         .human_react_range = 7,
         .armor = 20,
         .priority = 40,
         .basic_damage = 50,
         .piercing_damage = 0,
         .missile_weapon = 24,
         .type = 0,
         .decay_rate = 0,
         .annoy = 50,
         .mouse_right_btn = 255,
         .point_value = 250,
         .can_target = 3,
         .flags = 1573024,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [100] = {
         .overlap_frames = 0,
         .sight = 0,
         .hp = 0,
         .build_time = 0,
         .gold_cost = 0,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 2,
         .size_h = 2,
         .box_w = 63,
         .box_h = 63,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 0,
         .priority = 0,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 255,
         .point_value = 0,
         .can_target = 0,
         .flags = 32,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [101] = {
         .overlap_frames = 0,
         .sight = 0,
         .hp = 5000,
         .build_time = 0,
         .gold_cost = 0,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 4,
         .size_h = 4,
         .box_w = 127,
         .box_h = 127,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 0,
         .priority = 0,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 255,
         .point_value = 0,
         .can_target = 0,
         .flags = 32,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [102] = {
         .overlap_frames = 6,
         .sight = 4,
         .hp = 5000,
         .build_time = 175,
         .gold_cost = 90,
         .lumber_cost = 50,
         .oil_cost = 0,
         .size_w = 2,
         .size_h = 2,
         .box_w = 63,
         .box_h = 63,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 20,
         .priority = 15,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 35,
         .mouse_right_btn = 255,
         .point_value = 150,
         .can_target = 0,
         .flags = 32,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [103] = {
         .overlap_frames = 15,
         .sight = 1,
         .hp = 40,
         .build_time = 30,
         .gold_cost = 2,
         .lumber_cost = 1,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 31,
         .box_h = 31,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 0,
         .priority = 0,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 45,
         .mouse_right_btn = 255,
         .point_value = 1,
         .can_target = 0,
         .flags = 32,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [104] = {
         .overlap_frames = 15,
         .sight = 1,
         .hp = 40,
         .build_time = 30,
         .gold_cost = 2,
         .lumber_cost = 1,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 31,
         .box_h = 31,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 0,
         .priority = 0,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 45,
         .mouse_right_btn = 255,
         .point_value = 1,
         .can_target = 0,
         .flags = 32,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [105] = {
         .overlap_frames = 0,
         .sight = 1,
         .hp = 255,
         .build_time = 0,
         .gold_cost = 0,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 31,
         .box_h = 31,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 0,
         .priority = 0,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 255,
         .point_value = 0,
         .can_target = 0,
         .flags = 8192,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [106] = {
         .overlap_frames = 0,
         .sight = 2,
         .hp = 255,
         .build_time = 0,
         .gold_cost = 0,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 1,
         .size_h = 1,
         .box_w = 31,
         .box_h = 31,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 0,
         .priority = 0,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 255,
         .point_value = 0,
         .can_target = 0,
         .flags = 8224,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [107] = {
         .overlap_frames = 0,
         .sight = 2,
         .hp = 255,
         .build_time = 0,
         .gold_cost = 0,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 2,
         .size_h = 2,
         .box_w = 63,
         .box_h = 63,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 0,
         .priority = 0,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 255,
         .point_value = 0,
         .can_target = 0,
         .flags = 8224,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [108] = {
         .overlap_frames = 0,
         .sight = 3,
         .hp = 255,
         .build_time = 0,
         .gold_cost = 0,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 3,
         .size_h = 3,
         .box_w = 95,
         .box_h = 95,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 0,
         .priority = 0,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 255,
         .point_value = 0,
         .can_target = 0,
         .flags = 8224,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      },
      [109] = {
         .overlap_frames = 0,
         .sight = 3,
         .hp = 255,
         .build_time = 0,
         .gold_cost = 0,
         .lumber_cost = 0,
         .oil_cost = 0,
         .size_w = 4,
         .size_h = 4,
         .box_w = 127,
         .box_h = 127,
         .range = 0,
         .computer_react_range = 0,
         .human_react_range = 0,
         .armor = 0,
         .priority = 0,
         .basic_damage = 0,
         .piercing_damage = 0,
         .missile_weapon = 0,
         .type = 0,
         .decay_rate = 0,
         .annoy = 0,
         .mouse_right_btn = 255,
         .point_value = 0,
         .can_target = 0,
         .flags = 8224,
         .rect_sel = 0,
         .has_magic = 0,
         .weapons_upgradable = 0,
         .armor_upgradable = 0
      }
   };

   memcpy(&(pud->units_descr[0]), &(udata[0]), sizeof(udata));
}
