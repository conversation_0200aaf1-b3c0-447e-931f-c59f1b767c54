/**
 * MapsCore.js - Core Maps System Initialization and State Management
 * 
 * Extracted from the 72KB maps.js monster.
 * Handles core functionality, state management, authentication, and initialization.
 * 
 * Responsibilities:
 * - Core initialization and state management
 * - Authentication checking and user management
 * - Global event listeners and UI setup
 * - Tab switching and navigation logic
 * - Integration with other maps modules
 */

export class MapsCore {
  constructor() {
    this.currentTab = 'all';
    this.currentPage = 1;
    this.totalPages = 1;
    this.currentUser = null;
    this.elements = {};
    this.initialized = false;
    this.currentGame = 'war2'; // Add game state tracking
  }

  /**
   * Initialize the maps core system
   */
  async init() {
    console.log('🗺️ Initializing Maps Core...');
    
    this.setupElements();
    await this.checkAuthStatus();
    
    // TEMPORARY: Simulate logged-in user for testing rating functionality
    if (!this.currentUser) {
      this.currentUser = {
        id: '6847c3181a69eb49ffa3e775',
        username: 'TURTLEM<PERSON>',
        displayName: 'TURTLEMAN',
        role: 'user'
      };
  
    }
    
    this.setupEventListeners();
    this.setupGlobalFunctions();
    
    // Initialize stats for the currently active game
    setTimeout(() => {
      if (window.mapsGrid && typeof window.mapsGrid.updateGameStats === 'function') {
        console.log(`🎮 Initializing stats for current game (${this.currentGame})`);
        window.mapsGrid.updateGameStats(this.currentGame).catch(error => {
          console.error('❌ Error initializing game stats:', error);
        });
      }
    }, 100);
    
    this.initialized = true;
    console.log('✅ Maps Core initialized');
  }

  /**
   * Setup DOM element references
   */
  setupElements() {
    this.elements = {
      mapsGrid: document.getElementById('maps-grid'),
      pagination: document.getElementById('pagination'),
      searchInput: document.getElementById('search-input'),
      searchBtn: document.getElementById('search-btn'),
      war3SearchInput: document.getElementById('war3-search-input'),
      war3SearchBtn: document.getElementById('war3-search-btn'),
      tabButtons: document.querySelectorAll('.tab-btn'),
      uploadMapBtn: document.getElementById('upload-map-btn'),
      uploadMapModal: document.getElementById('upload-map-modal'),
      uploadMapForm: document.getElementById('upload-map-form'),
      mapDetailsModal: document.getElementById('map-details-modal'),
      mapDetailsContainer: document.getElementById('map-details-container'),
      closeModalButtons: document.querySelectorAll('.close-modal'),

      showRandomMapBtn: document.getElementById('show-random-map-btn')
    };

    console.log('📋 DOM elements setup complete');
  }

  /**
   * Setup global event listeners
   */
  setupEventListeners() {
    this.setupSearchListeners();
    this.setupTabListeners();
    this.setupModalListeners();
    this.setupButtonListeners();
    
    console.log('🔗 Event listeners setup complete');
  }

  /**
   * Setup search functionality
   */
  setupSearchListeners() {
    // WC2 Search functionality
    if (this.elements.searchBtn) {
      this.elements.searchBtn.addEventListener('click', () => {
        this.currentPage = 1;
        this.triggerMapsReload();
      });
    }

    if (this.elements.searchInput) {
      this.elements.searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          this.currentPage = 1;
          this.triggerMapsReload();
        }
      });

      // Clear results when search is emptied
      this.elements.searchInput.addEventListener('input', (e) => {
        if (e.target.value.trim() === '') {
          this.currentPage = 1;
          this.triggerMapsReload();
        }
      });
    }

    // WC3 Search functionality
    if (this.elements.war3SearchBtn) {
      this.elements.war3SearchBtn.addEventListener('click', () => {
        this.currentPage = 1;
        this.triggerWC3MapsReload();
      });
    }

    if (this.elements.war3SearchInput) {
      this.elements.war3SearchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          this.currentPage = 1;
          this.triggerWC3MapsReload();
        }
      });

      // Clear results when search is emptied
      this.elements.war3SearchInput.addEventListener('input', (e) => {
        if (e.target.value.trim() === '') {
          this.currentPage = 1;
          this.triggerWC3MapsReload();
        }
      });
    }
  }

  /**
   * Setup tab navigation
   */
  setupTabListeners() {
    this.elements.tabButtons.forEach(button => {
      button.addEventListener('click', () => {
        this.switchTab(button.getAttribute('data-tab'));
      });
    });
  }

  /**
   * Setup modal close listeners
   */
  setupModalListeners() {
    this.elements.closeModalButtons.forEach(button => {
      button.addEventListener('click', () => {
        this.closeModals();
      });
    });

    // Close modal when clicking outside
    window.addEventListener('click', (event) => {
      if (event.target.classList.contains('modal')) {
        this.closeModals();
      }
    });
  }

  /**
   * Setup action button listeners
   */
  setupButtonListeners() {
    // Random map button
    if (this.elements.showRandomMapBtn) {
      this.elements.showRandomMapBtn.addEventListener('click', async () => {
        await this.showRandomMap();
      });
    }

    // WC3 Random map button
    const war3RandomBtn = document.getElementById('war3-random-map-btn');
    if (war3RandomBtn) {
      war3RandomBtn.addEventListener('click', async () => {
        await this.showWC3RandomMap();
      });
    }

    // WC3 Upload buttons
    const war3UploadInput = document.getElementById('war3-map-upload-input');
    const enhancedUploadBtn = document.getElementById('enhanced-upload-war3-btn');
    
    if (war3UploadInput) {
      war3UploadInput.addEventListener('change', (e) => {
        this.handleWC3Upload(e);
      });
    }
    
    if (enhancedUploadBtn) {
      enhancedUploadBtn.addEventListener('click', () => {
        this.handleEnhancedWC3Upload();
      });
    }

    // Upload map button
    if (this.elements.uploadMapBtn) {
      this.elements.uploadMapBtn.addEventListener('click', () => {
        this.handleUploadMapClick();
      });
    }
  }

  /**
   * Setup global functions for backward compatibility
   */
  setupGlobalFunctions() {
    window.mapsCore = this;
    window.loadMaps = () => this.triggerMapsReload();
    window.switchTab = (tabName) => this.switchTab(tabName);
    window.closeModals = () => this.closeModals();
    
    console.log('🌐 Global functions registered');
  }

  /**
   * Check user authentication status
   */
  async checkAuthStatus() {
    try {
      const response = await fetch('/api/me', {
        credentials: 'include'
      });

      if (response.ok) {
        this.currentUser = await response.json();
        console.log('👤 User authenticated:', this.currentUser?.username);
      } else {
        this.currentUser = null;
        console.log('👤 User not authenticated');
      }
    } catch (error) {
      console.error('❌ Failed to check auth status:', error);
      this.currentUser = null;
    }
  }

  /**
   * Switch to a different tab
   */
  switchTab(tabName) {
    if (this.currentTab === tabName) return;

    console.log(`🔄 Switching to tab: ${tabName}`);
    
    // Remove active class from all buttons
    this.elements.tabButtons.forEach(btn => btn.classList.remove('active'));

    // Add active class to clicked button
    const targetButton = Array.from(this.elements.tabButtons)
      .find(btn => btn.getAttribute('data-tab') === tabName);
    
    if (targetButton) {
      targetButton.classList.add('active');
    }

    this.currentTab = tabName;
    this.currentPage = 1;

    // Load maps based on the selected tab
    this.triggerMapsReload();
    
    console.log(`✅ Switched to ${tabName} tab`);
  }

  /**
   * Show random map using the details modal instead of separate modal
   */
  async showRandomMap() {
    try {
      console.log('🎲 Fetching random map...');
      
      const response = await fetch('/api/war2maps/random');
      if (!response.ok) {
        throw new Error(`Failed to fetch random map: ${response.status}`);
      }

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.message || 'Failed to fetch random map');
      }
      
      const randomMap = result.data;
      console.log('🎯 Random map fetched:', randomMap.name);

      // Use the map details functionality to show the random map
      if (window.mapDetails && typeof window.mapDetails.viewMapDetails === 'function') {
        await window.mapDetails.viewMapDetails(randomMap._id);
      } else {
        console.error('❌ Map details functionality not available');
        this.showError('Map details functionality not available');
      }
      
    } catch (error) {
      console.error('❌ Failed to fetch random map:', error);
      this.showError(`Failed to load random map: ${error.message}`);
    }
  }

  /**
   * Show random WC3 map
   */
  async showWC3RandomMap() {
    try {
      console.log('🎲 Fetching random WC3 map...');
      
      // Get all WC3 maps first
      const response = await fetch('/api/war3maps?limit=1000');
      if (!response.ok) {
        throw new Error(`Failed to fetch WC3 maps: ${response.status}`);
      }

      const result = await response.json();
      const maps = result.maps || [];
      
      if (maps.length === 0) {
        throw new Error('No WC3 maps available');
      }
      
      // Pick a random map
      const randomMap = maps[Math.floor(Math.random() * maps.length)];
      console.log('🎯 Random WC3 map selected:', randomMap.name);

      // Use the map details functionality to show the random map
      if (window.mapDetails && typeof window.mapDetails.viewMapDetails === 'function') {
        await window.mapDetails.viewMapDetails(randomMap._id);
      } else {
        console.error('❌ Map details functionality not available');
        this.showError('Map details functionality not available');
      }
      
    } catch (error) {
      console.error('❌ Failed to fetch random WC3 map:', error);
      this.showError(`Failed to load random WC3 map: ${error.message}`);
    }
  }

  /**
   * Handle WC3 map upload
   */
  handleWC3Upload(event) {
    const file = event.target.files[0];
    if (!file) return;

    console.log('📤 WC3 map upload initiated:', file.name);
    
    // Validate file type
    const validExtensions = ['.w3m', '.w3x'];
    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
    
    if (!validExtensions.includes(fileExtension)) {
      this.showError('Please select a valid Warcraft III map file (.w3m or .w3x)');
      event.target.value = '';
      return;
    }

    // For now, show a placeholder message
    this.showSuccess(`WC3 map "${file.name}" selected. Upload functionality coming soon!`);
    
    // TODO: Implement actual WC3 upload functionality
    console.log('🚧 WC3 upload functionality to be implemented');
  }

  /**
   * Handle enhanced WC3 upload
   */
  handleEnhancedWC3Upload() {
    console.log('🎯 Enhanced WC3 upload clicked');
    
    // For now, show a placeholder message
    alert('Enhanced WC3 upload functionality coming soon! This will include:\n\n• Automatic minimap extraction\n• Map metadata parsing\n• Strategic analysis\n• Batch upload support');
    
    // TODO: Implement enhanced WC3 upload modal
    console.log('🚧 Enhanced WC3 upload functionality to be implemented');
  }

  /**
   * Handle upload map button click
   */
  handleUploadMapClick() {
    if (!this.currentUser) {
      this.showError('You must be logged in to upload maps');
      window.location.href = '/views/login.html';
      return;
    }

    // Show upload modal using maps management module
    if (window.mapManagement && typeof window.mapManagement.showUploadModal === 'function') {
      window.mapManagement.showUploadModal();
    } else {
      console.warn('Map management module not available');
    }
  }

  /**
   * Build API URL for maps with current filters and parameters
   */
  buildMapsApiUrl() {
    const params = new URLSearchParams();
    
    // Add pagination
    params.append('page', this.currentPage.toString());
    params.append('limit', '12');
    
    // Add sorting (default alphabetical)
    params.append('sort', 'name');
    params.append('order', 'asc');
    
    // Add search term if present
    const searchInput = document.getElementById('search-input');
    if (searchInput?.value.trim()) {
      params.append('search', searchInput.value.trim());
    }
    
    // Add current tab filter
    if (this.currentTab !== 'all') {
      if (this.currentTab === 'land') {
        params.append('waterType', 'land');
      } else if (this.currentTab === 'sea') {
        params.append('waterType', 'sea');
      }
    }
    
    // Get current game and build appropriate API URL
    const currentGame = this.getCurrentGame();
    let apiEndpoint = '/api/war2maps'; // default
    
    if (currentGame === 'war1') {
      apiEndpoint = '/api/war1maps';
    } else if (currentGame === 'war3') {
      apiEndpoint = '/api/war3maps';
    }
    
    return `${apiEndpoint}?${params}`;
  }

  /**
   * Build WC3 maps API URL with search and pagination
   */
  buildWC3MapsApiUrl() {
    const params = new URLSearchParams();
    
    // Add pagination
    params.append('page', this.currentPage.toString());
    params.append('limit', '12');
    
    // Add sorting (default alphabetical)
    params.append('sort', 'name');
    params.append('order', 'asc');
    
    // Add search term if present
    const war3SearchInput = document.getElementById('war3-search-input');
    if (war3SearchInput?.value.trim()) {
      params.append('search', war3SearchInput.value.trim());
    }
    
    // Add current tab filter for WC3
    if (this.currentTab !== 'all') {
      // WC3 specific filters can be added here
      params.append('category', this.currentTab);
    }
    
    return `/api/war3maps?${params}`;
  }

  /**
   * Get current game from game tabs or default to war2
   */
  getCurrentGame() {
    // Return the tracked current game
    return this.currentGame;
  }

  /**
   * Trigger maps reload based on current state
   */
  triggerMapsReload() {
    if (!this.initialized) {
      console.log('⚠️ Core not initialized, skipping reload');
      return;
    }

    const apiUrl = this.buildMapsApiUrl();
    console.log(`🔄 Triggering maps reload: ${apiUrl}`);
    
    // Notify MapsGrid to reload
    if (window.mapsGrid && typeof window.mapsGrid.loadMaps === 'function') {
      window.mapsGrid.loadMaps(apiUrl);
    } else {
      console.error('❌ MapsGrid not available for reload');
    }
  }

  /**
   * Trigger WC3 maps reload based on current state
   */
  triggerWC3MapsReload() {
    if (!this.initialized) {
      console.log('⚠️ Core not initialized, skipping WC3 reload');
      return;
    }

    const apiUrl = this.buildWC3MapsApiUrl();
    console.log(`🔄 Triggering WC3 maps reload: ${apiUrl}`);
    
    // Use the existing loadMaps method which already handles WC3
    if (window.mapsGrid && typeof window.mapsGrid.loadMaps === 'function') {
      window.mapsGrid.loadMaps(apiUrl);
    } else {
      console.error('❌ MapsGrid not available for reload');
    }
  }

  /**
   * Close all modals
   */
  closeModals() {
    // Close upload modal
    if (this.elements.uploadMapModal) {
      this.elements.uploadMapModal.classList.remove('show', 'active', 'opening');
      this.elements.uploadMapModal.classList.add('closing');
    }

    // Close map details modal
    if (this.elements.mapDetailsModal) {
      this.elements.mapDetailsModal.classList.remove('show', 'active', 'opening');
      this.elements.mapDetailsModal.classList.add('closing');
    }

    // Close random map modal
    const randomMapModal = document.getElementById('random-map-modal');
    if (randomMapModal) {
      randomMapModal.classList.remove('show', 'active', 'opening');
      randomMapModal.classList.add('closing');
    }

    // Clear map details container
    if (this.elements.mapDetailsContainer) {
      this.elements.mapDetailsContainer.innerHTML = '';
    }

    // Restore body scroll
    document.body.style.overflow = '';
    document.body.classList.remove('modal-open');
    
    // Remove closing classes after animation
    setTimeout(() => {
      if (this.elements.uploadMapModal) {
        this.elements.uploadMapModal.classList.remove('closing');
      }
      if (this.elements.mapDetailsModal) {
        this.elements.mapDetailsModal.classList.remove('closing');
      }
      if (randomMapModal) {
        randomMapModal.classList.remove('closing');
      }
    }, 300);
  }

  /**
   * Show error message
   */
  showError(message) {
    if (typeof window.NotificationUtils !== 'undefined') {
      window.NotificationUtils.error(message, 5000);
    } else {
      alert(`Error: ${message}`);
    }
  }

  /**
   * Show success message
   */
  showSuccess(message) {
    if (typeof window.NotificationUtils !== 'undefined') {
      window.NotificationUtils.success(message, 3000);
    } else {
      console.log(`Success: ${message}`);
    }
  }

  /**
   * Initialize game tabs functionality
   */
  initializeGameTabs() {
    const gameTabButtons = document.querySelectorAll('.game-tab');
    const gameContents = document.querySelectorAll('.game-content');

    // Restore saved preference or set initial state
    const savedGame = localStorage.getItem('mapsPageGame');
    
    if (savedGame && document.querySelector(`.game-tab[data-game="${savedGame}"]`)) {
      // Restore saved preference
      this.currentGame = savedGame;
      console.log(`🗺️ Restoring saved maps game preference: ${savedGame}`);
      
      // Update UI to match saved preference
      gameTabButtons.forEach(btn => btn.classList.remove('active'));
      document.querySelectorAll('.game-content').forEach(content => content.classList.remove('active'));
      
      const savedTab = document.querySelector(`.game-tab[data-game="${savedGame}"]`);
      const savedContent = document.getElementById(`${savedGame}-content`);
      
      if (savedTab) savedTab.classList.add('active');
      if (savedContent) savedContent.classList.add('active');
    } else {
      // Set initial state from HTML
      const activeTab = document.querySelector('.game-tab.active');
      if (activeTab) {
        this.currentGame = activeTab.getAttribute('data-game');
      }
    }

    gameTabButtons.forEach(button => {
      button.addEventListener('click', () => {
        const targetGame = button.getAttribute('data-game');

        // Update current game state
        this.currentGame = targetGame;
        
        // Save maps page preference
        localStorage.setItem('mapsPageGame', targetGame);
        console.log(`💾 Saved maps page preference: ${targetGame}`);

        // Remove active class from all tabs and contents
        gameTabButtons.forEach(btn => btn.classList.remove('active'));
        gameContents.forEach(content => content.classList.remove('active'));

        // Add active class to clicked tab and corresponding content
        button.classList.add('active');
        const targetContent = document.getElementById(`${targetGame}-content`);
        if (targetContent) {
          targetContent.classList.add('active');
        }

        // Handle game-specific loading
        if (targetGame === 'war2') {
          // Trigger WC2 maps reload
          this.triggerMapsReload();
        } else if (targetGame === 'war1') {
          // SIMPLE FIX: Just update the damn number immediately
          const currentGameMapsEl = document.getElementById('current-game-maps');
          if (currentGameMapsEl) {
            currentGameMapsEl.textContent = '21';
            console.log('✅ WC1 counter DIRECTLY updated to 21');
          } else {
            console.error('❌ current-game-maps element not found!');
          }
          
          // WC1 is handled by WC1Manager - render scenarios and update stats
          if (window.wc1Manager) {
            // Ensure scenarios are loaded first, then render and update stats
            if (typeof window.wc1Manager.loadScenarios === 'function') {
              window.wc1Manager.loadScenarios().then(() => {
                if (typeof window.wc1Manager.renderScenarios === 'function') {
                  window.wc1Manager.renderScenarios();
                }
                if (typeof window.wc1Manager.updateGameStats === 'function') {
                  window.wc1Manager.updateGameStats();
                }
              });
            }
          }
        } else if (targetGame === 'war3') {
          // Trigger WC3 maps reload
          this.triggerWC3MapsReload();
        }

        // Update game stats using the grid module
        if (window.mapsGrid && typeof window.mapsGrid.updateGameStats === 'function') {
          window.mapsGrid.updateGameStats(targetGame).catch(error => {
            console.error('❌ Error updating game stats:', error);
          });
        }
        
        console.log(`🎮 Switched to ${targetGame.toUpperCase()} tab, currentGame: ${this.currentGame}`);
      });
    });

    console.log('🎮 Game tabs initialized');
  }

  /**
   * Get current state
   */
  getState() {
    return {
      currentTab: this.currentTab,
      currentPage: this.currentPage,
      totalPages: this.totalPages,
      currentUser: this.currentUser,
      initialized: this.initialized
    };
  }

  /**
   * Update state
   */
  updateState(updates) {
    Object.assign(this, updates);
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    console.log('🧹 Cleaning up Maps Core...');
    
    this.currentTab = 'all';
    this.currentPage = 1;
    this.totalPages = 1;
    this.currentUser = null;
    this.currentGame = 'war2';
    this.elements = {};
    this.initialized = false;

    console.log('✅ Maps Core cleanup complete');
  }

  /**
   * Escape HTML to prevent XSS
   */
  escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * Capitalize first letter
   */
  capitalizeFirst(str) {
    if (!str) return '';
    return str.charAt(0).toUpperCase() + str.slice(1);
  }
}

// Create and export singleton instance
export const mapsCore = new MapsCore(); 