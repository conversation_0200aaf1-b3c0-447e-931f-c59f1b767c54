function createContentCreatorCard(creator) {
  const card = document.createElement('div');
  card.className = 'content-creator-card';
  
  // Extract platform usernames
  let youtubeUsername = '';
  let twitchUsername = '';
  
  if (creator.socialLinks?.youtube) {
    if (creator.socialLinks.youtube.includes('youtube.com/@')) {
      youtubeUsername = '@' + creator.socialLinks.youtube.split('@').pop().split('/')[0];
    }
  }
  
  if (creator.socialLinks?.twitch) {
    if (creator.socialLinks.twitch.includes('twitch.tv/')) {
      twitchUsername = creator.socialLinks.twitch.split('twitch.tv/').pop().split('/')[0];
    }
  }

  // Create card content
  card.innerHTML = `
    <div class="creator-info">
      <div class="platform-links">
        ${youtubeUsername ? `
          <div class="platform-link youtube">
            <i class="fab fa-youtube"></i>
            <span class="platform-username">${youtubeUsername}</span>
          </div>
        ` : ''}
        ${twitchUsername ? `
          <div class="platform-link twitch">
            <i class="fab fa-twitch"></i>
            <span class="platform-username">${twitchUsername}</span>
          </div>
        ` : ''}
      </div>
      <div class="stream-status ${creator.streaming?.isLive ? 'live' : 'offline'}">
        ${creator.streaming?.isLive ? 
          `<span class="live-indicator"></span> Live on ${creator.streaming.platform}` : 
          'Offline'}
      </div>
    </div>
  `;

  return card;
}

async function fetchStreams() {
  try {
    console.log('Fetching streams from:', '/api/streams');
    const response = await fetch('/api/streams', {
      credentials: 'include'
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const streams = await response.json();
    console.log('Received streams:', streams);
    
    const streamsContainer = document.getElementById('streams-container');
    if (!streamsContainer) {
      console.error('Streams container not found');
      return;
    }
    
    // Clear existing streams
    streamsContainer.innerHTML = '';
    
    if (!streams || streams.length === 0) {
      streamsContainer.innerHTML = '<p class="no-streams">No live streams at the moment</p>';
      return;
    }
    
    // Create and append stream cards
    streams.forEach(stream => {
      const card = createStreamCard(stream);
      streamsContainer.appendChild(card);
    });
  } catch (error) {
    console.error('Error fetching streams:', error);
    const streamsContainer = document.getElementById('streams-container');
    if (streamsContainer) {
      streamsContainer.innerHTML = '<p class="error">Failed to load streams. Please try again later.</p>';
    }
  }
}

function createStreamCard(stream) {
  const card = document.createElement('div');
  card.className = 'stream-card';
  
  const platform = stream.platform.toLowerCase();
  const username = stream.username;
  const title = stream.title || 'Untitled Stream';
  const thumbnail = stream.thumbnail || '';
  
  card.innerHTML = `
    <div class="stream-thumbnail">
      ${thumbnail ? `<img src="${thumbnail}" alt="${title}" onerror="this.parentElement.innerHTML='<i class=\'fas fa-play-circle\'></i>'">` : 
        `<i class="fas fa-play-circle"></i>`}
      <div class="stream-status live">
        <span class="live-indicator"></span>
        Live
      </div>
    </div>
    <div class="stream-info">
      <div class="stream-title">${title}</div>
      <div class="stream-creator">
        <i class="fab fa-${platform}"></i>
        <span>${username}</span>
      </div>
      <a href="${stream.url}" target="_blank" class="watch-button">
        <i class="fas fa-external-link-alt"></i> Watch on ${platform}
      </a>
    </div>
  `;
  
  return card;
}

// Initialize streams
document.addEventListener('DOMContentLoaded', () => {
  fetchStreams();
  // Refresh streams every 5 minutes
  setInterval(fetchStreams, 5 * 60 * 1000);
}); 