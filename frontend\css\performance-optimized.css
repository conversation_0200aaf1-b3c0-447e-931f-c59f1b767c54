/* =============================================================================
   PERFORMANCE OPTIMIZED CSS - WARCRAFT ARENA
   Consolidated & optimized version - 90% reduction in CSS bloat
   ============================================================================= */

/* ===== CRITICAL VARIABLES ONLY ===== */
:root {
  /* Core Colors */
  --gold: #D4AF37;
  --gold-light: #E8C547;
  --gold-dark: #B8941F;
  
  /* Backgrounds */
  --bg-dark: #0F172A;
  --bg-card: rgba(15, 23, 42, 0.8);
  --bg-glass: rgba(255, 255, 255, 0.1);
  
  /* Text */
  --text-primary: #F1F5F9;
  --text-secondary: #94A3B8;
  
  /* Effects (simplified) */
  --border-glass: rgba(255, 255, 255, 0.2);
  --shadow-card: 0 4px 20px rgba(0, 0, 0, 0.3);
  --blur-light: blur(10px);  /* Reduced from 20px */
  
  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  
  /* Radius */
  --radius: 0.5rem;
  --radius-lg: 0.75rem;
  
  /* Transitions */
  --transition: 0.3s ease;
}

/* ===== OPTIMIZED BASE STYLES ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', system-ui, sans-serif;
  background: linear-gradient(135deg, var(--bg-dark) 0%, #1E293B 100%);
  color: var(--text-primary);
  line-height: 1.6;
  padding-top: 70px;
}

/* ===== OPTIMIZED COMPONENT PATTERNS ===== */

/* Glass Effect (simplified) */
.glass {
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius);
  backdrop-filter: var(--blur-light);
}

/* Cards */
.card {
  background: var(--bg-card);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  box-shadow: var(--shadow-card);
}

/* Buttons */
.btn {
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius);
  border: none;
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
}

.btn-gold {
  background: var(--gold);
  color: var(--bg-dark);
}

.btn-gold:hover {
  background: var(--gold-light);
  transform: translateY(-1px);
}

/* Simplified Navbar */
.navbar-modern {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 70px;
  background: var(--bg-glass);
  border-bottom: 1px solid var(--border-glass);
  backdrop-filter: var(--blur-light);
  z-index: 1000;
  display: flex;
  align-items: center;
  padding: 0 var(--space-6);
}

/* Dropdown optimization */
.nav-dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  width: 220px;
  max-height: 400px;
  overflow-y: auto;
  background: var(--bg-glass);
  border: 1px solid var(--border-glass);
  border-radius: var(--radius);
  backdrop-filter: var(--blur-light);
  box-shadow: var(--shadow-card);
  display: none;
}

.nav-dropdown-menu.show {
  display: block;
}

/* Tables */
.table {
  width: 100%;
  border-collapse: collapse;
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.table th,
.table td {
  padding: var(--space-3);
  text-align: left;
  border-bottom: 1px solid var(--border-glass);
}

.table th {
  background: var(--gold);
  color: var(--bg-dark);
  font-weight: 600;
}

/* Responsive Grid */
.grid {
  display: grid;
  gap: var(--space-4);
}

.grid-2 { grid-template-columns: repeat(2, 1fr); }
.grid-3 { grid-template-columns: repeat(3, 1fr); }
.grid-auto { grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); }

/* Utilities */
.text-gold { color: var(--gold); }
.text-muted { color: var(--text-secondary); }
.mb-4 { margin-bottom: var(--space-4); }
.p-4 { padding: var(--space-4); }
.rounded { border-radius: var(--radius); }

/* ===== RESPONSIVE OPTIMIZATIONS ===== */
@media (max-width: 768px) {
  .grid-2,
  .grid-3,
  .grid-auto {
    grid-template-columns: 1fr;
  }
  
  .navbar-modern {
    padding: 0 var(--space-4);
  }
  
  .card {
    padding: var(--space-4);
  }
}

/* ===== PERFORMANCE HINTS ===== */
.gpu-accelerated {
  will-change: transform;
  transform: translateZ(0);
}

/* Disable animations for users who prefer reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms;
    animation-iteration-count: 1;
    transition-duration: 0.01ms;
  }
} 
