<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>WC Arena - War Table</title>
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-brands-400.woff2" as="font" type="font/woff2" crossorigin>
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>
  <link rel="stylesheet" href="/css/warcraft-app-modern.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700&display=swap" rel="stylesheet">
  <script src="/js/utils.js"></script>
  <style>
    .campaigns-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    .campaigns-header {
      text-align: center;
      margin-bottom: 30px;
    }

    .campaigns-header h1 {
      color: #d4af37;
      font-family: 'Cinzel', serif;
      font-size: 2.5rem;
      margin-bottom: 10px;
    }

    .campaigns-stats {
      display: flex;
      justify-content: center;
      gap: 30px;
      margin-bottom: 30px;
      flex-wrap: wrap;
    }

    .stat-card {
      background: rgba(0, 0, 0, 0.7);
      border: 2px solid #d4af37;
      border-radius: 10px;
      padding: 15px 20px;
      text-align: center;
      min-width: 150px;
    }

    .stat-value {
      font-size: 1.8rem;
      font-weight: bold;
      color: #d4af37;
      display: block;
    }

    .stat-label {
      font-size: 0.9rem;
      color: #ccc;
      margin-top: 5px;
    }

    .game-section {
      margin-bottom: 40px;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 15px;
      padding: 20px;
      border: 1px solid #444;
    }

    .game-header {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      cursor: pointer;
      padding: 10px;
      border-radius: 8px;
      transition: background-color 0.3s;
    }

    .game-header:hover {
      background: rgba(212, 175, 55, 0.1);
    }

    .game-icon {
      font-size: 2rem;
      margin-right: 15px;
      color: #d4af37;
    }

    .game-title {
      font-size: 1.5rem;
      font-weight: bold;
      color: #d4af37;
      font-family: 'Cinzel', serif;
    }

    .game-progress {
      margin-left: auto;
      font-size: 0.9rem;
      color: #ccc;
    }

    .campaigns-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }

    .campaign-card {
      background: rgba(0, 0, 0, 0.7);
      border: 1px solid #555;
      border-radius: 10px;
      padding: 20px;
      transition: all 0.3s;
    }

    .campaign-card:hover {
      border-color: #d4af37;
      transform: translateY(-2px);
    }

    .campaign-header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
    }

    .race-icon {
      width: 40px;
      height: 40px;
      margin-right: 15px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.2rem;
    }

    .race-human { background: #4a90e2; }
    .race-orc { background: #8b4513; }
    .race-undead { background: #800080; }
    .race-nightelf { background: #228b22; }
    .race-alliance { background: #0066cc; }
    .race-horde { background: #cc0000; }

    .campaign-title {
      font-size: 1.2rem;
      font-weight: bold;
      color: #d4af37;
      margin: 0;
    }

    .campaign-subtitle {
      font-size: 0.9rem;
      color: #ccc;
      margin: 0;
    }

    .campaign-progress {
      margin-bottom: 15px;
    }

    .progress-bar {
      width: 100%;
      height: 8px;
      background: #333;
      border-radius: 4px;
      overflow: hidden;
      margin-bottom: 5px;
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #d4af37, #f4d03f);
      transition: width 0.3s;
    }

    .progress-text {
      font-size: 0.8rem;
      color: #ccc;
      text-align: center;
    }

    .missions-list {
      max-height: 200px;
      overflow-y: auto;
      margin-bottom: 15px;
    }

    .mission-item {
      display: flex;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #333;
    }

    .mission-item:last-child {
      border-bottom: none;
    }

    .mission-status {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      margin-right: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.8rem;
    }

    .mission-completed {
      background: #28a745;
      color: white;
    }

    .mission-incomplete {
      background: #6c757d;
      color: white;
    }

    .mission-name {
      flex: 1;
      font-size: 0.9rem;
      color: #ccc;
    }

    .mission-rewards {
      display: flex;
      gap: 8px;
      align-items: center;
      font-size: 0.75rem;
    }

    .arena-gold {
      color: #ffd700;
      font-weight: bold;
      display: flex;
      align-items: center;
      gap: 3px;
    }

    .achievement-xp {
      color: #87ceeb;
      font-weight: bold;
      display: flex;
      align-items: center;
      gap: 3px;
    }

    .arena-gold i {
      color: #ffd700;
    }

    .achievement-xp i {
      color: #87ceeb;
    }

    .campaign-actions {
      display: flex;
      gap: 10px;
    }

    .btn-campaign {
      flex: 1;
      padding: 8px 16px;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 0.9rem;
      transition: all 0.3s;
    }

    .btn-view {
      background: #17a2b8;
      color: white;
    }

    .btn-view:hover {
      background: #138496;
    }

    .btn-complete {
      background: #28a745;
      color: white;
    }

    .btn-complete:hover {
      background: #218838;
    }

    .btn-complete:disabled {
      background: #6c757d;
      cursor: not-allowed;
    }

    .btn-complete-mission {
      background: linear-gradient(135deg, #d4af37, #f4d03f);
      color: #1a1a1a;
      border: none;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 0.7rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 3px;
      text-transform: uppercase;
      letter-spacing: 0.3px;
      box-shadow: 0 1px 4px rgba(212, 175, 55, 0.4);
      margin-left: 12px;
    }

    .btn-complete-mission:hover {
      background: linear-gradient(135deg, #f4d03f, #d4af37);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(212, 175, 55, 0.5);
    }

    .btn-complete-mission:active {
      transform: translateY(0);
    }

    .btn-complete-mission i {
      font-size: 0.7rem;
    }

    .btn-complete-main {
      background: linear-gradient(135deg, #d4af37, #f4d03f);
      color: #1a1a1a;
      border: none;
      padding: 8px 16px;
      border-radius: 6px;
      font-size: 0.9rem;
      font-weight: 700;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 6px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      box-shadow: 0 2px 8px rgba(212, 175, 55, 0.4);
      width: 100%;
      justify-content: center;
    }

    .btn-complete-main:hover {
      background: linear-gradient(135deg, #f4d03f, #d4af37);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(212, 175, 55, 0.5);
    }

    .btn-complete-main:active {
      transform: translateY(0);
    }

    .btn-complete-main i {
      font-size: 1rem;
    }

    .collapsed .campaigns-grid {
      display: none;
    }

    .loading {
      text-align: center;
      padding: 40px;
      color: #ccc;
    }

    .error {
      text-align: center;
      padding: 40px;
      color: #dc3545;
    }

    /* Modal Styles */
    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.8);
    }

    .modal-content {
      background: #1a1a1a;
      margin: 2% auto;
      padding: 15px;
      border: 2px solid #d4af37;
      border-radius: 10px;
      width: 95%;
      max-width: 1000px;
      max-height: 95vh;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
    }

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      border-bottom: 1px solid #444;
      padding-bottom: 8px;
      flex-shrink: 0;
    }

    .modal-title {
      color: #d4af37;
      font-size: 1.3rem;
      margin: 0;
    }

    /* Compact form layout */
    .modal-form-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      flex: 1;
      min-height: 0;
    }

    .form-left-column {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .form-right-column {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .form-group {
      margin-bottom: 0;
    }

    .form-group label {
      display: block;
      margin-bottom: 4px;
      color: #d4af37;
      font-size: 0.9rem;
      font-weight: 600;
    }

    .form-group input, 
    .form-group select, 
    .form-group textarea {
      width: 100%;
      padding: 6px 10px;
      background: #2a2a2a;
      border: 1px solid #444;
      border-radius: 4px;
      color: #fff;
      font-size: 0.9rem;
    }

    .form-group textarea {
      resize: vertical;
      min-height: 60px;
      max-height: 80px;
    }

    .form-group small {
      color: #aaa;
      font-size: 0.75rem;
      margin-top: 2px;
      display: block;
    }

    .close {
      color: #aaa;
      float: right;
      font-size: 28px;
      font-weight: bold;
      cursor: pointer;
      margin-left: auto;
    }

    .close:hover {
      color: #d4af37;
    }

    .form-group {
      margin-bottom: 20px;
    }

    .form-group label {
      display: block;
      margin-bottom: 5px;
      color: #d4af37;
      font-weight: bold;
    }

    .form-group textarea {
      width: 100%;
      padding: 10px;
      border: 1px solid #555;
      border-radius: 5px;
      background: #333;
      color: #fff;
      resize: vertical;
      min-height: 80px;
    }

    .file-upload {
      border: 2px dashed #555;
      border-radius: 10px;
      padding: 20px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s;
    }

    .file-upload:hover {
      border-color: #d4af37;
      background: rgba(212, 175, 55, 0.1);
    }

    .file-upload.dragover {
      border-color: #d4af37;
      background: rgba(212, 175, 55, 0.2);
    }

    .file-upload input {
      display: none;
    }

    .upload-text {
      color: #ccc;
      margin-bottom: 10px;
    }

    .file-list {
      margin-top: 15px;
    }

    .file-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 5px;
      margin-bottom: 5px;
    }

    .file-name {
      color: #ccc;
      font-size: 0.9rem;
    }

    .file-remove {
      color: #dc3545;
      cursor: pointer;
      font-size: 1.2rem;
    }

    .file-remove:hover {
      color: #c82333;
    }

    /* Achievement Preview Styles */
    .achievement-preview {
      margin-top: 20px;
      padding: 15px;
      background: linear-gradient(135deg, #1a1a1a 0%, #2d1810 100%);
      border: 1px solid #d4af37;
      border-radius: 8px;
    }

    .potential-achievements h4 {
      color: #d4af37;
      margin: 0 0 15px 0;
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 1.1rem;
    }

    .achievement-list {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    .achievement-preview-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 10px;
      background: rgba(212, 175, 55, 0.1);
      border-radius: 6px;
      border: 1px solid rgba(212, 175, 55, 0.3);
      transition: all 0.3s;
    }

    .achievement-preview-item:hover {
      background: rgba(212, 175, 55, 0.2);
      border-color: rgba(212, 175, 55, 0.5);
    }

    .achievement-preview-item i {
      font-size: 1.2rem;
      color: #d4af37;
      width: 20px;
      text-align: center;
    }

    .achievement-info {
      flex: 1;
    }

    .achievement-name {
      font-weight: bold;
      color: #fff;
      font-size: 0.95rem;
      margin-bottom: 2px;
    }

    .achievement-desc {
      color: #ccc;
      font-size: 0.85rem;
      margin-bottom: 3px;
    }

    .achievement-points {
      color: #d4af37;
      font-size: 0.8rem;
      font-weight: bold;
    }

    /* Enhanced Reward Preview Styles */
    .reward-preview {
      background: linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(40, 167, 69, 0.1));
      border: 2px solid rgba(212, 175, 55, 0.3);
      border-radius: 12px;
      padding: 20px;
      margin: 20px 0;
    }

    .reward-preview h4 {
      color: #d4af37;
      margin-bottom: 15px;
      font-size: 1.2rem;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }

    .reward-breakdown {
      display: grid;
      gap: 12px;
    }

    .reward-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 15px;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 8px;
      border-left: 4px solid #555;
      transition: all 0.3s ease;
    }

    .reward-item:hover {
      background: rgba(255, 255, 255, 0.08);
      transform: translateX(3px);
    }

    .reward-item.primary {
      border-left-color: #d4af37;
      background: rgba(212, 175, 55, 0.1);
    }

    .reward-item.bonus {
      border-left-color: #28a745;
      background: rgba(40, 167, 69, 0.1);
    }

    .reward-label {
      font-weight: 600;
      color: #ccc;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .reward-value {
      font-weight: bold;
      font-size: 1.1rem;
    }

    .arena-gold-reward {
      color: #ffd700;
      text-shadow: 0 0 10px #ffd700;
    }

    .achievement-xp-reward {
      color: #87ceeb;
      text-shadow: 0 0 10px #87ceeb;
    }

    .reward-item.primary .reward-value {
      font-size: 1.2rem;
    }

    @media (max-width: 768px) {
      .campaigns-stats {
        gap: 15px;
      }

      .stat-card {
        min-width: 120px;
        padding: 10px 15px;
      }

      .campaigns-grid {
        grid-template-columns: 1fr;
      }

      .modal-content {
        width: 95%;
        margin: 10% auto;
      }
    }
  </style>
</head>
<body>
  <div id="navbar-container"></div>

  <main>
    <div class="campaigns-container">
      <div class="campaigns-header">
        <h1>War Table</h1>
        <p>Complete War Table missions to earn points, experience, and achievements!</p>
      </div>

      <div class="campaigns-stats" id="campaigns-stats">
        <div class="stat-card">
          <span class="stat-value" id="missions-completed">0</span>
          <div class="stat-label">Missions Completed</div>
        </div>

        <div class="stat-card">
          <span class="stat-value" id="total-experience">0</span>
          <div class="stat-label">Experience Gained</div>
        </div>
        <div class="stat-card">
          <span class="stat-value" id="arena-gold">0</span>
          <div class="stat-label">Arena Gold</div>
        </div>
        <div class="stat-card">
          <span class="stat-value" id="honor-points">0</span>
          <div class="stat-label">Honor Points</div>
        </div>
        <div class="stat-card">
          <span class="stat-value" id="achievements-unlocked">0</span>
          <div class="stat-label">Achievements</div>
        </div>
      </div>

      <div id="campaigns-content" class="loading">
        <i class="fas fa-spinner fa-spin"></i>
                    <p>Loading War Table...</p>
      </div>
    </div>
  </main>

  <!-- Mission Completion Modal -->
  <div id="completion-modal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="modal-title" id="modal-title">Complete Mission</h2>
        <span class="close" id="modal-close">&times;</span>
      </div>
      <form id="completion-form" enctype="multipart/form-data">
        <input type="hidden" id="campaign-id" name="campaignId">
        <input type="hidden" id="campaign-game" name="campaignGame">
        
        <div class="modal-form-content">
          <!-- Left Column: Form Inputs -->
          <div class="form-left-column">
            <!-- Difficulty Selection (for WC3 only) -->
            <div class="form-group" id="difficulty-group" style="display: none;">
              <label for="difficulty">Difficulty Mode</label>
              <select id="difficulty" name="difficulty">
                <option value="story">Story Mode</option>
                <option value="normal" selected>Normal</option>
                <option value="hard">Hard</option>
              </select>
              <small>Higher difficulties provide bonus rewards!</small>
            </div>

            <!-- Speedrun Options -->
            <div class="form-group">
              <label class="checkbox-label">
                <input type="checkbox" id="is-speedrun" name="isSpeedrun">
                <span class="checkmark"></span>
                Speedrun attempt
              </label>
              <small>Check if attempting to complete quickly</small>
            </div>

            <!-- Speedrun Time (shown when speedrun is checked) -->
            <div class="form-group" id="speedrun-time-group" style="display: none;">
              <label for="completion-time">Completion Time</label>
              <div class="time-input-group">
                <input type="number" id="completion-minutes" min="0" max="999" placeholder="MM">
                <span>:</span>
                <input type="number" id="completion-seconds" min="0" max="59" placeholder="SS">
                <input type="hidden" id="completion-time" name="completionTime">
              </div>
              <small>Enter time in minutes and seconds</small>
            </div>

            <!-- Video Proof (shown when speedrun is checked) -->
            <div class="form-group" id="video-proof-group" style="display: none;">
              <label for="video-proof">Video Proof (Optional)</label>
              <input type="url" id="video-proof" name="videoProof" placeholder="https://youtube.com/watch?v=...">
              <small>YouTube link for verification</small>
            </div>
            
            <!-- Screenshot Upload -->
            <div class="form-group">
              <label for="screenshots">Screenshots (Required)</label>
              <div class="file-upload" id="file-upload">
                <input type="file" id="screenshots" name="screenshots" multiple accept="image/*">
                <div class="upload-text">
                  <i class="fas fa-cloud-upload-alt"></i>
                  <p>Click or drag screenshots</p>
                  <small>Up to 5 images (JPG, PNG, GIF, WebP)</small>
                </div>
              </div>
              <div class="file-list" id="file-list"></div>
            </div>

            <!-- Notes -->
            <div class="form-group">
              <label for="notes">Notes (Optional)</label>
              <textarea id="notes" name="notes" placeholder="Add any notes about your completion..."></textarea>
            </div>
          </div>

          <!-- Right Column: Rewards & Achievements -->
          <div class="form-right-column">
            <!-- Mission Info -->
            <div class="mission-info-card">
              <h3 id="mission-display-name">Mission</h3>
              <p class="mission-subtitle">Complete to earn rewards</p>
            </div>
            
            <!-- Reward Preview -->
            <div class="reward-preview" id="reward-preview">
              <h4><i class="fas fa-gift"></i> Mission Rewards</h4>
              <div class="reward-breakdown">
                <div class="reward-item primary">
                  <span class="reward-label"><i class="fas fa-coins"></i> Gold:</span>
                  <span class="reward-value" id="arena-gold-reward">5</span>
                </div>
                <div class="reward-item primary">
                  <span class="reward-label"><i class="fas fa-star"></i> XP:</span>
                  <span class="reward-value" id="achievement-xp-reward">5</span>
                </div>
                <div class="reward-item primary">
                  <span class="reward-label"><i class="fas fa-shield-alt"></i> Honor:</span>
                  <span class="reward-value" id="honor-points-reward">0</span>
                </div>
                <div class="reward-item bonus war3-only" style="display: none;">
                  <span class="reward-label"><i class="fas fa-trophy"></i> Difficulty:</span>
                  <span class="reward-value" id="difficulty-multiplier">1.0x</span>
                </div>
                <div class="reward-item" id="speedrun-bonus" style="display: none;">
                  <span class="reward-label"><i class="fas fa-stopwatch"></i> Speedrun:</span>
                  <span class="reward-value">+10%</span>
                </div>
              </div>
              
              <!-- Campaign Completion Bonuses (WC1 specific) -->
              <div class="campaign-completion-rewards" id="campaign-completion-rewards" style="display: none;">
                <h5><i class="fas fa-crown"></i> Campaign Bonuses</h5>
                <div class="completion-reward-breakdown" id="completion-reward-breakdown">
                  <!-- Will be populated by JavaScript -->
                </div>
              </div>
            </div>

            <!-- Achievement Preview -->
            <div class="achievement-preview" id="achievement-preview" style="display: none;">
              <!-- Achievement content will be populated by JavaScript -->
            </div>
          </div>
        </div>

        <div class="campaign-actions">
          <button type="button" class="btn-campaign btn-view" id="cancel-btn">Cancel</button>
          <button type="submit" class="btn-campaign btn-complete" id="submit-btn">Submit Completion</button>
        </div>
      </form>
    </div>
  </div>

  <!-- Footer -->
  <div id="footer-container"></div>

  <!-- Load main.js first to handle navbar loading -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
  <script src="/js/notifications.js"></script>
  <script src="/js/main.js"></script>
  <!-- Ultra-Aggressive Image Compression Integration -->
  <script src="/js/modules/ImageCompressor.js" type="module"></script>
  <script src="/js/ultraCompressionIntegration.js" type="module"></script>
  <script src="/js/campaigns.js"></script>

  <style>
    /* Compact file upload */
    .file-upload {
      border: 2px dashed #444;
      border-radius: 6px;
      padding: 15px;
      text-align: center;
      background: #2a2a2a;
      transition: all 0.3s ease;
      min-height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .upload-text p {
      margin: 4px 0 2px 0;
      font-size: 0.9rem;
    }

    .upload-text small {
      font-size: 0.75rem;
      color: #aaa;
    }

    /* Compact rewards section */
    .reward-preview {
      background: #2a2a2a;
      border-radius: 6px;
      padding: 12px;
      border: 1px solid #444;
    }

    .reward-preview h4 {
      color: #d4af37;
      font-size: 1rem;
      margin: 0 0 8px 0;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .reward-breakdown {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 6px;
    }

    .reward-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 4px 8px;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 4px;
      font-size: 0.8rem;
    }

    .reward-label {
      color: #ccc;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .reward-value {
      color: #d4af37;
      font-weight: bold;
    }

    /* Compact achievements section */
    .achievement-preview {
      background: #2a2a2a;
      border-radius: 6px;
      padding: 12px;
      border: 1px solid #444;
    }

    .potential-achievements h4 {
      color: #d4af37;
      font-size: 1rem;
      margin: 0 0 8px 0;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .achievement-preview-item {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 8px;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 4px;
      margin-bottom: 6px;
    }

    .achievement-preview-item i {
      color: #d4af37;
      font-size: 1.1rem;
      width: 20px;
      text-align: center;
    }

    .achievement-info {
      flex: 1;
    }

    .achievement-name {
      color: #fff;
      font-weight: bold;
      font-size: 0.9rem;
      margin-bottom: 2px;
    }

    .achievement-desc {
      color: #aaa;
      font-size: 0.75rem;
      margin-bottom: 2px;
    }

    .achievement-experience {
      color: #87ceeb;
      font-size: 0.75rem;
      font-weight: bold;
    }

    /* Compact action buttons */
    .campaign-actions {
      display: flex;
      gap: 10px;
      margin-top: 15px;
      flex-shrink: 0;
    }

    .campaign-actions .btn-campaign {
      flex: 1;
      padding: 10px 16px;
      font-size: 0.9rem;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
      .modal-form-content {
        grid-template-columns: 1fr;
        gap: 15px;
      }
      
      .modal-content {
        width: 98%;
        margin: 1% auto;
        padding: 12px;
      }
      
      .reward-breakdown {
        grid-template-columns: 1fr;
      }
    }

    /* Campaign Completion Rewards Styling */
    .campaign-completion-rewards {
      margin-top: 10px;
      padding-top: 8px;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .campaign-completion-rewards h5 {
      color: var(--primary-gold);
      margin-bottom: 6px;
      font-size: 0.9rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .completion-reward-item {
      display: flex;
      align-items: flex-start;
      gap: 8px;
      padding: 6px;
      background: rgba(255, 255, 255, 0.03);
      border: 1px solid rgba(255, 255, 255, 0.08);
      border-radius: 4px;
      margin-bottom: 4px;
      font-size: 0.8rem;
    }

    .completion-reward-item:hover {
      background: rgba(255, 255, 255, 0.05);
      border-color: rgba(212, 175, 55, 0.2);
    }

    /* Mission Completion and Achievement Notification Styles */
    .mission-completion-notification,
    .achievement-notification {
      position: fixed;
      top: 20px;
      right: 20px;
      background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
      border: 2px solid #d4af37;
      border-radius: 10px;
      padding: 15px;
      max-width: 350px;
      z-index: 9999;
      transform: translateX(100%);
      opacity: 0;
      transition: all 0.3s ease;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    }

    .mission-completion-notification.show,
    .achievement-notification.show {
      transform: translateX(0);
      opacity: 1;
    }

    .mission-completion-notification {
      border-color: #28a745;
    }

    .achievement-notification {
      border-color: #ffd700;
    }

    .completion-icon,
    .achievement-icon {
      display: inline-block;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      float: left;
    }

    .completion-icon {
      background: #28a745;
      color: white;
    }

    .achievement-icon {
      background: #ffd700;
      color: #1a1a1a;
    }

    .completion-content,
    .achievement-content {
      margin-left: 52px;
    }

    .completion-content h4,
    .achievement-content h4 {
      margin: 0 0 4px 0;
      color: #d4af37;
      font-size: 1rem;
    }

    .completion-content p,
    .achievement-content p {
      margin: 0 0 8px 0;
      color: #ccc;
      font-size: 0.9rem;
    }

    .completion-subtitle {
      margin: 0 0 8px 0 !important;
      color: #aaa !important;
      font-size: 0.8rem !important;
    }

    /* Mission Info Card */
    .mission-info-card {
      background: linear-gradient(135deg, #2a2a2a, #1a1a1a);
      border: 2px solid #d4af37;
      border-radius: 8px;
      padding: 15px;
      text-align: center;
      margin-bottom: 15px;
    }

    .mission-info-card h3 {
      color: #d4af37;
      font-size: 1.2rem;
      margin: 0 0 5px 0;
      font-weight: bold;
    }

    .mission-subtitle {
      color: #aaa;
      font-size: 0.85rem;
      margin: 0;
    }

    .completion-rewards {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    .completion-rewards span {
      background: rgba(255, 255, 255, 0.1);
      padding: 2px 6px;
      border-radius: 3px;
      font-size: 0.75rem;
      color: #d4af37;
      display: flex;
      align-items: center;
      gap: 3px;
    }

    .achievement-rewards {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
    }

    .achievement-rewards small {
      background: rgba(255, 255, 255, 0.1);
      padding: 2px 6px;
      border-radius: 3px;
      font-size: 0.75rem;
      color: #87ceeb;
    }

    /* Stack multiple notifications */
    .mission-completion-notification:nth-of-type(2) {
      top: 100px;
    }

    .achievement-notification:nth-of-type(2) {
      top: 100px;
    }

    .achievement-notification:nth-of-type(3) {
      top: 180px;
    }

    .reward-icon {
      width: 3rem;
      height: 3rem;
      background: linear-gradient(135deg, var(--primary-gold), #ffd700);
      color: #0f172a;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.25rem;
      flex-shrink: 0;
    }

    .reward-info {
      flex: 1;
    }

    .reward-name {
      color: var(--primary-gold);
      font-weight: 600;
      font-size: 1rem;
      margin-bottom: 0.25rem;
    }

    .reward-desc {
      color: rgba(255, 255, 255, 0.8);
      font-size: 0.875rem;
      margin-bottom: 0.5rem;
      line-height: 1.4;
    }

    .reward-requirement {
      color: rgba(255, 255, 255, 0.6);
      font-size: 0.75rem;
      font-style: italic;
      margin-bottom: 0.5rem;
    }

    .reward-values {
      display: flex;
      gap: 1rem;
    }

    .reward-gold, .reward-xp {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      font-size: 0.875rem;
      font-weight: 600;
    }

    .reward-gold {
      color: #ffd700;
    }

    .reward-xp {
      color: #60a5fa;
    }

    /* Ensure reward items display properly */
    .reward-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.75rem 0;
      border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    }

    .reward-item:last-child {
      border-bottom: none;
    }

    .reward-label {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: rgba(255, 255, 255, 0.8);
      font-size: 0.875rem;
    }

    .reward-value {
      color: var(--primary-gold);
      font-weight: 600;
    }

    /* File upload drag-over styling */
    .file-upload.drag-over {
      border-color: var(--primary-gold) !important;
      background: rgba(212, 175, 55, 0.1) !important;
      transform: scale(1.02);
    }

    .file-upload {
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .file-upload:hover {
      border-color: rgba(212, 175, 55, 0.5);
      background: rgba(255, 255, 255, 0.08);
    }

    /* Achievement experience styling */
    .achievement-experience {
      color: #60a5fa;
      font-weight: 600;
      font-size: 0.875rem;
    }
  </style>
</body>
</html> 