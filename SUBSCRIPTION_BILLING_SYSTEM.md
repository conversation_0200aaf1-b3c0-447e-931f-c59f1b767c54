# 30-Day Subscription Billing System

## Overview

This document outlines the comprehensive 30-day subscription billing system that replaces the previous one-time payment model. The system includes graceful cancellations, scheduled downgrades, prorated upgrades, and automatic renewals.

## Key Features

### 🗓️ **30-Day Billing Periods**
- All memberships run on 30-day cycles
- Automatic renewal at the end of each period
- Clear period tracking with `currentPeriodStart` and `currentPeriodEnd`

### 🔄 **Graceful Cancellations**
- Cancel subscription while retaining access until period end
- Set `willCancelAt` date to maintain service
- Prevents immediate access loss
- Automatic tier change to 0 when period expires

### ⬇️ **Scheduled Downgrades**
- Downgrade to lower tier at period end
- No immediate disruption to current tier benefits
- Uses `pendingTierChange` system for scheduling
- Automatic processing by scheduler service

### ⬆️ **Immediate Prorated Upgrades**
- Calculate exact prorated amount for remaining days
- Instant tier upgrade with payment
- Formula: `(priceDifference × daysRemaining) / 30`
- Preserves current billing cycle

### 🤖 **Automated Processing**
- Hourly background scheduler checks for pending changes
- Automatic subscription renewals
- Cleanup of expired subscriptions
- Payment processing integration

## Database Schema Changes

### UserMembership Model Updates

```javascript
// New fields added:
currentPeriodStart: Date,      // Start of current 30-day period
currentPeriodEnd: Date,        // End of current 30-day period
willCancelAt: Date,            // When cancelled subscription ends
pendingTierChange: {           // Scheduled tier changes
  newTier: Number,
  effectiveDate: Date,
  reason: String,              // 'downgrade' or 'cancellation'
  scheduledBy: Date
},

// Enhanced payment history:
paymentHistory: [{
  type: String,                // Added 'prorated_upgrade'
  fromTier: Number,            // Track tier changes
  toTier: Number,
  periodStart: Date,           // Period tracking
  periodEnd: Date,
  prorationDetails: {          // Proration calculations
    daysRemaining: Number,
    proratedAmount: Number,
    fullAmount: Number
  }
}]
```

## API Endpoints

### New/Updated Routes

1. **POST /api/membership/create-payment**
   - Added `changeType` parameter ('new', 'upgrade')
   - Calculates prorated amounts for upgrades
   - Returns detailed pricing information

2. **POST /api/membership/process-payment**
   - Handles both new subscriptions and upgrades
   - Immediate tier changes for upgrades
   - 30-day period setup for new subscriptions

3. **POST /api/membership/cancel**
   - Graceful cancellation preserving access
   - Sets `willCancelAt` to period end
   - Schedules tier 0 change

4. **POST /api/membership/schedule-downgrade**
   - Schedule downgrade to lower tier
   - Takes effect at current period end
   - No immediate service disruption

5. **POST /api/membership/calculate-upgrade**
   - Calculate prorated upgrade costs
   - Preview pricing before payment
   - Shows days remaining and price difference

## Subscription Methods

### Core Subscription Management

```javascript
// Start new 30-day subscription
await membership.startSubscription(tier, paymentData);

// Immediate prorated upgrade
await membership.upgradeSubscription(newTier, paymentData);

// Schedule downgrade for period end
await membership.scheduleDowngrade(newTier);

// Cancel with grace period
await membership.cancelSubscription(reason);

// Calculate upgrade pricing
const proration = membership.calculateProratedUpgrade(newTier);
```

### Automated Processing

```javascript
// Process pending changes (called by scheduler)
await membership.processPendingChange();

// Renew subscription for next 30 days
await membership.renewSubscription();

// Check active status including grace periods
const isActive = membership.isSubscriptionActive();
```

## Scheduler Service

### SubscriptionScheduler Class

- **Frequency**: Runs every hour
- **Functions**:
  - Process pending tier changes
  - Handle subscription renewals
  - Clean up expired subscriptions
  - Manage grace periods

### Scheduled Tasks

1. **Pending Changes**: Process downgrades and cancellations
2. **Renewals**: Flag subscriptions for payment processing
3. **Cleanup**: Handle expired subscriptions and failed payments

## User Experience Flow

### New Subscription
1. User selects tier and payment method
2. Payment processed for full monthly amount
3. 30-day period starts immediately
4. Full tier benefits unlocked

### Upgrade Process
1. User requests higher tier
2. System calculates prorated difference
3. Payment processed for prorated amount
4. Tier upgraded immediately
5. Billing cycle unchanged

### Downgrade Process
1. User requests lower tier
2. Downgrade scheduled for period end
3. Current tier maintained until then
4. Automatic tier change at period end
5. New 30-day cycle starts at lower rate

### Cancellation Process
1. User cancels subscription
2. Access maintained until period end
3. No future billing
4. Automatic tier 0 at expiration

## Pricing Calculations

### Tier Pricing (cents)
- **Forest Guardian**: $5/month (500 cents)
- **Mountain Warrior**: $10/month (1000 cents)  
- **Arcane Master**: $20/month (2000 cents)
- **Dragon Lord**: $40/month (4000 cents)

### Proration Formula

```javascript
const daysRemaining = Math.ceil((periodEnd - now) / (24 * 60 * 60 * 1000));
const priceDifference = newTierPrice - currentTierPrice;
const proratedAmount = Math.round((priceDifference * daysRemaining) / 30);
```

### Example Calculations

**Upgrade from Mountain Warrior ($10) to Dragon Lord ($40) with 15 days remaining:**
- Price difference: $30 (3000 cents)
- Prorated amount: (3000 × 15) / 30 = 1500 cents ($15)
- User pays $15, gets immediate Dragon Lord access

## Benefits of This System

### For Users
- **No immediate loss of access** when cancelling
- **Fair prorated pricing** for upgrades
- **Flexible tier management** with scheduled changes
- **Predictable 30-day billing cycles**

### For Business
- **Reduced churn** through graceful cancellations
- **Increased revenue** from easier upgrades
- **Better cash flow** with predictable billing
- **Automated operations** reducing manual work

## Implementation Status

✅ **Database schema updated** with new fields
✅ **UserMembership model** enhanced with new methods
✅ **API routes** created for all subscription operations
✅ **Subscription scheduler** service implemented
✅ **Prorated billing** calculations working
✅ **Automated processing** system active

## Usage Examples

### Frontend Integration

```javascript
// Calculate upgrade cost
const upgradeInfo = await fetch('/api/membership/calculate-upgrade', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ newTier: 4 })
});

// Process immediate upgrade
const payment = await fetch('/api/membership/create-payment', {
  method: 'POST',
  body: JSON.stringify({ 
    tier: 4, 
    provider: 'square', 
    changeType: 'upgrade' 
  })
});

// Schedule downgrade
const downgrade = await fetch('/api/membership/schedule-downgrade', {
  method: 'POST',
  body: JSON.stringify({ newTier: 2 })
});
```

This system provides a complete, production-ready subscription billing solution that handles all edge cases while maintaining excellent user experience. 