const { BrowserWindow } = require('electron');
const axios = require('axios');
const crypto = require('crypto');

class AuthManager {
  constructor(store) {
    this.store = store;
    this.serverUrl = store.get('serverUrl');
    this.isAuthenticated = false;
    this.user = null;
    this.authWindow = null;
    this.pendingAuthPromise = null;
  }

  updateServerUrl(url) {
    this.serverUrl = url;
  }

  getAuthStatus() {
    return {
      isAuthenticated: this.isAuthenticated,
      user: this.user,
      serverUrl: this.serverUrl
    };
  }

  async validateStoredUser(storedUser) {
    try {
      // Try to validate the stored user with the server
      const response = await axios.get(`${this.serverUrl}/auth/validate-user`, {
        headers: {
          'X-User-ID': storedUser._id,
          'X-Electron-Client': 'true'
        },
        timeout: 5000
      });

      if (response.data.success && response.data.user) {
        this.user = response.data.user;
        this.isAuthenticated = true;
        return true;
      }
      return false;
    } catch (error) {
      console.log('User validation failed:', error.message);
      return false;
    }
  }

  async authenticateWithOAuth(provider, windowManager) {
    // Prevent multiple concurrent auth attempts
    if (this.pendingAuthPromise) {
      return this.pendingAuthPromise;
    }

    this.pendingAuthPromise = this._performOAuthFlow(provider, windowManager);
    
    try {
      const result = await this.pendingAuthPromise;
      return result;
    } finally {
      this.pendingAuthPromise = null;
    }
  }

  async _performOAuthFlow(provider, windowManager) {
    return new Promise((resolve, reject) => {
      // Generate a state parameter for CSRF protection
      const state = crypto.randomBytes(32).toString('hex');
      
      // Create OAuth window
      this.authWindow = new BrowserWindow({
        width: 500,
        height: 700,
        show: false,
        parent: windowManager.mainWindow,
        modal: true,
        webPreferences: {
          nodeIntegration: false,
          contextIsolation: true,
          webSecurity: true
        },
        autoHideMenuBar: true,
        title: `Login with ${provider.charAt(0).toUpperCase() + provider.slice(1)}`
      });

      // Build the OAuth URL with electron flag and state
      const authUrl = `${this.serverUrl}/auth/${provider}?electron=true&state=${state}`;
      console.log('Loading OAuth URL:', authUrl);
      
      this.authWindow.loadURL(authUrl);

      this.authWindow.once('ready-to-show', () => {
        this.authWindow.show();
      });

      // Handle navigation events
      this.authWindow.webContents.on('will-navigate', (event, navigationUrl) => {
        console.log('Will navigate to:', navigationUrl);
        
        if (navigationUrl.startsWith('warcraftarena://')) {
          event.preventDefault();
          this.authWindow.close();
          this.handleOAuthCallback(navigationUrl, state)
            .then(resolve)
            .catch(reject);
        }
      });

      // Handle redirect events (for some OAuth providers)
      this.authWindow.webContents.on('will-redirect', (event, redirectUrl) => {
        console.log('Will redirect to:', redirectUrl);
        
        if (redirectUrl.startsWith('warcraftarena://')) {
          event.preventDefault();
          this.authWindow.close();
          this.handleOAuthCallback(redirectUrl, state)
            .then(resolve)
            .catch(reject);
        }
      });

      // Handle manual close
      this.authWindow.on('closed', () => {
        this.authWindow = null;
        if (!this.isAuthenticated) {
          reject(new Error('Authentication window was closed by user'));
        }
      });

      // Handle load failures
      this.authWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
        console.error('Auth window failed to load:', errorDescription);
        reject(new Error(`Failed to load authentication page: ${errorDescription}`));
      });
    });
  }

  async handleOAuthCallback(callbackUrl, expectedState = null) {
    try {
      console.log('Handling OAuth callback:', callbackUrl);
      
      // Parse the callback URL
      const url = new URL(callbackUrl);
      const token = url.searchParams.get('token');
      const userId = url.searchParams.get('userId');
      const error = url.searchParams.get('error');
      const state = url.searchParams.get('state');

      // Verify state parameter if provided
      if (expectedState && state !== expectedState) {
        throw new Error('Invalid state parameter - possible CSRF attack');
      }

      if (error) {
        throw new Error(`Authentication failed: ${error}`);
      }

      if (!token || !userId) {
        throw new Error('Invalid authentication response - missing token or user ID');
      }

      // Verify token with server and get user data
      console.log('Verifying token with server...');
      const response = await axios.get(`${this.serverUrl}/auth/electron/verify`, {
        headers: { 
          Authorization: `Bearer ${token}`,
          'X-Electron-Client': 'true'
        },
        timeout: 10000
      });

      if (response.data.success && response.data.user) {
        this.user = response.data.user;
        this.isAuthenticated = true;

        // Store user data if remember login is enabled
        if (this.store.get('rememberLogin')) {
          this.store.set('user', this.user);
        }

        console.log('Authentication successful for user:', this.user.username);
        return this.user;
      } else {
        throw new Error('Token verification failed');
      }
    } catch (error) {
      console.error('OAuth callback error:', error);
      this.isAuthenticated = false;
      this.user = null;
      throw error;
    } finally {
      // Clean up auth window
      if (this.authWindow && !this.authWindow.isDestroyed()) {
        this.authWindow.close();
        this.authWindow = null;
      }
    }
  }

  async logout() {
    try {
      // Clear stored user data
      this.store.delete('user');
      this.user = null;
      this.isAuthenticated = false;

      // Notify server of logout (optional, don't fail if it doesn't work)
      try {
        await axios.post(`${this.serverUrl}/auth/electron/logout`, {}, {
          headers: { 'X-Electron-Client': 'true' },
          timeout: 5000
        });
      } catch (error) {
        console.log('Server logout notification failed (non-critical):', error.message);
      }

      console.log('User logged out successfully');
      return true;
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  }

  // Clean up method
  destroy() {
    if (this.authWindow && !this.authWindow.isDestroyed()) {
      this.authWindow.close();
      this.authWindow = null;
    }
  }
}

module.exports = { AuthManager }; 