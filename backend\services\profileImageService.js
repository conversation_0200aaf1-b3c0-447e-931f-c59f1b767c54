const axios = require('axios');
const { getBannerFallback, getAvatarFallback } = require('../config/defaultImages');

/**
 * Service to fetch profile images and banners from YouTube and Twitch APIs
 */
class ProfileImageService {
  constructor() {
    this.youtubeApiKey = process.env.YOUTUBE_API_KEY;
    this.twitchClientId = process.env.TWITCH_CLIENT_ID;
    this.twitchClientSecret = process.env.TWITCH_CLIENT_SECRET;
    this.twitchAccessToken = null;
    this.twitchTokenExpiry = null;
    this.youtubeQuotaExhausted = null; // Track when quota was exhausted
    
    // Log API credential status on initialization
    console.log('🔑 ProfileImageService initialized with credentials:', {
      youtubeApiKey: this.youtubeApiKey ? 'CONFIGURED' : 'MISSING',
      twitchClientId: this.twitchClientId ? 'CONFIGURED' : 'MISSING',
      twitchClientSecret: this.twitchClientSecret ? 'CONFIGURED' : 'MISSING'
    });
  }

  /**
   * Get Twitch access token (app access token for public API calls)
   */
  async getTwitchAccessToken() {
    if (this.twitchAccessToken && this.twitchTokenExpiry && Date.now() < this.twitchTokenExpiry) {
      return this.twitchAccessToken;
    }

    try {
      const response = await axios.post('https://id.twitch.tv/oauth2/token', {
        client_id: this.twitchClientId,
        client_secret: this.twitchClientSecret,
        grant_type: 'client_credentials'
      }, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      this.twitchAccessToken = response.data.access_token;
      this.twitchTokenExpiry = Date.now() + (response.data.expires_in * 1000) - 60000; // Subtract 1 minute for safety
      
      return this.twitchAccessToken;
    } catch (error) {
      console.error('Error getting Twitch access token:', error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * Extract YouTube channel ID from various URL formats
   */
  extractYouTubeChannelId(url) {
    if (!url) return null;

    // Handle various YouTube URL formats
    const patterns = [
      /youtube\.com\/channel\/([a-zA-Z0-9_-]+)/,
      /youtube\.com\/c\/([a-zA-Z0-9_-]+)/,
      /youtube\.com\/user\/([a-zA-Z0-9_-]+)/,
      /youtube\.com\/@([a-zA-Z0-9_-]+)/
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) {
        return { type: pattern.source.includes('channel') ? 'id' : 'username', value: match[1] };
      }
    }

    // If just a username is provided
    if (!url.includes('/') && !url.includes('.')) {
      return { type: 'username', value: url };
    }

    return null;
  }

  /**
   * Extract Twitch username from URL
   */
  extractTwitchUsername(url) {
    if (!url) return null;

    const match = url.match(/twitch\.tv\/([a-zA-Z0-9_-]+)/);
    if (match) {
      return match[1];
    }

    // If just a username is provided
    if (!url.includes('/') && !url.includes('.')) {
      return url;
    }

    return null;
  }

  /**
   * Fetch YouTube channel profile images
   */
  async fetchYouTubeProfile(channelUrl) {
    if (!this.youtubeApiKey) {
      console.warn('YouTube API key not configured');
      return null;
    }

    // Check if we've hit quota recently
    const now = Date.now();
    if (this.youtubeQuotaExhausted && (now - this.youtubeQuotaExhausted) < 3600000) { // 1 hour cooldown
      console.warn('YouTube API quota recently exhausted, using fallback');
      const channelInfo = this.extractYouTubeChannelId(channelUrl);
      return this.createFallbackYouTubeProfile(channelInfo?.value || 'Unknown');
    }

    try {
      const channelInfo = this.extractYouTubeChannelId(channelUrl);
      if (!channelInfo) {
        console.warn('Could not extract YouTube channel info from:', channelUrl);
        return null;
      }

      let apiUrl;
      if (channelInfo.type === 'id') {
        apiUrl = `https://www.googleapis.com/youtube/v3/channels?part=snippet,brandingSettings&id=${channelInfo.value}&key=${this.youtubeApiKey}`;
      } else {
        // For username/@handle, we need to search first
        const searchUrl = `https://www.googleapis.com/youtube/v3/search?part=snippet&type=channel&q=${channelInfo.value}&key=${this.youtubeApiKey}&maxResults=1`;
        
        try {
          const searchResponse = await axios.get(searchUrl);
          
          if (!searchResponse.data.items || searchResponse.data.items.length === 0) {
            console.warn('YouTube channel not found:', channelInfo.value);
            return this.createFallbackYouTubeProfile(channelInfo.value);
          }

          const channelId = searchResponse.data.items[0].snippet.channelId;
          apiUrl = `https://www.googleapis.com/youtube/v3/channels?part=snippet,brandingSettings&id=${channelId}&key=${this.youtubeApiKey}`;
        } catch (searchError) {
          if (searchError.response?.status === 403) {
            console.warn('YouTube API quota exceeded during search - using fallback profile');
            this.youtubeQuotaExhausted = Date.now(); // Mark quota as exhausted
            return this.createFallbackYouTubeProfile(channelInfo.value);
          }
          throw searchError;
        }
      }

      const response = await axios.get(apiUrl);
      
      if (!response.data.items || response.data.items.length === 0) {
        console.warn('YouTube channel data not found');
        return this.createFallbackYouTubeProfile(channelInfo.value);
      }

      const channel = response.data.items[0];
      const snippet = channel.snippet;
      const branding = channel.brandingSettings;

      return {
        avatar: snippet.thumbnails?.high?.url || snippet.thumbnails?.medium?.url || snippet.thumbnails?.default?.url || '/assets/img/ranks/emblem.png',
        banner: branding?.image?.bannerExternalUrl || snippet.thumbnails?.high?.url || '/assets/img/bgs/portal.png',
        title: snippet.title,
        description: snippet.description,
        subscriberCount: snippet.customUrl ? null : null // We'd need different endpoint for subscriber count
      };
    } catch (error) {
      // Handle quota exceeded errors gracefully
      if (error.response?.status === 403 && error.response?.data?.error?.message?.includes('quota')) {
        console.warn('YouTube API quota exceeded - using fallback profile for:', channelUrl);
        this.youtubeQuotaExhausted = Date.now(); // Mark quota as exhausted
        const channelInfo = this.extractYouTubeChannelId(channelUrl);
        return this.createFallbackYouTubeProfile(channelInfo?.value || 'Unknown');
      }
      
      console.error('Error fetching YouTube profile:', error.response?.data || error.message);
      return null;
    }
  }

  /**
   * Create a fallback YouTube profile when API is unavailable
   */
  createFallbackYouTubeProfile(channelName) {
    return {
      avatar: '/assets/img/ranks/emblem.png',
      banner: '/assets/img/bgs/portal.png',
      title: channelName || 'YouTube Channel',
      description: `${channelName}'s YouTube channel`,
      subscriberCount: null
    };
  }

  /**
   * Fetch Twitch channel profile images
   */
  async fetchTwitchProfile(channelUrl) {
    if (!this.twitchClientId || !this.twitchClientSecret) {
      console.warn('Twitch API credentials not configured');
      return null;
    }

    try {
      const username = this.extractTwitchUsername(channelUrl);
      if (!username) {
        console.warn('Could not extract Twitch username from:', channelUrl);
        return null;
      }

      const accessToken = await this.getTwitchAccessToken();
      
      const response = await axios.get(`https://api.twitch.tv/helix/users?login=${username}`, {
        headers: {
          'Client-ID': this.twitchClientId,
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.data.data || response.data.data.length === 0) {
        console.warn('Twitch user not found:', username);
        return null;
      }

      const user = response.data.data[0];

      return {
        avatar: user.profile_image_url || '/assets/img/ranks/emblem.png',
        banner: user.offline_image_url || user.profile_image_url || '/assets/img/bgs/featured.png',
        title: user.display_name,
        description: user.description || `${user.display_name}'s Twitch channel`,
        followerCount: null // We'd need different endpoint for follower count
      };
    } catch (error) {
      console.error('Error fetching Twitch profile:', error.response?.data || error.message);
      return null;
    }
  }

  /**
   * Cache profile data in user record
   */
  async updateUserProfileImages(user) {
    const updates = {};
    
    try {
      // Fetch YouTube profile if available
      if (user.socialLinks?.youtube) {
        const ytProfile = await this.fetchYouTubeProfile(user.socialLinks.youtube);
        if (ytProfile) {
          updates.youtubeAvatar = ytProfile.avatar;
          updates.youtubeBanner = ytProfile.banner;
          
          // DO NOT update main avatar field - keep social avatars separate
          console.log('💾 Cached YouTube avatar for user:', user.username);
        }
      }

      // Fetch Twitch profile if available
      if (user.socialLinks?.twitch) {
        const twitchProfile = await this.fetchTwitchProfile(user.socialLinks.twitch);
        if (twitchProfile) {
          updates.twitchAvatar = twitchProfile.avatar;
          updates.twitchBanner = twitchProfile.banner;
          
          // DO NOT update main avatar field - keep social avatars separate
          console.log('💾 Cached Twitch avatar for user:', user.username);
        }
      }

      return updates;
    } catch (error) {
      console.error('Error updating user profile images:', error);
      return {};
    }
  }
}

module.exports = new ProfileImageService(); 