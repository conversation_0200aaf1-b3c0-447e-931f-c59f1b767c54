const express = require('express');
const mongoose = require('mongoose');
const router = express.Router();
const User = require('../models/User');
const Notification = require('../models/Notification');

// Add request logging for all API routes
router.use((req, res, next) => {
  console.log(`🌐 API Request: ${req.method} ${req.path} from ${req.ip}`);
  if (req.user) {
    console.log(`👤 Authenticated user: ${req.user.username}`);
  } else {
    console.log(`🔒 Unauthenticated request`);
  }
  next();
});

// Middleware to check if user is authenticated
function isAuthenticated(req, res, next) {
  if (req.isAuthenticated && req.isAuthenticated()) {
    return next();
  }
  res.status(401).json({ error: 'Not authenticated' });
}

// GET /api/health - simple health check
router.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    mongodb: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected'
  });
});

// GET /api/me - get current user data
router.get('/me', isAuthenticated, async (req, res) => {
  const user = req.user;
  console.log('🔍 /api/me called for user:', user ? { id: user._id, username: user.username } : 'No user');

  // Award test achievement for visiting profile
  let achievementsAwarded = 0;
  let newAchievementData = [];
  try {
    const AchievementService = require('../services/achievementService');
    console.log(`🔍 Attempting to award first visit achievement to user ${user.username} (${user._id})`);
    const result = await AchievementService.awardAchievement(user._id, 'test_visit_profile');
    if (result.newAchievements.length > 0) {
      achievementsAwarded = result.newAchievements.length;
      newAchievementData = result.newAchievements;
      // Refresh user data to get updated experience
      const updatedUser = await User.findById(user._id);
      user.experience = updatedUser.experience;
      user.honor = updatedUser.honor;
      user.arenaGold = updatedUser.arenaGold;
      user.achievementLevel = updatedUser.achievementLevel;
      console.log(`🏆 ${achievementsAwarded} achievement(s) awarded on profile visit to ${user.username}!`);
      console.log(`📊 Updated stats: ${user.experience} EXP, ${user.arenaGold} gold, ${user.honor} honor`);
    } else {
      console.log(`ℹ️ First visit achievement already awarded to ${user.username}`);
    }
  } catch (error) {
    console.log(`⚠️ Could not award test achievement to ${user.username}:`, error.message);
  }
  
  // Calculate username change eligibility
  const lastUsernameChange = user.lastUsernameChange;
  const canChangeUsername = !lastUsernameChange || 
    (Date.now() - lastUsernameChange.getTime()) >= (30 * 24 * 60 * 60 * 1000); // 30 days

  // Calculate next allowed change date
  let nextUsernameChangeDate = null;
  if (!canChangeUsername && lastUsernameChange) {
    nextUsernameChangeDate = new Date(lastUsernameChange.getTime() + (30 * 24 * 60 * 60 * 1000));
  }

  // Parse registeredAt to ensure it's a valid date
  let registeredAt = user.registeredAt;
  if (registeredAt && typeof registeredAt === 'string') {
    registeredAt = new Date(registeredAt);
  }
  if (!registeredAt || isNaN(registeredAt.getTime())) {
    registeredAt = user._id.getTimestamp(); // Fallback to ObjectId timestamp
  }

  res.json({
    id: user._id,
    username: user.username,
    // Use user's own displayName or fallback to username
    displayName: user.displayName || user.username || 'User',
    email: user.email,
    // Use user's own avatar or default
    avatar: user.avatar || '/assets/img/ranks/emblem.png',
    avatarPreferences: user.avatarPreferences || { type: 'default' },
    isUsernameDefined: user.isUsernameDefined,
    suggestedUsername: user.suggestedUsername || '',
    
    bio: user.bio || '',
    profile: user.profile || {
      age: null,
      gender: '',
      country: '',
      warcraftPreferences: {
        favoriteGame: '',
        favoriteRace: '',
        favoriteStrategy: '',
        firstPlayed: null
      }
    },
    socialLinks: user.socialLinks || { youtube: '', twitch: '' },
    streaming: (() => {
      const streaming = user.streaming || {};
      
      // Ensure all required fields exist with defaults
      const cleanStreaming = {
        isLive: streaming.isLive || false,
        lastChecked: streaming.lastChecked || null,
        description: streaming.description || '',
        youtubeDescription: streaming.youtubeDescription || '',
        twitchDescription: streaming.twitchDescription || '',
        youtubeGames: streaming.youtubeGames || [],
        twitchGames: streaming.twitchGames || [],
        platform: streaming.platform || null
      };
      
      return cleanStreaming;
    })(),
    isContentCreator: user.isContentCreator || false,
    role: user.role || 'user',
    honor: user.honor || 0,
    arenaGold: user.arenaGold || 100,
    experience: user.experience || 0,
    lastUsernameChange: user.lastUsernameChange,
    canChangeUsername: canChangeUsername,
    nextUsernameChangeDate: nextUsernameChangeDate,
    registeredAt: registeredAt,
    achievementsAwarded: achievementsAwarded,
    newAchievementData: newAchievementData
  });
});

// GET /api/user/:id - get public user data
router.get('/user/:id', async (req, res) => {
  try {
    const userId = req.params.id;

    // Find user by ID
    const user = await User.findById(userId).select('-password -email -role -isUsernameDefined -suggestedUsername');

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Award Profile Visitor achievement if logged in and viewing another user's profile
    if (req.user && req.user._id.toString() !== userId) {
      try {
        const AchievementService = require('../services/achievementService');
        await AchievementService.awardAchievement(req.user._id, 'profile_visitor');
        console.log(`🏆 Profile Visitor achievement awarded to ${req.user.username} for viewing ${user.username}'s profile`);
      } catch (error) {
        console.log('Could not award Profile Visitor achievement (may already be awarded):', error.message);
      }
    }

    res.json({
      id: user._id,
      username: user.username,
      // Use user's own displayName or fallback to username
      displayName: user.displayName || user.username || 'User',
      // Use user's own avatar or default
      avatar: user.avatar || '/assets/img/ranks/emblem.png',
      bio: user.bio || '',
      socialLinks: user.socialLinks || { youtube: '', twitch: '' }
    });
  } catch (err) {
    console.error('Error getting user data:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// PUT /api/me/change-username - change username
router.put('/me/change-username', isAuthenticated, async (req, res) => {
  try {
    let { username } = req.body;
    const user = req.user;

    // Validate username
    if (!username || typeof username !== 'string') {
      return res.status(400).json({ error: 'Username is required' });
    }

    // Convert to uppercase
    username = username.toUpperCase();

    // Check if username is different from current
    if (username === user.username) {
      return res.status(400).json({ error: 'New username must be different from current username' });
    }

    // Check if username is available
    const isUsernameTaken = await User.isUsernameTaken(username);
    if (isUsernameTaken) {
      return res.status(400).json({ error: 'Username is already taken' });
    }

    // Check if user can change username
    const now = new Date();
    const registeredAt = user.registeredAt || user.createdAt;
    const daysSinceRegistration = Math.floor((now - registeredAt) / (1000 * 60 * 60 * 24));
    const daysSinceLastChange = user.lastUsernameChange ?
      Math.floor((now - user.lastUsernameChange) / (1000 * 60 * 60 * 24)) :
      null;

    // First 30 days: can change once
    // After that: can change once every 30 days
    if (daysSinceRegistration <= 30) {
      if (user.lastUsernameChange) {
        return res.status(400).json({
          error: 'You can only change your username once within the first 30 days of account creation',
          nextChangeDate: null
        });
      }
    } else if (daysSinceLastChange !== null && daysSinceLastChange < 30) {
      const nextChangeDate = new Date(user.lastUsernameChange);
      nextChangeDate.setDate(nextChangeDate.getDate() + 30);

      return res.status(400).json({
        error: 'You can only change your username once every 30 days',
        nextChangeDate: nextChangeDate
      });
    }

    // Update username
    user.username = username;
    user.lastUsernameChange = now;
    await user.save();

    res.json({
      message: 'Username updated successfully',
      user: {
        id: user._id,
        username: user.username,
        displayName: user.displayName,
        email: user.email,
        avatar: user.avatar,
        lastUsernameChange: user.lastUsernameChange
      }
    });
  } catch (err) {
    console.error('Error changing username:', err);

    // Handle validation errors
    if (err.name === 'ValidationError') {
      return res.status(400).json({ error: err.message });
    }

    res.status(500).json({ error: 'Server error' });
  }
});

// POST /api/me/refresh-avatar - refresh user avatar based on linked players
router.post('/me/refresh-avatar', isAuthenticated, async (req, res) => {
  try {
    const avatarService = require('../services/avatarService');
    const success = await avatarService.updateUserAvatar(req.user._id);
    
    if (success) {
      // Return fresh user data with updated avatar
      const updatedUser = await User.findById(req.user._id);
      res.json({
        message: 'Avatar refreshed successfully',
        avatar: updatedUser.avatar || '/assets/img/ranks/emblem.png'
      });
    } else {
      res.status(500).json({ error: 'Failed to refresh avatar' });
    }
  } catch (error) {
    console.error('Error refreshing avatar:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// PUT /api/me/avatar-preferences - update user avatar preferences
router.put('/me/avatar-preferences', isAuthenticated, async (req, res) => {
  try {
    const { type, customImage } = req.body;
    
    // Validate avatar type
    const validTypes = ['default', 'highest_rank', 'custom'];
    if (!validTypes.includes(type)) {
      return res.status(400).json({ error: 'Invalid avatar type' });
    }
    
    // Validate custom image if type is custom
    const validImages = ['mage.png', 'dragon.png', 'dwarf.png', 'elf.png'];
    if (type === 'custom' && (!customImage || !validImages.includes(customImage))) {
      return res.status(400).json({ error: 'Invalid custom image selection' });
    }
    
    // Update user preferences
    const user = await User.findById(req.user._id);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    // Set avatar preferences
    user.avatarPreferences = {
      type: type,
      customImage: type === 'custom' ? customImage : null,
      lastUpdated: new Date()
    };
    
    await user.save();
    
    // Recalculate avatar based on new preferences
    const avatarService = require('../services/avatarService');
    const newAvatar = await avatarService.calculateUserAvatar(req.user._id);
    
    // Update the avatar field
    user.avatar = newAvatar;
    await user.save();
    
    console.log(`✅ Updated avatar preferences for user ${user.username}: ${type} ${customImage ? `(${customImage})` : ''} → ${newAvatar}`);
    
    res.json({
      message: 'Avatar preferences updated successfully',
      preferences: user.avatarPreferences,
      avatar: newAvatar
    });
    
  } catch (error) {
    console.error('Error updating avatar preferences:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// GET /api/me/avatar-options - get available avatar options for user
router.get('/me/avatar-options', isAuthenticated, async (req, res) => {
  try {
    const Player = require('../models/Player');
    
    // Get user's linked players for rank info
    const linkedPlayers = await Player.find({ user: req.user._id });
    
    // Find highest rank player
    let highestRankPlayer = null;
    if (linkedPlayers && linkedPlayers.length > 0) {
      highestRankPlayer = linkedPlayers.reduce((highest, current) => {
        const currentMMR = current.mmr || 0;
        const highestMMR = highest.mmr || 0;
        return currentMMR > highestMMR ? current : highest;
      });
    }
    
    // Available custom images
    const customImages = [
      { id: 'mage.png', name: 'Mage', path: '/assets/img/profiles/mage.png' },
      { id: 'dragon.png', name: 'Dragon', path: '/assets/img/profiles/dragon.png' },
      { id: 'dwarf.png', name: 'Dwarf', path: '/assets/img/profiles/dwarf.png' },
      { id: 'elf.png', name: 'Elf', path: '/assets/img/profiles/elf.png' }
    ];
    
    res.json({
      currentPreferences: req.user.avatarPreferences || { type: 'default' },
      currentAvatar: req.user.avatar || '/assets/img/ranks/emblem.png',
      options: {
        default: {
          type: 'default',
          name: 'Default Emblem',
          description: 'Classic WC Arena emblem',
          path: '/assets/img/ranks/emblem.png'
        },
        highestRank: {
          type: 'highest_rank',
          name: 'Highest Rank',
          description: highestRankPlayer ? 
            `Your ${highestRankPlayer.rank?.name || 'current'} rank (${highestRankPlayer.name})` :
            'Use your highest rank (no players linked)',
          path: highestRankPlayer?.rank?.image || '/assets/img/ranks/emblem.png',
          available: linkedPlayers.length > 0,
          rankInfo: highestRankPlayer ? {
            name: highestRankPlayer.rank?.name,
            mmr: highestRankPlayer.mmr,
            playerName: highestRankPlayer.name
          } : null
        },
        custom: {
          type: 'custom',
          name: 'Custom Images',
          description: 'Choose from special profile images',
          images: customImages
        }
      }
    });
    
  } catch (error) {
    console.error('Error getting avatar options:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// POST /api/refresh-streaming - refresh streaming data (profile images + live status)
router.post('/refresh-streaming', async (req, res) => {
  try {
    console.log('🔄 Manual streaming data refresh triggered');
    
    const profileImageService = require('../services/profileImageService');
    const { checkAllLiveStatus } = require('../services/streamChecker');
    
    // Find users with social links
    const users = await User.find({
      $or: [
        { 'socialLinks.youtube': { $exists: true, $ne: '' } },
        { 'socialLinks.twitch': { $exists: true, $ne: '' } }
      ]
    });
    
    console.log(`📦 Found ${users.length} users with social links`);
    
    let profileUpdates = 0;
    let profileErrors = 0;
    
    // REMOVED: Profile image refresh to avoid quota exhaustion
    // Profile images are only refreshed via the 30-minute cron job
    console.log('📸 Profile images are refreshed every 30 minutes via cron job, not on-demand');
    
    // Refresh live status
    let liveStatusSuccess = false;
    try {
      await checkAllLiveStatus();
      liveStatusSuccess = true;
      console.log('✅ Live status check completed');
    } catch (error) {
      console.error('❌ Live status check failed:', error.message);
    }
    
    res.json({
      success: true,
      message: 'Streaming data refresh completed',
      results: {
        profileImages: {
          total: users.length,
          updated: 0, // No longer updating on-demand
          errors: 0,
          note: 'Profile images are refreshed every 30 minutes via cron job'
        },
        liveStatus: {
          success: liveStatusSuccess
        }
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Error refreshing streaming data:', error);
    res.status(500).json({ 
      error: 'Server error', 
      details: error.message 
    });
  }
});

// GET /api/me/avatar-debug - debug avatar system for current user
router.get('/me/avatar-debug', isAuthenticated, async (req, res) => {
  try {
    const avatarService = require('../services/avatarService');
    const Player = require('../models/Player');
    
    // Get user's linked players
    const linkedPlayers = await Player.find({ user: req.user._id });
    
    // Calculate what the avatar should be
    const calculatedAvatar = await avatarService.calculateUserAvatar(req.user._id);
    
    res.json({
      userId: req.user._id,
      username: req.user.username,
      currentAvatar: req.user.avatar,
      calculatedAvatar: calculatedAvatar,
      linkedPlayers: linkedPlayers.map(p => ({
        id: p._id,
        name: p.name,
        mmr: p.mmr,
        rank: p.rank
      })),
      avatarMatches: req.user.avatar === calculatedAvatar
    });
  } catch (error) {
    console.error('Error debugging avatar:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// PUT /api/me - update user profile
router.put('/me', isAuthenticated, async (req, res) => {
  try {
    console.log('🔧 PUT /api/me called with body:', JSON.stringify(req.body, null, 2));
    const { bio, profile, socialLinks, streaming } = req.body;

    // First find the user to ensure it exists
    const user = await User.findById(req.user._id);
    if (!user) {
      console.log('❌ User not found:', req.user._id);
      return res.status(404).json({ error: 'User not found' });
    }

    console.log('✅ User found:', user.username);

    // Update the user document
    user.bio = bio;
    console.log('📝 Bio updated to:', bio);
    
    // Update profile data if provided
    if (profile) {
      // Helper function to recursively remove undefined values
      const removeUndefined = (obj) => {
        if (obj === null || typeof obj !== 'object') {
          return obj;
        }
        
        const cleaned = {};
        for (const [key, value] of Object.entries(obj)) {
          if (value !== undefined) {
            if (value !== null && typeof value === 'object' && !Array.isArray(value)) {
              cleaned[key] = removeUndefined(value);
            } else {
              cleaned[key] = value;
            }
          }
        }
        return cleaned;
      };
      
      // Clean both the existing profile and the new profile data
      const cleanExistingProfile = removeUndefined(user.profile || {});
      const cleanProfile = removeUndefined(profile);
      
      user.profile = {
        ...cleanExistingProfile,
        ...cleanProfile
      };
    }

    // Check if social links are being updated
    const oldSocialLinks = user.socialLinks || {};
    const newSocialLinks = {
      youtube: socialLinks?.youtube || '',
      twitch: socialLinks?.twitch || ''
    };
    
    // Update social links
    user.socialLinks = newSocialLinks;

    // Update streaming data if provided
    if (streaming) {
      // Ensure streaming object exists
      if (!user.streaming) {
        user.streaming = {
          isLive: false,
          lastChecked: null,
          description: '',
          youtubeDescription: '',
          twitchDescription: '',
          youtubeGames: [],
          twitchGames: []
        };
      }

      // Update streaming data while preserving existing values
      user.streaming = {
        ...user.streaming,
        ...streaming
      };

      // Handle platform-specific descriptions
      if (streaming.youtubeDescription !== undefined) {
        user.streaming.youtubeDescription = streaming.youtubeDescription;
      }
      if (streaming.twitchDescription !== undefined) {
        user.streaming.twitchDescription = streaming.twitchDescription;
      }

      // Handle platform-specific games arrays
      if (streaming.youtubeGames !== undefined) {
        user.streaming.youtubeGames = Array.isArray(streaming.youtubeGames) ? 
          streaming.youtubeGames.filter(game => ['wc12', 'wc3'].includes(game)) : [];
      }
      if (streaming.twitchGames !== undefined) {
        user.streaming.twitchGames = Array.isArray(streaming.twitchGames) ? 
          streaming.twitchGames.filter(game => ['wc12', 'wc3'].includes(game)) : [];
      }

      // Remove old contentCreator structure if it exists (backward compatibility cleanup)
      if (user.streaming.contentCreator) {
        delete user.streaming.contentCreator;
      }
      
      // Also remove old games array if it exists
      if (user.streaming.games) {
        delete user.streaming.games;
      }
    }

    console.log('💾 About to save user...');
    await user.save();
    console.log('✅ User saved successfully!');

    // If social links changed, profile images will be updated by the daily cron job
    if (oldSocialLinks.youtube !== newSocialLinks.youtube || 
        oldSocialLinks.twitch !== newSocialLinks.twitch) {
      console.log(`📸 Social links updated for ${user.username}. Profile images will be refreshed in the next daily cron job.`);
    }
    
    // Calculate username change eligibility (same as GET /api/me)
    const lastUsernameChange = user.lastUsernameChange;
    const canChangeUsername = !lastUsernameChange || 
      (Date.now() - lastUsernameChange.getTime()) >= (30 * 24 * 60 * 60 * 1000); // 30 days

    // Calculate next allowed change date
    let nextUsernameChangeDate = null;
    if (!canChangeUsername && lastUsernameChange) {
      nextUsernameChangeDate = new Date(lastUsernameChange.getTime() + (30 * 24 * 60 * 60 * 1000));
    }

    // Parse registeredAt to ensure it's a valid date
    let registeredAt = user.registeredAt;
    if (registeredAt && typeof registeredAt === 'string') {
      registeredAt = new Date(registeredAt);
    }
    if (!registeredAt || isNaN(registeredAt.getTime())) {
      registeredAt = user._id.getTimestamp(); // Fallback to ObjectId timestamp
    }

    // Return cleaned user data (same format as GET /api/me)
    res.json({
      id: user._id,
      username: user.username,
      // Use user's own displayName or fallback to username
      displayName: user.displayName || user.username || 'User',
      email: user.email,
      // Use user's own avatar or default
      avatar: user.avatar || '/assets/img/ranks/emblem.png',
      avatarPreferences: user.avatarPreferences || { type: 'default' },
      isUsernameDefined: user.isUsernameDefined,
      suggestedUsername: user.suggestedUsername || '',
      bio: user.bio || '',
      profile: user.profile || {
        age: null,
        gender: '',
        country: '',
        warcraftPreferences: {
          favoriteGame: '',
          favoriteRace: '',
          favoriteStrategy: '',
          firstPlayed: null
        }
      },
      socialLinks: user.socialLinks || { youtube: '', twitch: '' },
      streaming: (() => {
        const streaming = user.streaming || {};
        
        // Ensure all required fields exist with defaults
        const cleanStreaming = {
          isLive: streaming.isLive || false,
          lastChecked: streaming.lastChecked || null,
          description: streaming.description || '',
          youtubeDescription: streaming.youtubeDescription || '',
          twitchDescription: streaming.twitchDescription || '',
          youtubeGames: streaming.youtubeGames || [],
          twitchGames: streaming.twitchGames || [],
          platform: streaming.platform || null
        };
        
        return cleanStreaming;
      })(),
      isContentCreator: user.isContentCreator || false,
      role: user.role || 'user',
      honor: user.honor || 0,
      arenaGold: user.arenaGold || 100,
      experience: user.experience || 0,
      lastUsernameChange: user.lastUsernameChange,
      canChangeUsername: canChangeUsername,
      nextUsernameChangeDate: nextUsernameChangeDate,
      registeredAt: registeredAt
    });
  } catch (error) {
    console.error('❌ Error updating user profile:', error);
    console.error('❌ Error stack:', error.stack);
    res.status(500).json({ error: 'Failed to update user profile' });
  }
});

// GET /api/chat/online-users - Get online users for chat
router.get('/chat/online-users', isAuthenticated, async (req, res) => {
  try {
    // Get all registered users
    const OnlineUser = require('../models/OnlineUser');

    // Get all users with a username defined
    const allUsers = await User.find({ isUsernameDefined: true })
      .select('_id username displayName avatar lastLogin')
      .sort({ username: 1 })
      .lean();

    // Get currently online users
    const onlineUsers = await OnlineUser.getAllOnlineUsers();

    // Create a map of online users by userId for quick lookup
    const onlineUsersMap = new Map();
    onlineUsers.forEach(user => {
      onlineUsersMap.set(user.userId.toString(), user);
    });

    // Calculate the threshold for "recently active" (3 days ago)
    const recentlyActiveThreshold = new Date();
    recentlyActiveThreshold.setDate(recentlyActiveThreshold.getDate() - 3);

    // Combine the data
    const combinedUsers = allUsers.map(user => {
      const onlineUser = onlineUsersMap.get(user._id.toString());
      const isOnline = !!onlineUser;
      const lastActivity = onlineUser ? onlineUser.lastActivity : null;
      const isRecentlyActive = user.lastLogin && new Date(user.lastLogin) >= recentlyActiveThreshold;

      // Fix avatar path - ensure we never send the old path
      let avatarPath = user.avatar;
      if (!avatarPath || avatarPath === '/assets/img/emblem.png' || avatarPath === 'null' || avatarPath === 'undefined') {
        avatarPath = '/assets/img/ranks/emblem.png';
        console.log(`🔧 Fixed avatar path for user ${user.username}: ${user.avatar} -> ${avatarPath}`);
      }

      return {
        userId: user._id,
        username: user.username || user.displayName,
        displayName: user.displayName,
        avatar: avatarPath,
        status: isOnline ? onlineUser.status : 'offline',
        lastActivity: lastActivity,
        isOnline: isOnline,
        isRecentlyActive: isRecentlyActive,
        lastLogin: user.lastLogin
      };
    });

    // Make sure current user is included and marked as online
    const currentUserIndex = combinedUsers.findIndex(u => u.userId.toString() === req.user._id.toString());

    if (currentUserIndex >= 0) {
      combinedUsers[currentUserIndex].status = 'online';
      combinedUsers[currentUserIndex].isOnline = true;
      combinedUsers[currentUserIndex].lastActivity = new Date();
    } else {
      // Add current user if not found
      let currentUserAvatar = req.user.avatar;
      if (!currentUserAvatar || currentUserAvatar === '/assets/img/emblem.png' || currentUserAvatar === 'null' || currentUserAvatar === 'undefined') {
        currentUserAvatar = '/assets/img/ranks/emblem.png';
        console.log(`🔧 Fixed current user avatar path: ${req.user.avatar} -> ${currentUserAvatar}`);
      }
      
      combinedUsers.push({
        userId: req.user._id,
        username: req.user.username || req.user.displayName,
        displayName: req.user.displayName,
        avatar: currentUserAvatar,
        status: 'online',
        lastActivity: new Date(),
        isOnline: true,
        isRecentlyActive: true,
        lastLogin: req.user.lastLogin || new Date()
      });
    }

    // Try to add/update the current user in the online users collection
    try {
      let userAvatar = req.user.avatar;
      if (!userAvatar || userAvatar === '/assets/img/emblem.png' || userAvatar === 'null' || userAvatar === 'undefined') {
        userAvatar = '/assets/img/ranks/emblem.png';
      }
      
      await OnlineUser.findOneAndUpdate(
        { userId: req.user._id },
        {
          userId: req.user._id,
          username: req.user.username || req.user.displayName,
          displayName: req.user.displayName,
          avatar: userAvatar,
          socketId: 'api-request', // Temporary socket ID
          status: 'online',
          lastActivity: Date.now()
        },
        { upsert: true, new: true }
      );
    } catch (updateError) {
      console.error('Error updating online user:', updateError);
    }

    res.json(combinedUsers);
  } catch (err) {
    console.error('Error getting chat users:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// GET /api/notifications - Get user notifications
router.get('/notifications', isAuthenticated, async (req, res) => {
  try {
    // Get unread notifications for the current user
    const notifications = await Notification.getUnreadNotifications(req.user._id);

    res.json(notifications);
  } catch (err) {
    console.error('Error getting notifications:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// GET /api/notifications/all - Get all user notifications
router.get('/notifications/all', isAuthenticated, async (req, res) => {
  try {
    // Get all notifications for the current user
    const notifications = await Notification.getUserNotifications(req.user._id);

    res.json(notifications);
  } catch (err) {
    console.error('Error getting all notifications:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// PUT /api/notifications/:id/read - Mark a notification as read
router.put('/notifications/:id/read', isAuthenticated, async (req, res) => {
  try {
    const notificationId = req.params.id;

    // Mark notification as read
    const notification = await Notification.markAsRead(notificationId);

    if (!notification) {
      return res.status(404).json({ error: 'Notification not found' });
    }

    res.json({ message: 'Notification marked as read', notification });
  } catch (err) {
    console.error('Error marking notification as read:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// PUT /api/notifications/read-all - Mark all notifications as read
router.put('/notifications/read-all', isAuthenticated, async (req, res) => {
  try {
    // Mark all notifications as read for the current user
    await Notification.markAllAsRead(req.user._id);

    res.json({ message: 'All notifications marked as read' });
  } catch (err) {
    console.error('Error marking all notifications as read:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// GET /api/user/:id/players - Get players for a user
router.get('/user/:id/players', async (req, res) => {
  try {
    const userId = req.params.id;
    console.log(`🎮 GET /api/user/${userId}/players called`);

    // Validate ObjectId format
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      console.log(`❌ Invalid ObjectId format: ${userId}`);
      return res.status(400).json({ error: 'Invalid user ID format' });
    }

    // Find user by ID
    const user = await User.findById(userId);

    if (!user) {
      console.log(`❌ User not found: ${userId}`);
      return res.status(404).json({ error: 'User not found' });
    }

    console.log(`✅ User found: ${user.username || user.email}`);

    // Get player IDs linked to this user
    const Player = require('../models/Player');
    const players = await Player.find({ user: userId }).lean();

    console.log(`🎮 Found ${players.length} players for user ${user.username || user.email}`);
    res.json(players);
  } catch (err) {
    console.error('❌ Error getting user players:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// GET /api/profile/layout - Get user's profile layout
router.get('/profile/layout', isAuthenticated, async (req, res) => {
  try {
    console.log('📋 GET /api/profile/layout called by user:', req.user.username);
    
    const user = await User.findById(req.user._id);
    
    if (!user) {
      console.log('❌ User not found for ID:', req.user._id);
      return res.status(404).json({ error: 'User not found' });
    }

    console.log('✅ Profile layout retrieved for user:', user.username, 'Layout exists:', !!user.profileLayout);
    
    // Return the layout or null if not set
    res.json({
      layout: user.profileLayout || null
    });
  } catch (err) {
    console.error('❌ Error getting profile layout:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// POST /api/profile/layout - Save user's profile layout
router.post('/profile/layout', isAuthenticated, async (req, res) => {
  try {
    console.log('💾 POST /api/profile/layout called by user:', req.user.username);
    console.log('💾 Layout data received:', JSON.stringify(req.body, null, 2));
    
    const { layout } = req.body;
    
    if (!layout) {
      console.log('❌ No layout data provided');
      return res.status(400).json({ error: 'Layout data is required' });
    }

    // Validate layout structure
    if (!layout.sections || !Array.isArray(layout.sections)) {
      console.log('❌ Invalid layout structure - sections missing or not array');
      return res.status(400).json({ error: 'Invalid layout structure' });
    }

    // Validate each section
    for (const section of layout.sections) {
      if (!section.id || !section.sectionName || typeof section.position !== 'number') {
        console.log('❌ Invalid section data:', section);
        return res.status(400).json({ error: 'Invalid section data' });
      }
    }

    console.log('✅ Layout validation passed, updating user...');

    // Update user with new layout
    const user = await User.findByIdAndUpdate(
      req.user._id,
      { 
        profileLayout: {
          ...layout,
          lastUpdated: new Date()
        }
      },
      { new: true }
    );

    if (!user) {
      console.log('❌ User not found for update, ID:', req.user._id);
      return res.status(404).json({ error: 'User not found' });
    }

    console.log('✅ Profile layout saved successfully for user:', user.username);

    res.json({
      message: 'Profile layout saved successfully',
      layout: user.profileLayout
    });
  } catch (err) {
    console.error('❌ Error saving profile layout:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// DELETE /api/profile/layout - Clear user's profile layout
router.delete('/profile/layout', isAuthenticated, async (req, res) => {
  try {
    console.log('🗑️ DELETE /api/profile/layout called by user:', req.user.username);
    
    const user = await User.findByIdAndUpdate(
      req.user._id,
      { $unset: { profileLayout: "" } },
      { new: true }
    );

    if (!user) {
      console.log('❌ User not found for deletion, ID:', req.user._id);
      return res.status(404).json({ error: 'User not found' });
    }

    console.log('✅ Profile layout cleared successfully for user:', user.username);

    res.json({
      message: 'Profile layout cleared successfully'
    });
  } catch (err) {
    console.error('❌ Error clearing profile layout:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// DEBUG ENDPOINT - Test route and authentication
router.get('/debug/test', isAuthenticated, (req, res) => {
  console.log('🔍 DEBUG: /api/debug/test called by user:', req.user.username);
  res.json({ 
    message: 'Debug endpoint working!', 
    user: req.user.username,
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
