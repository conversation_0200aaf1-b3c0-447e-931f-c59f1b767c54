/* Achievement Cards and Grid Styling */

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.achievement-card {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border: 2px solid #2a2a4a;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.achievement-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  border-color: #4a4a6a;
}

.achievement-card.completed {
  border-color: #ffd700;
  background: linear-gradient(135deg, #2a2a1e 0%, #3a3a2e 100%);
}

.achievement-card.completed::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #ffd700, #ffed4e, #ffd700);
}

.achievement-card.locked {
  opacity: 0.6;
  filter: grayscale(0.5);
}

.achievement-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #4a4a6a, #6a6a8a);
  border-radius: 50%;
  margin: 0 auto 1rem;
  font-size: 1.5rem;
  color: #fff;
}

.achievement-card.completed .achievement-icon {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #1a1a1a;
}

.achievement-content {
  text-align: center;
  margin-bottom: 1rem;
}

.achievement-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #fff;
  margin-bottom: 0.5rem;
}

.achievement-card.completed .achievement-name {
  color: #ffd700;
}

.achievement-description {
  font-size: 0.9rem;
  color: #b0b0b0;
  line-height: 1.4;
  margin-bottom: 0.75rem;
}

.achievement-rewards {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #ffd700;
  font-weight: 500;
}

.achievement-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #3a3a5a;
}

.achievement-card.completed .achievement-status {
  color: #4ade80;
}

.achievement-card.locked .achievement-status {
  color: #6b7280;
}

.completed-date {
  font-size: 0.8rem;
  color: #9ca3af;
}

.locked-text {
  font-size: 0.8rem;
  color: #6b7280;
}

/* Achievement Game Groups */
.achievement-game-group {
  margin-bottom: 2rem;
}

.game-group-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #ffd700;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #ffd700;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.game-group-title::before {
  content: '🏆';
  font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .achievements-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
  
  .achievement-card {
    padding: 1rem;
  }
  
  .achievement-icon {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }
  
  .achievement-name {
    font-size: 1rem;
  }
  
  .achievement-description {
    font-size: 0.85rem;
  }
}

/* Loading and Empty States */
.achievements-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #9ca3af;
}

.achievements-empty {
  text-align: center;
  padding: 3rem;
  color: #9ca3af;
}

.achievements-empty i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

/* Tab Integration */
.activity-content .achievements-grid {
  margin-top: 0;
}

.section-content .achievements-grid {
  margin-top: 1rem;
} 
