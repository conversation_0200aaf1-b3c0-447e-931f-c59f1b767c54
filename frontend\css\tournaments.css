/* ==========================================================================
   WARCRAFT ARENA - TOURNAMENTS STYLES
   Tournament-specific styles that aren't covered by base.css or components.css
   ========================================================================== */

/* ==========================================================================
   TOURNAMENTS LAYOUT
   ========================================================================== */
.tournaments-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--spacing-xl) var(--spacing-md);
  min-height: calc(100vh - 140px);
}

.tournaments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  position: relative;
  padding: var(--spacing-lg);
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-xl);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.tournaments-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-hover), transparent);
  border-radius: 2px;
}

.tournaments-header h1 {
  margin: 0;
  color: var(--primary-color);
  font-size: 2.5rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  position: relative;
  display: inline-block;
  font-family: var(--font-display);
}

.tournaments-header h1::before {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 60px;
  height: 3px;
  background: var(--primary-color);
  border-radius: 3px;
  box-shadow: 0 0 10px rgba(212, 175, 55, 0.5);
}

.tournaments-actions {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

/* ==========================================================================
   TOURNAMENTS FILTER
   ========================================================================== */
.tournaments-filter {
  margin-bottom: var(--spacing-xl);
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.filter-buttons {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
  justify-content: center;
}

.filter-btn {
  padding: var(--spacing-sm) var(--spacing-lg);
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition-normal);
  font-weight: 500;
  font-size: 0.95rem;
  min-width: 120px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.filter-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.filter-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(212, 175, 55, 0.2);
}

.filter-btn:hover::before {
  left: 100%;
}

.filter-btn.active {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
  border-color: var(--primary-color);
  font-weight: 600;
  box-shadow: 0 4px 20px rgba(212, 175, 55, 0.4);
  transform: translateY(-2px);
}

/* ==========================================================================
   TOURNAMENTS GRID
   ========================================================================== */
.tournaments-list,
.tournaments-grid {
  margin-top: var(--spacing-lg);
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--spacing-xl);
  padding: var(--spacing-sm);
}

.no-tournaments {
  grid-column: 1 / -1;
  text-align: center;
  padding: var(--spacing-xl);
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  font-size: 1.2rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.no-tournaments::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
}

/* ==========================================================================
   TOURNAMENT CARDS
   ========================================================================== */
.tournament-card {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-lg);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  padding: 0;
  transition: all var(--transition-normal);
  overflow: hidden;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.tournament-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 64px rgba(0, 0, 0, 0.4);
  border-color: var(--primary-color);
  background: rgba(255, 255, 255, 0.12);
}

.tournament-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
  z-index: 1;
  box-shadow: 0 0 20px rgba(212, 175, 55, 0.5);
}

.tournament-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-sm);
  position: relative;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
}

.tournament-card h3 {
  margin: 0;
  color: var(--primary-color);
  font-size: 1.4rem;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  flex: 1;
  padding-right: var(--spacing-sm);
  font-family: var(--font-display);
}

.tournament-description {
  padding: var(--spacing-md) var(--spacing-lg);
  color: var(--text-secondary);
  line-height: 1.6;
  font-size: 0.95rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.02);
}

.tournament-info {
  padding: var(--spacing-lg);
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-md);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.03), rgba(255, 255, 255, 0.01));
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.info-item::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.info-item:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.05);
}

.info-item:hover::before {
  opacity: 1;
}

.info-item i {
  font-size: 1.2rem;
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
  position: relative;
  z-index: 1;
}

.info-item span {
  font-size: 0.85rem;
  color: var(--text-secondary);
  font-weight: 500;
  position: relative;
  z-index: 1;
}

.info-item strong {
  color: var(--text-primary);
  font-weight: 600;
}

/* ==========================================================================
   TOURNAMENT STATUS
   ========================================================================== */
.tournament-status {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
}

.tournament-status::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: var(--radius-full);
  margin-right: var(--spacing-xs);
}

.tournament-status.draft {
  background: rgba(76, 86, 106, 0.2);
  color: var(--text-muted);
  border: 1px solid var(--text-muted);
}

.tournament-status.draft::before {
  background: var(--text-muted);
}

.tournament-status.registration {
  background: rgba(235, 203, 139, 0.2);
  color: var(--warning-color);
  border: 1px solid var(--warning-color);
}

.tournament-status.registration::before {
  background: var(--warning-color);
  animation: pulse 2s infinite;
}

.tournament-status.in_progress {
  background: rgba(136, 192, 208, 0.2);
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.tournament-status.in_progress::before {
  background: var(--primary-color);
  animation: pulse 2s infinite;
}

.tournament-status.completed {
  background: rgba(163, 190, 140, 0.2);
  color: var(--success-color);
  border: 1px solid var(--success-color);
}

.tournament-status.completed::before {
  background: var(--success-color);
}

.tournament-status.cancelled {
  background: rgba(191, 97, 106, 0.2);
  color: var(--danger-color);
  border: 1px solid var(--danger-color);
}

.tournament-status.cancelled::before {
  background: var(--danger-color);
}

/* ==========================================================================
   TOURNAMENT DETAILS PAGE
   ========================================================================== */
.tournament-details {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-lg);
}

.tournament-details-header {
  margin-bottom: var(--spacing-xl);
}

.tournament-details-header h1 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
}

.tournament-meta {
  display: flex;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
  margin-bottom: var(--spacing-lg);
}

.tournament-tabs {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
  border-bottom: 2px solid var(--bg-secondary);
}

.tab-btn {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  color: var(--text-primary);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-md) var(--radius-md) 0 0;
  cursor: pointer;
  transition: all var(--transition-normal);
  font-weight: 600;
  border-bottom: none;
}

.tab-btn:hover {
  background: var(--bg-hover);
  border-color: var(--border-hover);
}

.tab-btn.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
  animation: fadeIn var(--transition-normal);
}

/* ==========================================================================
   TOURNAMENT INFO SECTIONS
   ========================================================================== */
.info-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--border-primary);
}

.info-card h3 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-lg);
  font-size: 1.3rem;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-primary);
}

.info-row:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.info-label {
  font-weight: 600;
  color: var(--text-secondary);
}

.info-value {
  color: var(--text-primary);
  font-weight: 500;
}

/* ==========================================================================
   TOURNAMENT BRACKETS
   ========================================================================== */
.tournament-brackets {
  margin-top: var(--spacing-lg);
}

.bracket-round {
  margin-bottom: var(--spacing-xl);
}

.bracket-round-title {
  background: var(--bg-secondary);
  color: var(--primary-color);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  text-align: center;
  font-weight: 600;
  font-size: 1.1rem;
  margin-bottom: var(--spacing-lg);
}

.bracket-matches {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.bracket-match {
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-sm);
}

.bracket-match-player {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm);
  margin-bottom: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.bracket-match-player.winner {
  background: rgba(163, 190, 140, 0.2);
  border: 1px solid var(--success-color);
  font-weight: 600;
}

.bracket-match-player.loser {
  opacity: 0.6;
  background: var(--bg-secondary);
}

.bracket-match-player-name {
  color: var(--text-primary);
  font-weight: 500;
}

.bracket-match-player-seed {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-size: 0.8rem;
  font-weight: 600;
}

/* ==========================================================================
   PLAYER SELECTION
   ========================================================================== */
.player-selection-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-lg);
  margin: var(--spacing-lg) 0;
  max-height: 400px;
  overflow-y: auto;
  padding: var(--spacing-sm);
}

.player-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.player-card:hover {
  background: var(--bg-hover);
  border-color: var(--border-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.player-card.selected {
  background: rgba(136, 192, 208, 0.2);
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(136, 192, 208, 0.3);
}

.player-info {
  flex: 1;
  min-width: 0;
}

.player-rank {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-tertiary);
  border: 2px solid var(--border-primary);
}

.player-rank img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: var(--radius-full);
}

.player-details h3 {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--text-primary);
  font-size: 1rem;
}

.player-details p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.85rem;
}

.player-stats {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xs);
}

.player-mmr {
  background: var(--bg-tertiary);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--primary-color);
}

.player-record {
  font-size: 0.8rem;
  color: var(--text-muted);
}

.player-selection-actions {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
  justify-content: flex-end;
}

.no-players-message {
  grid-column: 1 / -1;
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);
}

.create-player-prompt {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  text-align: center;
  margin: var(--spacing-lg) 0;
}

.create-player-prompt p {
  margin-bottom: var(--spacing-md);
  color: var(--text-secondary);
}

.create-player-prompt a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 600;
  transition: color var(--transition-fast);
}

.create-player-prompt a:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

/* ==========================================================================
   PARTICIPANT STATUS
   ========================================================================== */
.participant-status {
  margin-top: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-primary);
}

.participant-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.participant-info i {
  color: var(--success-color);
  font-size: 1.2rem;
}

.participant-info .player-name {
  font-weight: 600;
  color: var(--text-primary);
}

.tournament-actions {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
  flex-wrap: wrap;
}

/* ==========================================================================
   LEAVE CONFIRMATION
   ========================================================================== */
.leave-confirmation {
  text-align: center;
}

.leave-tournament-content {
  margin-bottom: var(--spacing-lg);
}

.warning-icon {
  font-size: 3rem;
  color: var(--warning-color);
  margin-bottom: var(--spacing-md);
  display: block;
}

.tournament-info-preview {
  background: var(--bg-secondary);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  margin: var(--spacing-md) 0;
}

.tournament-info-preview h3 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
}

.player-info-preview {
  margin-bottom: var(--spacing-sm);
}

.player-info-preview strong {
  color: var(--text-primary);
}

.warning-text {
  color: var(--danger-color);
  font-weight: 600;
  margin: var(--spacing-md) 0;
}

.leave-tournament-actions {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: center;
}

/* ==========================================================================
   TOURNAMENT NAVIGATION
   ========================================================================== */
.tournament-nav-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
}

.tournament-nav-header h1 {
  margin: 0;
  color: var(--primary-color);
  font-size: 1.8rem;
}

.tournament-details-content {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: var(--spacing-xl);
}

.info-section {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  border: 1px solid var(--border-primary);
}

.info-section h2 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-lg);
  font-size: 1.3rem;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

/* ==========================================================================
   RESPONSIVE DESIGN
   ========================================================================== */
/* Media query removed - Phase 3 CSS optimization */
  
  .tournaments-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }
  
  .tournaments-header h1 {
    font-size: 2rem;
    text-align: center;
  }
  
  .tournaments-actions {
    justify-content: center;
  }
  
  .tournaments-list,
  .tournaments-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  
  .tournament-header {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .tournament-info {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }
  
  .tournament-tabs {
    flex-wrap: wrap;
    gap: var(--spacing-xs);
  }
  
  .bracket-matches {
    grid-template-columns: 1fr;
  }
  
  .player-selection-container {
    grid-template-columns: 1fr;
  }
  
  .tournament-details-content {
    grid-template-columns: 1fr;
  }
  
  .tournament-actions {
    flex-direction: column;
  }
  
  .leave-tournament-actions {
    flex-direction: column;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
}
