<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Tournaments - Epic WC Arena Battles</title>
  
  <!-- Preload critical resources -->
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-brands-400.woff2" as="font" type="font/woff2" crossorigin>
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>
  
  <!-- Stylesheets -->
  <link rel="stylesheet" href="/css/warcraft-app-modern.css" />
  <link rel="stylesheet" href="/css/tournaments.css" />
  <link rel="stylesheet" href="/css/bracket.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- Enhanced Tournament Styling -->
  <style>
    /* ===== EPIC TOURNAMENT HERO ===== */
    .tournament-hero {
      position: relative;
      min-height: 40vh;
      background: linear-gradient(135deg, 
        #0a0a0a 0%,
        #1a1a2e 25%,
        #16213e 50%,
        #0f3460 75%,
        #1a1a2e 100%
      );
      background-size: 400% 400%;
      animation: gradientShift 8s ease infinite;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      overflow: hidden;
    }

    @keyframes gradientShift {
      0% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
      100% { background-position: 0% 50%; }
    }

    /* Animated background particles */
    .tournament-hero::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image: 
        radial-gradient(2px 2px at 20px 30px, rgba(255, 215, 0, 0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.2), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(255, 215, 0, 0.4), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255, 255, 255, 0.1), transparent);
      background-repeat: repeat;
      background-size: 200px 100px;
      animation: sparkle 3s linear infinite;
      pointer-events: none;
    }

    @keyframes sparkle {
      0% { transform: translateY(0) scale(1); opacity: 1; }
      100% { transform: translateY(-100px) scale(0.8); opacity: 0; }
    }

    .hero-content {
      position: relative;
      z-index: 2;
      max-width: 800px;
      padding: 2rem;
    }

    .hero-title {
      font-family: 'Cinzel', serif;
      font-size: clamp(2.5rem, 6vw, 4.5rem);
      font-weight: 800;
      background: linear-gradient(45deg, #ffd700, #ffed4e, #ffd700, #ff6b35);
      background-size: 300% 300%;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      animation: goldShimmer 4s ease-in-out infinite;
      margin-bottom: 1rem;
      text-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
      letter-spacing: 1px;
    }

    @keyframes goldShimmer {
      0%, 100% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
    }

    .hero-subtitle {
      font-family: 'Inter', sans-serif;
      font-size: clamp(1rem, 2.5vw, 1.5rem);
      font-weight: 300;
      color: rgba(255, 255, 255, 0.9);
      margin-bottom: 2rem;
      line-height: 1.6;
      text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
    }

    .hero-actions {
      display: flex;
      gap: 1.5rem;
      justify-content: center;
      flex-wrap: wrap;
    }

    /* ===== ENHANCED BUTTONS ===== */
    .epic-btn {
      position: relative;
      display: inline-flex;
      align-items: center;
      gap: 0.75rem;
      padding: 1rem 2rem;
      font-family: 'Cinzel', serif;
      font-size: 1.1rem;
      font-weight: 600;
      text-decoration: none;
      color: #000;
      background: linear-gradient(45deg, #ffd700, #ffed4e);
      border: none;
      border-radius: 50px;
      box-shadow: 
        0 8px 25px rgba(255, 215, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
      transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      overflow: hidden;
      cursor: pointer;
    }

    .epic-btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
      transition: left 0.5s;
    }

    .epic-btn:hover {
      transform: translateY(-3px) scale(1.05);
      box-shadow: 
        0 15px 35px rgba(255, 215, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    }

    .epic-btn:hover::before {
      left: 100%;
    }

    .epic-btn i {
      font-size: 1.2rem;
    }

    /* ===== GLASS MORPHISM CONTAINERS ===== */
    .glass-container {
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 20px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      position: relative;
      overflow: hidden;
    }

    .glass-container::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.5), transparent);
    }

    /* ===== ENHANCED GAME TABS ===== */
    .game-tabs-container {
      margin: 2rem auto;
      max-width: 1200px;
      padding: 2rem;
    }

    .game-tabs {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;
    }

    .game-tab {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 1rem 2rem;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 15px;
      color: rgba(255, 255, 255, 0.8);
      font-family: 'Cinzel', serif;
      font-size: 1.1rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .game-tab::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.1), transparent);
      transition: left 0.5s;
    }

    .game-tab:hover {
      background: rgba(255, 215, 0, 0.1);
      border-color: rgba(255, 215, 0, 0.3);
      color: #ffd700;
      transform: translateY(-2px);
    }

    .game-tab:hover::before {
      left: 100%;
    }

    .game-tab.active {
      background: linear-gradient(45deg, rgba(255, 215, 0, 0.2), rgba(255, 237, 78, 0.2));
      border-color: #ffd700;
      color: #ffd700;
      transform: translateY(-2px);
      box-shadow: 0 4px 20px rgba(255, 215, 0, 0.3);
    }

    .game-tab i {
      font-size: 1.3rem;
    }

    /* ===== ENHANCED FILTERS ===== */
    .tournaments-filter {
      margin: 2rem auto;
      max-width: 1200px;
      padding: 2rem;
      text-align: center;
    }

    .filter-buttons {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;
    }

    .filter-btn {
      padding: 0.75rem 1.5rem;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 25px;
      color: rgba(255, 255, 255, 0.8);
      font-family: 'Inter', sans-serif;
      font-size: 0.95rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      min-width: 120px;
    }

    .filter-btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      transition: left 0.5s;
    }

    .filter-btn:hover {
      background: rgba(255, 215, 0, 0.1);
      border-color: rgba(255, 215, 0, 0.3);
      color: #ffd700;
      transform: translateY(-2px);
    }

    .filter-btn:hover::before {
      left: 100%;
    }

    .filter-btn.active {
      background: linear-gradient(45deg, #ffd700, #ffed4e);
      color: #000;
      border-color: #ffd700;
      font-weight: 600;
      transform: translateY(-2px);
      box-shadow: 0 4px 20px rgba(255, 215, 0, 0.4);
    }

    /* ===== TOURNAMENTS GRID ===== */
    .tournaments-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem;
    }

    .tournaments-list {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
      gap: 1.5rem;
      margin-top: 2rem;
    }

    /* ===== ENHANCED TOURNAMENT CARDS ===== */
    .tournament-card {
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 15px;
      padding: 0;
      transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      overflow: hidden;
      position: relative;
      cursor: pointer;
      height: fit-content;
      display: flex;
      flex-direction: column;
    }

    .tournament-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, #ffd700, #ffed4e, #ff6b35);
    }

    .tournament-card:hover {
      transform: translateY(-5px) scale(1.01);
      box-shadow: 
        0 15px 30px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 215, 0, 0.3);
      background: rgba(255, 255, 255, 0.08);
    }

    .tournament-header {
      padding: 1.2rem;
      background: linear-gradient(135deg, rgba(255, 215, 0, 0.08), rgba(255, 215, 0, 0.03));
      position: relative;
    }

    .tournament-card h3 {
      font-family: 'Cinzel', serif;
      font-size: 1.2rem;
      font-weight: 700;
      color: #ffd700;
      margin: 0 0 0.5rem 0;
      text-shadow: 0 2px 10px rgba(255, 215, 0, 0.3);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .tournament-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.8rem;
    }

    .tournament-description {
      color: rgba(255, 255, 255, 0.8);
      font-size: 0.85rem;
      line-height: 1.4;
      margin: 0;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .tournament-info {
      padding: 1rem 1.2rem 1.2rem;
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 0.6rem;
    }

    .info-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0.4rem;
      border-radius: 8px;
      transition: all 0.3s ease;
      font-size: 0.85rem;
    }

    .info-row:hover {
      background: rgba(255, 215, 0, 0.05);
    }

    .info-label {
      display: flex;
      align-items: center;
      gap: 0.4rem;
      color: rgba(255, 255, 255, 0.7);
      font-weight: 500;
    }

    .info-value {
      color: #fff;
      font-weight: 600;
    }

    .info-label i {
      width: 14px;
      text-align: center;
      color: #ffd700;
      font-size: 0.9rem;
    }

    /* ===== COMPACT STATUS BADGES ===== */
    .tournament-status {
      display: inline-flex;
      align-items: center;
      gap: 0.3rem;
      padding: 0.3rem 0.7rem;
      border-radius: 15px;
      font-size: 0.75rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.3px;
      position: relative;
      overflow: hidden;
    }

    .tournament-status::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      animation: statusShine 3s ease-in-out infinite;
    }

    @keyframes statusShine {
      0% { left: -100%; }
      50% { left: 100%; }
      100% { left: 100%; }
    }

    .tournament-status.registration {
      background: linear-gradient(45deg, #28a745, #20c997);
      color: white;
      box-shadow: 0 2px 10px rgba(40, 167, 69, 0.3);
    }

    .tournament-status.in_progress {
      background: linear-gradient(45deg, #ffc107, #fd7e14);
      color: #000;
      box-shadow: 0 2px 10px rgba(255, 193, 7, 0.3);
    }

    .tournament-status.completed {
      background: linear-gradient(45deg, #6f42c1, #e83e8c);
      color: white;
      box-shadow: 0 2px 10px rgba(111, 66, 193, 0.3);
    }

    .tournament-status.draft {
      background: linear-gradient(45deg, #6c757d, #495057);
      color: white;
      box-shadow: 0 2px 10px rgba(108, 117, 125, 0.3);
    }

    .tournament-dates {
      font-size: 0.8rem;
      color: rgba(255, 255, 255, 0.6);
      text-align: right;
    }

    /* ===== LOADING STATES ===== */
    .loading {
      text-align: center;
      padding: 3rem;
      color: rgba(255, 255, 255, 0.6);
      font-size: 1.1rem;
      position: relative;
    }

    .loading::after {
      content: '';
      width: 40px;
      height: 40px;
      border: 3px solid rgba(255, 215, 0, 0.2);
      border-top: 3px solid #ffd700;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 1rem auto 0;
      display: block;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* ===== ANIMATIONS ===== */
    .fade-in-up {
      opacity: 0;
      transform: translateY(30px);
      animation: fadeInUp 0.8s ease forwards;
    }

    @keyframes fadeInUp {
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .stagger-animation {
      animation-delay: var(--delay, 0s);
    }

    /* ===== RESPONSIVE DESIGN ===== */
    @media (max-width: 768px) {
      .hero-actions {
        flex-direction: column;
        align-items: center;
      }
      
      .tournaments-list {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }
      
      .game-tabs {
        flex-direction: column;
        align-items: center;
      }
      
      .filter-buttons {
        flex-direction: column;
        align-items: center;
      }
      
      .tournaments-container {
        padding: 1rem;
      }
    }

    /* ===== NO TOURNAMENTS STATE ===== */
    .no-tournaments {
      grid-column: 1 / -1;
      text-align: center;
      padding: 4rem 2rem;
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 20px;
      color: rgba(255, 255, 255, 0.7);
      position: relative;
      overflow: hidden;
    }

    .no-tournaments::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, #ffd700, #ffed4e, #ff6b35);
    }

    .no-tournaments h3 {
      font-family: 'Cinzel', serif;
      font-size: 1.8rem;
      color: #ffd700;
      margin-bottom: 1rem;
    }

    .no-tournaments p {
      font-size: 1.1rem;
      margin-bottom: 2rem;
    }

    /* ===== TOURNAMENT ACTIONS ===== */
    .tournament-actions {
      padding: 0 1.2rem 1.2rem;
      display: flex;
      gap: 0.5rem;
    }

    .tournament-btn {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.4rem;
      padding: 0.6rem;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      color: rgba(255, 255, 255, 0.8);
      text-decoration: none;
      font-size: 0.8rem;
      font-weight: 500;
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .tournament-btn:hover {
      background: rgba(255, 215, 0, 0.1);
      border-color: rgba(255, 215, 0, 0.3);
      color: #ffd700;
      transform: translateY(-1px);
    }

    .tournament-btn.primary {
      background: linear-gradient(45deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.1));
      border-color: rgba(255, 215, 0, 0.3);
      color: #ffd700;
    }

    .tournament-btn.primary:hover {
      background: linear-gradient(45deg, rgba(255, 215, 0, 0.3), rgba(255, 215, 0, 0.2));
      border-color: rgba(255, 215, 0, 0.5);
    }
  </style>
</head>
<body>
  <div id="navbar-container"></div>

  <main>
    <!-- Tournament Hero Section -->
    <section class="tournament-hero">
      <div class="hero-content fade-in-up">
        <h1 class="hero-title">Tournaments</h1>
        <div class="hero-actions">
          <button id="create-tournament-btn" class="epic-btn">
            <i class="fas fa-trophy"></i>
            <span>Create Tournament</span>
          </button>
        </div>
      </div>
    </section>

    <!-- Game Selection Tabs -->
    <section class="game-tabs-container glass-container fade-in-up">
      <div class="game-tabs">
        <button class="game-tab active" data-game="war2">
          <i class="fas fa-shield-alt"></i>
          <span>WC II</span>
        </button>
        <button class="game-tab" data-game="war3">
          <i class="fas fa-dragon"></i>
          <span>WC III</span>
        </button>
      </div>
    </section>

    <!-- Tournament Filters -->
    <section class="tournaments-filter glass-container fade-in-up">
      <div class="filter-buttons">
        <button class="filter-btn active" data-status="all">All Tournaments</button>
        <button class="filter-btn" data-status="registration">Open Registration</button>
        <button class="filter-btn" data-status="in_progress">In Progress</button>
        <button class="filter-btn" data-status="completed">Completed</button>
      </div>
    </section>

    <!-- Tournaments Container -->
    <div class="tournaments-container">
      <div class="tournaments-list" id="tournaments-list">
        <div class="loading">Loading epic tournaments...</div>
      </div>
    </div>

    <!-- Tournament Details View (hidden by default) -->
    <div class="tournament-details d-none" id="tournament-details">
      <div class="tournament-details-header">
        <button id="back-to-tournaments" class="btn btn-secondary">Back to Tournaments</button>
        <h1 id="tournament-name">Tournament Name</h1>
        <div class="tournament-meta">
          <span id="tournament-status" class="tournament-status">Status</span>
          <span id="tournament-dates">Dates</span>
        </div>
        <p id="tournament-description">Tournament description will appear here.</p>
      </div>

      <div class="tournament-stats">
        <div class="stat">
          <span class="stat-label">Participants:</span>
          <span id="participant-count" class="stat-value">0</span>
        </div>
        <div class="stat">
          <span class="stat-label">Matches:</span>
          <span id="match-count" class="stat-value">0</span>
        </div>
        <div class="stat">
          <span class="stat-label">Completed:</span>
          <span id="completed-match-count" class="stat-value">0</span>
        </div>
      </div>

      <div class="tournament-tabs">
        <button class="tab-btn active" data-tab="overview">Overview</button>
        <button class="tab-btn" data-tab="participants">Participants</button>
        <button class="tab-btn" data-tab="brackets">Brackets</button>
        <button class="tab-btn" data-tab="matches">Matches</button>
      </div>

      <div class="tournament-tab-content">
        <!-- Overview Tab -->
        <div class="tab-content active" id="overview-tab">
          <div class="tournament-info">
            <div class="info-card">
              <h3>Tournament Details</h3>
              <div class="info-row">
                <span class="info-label">Format:</span>
                <span id="tournament-format" class="info-value">Single Elimination</span>
              </div>
              <div class="info-row">
                <span class="info-label">Max Participants:</span>
                <span id="tournament-max-participants" class="info-value">8</span>
              </div>
              <div class="info-row">
                <span class="info-label">Match Type:</span>
                <span id="tournament-match-type" class="info-value">1v1</span>
              </div>
              <div class="info-row">
                <span class="info-label">Organizer:</span>
                <span id="tournament-organizer" class="info-value">Username</span>
              </div>
            </div>
            <div class="registration-card">
              <h3>Registration</h3>
              <div id="registration-status">
                <p>Registration is open until <span id="registration-deadline">date</span></p>
                <button id="register-btn" class="btn btn-primary">Register</button>
              </div>
            </div>
          </div>
        </div>

        <!-- Participants Tab -->
        <div class="tab-content" id="participants-tab">
          <div class="participants-list" id="participants-list">
            <div class="loading">Loading participants...</div>
          </div>
        </div>

        <!-- Brackets Tab -->
        <div class="tab-content" id="brackets-tab">
          <div class="brackets-header">
            <h3>Tournament Bracket</h3>
          </div>
          <div id="bracket-container" class="bracket-container">
            <div class="loading">Loading bracket...</div>
          </div>
        </div>

        <!-- Matches Tab -->
        <div class="tab-content" id="matches-tab">
          <div class="tournament-matches" id="tournament-matches">
            <div class="loading">Loading matches...</div>
          </div>
        </div>
      </div>
    </div>
  </main>

  <!-- Create Tournament Modal -->
  <div id="create-tournament-modal" class="modal">
    <div class="modal-content">
      <span class="close-modal">&times;</span>
      <h2>Create Tournament</h2>
      <form id="create-tournament-form">
        <div class="form-group">
          <label for="tournament-name-input">Tournament Name:</label>
          <input type="text" id="tournament-name-input" required>
        </div>
        <div class="form-group">
          <label for="tournament-description-input">Description:</label>
          <textarea id="tournament-description-input" rows="4"></textarea>
        </div>
        <div class="form-group">
          <label for="tournament-type-input">Tournament Type:</label>
          <select id="tournament-type-input" required>
            <option value="single_elimination">Single Elimination</option>
            <option value="double_elimination">Double Elimination</option>
            <option value="round_robin">Round Robin</option>
            <option value="swiss">Swiss</option>
          </select>
        </div>
        <div class="form-group">
          <label for="tournament-max-participants-input">Max Participants:</label>
          <input type="number" id="tournament-max-participants-input" min="4" max="64" value="8" required>
        </div>
        <div class="form-group">
          <label for="tournament-start-date-input">Start Date:</label>
          <input type="datetime-local" id="tournament-start-date-input" required>
        </div>
        <div class="form-group">
          <label for="tournament-end-date-input">End Date:</label>
          <input type="datetime-local" id="tournament-end-date-input">
        </div>
        <div class="form-group">
          <label for="tournament-match-type-input">Match Type:</label>
          <select id="tournament-match-type-input" required>
            <option value="1v1">1v1</option>
            <option value="2v2">2v2</option>
            <option value="3v3">3v3</option>
            <option value="4v4">4v4</option>
            <option value="ffa">FFA</option>
          </select>
        </div>
        <button type="submit" class="btn btn-primary">Create Tournament</button>
      </form>
    </div>
  </div>

  <!-- Player Selection Modal -->
  <div id="player-selection-modal" class="modal">
    <div class="modal-content">
      <span class="close-modal">&times;</span>
      <h2>Select Player</h2>
      <p>Choose which player you want to join the tournament with:</p>
      <div class="player-selection-container">
        <div class="loading">Loading your players...</div>
      </div>
      <div class="player-selection-actions">
        <button id="cancel-player-selection" class="btn btn-secondary">Cancel</button>
      </div>
    </div>
  </div>

  <!-- Leave Tournament Confirmation Modal -->
  <div id="leave-tournament-modal" class="modal">
    <div class="modal-content leave-confirmation">
      <span class="close-modal">&times;</span>
      <h2>Leave Tournament</h2>
      <div class="leave-tournament-content">
        <div class="warning-icon">
          <i class="fas fa-exclamation-triangle"></i>
        </div>
        <p>Are you sure you want to leave this tournament?</p>
        <div class="tournament-info-preview">
          <h3 id="leave-tournament-name">Tournament Name</h3>
          <div class="player-info-preview">
            <span>Participating as: </span>
            <strong id="leave-tournament-player">Player Name</strong>
          </div>
        </div>
        <p class="warning-text">This action cannot be undone if the tournament has already started.</p>
      </div>
      <div class="leave-tournament-actions">
        <button id="cancel-leave-tournament" class="btn btn-secondary">Cancel</button>
        <button id="confirm-leave-tournament" class="btn btn-danger">Leave Tournament</button>
      </div>
    </div>
  </div>

  <div id="footer-container"></div>

  <!-- Scripts -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
  <script src="/js/utils.js"></script>
  <script src="/js/main.js"></script>
  <script src="/js/tournaments-consolidated.js"></script>
  
  <script>
    // Intersection Observer for animations
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry, index) => {
        if (entry.isIntersecting) {
          entry.target.style.setProperty('--delay', `${index * 0.1}s`);
          entry.target.classList.add('fade-in-up', 'stagger-animation');
        }
      });
    }, observerOptions);

    // Observe all sections for animations
    document.addEventListener('DOMContentLoaded', () => {
      document.querySelectorAll('.glass-container, .tournament-card').forEach(element => {
        observer.observe(element);
      });

      // Add stagger animation to tournament cards
      const tournamentCards = document.querySelectorAll('.tournament-card');
      tournamentCards.forEach((card, index) => {
        card.style.setProperty('--delay', `${index * 0.1}s`);
        card.classList.add('fade-in-up', 'stagger-animation');
      });
    });

    // Enhanced button interactions
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('epic-btn') || e.target.closest('.epic-btn')) {
        const btn = e.target.classList.contains('epic-btn') ? e.target : e.target.closest('.epic-btn');
        
        // Create ripple effect
        const ripple = document.createElement('span');
        ripple.style.position = 'absolute';
        ripple.style.borderRadius = '50%';
        ripple.style.background = 'rgba(255, 255, 255, 0.5)';
        ripple.style.transform = 'scale(0)';
        ripple.style.animation = 'ripple 0.6s linear';
        ripple.style.left = e.offsetX + 'px';
        ripple.style.top = e.offsetY + 'px';
        ripple.style.width = ripple.style.height = '20px';
        ripple.style.marginLeft = ripple.style.marginTop = '-10px';
        
        btn.appendChild(ripple);
        
        setTimeout(() => {
          ripple.remove();
        }, 600);
      }
    });

    // Add ripple animation keyframes
    const style = document.createElement('style');
    style.textContent = `
      @keyframes ripple {
        to {
          transform: scale(4);
          opacity: 0;
        }
      }
    `;
    document.head.appendChild(style);
  </script>
</body>
</html> 