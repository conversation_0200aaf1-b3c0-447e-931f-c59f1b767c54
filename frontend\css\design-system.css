/* ==========================================================================
   WARCRAFT ARENA - UNIFIED DESIGN SYSTEM
   The one true source of all design tokens, components, and styles
   ========================================================================== */

/* =============================================================================
   DESIGN TOKENS - ALL VARIABLES IN ONE PLACE
   ============================================================================= */
:root {
  /* ===== BRAND COLORS ===== */
  --warcraft-gold: #D4AF37;
  --warcraft-gold-dark: #B8941F;
  --warcraft-gold-light: #E8C547;
  
  --alliance-blue: #2563EB;
  --alliance-blue-dark: #1D4ED8;
  --alliance-blue-light: #3B82F6;
  
  --horde-red: #DC2626;
  --horde-red-dark: #B91C1C;
  --horde-red-light: #EF4444;
  
  /* ===== NEUTRAL PALETTE ===== */
  --neutral-50: #F8FAFC;
  --neutral-100: #F1F5F9;
  --neutral-200: #E2E8F0;
  --neutral-300: #CBD5E1;
  --neutral-400: #94A3B8;
  --neutral-500: #64748B;
  --neutral-600: #475569;
  --neutral-700: #334155;
  --neutral-800: #1E293B;
  --neutral-900: #0F172A;
  
  /* ===== SEMANTIC COLORS ===== */
  --success: #10B981;
  --error: #EF4444;
  --warning: #F59E0B;
  --info: #3B82F6;
  
  /* ===== BACKGROUND SYSTEM ===== */
  --bg-primary: linear-gradient(135deg, #0F172A 0%, #1E293B 50%, #334155 100%);
  --bg-secondary: rgba(30, 41, 59, 0.8);
  --bg-tertiary: rgba(51, 65, 85, 0.6);
  --bg-card: rgba(15, 23, 42, 0.8);
  --bg-hover: rgba(30, 41, 59, 0.9);
  --bg-overlay: rgba(0, 0, 0, 0.8);
  
  /* ===== GLASS EFFECTS ===== */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  
  /* ===== TYPOGRAPHY ===== */
  --font-primary: 'Inter', 'Segoe UI', system-ui, sans-serif;
  --font-display: 'Cinzel', serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
  
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  
  /* ===== SPACING SCALE ===== */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  
  /* ===== BORDER RADIUS ===== */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  
  /* ===== SHADOWS ===== */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
  --shadow-glow: 0 0 20px rgba(212, 175, 55, 0.3);
  
  /* ===== TRANSITIONS ===== */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  
  /* ===== Z-INDEX STACK ===== */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
  
  /* ===== LEGACY ALIASES (for compatibility) ===== */
  --primary-gold: var(--warcraft-gold);
  --primary-color: var(--warcraft-gold);
  --text-primary: var(--neutral-100);
  --text-secondary: var(--neutral-400);
  --text-muted: var(--neutral-500);
  --border-primary: var(--glass-border);
  --spacing-sm: var(--space-2);
  --spacing-md: var(--space-4);
  --spacing-lg: var(--space-6);
  --spacing-xl: var(--space-8);
}

/* =============================================================================
   RESET & BASE STYLES
   ============================================================================= */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-primary);
  background: var(--bg-primary);
  color: var(--neutral-100);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* =============================================================================
   TYPOGRAPHY SYSTEM
   ============================================================================= */
.text-display { font-family: var(--font-display); }
.text-mono { font-family: var(--font-mono); }

.text-xs { font-size: var(--text-xs); }
.text-sm { font-size: var(--text-sm); }
.text-base { font-size: var(--text-base); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.text-2xl { font-size: var(--text-2xl); }
.text-3xl { font-size: var(--text-3xl); }
.text-4xl { font-size: var(--text-4xl); }

.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-primary { color: var(--warcraft-gold); }
.text-alliance { color: var(--alliance-blue); }
.text-horde { color: var(--horde-red); }
.text-success { color: var(--success); }
.text-error { color: var(--error); }
.text-warning { color: var(--warning); }
.text-info { color: var(--info); }
.text-muted { color: var(--neutral-400); }

/* =============================================================================
   BUTTON SYSTEM
   ============================================================================= */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  font-family: var(--font-primary);
  font-size: var(--text-sm);
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  white-space: nowrap;
}

.btn-primary {
  background: linear-gradient(135deg, var(--warcraft-gold), var(--warcraft-gold-dark));
  color: var(--neutral-900);
  border-color: var(--warcraft-gold);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--warcraft-gold-light), var(--warcraft-gold));
  transform: translateY(-1px);
  box-shadow: var(--shadow-glow);
}

.btn-secondary {
  background: var(--glass-bg);
  color: var(--neutral-100);
  border-color: var(--glass-border);
  backdrop-filter: blur(10px);
}

.btn-secondary:hover {
  background: var(--glass-border);
  border-color: var(--warcraft-gold);
  color: var(--warcraft-gold);
}

.btn-danger {
  background: var(--horde-red);
  color: white;
  border-color: var(--horde-red);
}

.btn-danger:hover {
  background: var(--horde-red-dark);
  transform: translateY(-1px);
}

.btn-sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-xs);
}

.btn-lg {
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-lg);
}

/* =============================================================================
   MODAL SYSTEM - UNIFIED AND WORKING
   ============================================================================= */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--bg-overlay);
  backdrop-filter: blur(10px);
  z-index: var(--z-modal);
  display: none;
  align-items: center;
  justify-content: center;
  padding: var(--space-4);
}

.modal.show {
  display: flex;
}

.modal-content {
  background: linear-gradient(135deg, rgba(20, 20, 20, 0.95), rgba(40, 40, 40, 0.95));
  border: 2px solid var(--warcraft-gold);
  border-radius: var(--radius-xl);
  box-shadow: var(--glass-shadow), var(--shadow-glow);
  backdrop-filter: blur(20px);
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  padding: var(--space-6);
  color: var(--neutral-100);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-title {
  color: var(--warcraft-gold);
  font-family: var(--font-display);
  font-size: var(--text-2xl);
  font-weight: 700;
  margin: 0;
}

.modal-close {
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  background: rgba(220, 38, 38, 0.2);
  border: 2px solid rgba(220, 38, 38, 0.4);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #dc2626;
  font-size: 18px;
  cursor: pointer;
  transition: all var(--transition-normal);
  z-index: 1;
}

.modal-close:hover {
  background: rgba(220, 38, 38, 0.4);
  color: white;
  transform: scale(1.1) rotate(90deg);
}

.modal-body {
  margin-bottom: var(--space-6);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-3);
  padding-top: var(--space-4);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* =============================================================================
   CARD SYSTEM
   ============================================================================= */
.card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  box-shadow: var(--glass-shadow);
  padding: var(--space-6);
  transition: all var(--transition-normal);
}

.card:hover {
  background: var(--glass-border);
  border-color: var(--warcraft-gold);
  transform: translateY(-2px);
  box-shadow: var(--shadow-glow);
}

.card-header {
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--glass-border);
}

.card-title {
  color: var(--warcraft-gold);
  font-family: var(--font-display);
  font-size: var(--text-xl);
  font-weight: 600;
  margin: 0;
}

/* =============================================================================
   FORM SYSTEM
   ============================================================================= */
.form-group {
  margin-bottom: var(--space-4);
}

.form-label {
  display: block;
  margin-bottom: var(--space-2);
  color: var(--neutral-300);
  font-weight: 500;
  font-size: var(--text-sm);
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: var(--space-3);
  background: var(--bg-card);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-md);
  color: var(--neutral-100);
  font-family: var(--font-primary);
  font-size: var(--text-base);
  transition: all var(--transition-normal);
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: var(--warcraft-gold);
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.2);
}

.form-error {
  border-color: var(--error);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
}

.form-help {
  margin-top: var(--space-1);
  font-size: var(--text-xs);
  color: var(--neutral-400);
}

/* =============================================================================
   UTILITY CLASSES
   ============================================================================= */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }
.mt-4 { margin-top: var(--space-4); }
.mt-6 { margin-top: var(--space-6); }

.p-4 { padding: var(--space-4); }
.p-6 { padding: var(--space-6); }

.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }

.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }

.transition { transition: all var(--transition-normal); }

/* Force body scroll lock when modal is open */
body.modal-open {
  overflow: hidden;
} 
