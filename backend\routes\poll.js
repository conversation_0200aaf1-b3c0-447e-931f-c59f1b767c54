const express = require('express');
const router = express.Router();
const Poll = require('../models/Poll');
const { ensureAuthenticated, isAdmin } = require('../middleware/auth');

// POST /api/poll - Create a new poll (admin only)
router.post('/', isAdmin, async (req, res) => {
  try {
    const { question, options } = req.body;
    
    if (!question || !options || !Array.isArray(options) || options.length < 2) {
      return res.status(400).json({ error: 'Question and at least 2 options are required' });
    }
    
    const formattedOptions = options.map(opt => ({ value: opt, votes: 0 }));
    const poll = new Poll({
      question,
      options: formattedOptions,
      voters: []
    });
    
    await poll.save();
    res.status(201).json(poll);
  } catch (err) {
    console.error('Error creating poll:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// GET /api/poll/:identifier - Get poll data
router.get('/:identifier', async (req, res) => {
  try {
    const { identifier } = req.params;
    const poll = await Poll.getPollByIdentifier(identifier);
    
    if (!poll) {
      // Create the divorce poll if it doesn't exist
      if (identifier === 'divorce') {
        const newPoll = new Poll({
          identifier: 'divorce',
          question: 'Is my wife going to divorce me?',
          options: [
            { value: 'yes', votes: 0 },
            { value: 'no', votes: 0 }
          ]
        });
        await newPoll.save();
        return res.json(newPoll);
      }
      return res.status(404).json({ error: 'Poll not found' });
    }

    // Add hasVoted property if user is authenticated
    const response = { ...poll.toObject() };
    if (req.user) {
      response.hasVoted = poll.voters.some(voter => voter.userId.toString() === req.user._id.toString());
    }
    
    res.json(response);
  } catch (err) {
    console.error('Error getting poll:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// POST /api/poll/vote - Vote on a poll (requires authentication)
const handleVote = async (req, res) => {
  try {
    const { poll: identifier, vote: option } = req.body;
    if (!identifier || !option) {
      return res.status(400).json({ error: 'Poll identifier and vote option are required' });
    }
    // Vote on the poll (creating it first if it's the divorce poll)
    let poll = await Poll.getPollByIdentifier(identifier);
    if (!poll && identifier === 'divorce') {
      poll = new Poll({
        identifier: 'divorce',
        question: 'Is my wife going to divorce me?',
        options: [
          { value: 'yes', votes: 0 },
          { value: 'no', votes: 0 }
        ]
      });
      await poll.save();
    } else if (!poll) {
      return res.status(404).json({ error: 'Poll not found' });
    }
    // Vote on the poll with user ID
    try {
      poll = await Poll.vote(identifier, option, req.user._id);
      res.json({
        message: 'Vote recorded successfully',
        poll: {
          ...poll.toObject(),
          hasVoted: true
        }
      });
    } catch (voteErr) {
      if (voteErr.message === 'User has already voted on this poll') {
        return res.status(400).json({ error: voteErr.message });
      }
      throw voteErr;
    }
  } catch (err) {
    console.error('Error voting on poll:', err);
    res.status(500).json({ error: 'Server error: ' + err.message });
  }
};

router.post('/vote', ensureAuthenticated, handleVote);

module.exports = router;
