# Warcraft Arena - Electron App

This folder contains the Electron desktop application for Warcraft Arena.

## Structure

```
electron-app/
├── main.js              # Main Electron process
├── preload.js           # Preload script for secure IPC
├── src/                 # Source modules
│   ├── auth-manager.js  # OAuth authentication handling
│   └── window-manager.js # Window creation and management
├── pages/               # Electron-specific pages
│   ├── login.html       # Custom login page
│   └── error.html       # Error page (to be created)
├── assets/              # App icons and assets
│   ├── icon.png         # Linux icon
│   ├── icon.ico         # Windows icon
│   └── icon.icns        # macOS icon
└── README.md           # This file
```

## Features

- **OAuth Integration**: Seamless authentication with Google, Discord, and Twitch
- **Custom Protocol**: `warcraftarena://` for deep linking and OAuth callbacks
- **Secure Storage**: User data stored securely using electron-store
- **Auto-updater**: Ready for automatic updates (electron-updater)
- **Cross-platform**: Windows, macOS, and Linux support

## Development

### Running in Development

```bash
# Start the backend server
npm start

# In another terminal, start the Electron app
npm run electron-dev
```

### Building for Production

```bash
# Build for all platforms
npm run dist

# Build for specific platform
npm run dist-win    # Windows
npm run dist-mac    # macOS
npm run dist-linux  # Linux
```

## OAuth Flow

1. User clicks OAuth button in Electron login page
2. Electron opens OAuth window pointing to server OAuth endpoint
3. User completes OAuth on provider (Google/Discord/Twitch)
4. Server redirects to `warcraftarena://` protocol with JWT token
5. Electron intercepts the protocol, validates token with server
6. User is logged in and main app loads

## Configuration

The app stores configuration in:
- **Windows**: `%APPDATA%/warcraft-arena-config/config.json`
- **macOS**: `~/Library/Application Support/warcraft-arena-config/config.json`
- **Linux**: `~/.config/warcraft-arena-config/config.json`

## Security

- No node integration in renderer processes
- Context isolation enabled
- All IPC communication through secure preload script
- JWT tokens for authentication
- CSRF protection with state parameters

## Customization

### Changing Server URL

Users can change the server URL through the settings (to be implemented) or by modifying the config file directly.

### Custom Icons

Replace the icon files in `assets/` with your own:
- `icon.png` - 256x256 PNG for Linux
- `icon.ico` - Multi-size ICO for Windows
- `icon.icns` - Multi-size ICNS for macOS

## Troubleshooting

### Protocol Handler Issues

If the OAuth callback doesn't work, ensure the custom protocol is registered:

**Windows**: Run the app as administrator once to register the protocol
**macOS**: Protocol should register automatically
**Linux**: May need manual registration depending on desktop environment

### Token Verification Fails

Check that:
1. Backend server is running
2. JWT_SECRET is set in backend environment
3. Server URL in Electron app matches actual server

### Network Issues

The app includes automatic server status checking and will show appropriate error messages for connection issues. 