// Load avatar utilities
async function loadAvatarUtils() {
  try {
    const script = document.createElement('script');
    script.src = '/js/utils/avatarUtils.js';
    script.onload = () => {
      console.log('✅ Avatar utilities loaded');
    };
    script.onerror = () => {
      console.error('❌ Failed to load avatar utilities');
    };
    document.head.appendChild(script);
  } catch (error) {
    console.error('❌ Error loading avatar utilities:', error);
  }
}

async function loadComponent(componentName) {
  try {
    console.log(`📦 Loading component: ${componentName}`);
    
    // FOR NAVBAR: Force fresh load by clearing cache (temporary fix for support icons issue)
    if (componentName === 'navbar') {
      console.log('🧹 Clearing navbar cache to ensure fresh content');
      localStorage.removeItem('navbar_html_cache');
      localStorage.removeItem('navbar_html_timestamp');
    }
    
    // Cache navbar HTML to avoid re-fetching every page
    if (componentName === 'navbar') {
      const cacheKey = 'navbar_html_cache';
      const cacheTimestampKey = 'navbar_html_timestamp';
      const cacheExpiry = 30 * 60 * 1000; // 30 minutes
      
      const cachedHtml = localStorage.getItem(cacheKey);
      const cacheTimestamp = localStorage.getItem(cacheTimestampKey);
      const now = Date.now();
      
      // Use cached HTML if available and not expired
      if (cachedHtml && cacheTimestamp && (now - parseInt(cacheTimestamp)) < cacheExpiry) {
        console.log(`✅ Using cached navbar HTML`);
        const container = document.getElementById(`${componentName}-container`);
        if (container) {
          container.innerHTML = cachedHtml;
          await loadNavbarScript();
        }
        return;
      }
    }
    
    const response = await fetch(`/components/${componentName}.html`);
    if (!response.ok) {
      throw new Error(`Failed to load ${componentName}: ${response.status}`);
    }
    const html = await response.text();
    
    // Cache navbar HTML
    if (componentName === 'navbar') {
      localStorage.setItem('navbar_html_cache', html);
      localStorage.setItem('navbar_html_timestamp', Date.now().toString());
    }
    
    const container = document.getElementById(`${componentName}-container`);
    if (container) {
      container.innerHTML = html;
      console.log(`✅ Component loaded: ${componentName}`);
      
      // Initialize navbar functionality after HTML is loaded
      if (componentName === 'navbar') {
        await loadNavbarScript();
      }
    }
  } catch (error) {
    console.error(`❌ Error loading component ${componentName}:`, error);
  }
}

// Load music script
function loadMusicScript() {
  return new Promise((resolve, reject) => {
    // Check if script is already loaded
    if (window.initializeMusic || document.querySelector('script[src="/js/music.js"]')) {
      console.log('✅ Music script already loaded');
      resolve();
      return;
    }
    
    console.log('🎵 Loading music script...');
    const script = document.createElement('script');
    script.src = '/js/music.js';
    script.onload = () => {
      console.log('✅ Music script loaded');
      resolve();
    };
    script.onerror = (error) => {
      console.error('❌ Error loading music script:', error);
      reject(error);
    };
    document.head.appendChild(script);
  });
}

// Load playerDetails script
function loadPlayerDetailsScript() {
  return new Promise((resolve, reject) => {
    // Check if script is already loaded
    if (window.openPlayerDetailsModal || document.querySelector('script[src="/js/playerDetails.js"]')) {
      console.log('✅ PlayerDetails script already loaded');
      resolve();
      return;
    }
    
    console.log('📊 Loading playerDetails script...');
    const script = document.createElement('script');
    script.src = '/js/playerDetails.js';
    script.onload = () => {
      console.log('✅ PlayerDetails script loaded');
      resolve();
    };
    script.onerror = (error) => {
      console.error('❌ Error loading playerDetails script:', error);
      reject(error);
    };
    document.head.appendChild(script);
  });
}

// Load navbar script
function loadNavbarScript() {
  return new Promise((resolve, reject) => {
    // Check if script is already loaded
    if (window.modernNavbar || document.querySelector('script[src="/js/navbar-modern.js"]')) {
      console.log('✅ Navbar script already loaded');
      if (typeof window.initModernNavbar === 'function') {
        window.initModernNavbar();
      }
      resolve();
      return;
    }
    
    console.log('🚀 Loading modern navbar script...');
    const script = document.createElement('script');
    script.src = '/js/navbar-modern.js';
    script.onload = () => {
      console.log('✅ Modern navbar script loaded');
      // Initialize the navbar
      if (typeof window.initModernNavbar === 'function') {
        window.initModernNavbar();
      }
      resolve();
    };
    script.onerror = (error) => {
      console.error('❌ Error loading navbar script:', error);
      reject(error);
    };
    document.head.appendChild(script);
  });
}

// Load game tabs script
function loadGameTabsScript() {
  return new Promise((resolve, reject) => {
    // Check if script is already loaded
    if (window.GameTabs || document.querySelector('script[src="/js/game-tabs.js"]')) {
      console.log('✅ Game tabs script already loaded');
      resolve();
      return;
    }
    
    console.log('🎮 Loading game tabs script...');
    const script = document.createElement('script');
    script.src = '/js/game-tabs.js';
    script.onload = () => {
      console.log('✅ Game tabs script loaded');
      resolve();
    };
    script.onerror = (error) => {
      console.error('❌ Error loading game tabs script:', error);
      reject(error);
    };
    document.head.appendChild(script);
  });
}

// Load chat system
function loadChatSystem() {
  return new Promise((resolve, reject) => {
    // Check if script is already loaded
    if (window.chatManager || document.querySelector('script[src="/js/chat-init.js"]')) {
      console.log('✅ Chat system already loaded');
      if (window.initializeChat) {
        window.initializeChat();
      }
      resolve();
      return;
    }
    
    console.log('💬 Loading chat system...');
    
    // Load CSS first
    if (!document.querySelector('link[href="/css/chat-unified.css"]')) {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = '/css/chat-unified.css';
      document.head.appendChild(link);
      console.log('✅ Chat CSS loaded');
    }
    
    // Load JavaScript module
    const script = document.createElement('script');
    script.type = 'module';
    script.src = '/js/chat-init.js';
    script.onload = () => {
      console.log('✅ Chat system loaded');
      resolve();
    };
    script.onerror = (error) => {
      console.error('❌ Error loading chat system:', error);
      reject(error);
    };
    document.head.appendChild(script);
  });
}

window.addEventListener('DOMContentLoaded', async () => {
  console.log('🚀 DOM Content Loaded - Starting component loading...');
  
  // Load scripts first
  try {
    await loadMusicScript();
  } catch (error) {
    console.error('❌ Failed to load music script:', error);
  }
  
  try {
    await loadPlayerDetailsScript();
  } catch (error) {
    console.error('❌ Failed to load playerDetails script:', error);
  }
  
  try {
    await loadGameTabsScript();
  } catch (error) {
    console.error('❌ Failed to load game tabs script:', error);
  }
  
  // Load components in sequence
  await loadComponent('navbar');
  await loadComponent('footer'); // Optional footer
  
  console.log('📦 All components loaded');
  
  // Load user data automatically after navbar is loaded
  console.log('👤 Auto-loading user data...');
  setTimeout(async () => {
    if (typeof window.loadUser === 'function') {
      try {
        await window.loadUser();
        console.log('✅ User data auto-loaded successfully');
        
        // Initialize chat system after user data is loaded
        if (!window.location.pathname.includes('login')) {
          try {
            await loadChatSystem();
            console.log('✅ Chat system initialized');
          } catch (error) {
            console.error('❌ Failed to initialize chat system:', error);
          }
        }
      } catch (error) {
        console.error('❌ Failed to auto-load user data:', error);
      }
    } else {
      console.warn('⚠️ loadUser function not available');
    }
  }, 500);
});

// Export loadUser function for use by navbar
window.loadUser = loadUser;

async function loadUser() {
  try {
    // CACHE TEMPORARILY DISABLED FOR AVATAR TESTING
    // Will re-enable after avatar system is working properly
    console.log('🔄 Loading fresh user data...');

    // Add cache-busting timestamp to ensure fresh data
    const timestamp = Date.now();
    const apiUrl = window.API_CONFIG ? window.API_CONFIG.getApiUrl('/api/me') : '/api/me';
    const urlWithCacheBust = `${apiUrl}?_t=${timestamp}`;
    
    const res = await fetch(urlWithCacheBust, {
      credentials: 'include',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });

    if (!res.ok) {
      // Don't redirect if on login/setup pages
      const currentPath = window.location.pathname;
      const isAuthPage =
        currentPath.includes('/views/login.html') ||
        currentPath.includes('/views/setup-username.html');

      if (!isAuthPage) {
        window.location.href = '/views/login.html';
      }

      return;
    }

    const user = await res.json();
    console.log('User data from /api/me:', user);
    
    // Cache temporarily disabled for avatar testing
    // sessionStorage.setItem(cacheKey, JSON.stringify(user));
    // sessionStorage.setItem(cacheTimestampKey, now.toString());
    
    // Store user data in localStorage for other parts of the app
    localStorage.setItem('user', JSON.stringify(user));

    // Note: We don't need to fetch player data separately anymore
    // The avatar is now managed entirely by the backend AvatarService
    // and is included in the user object from /api/me
    console.log('👤 Avatar system: Using database avatar from user object');

    // Redirect only on protected pages if username is not defined
    if (!user.isUsernameDefined) {
      const currentPath = window.location.pathname;
      const isAuthPage =
        currentPath.includes('/views/login.html') ||
        currentPath.includes('/views/setup-username.html') ||
        currentPath.includes('/setup-username');

      if (!isAuthPage) {
        // Use the server-side route for better consistency
        window.location.href = '/setup-username';
      }

      return;
    }

    // Update navbar if it exists
    try {
      // Modern navbar update - simplified to use only database avatar
      if (window.modernNavbar && typeof window.modernNavbar.updateUserDisplay === 'function') {
        await window.modernNavbar.updateUserDisplay(user);
      } else {
        // Fallback to direct DOM updates using database avatar only
        const profileUsername = document.getElementById('navbar-username');
        const profileUsernameMobile = document.getElementById('navbar-username-mobile');
        const profileImage = document.getElementById('profile-image');
        const profileImageMobile = document.getElementById('profile-image-mobile');
        const adminLink = document.getElementById('admin-link');

        if (profileUsername) profileUsername.textContent = user.username || 'User';
        if (profileUsernameMobile) profileUsernameMobile.textContent = user.username || 'User';
        
        // Use user's database avatar (managed by AvatarService)
        const avatarUrl = user.avatar || '/assets/img/ranks/emblem.png';
        console.log('👤 Setting navbar to database avatar:', avatarUrl);
        
        if (profileImage) profileImage.src = avatarUrl;
        if (profileImageMobile) profileImageMobile.src = avatarUrl;
        
        // Show admin link if user is admin
        if (user.role === 'admin' && adminLink) {
          adminLink.style.display = 'flex';
        }
      }
    } catch (error) {
      console.error('❌ Error updating navbar display:', error);
    }

    // Store user data globally
    window.currentUser = user;
  } catch (error) {
    console.error('Error loading user:', error);
    // Clear potentially corrupted cache
    sessionStorage.removeItem('user_session_cache');
    sessionStorage.removeItem('user_session_timestamp');
    
    // Avoid redirect loop on login page
    const currentPath = window.location.pathname;
    if (!currentPath.includes('/views/login.html')) {
      window.location.href = '/views/login.html';
    }
  }
}

/**
 * Load and display the monthly donation goal
 */
async function loadDonationGoal() {
  try {
    const response = await fetch(window.API_CONFIG ? window.API_CONFIG.getApiUrl('/api/donations/monthly-goal') : '/api/donations/monthly-goal', { credentials: 'include' });
    if (!response.ok) {
      throw new Error('Failed to fetch donation goal');
    }
    
    const data = await response.json();
    const { current = 0, goal: target = 1000 } = data;

    // Update the display
    const goalBar = document.getElementById('donation-goal-bar');
    const currentAmount = document.getElementById('donation-current-amount');
    const targetAmount = document.getElementById('donation-target-amount');
    const percentageDisplay = document.getElementById('donation-percentage');

    if (goalBar && currentAmount && targetAmount && percentageDisplay) {
      // Calculate percentage with a maximum of 100%
      const percentage = Math.min(Math.round((current / target) * 100), 100);
      
      // Update the elements
      goalBar.style.width = `${percentage}%`;
      goalBar.classList.toggle('goal-reached', percentage >= 100);
      currentAmount.textContent = formatCurrency(current);
      targetAmount.textContent = formatCurrency(target);
      percentageDisplay.textContent = percentage;
      targetAmount.textContent = formatCurrency(target);
      percentageDisplay.textContent = `${percentage}%`;
    }
  } catch (error) {
    console.error('Error loading donation goal:', error);
    // Set default values if there's an error
    const elements = {
      bar: document.getElementById('donation-goal-bar'),
      current: document.getElementById('donation-current-amount'),
      target: document.getElementById('donation-target-amount'),
      percentage: document.getElementById('donation-percentage')
    };

    if (elements.bar) elements.bar.style.width = '0%';
    if (elements.current) elements.current.textContent = formatCurrency(0);
    if (elements.target) elements.target.textContent = formatCurrency(1000);
    if (elements.percentage) elements.percentage.textContent = '0%';
  }
}

/**
 * Format currency amount
 * @param {number} amount - Amount to format
 * @param {string} currency - Currency code (default: USD)
 * @returns {string} Formatted currency string
 */
function formatCurrency(amount, currency = 'USD') {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  }).format(amount);
}

/**
 * Clear all navbar and user caches
 * Useful for logout, data refresh, or cache corruption
 */
function clearNavbarCaches() {
  console.log('🧹 Clearing navbar caches...');
  
  // Clear localStorage caches
  localStorage.removeItem('navbar_html_cache');
  localStorage.removeItem('navbar_html_timestamp');
  localStorage.removeItem('user');
  
  // Clear sessionStorage caches
  sessionStorage.removeItem('user_session_cache');
  sessionStorage.removeItem('user_session_timestamp');
  sessionStorage.removeItem('players_session_cache');
  sessionStorage.removeItem('players_session_timestamp');
  
  console.log('✅ Navbar caches cleared');
}

/**
 * Force refresh user data (bypass cache)
 */
async function refreshUserData() {
  console.log('🔄 Force refreshing user data...');
  
  // Clear user caches
  sessionStorage.removeItem('user_session_cache');
  sessionStorage.removeItem('user_session_timestamp');
  sessionStorage.removeItem('players_session_cache');
  sessionStorage.removeItem('players_session_timestamp');
  
  // Reload user data
  await loadUser();
  
  console.log('✅ User data refreshed');
}

// Removed debugNavbarRankImage - use /api/me/avatar-debug endpoint instead

// Export functions for use in other files
window.loadDonationGoal = loadDonationGoal;
window.clearNavbarCaches = clearNavbarCaches;
window.refreshUserData = refreshUserData;

// Initialize avatar utilities when the page loads
document.addEventListener('DOMContentLoaded', async () => {
  console.log('🚀 Initializing avatar system...');
  await loadAvatarUtils();
  console.log('✅ Avatar system initialized');
});
