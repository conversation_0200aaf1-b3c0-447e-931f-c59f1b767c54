<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>WC Arena - User Profile</title>
  <link rel="stylesheet" href="/css/warcraft-app-modern.css" />
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-brands-400.woff2" as="font" type="font/woff2" crossorigin>
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700&display=swap" rel="stylesheet">
  <style>
    /* Profile Rank Info Section Styling - Fix for overlapping elements */
    .profile-rank-info {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-top: 1rem;
      align-items: center;
    }

    .rank-display,
    .mmr-display,
    .stats-display {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-right: 1.5rem;
      margin-bottom: 0.5rem;
    }

    .rank-label,
    .mmr-label,
    .stats-label {
      font-weight: 600;
      color: rgba(255, 255, 255, 0.8);
      white-space: nowrap;
    }

    .points-display {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.4rem 0.8rem;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      margin-right: 0.75rem;
      margin-bottom: 0.5rem;
      color: #ffd700;
      font-weight: 600;
      border: 1px solid rgba(255, 215, 0, 0.3);
      transition: all 0.3s ease;
    }

    .points-display:hover {
      background: rgba(255, 215, 0, 0.15);
      border-color: rgba(255, 215, 0, 0.5);
      transform: translateY(-1px);
    }

    .points-display.honor-points {
      border-color: rgba(138, 43, 226, 0.5);
      color: #da70d6;
    }

    .points-display.arena-gold {
      border-color: rgba(255, 165, 0, 0.5);
      color: #ffa500;
    }

    .points-display i {
      font-size: 1.1rem;
    }

    /* Responsive design for smaller screens */
    @media (max-width: 768px) {
      .profile-rank-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
      }

      .rank-display,
      .mmr-display,
      .stats-display {
        margin-right: 0;
        width: 100%;
        justify-content: space-between;
      }

      .points-display {
        margin-right: 0;
        justify-content: center;
        min-width: 120px;
      }
    }
  </style>
</head>
<body>
  <div id="navbar-container"></div>

  <main>
    <div class="profile-container">
      <div class="profile-header">
        <div id="profile-avatar" class="profile-avatar">
          <!-- Avatar will be loaded here -->
        </div>
        <div class="profile-info">
          <div class="profile-top-row">
            <h1 id="profile-username">Loading profile...</h1>
            <div class="profile-actions">
              <button id="back-to-chat" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Back to Chat</button>
              <button id="send-message" class="btn btn-primary"><i class="fas fa-envelope"></i> Send Message</button>
            </div>
          </div>
          <p id="profile-email"></p>
          <div id="profile-rank-info" class="profile-rank-info">
            <div class="rank-display">
              <span class="rank-label">Highest Rank:</span>
              <span id="highest-rank-name">Loading...</span>
            </div>
            <div class="mmr-display">
              <span class="mmr-label">MMR:</span>
              <span id="highest-mmr">Loading...</span>
            </div>
            <div class="stats-display">
              <span class="stats-label">W/L:</span>
              <span id="wins-losses">0/0</span>
              <span class="stats-label">Win Rate:</span>
              <span id="win-rate">0%</span>
            </div>
            <div class="points-display honor-points" title="Honor Points: Earned by reporting defeats and good sportsmanship">
              <i class="fas fa-medal"></i>
              <span id="honor-points-small">0</span>
              <span>Honor</span>
            </div>
            <div class="points-display arena-gold" title="Arena Gold: Earned from wins (10) and losses (5), redeemable for prizes">
              <i class="fas fa-coins"></i>
              <span id="arena-points-small">0</span>
              <span>Gold</span>
            </div>
          </div>
        </div>
      </div>

      <div class="profile-sections">
        <!-- Bio and Social Links Section -->
        <section class="profile-section">
          <h2>About</h2>
          <div class="bio-container">
            <div class="bio-content" id="bio-content">
              <p id="bio-text">No bio available.</p>
            </div>
            <div class="social-links" id="social-links">
              <div class="social-link" id="youtube-link">
                <i class="fab fa-youtube"></i>
                <span id="youtube-url">Not set</span>
                <span class="creator-badge d-none" id="youtube-creator-badge">Content Creator</span>
              </div>
              <div class="social-link" id="twitch-link">
                <i class="fab fa-twitch"></i>
                <span id="twitch-url">Not set</span>
                <span class="creator-badge d-none" id="twitch-creator-badge">Content Creator</span>
              </div>
            </div>
            <div class="supporter-status">
              <h4>Supporter Status</h4>
              <div class="supporter-links">
                <a href="https://www.patreon.com/WarCraftArena" target="_blank" class="supporter-link" title="Support on Patreon">
                  <i class="fab fa-patreon"></i>
                </a>
                <a href="https://www.paypal.com/paypalme/wolfandman" target="_blank" class="supporter-link" title="Support via PayPal">
                  <i class="fab fa-paypal"></i>
                </a>
                <a href="https://commerce.coinbase.com/checkout/fce0e325-d5b9-4b8c-a270-5508bdde7eeb" target="_blank" class="supporter-link" title="Support via Coinbase Commerce">
                  <i class="fab fa-bitcoin"></i>
                </a>
              </div>
            </div>
          </div>
        </section>

        <!-- Player Names Section -->
        <section class="profile-section">
          <h2>Player Names</h2>
          <div id="player-names-container">
            <div class="loading">Loading player names...</div>
          </div>
        </section>

        <!-- Tournaments and Forum Section -->
        <section class="profile-section">
          <h2>Activity</h2>
          <div class="activity-tabs">
            <button class="activity-tab active" data-tab="tournaments">Tournaments</button>
            <button class="activity-tab" data-tab="forum">Forum</button>
          </div>

          <div class="activity-content active" id="tournaments-tab">
            <div id="tournaments-container">
              <div class="loading">Loading tournaments...</div>
            </div>
            <div class="tournaments-actions">
              <a href="/views/tournaments.html" class="btn btn-primary">View All Tournaments</a>
            </div>
          </div>

          <div class="activity-content" id="forum-tab">
            <div id="forum-activity-container">
              <div class="loading">Loading forum activity...</div>
            </div>
          </div>
        </section>

        <!-- Map Contributions Section -->
        <div class="profile-section" style="background-color: rgba(0, 0, 0, 0.7); border-radius: 8px; padding: 1.5rem; margin-bottom: 1.5rem;">
          <h2 style="color: #f8f9fa; margin-top: 0; margin-bottom: 1.5rem; font-size: 1.5rem; font-weight: 600; border-bottom: 1px solid rgba(255, 255, 255, 0.1); padding-bottom: 0.75rem;">
            <i class="fas fa-map-marked-alt" style="margin-right: 0.5rem;"></i>Map Contributions
          </h2>
          <div id="user-maps-container">
            <div class="user-maps-grid">
              <!-- Maps will be loaded here -->
            </div>
            <div class="user-maps-pagination">
              <!-- Pagination will be added here -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>

  <div id="footer-container"></div>

  <!-- Load Chart.js before our scripts -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>

  <!-- Load our scripts in the correct order -->
  <script src="/js/utils.js"></script>
  <script src="/js/main.js"></script>
  <script src="/js/playerDetails.js?v=1.0.1"></script>
  <script src="/js/achievements.js"></script>
  <script src="/js/otherprofile.js"></script>
</body>
</html>
