# Dependencies
node_modules/
/frontend/node_modules/
/backend/node_modules/

# External dependencies
pyw3x/

# Environment files
.env
.env.local
.env.*.local

# Build output
/dist
/build
/frontend/build
/frontend/dist
/backend/dist

# Electron build output
/dist/
*.exe
*.blockmap
/win-unpacked/
builder-*.yml
builder-*.yaml

# Downloads folder (contains large executables)
frontend/downloads/
**/downloads/

# IDE and editor files
.idea/
.vscode/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# MongoDB data (if running locally)
/data/
/mongodb-data/

# Uploads and temporary files
uploads/
temp/

# War3Net extraction working directory
backend/war3net_extracted/

# Blizzard Picture files (should be converted to PNG)
*.blp
**/*.blp

# Runtime files
*.pid
*.seed
*.pid.lock

# Backup files
/dbbackups/

# Tools and scripts (optional - remove if you want to track them)
/tools/

# Docker files (if not using Docker)
rebuild-docker.ps1
