const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Forum Topic Schema
 * Represents a topic in the forum
 */
const ForumTopicSchema = new Schema({
  // Topic title
  title: {
    type: String,
    required: true,
    trim: true
  },

  // Category this topic belongs to (optional)
  category: {
    type: Schema.Types.ObjectId,
    ref: 'ForumCategory'
  },

  // User who created this topic
  author: {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    username: {
      type: String,
      required: true
    },
    avatar: String
  },

  // Topic content (first post)
  content: {
    type: String,
    required: true
  },

  // Is this topic pinned to the top?
  isPinned: {
    type: Boolean,
    default: false
  },

  // Is this topic locked (no new replies)?
  isLocked: {
    type: Boolean,
    default: false
  },

  // Number of views
  viewCount: {
    type: Number,
    default: 0
  },

  // Number of replies
  replyCount: {
    type: Number,
    default: 0
  },

  // Last reply information
  lastReply: {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    username: String,
    date: Date
  },

  // Like/dislike tracking
  likes: [{
    type: Schema.Types.ObjectId,
    ref: 'User'
  }],
  dislikes: [{
    type: Schema.Types.ObjectId,
    ref: 'User'
  }],

  // Creation date
  createdAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Create indexes for faster lookups
ForumTopicSchema.index({ category: 1 });
ForumTopicSchema.index({ 'author.userId': 1 });
ForumTopicSchema.index({ createdAt: -1 });
ForumTopicSchema.index({ isPinned: -1, createdAt: -1 });

module.exports = mongoose.model('ForumTopic', ForumTopicSchema);
