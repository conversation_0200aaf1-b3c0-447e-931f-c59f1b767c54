const { app, BrowserWindow, shell, protocol, dialog, ipcMain } = require('electron');
const path = require('path');
const axios = require('axios');
const Store = require('electron-store');
const { AuthManager } = require('./src/auth-manager');
const { WindowManager } = require('./src/window-manager');

class WarcraftArenaApp {
  constructor() {
    this.store = new Store({
      name: 'warcraft-arena-config',
      encryptionKey: 'warcraft-arena-secret-key', // In production, generate this securely
      defaults: {
        user: null,
        serverUrl: 'http://localhost:3000',
        rememberLogin: true,
        windowBounds: {
          width: 1400,
          height: 900
        }
      }
    });

    this.authManager = new AuthManager(this.store);
    this.windowManager = new WindowManager(this.store);
    
    this.initializeApp();
  }

  initializeApp() {
    // Handle app ready
    app.whenReady().then(() => {
      this.registerProtocol();
      this.setupIPC();
      this.windowManager.createMainWindow();
      
      // Load appropriate page based on auth status
      this.loadInitialPage();
      
      app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
          this.windowManager.createMainWindow();
          this.loadInitialPage();
        }
      });
    });

    // Handle app window closed
    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    // Handle custom protocol activation (OAuth callback)
    app.on('open-url', (event, url) => {
      event.preventDefault();
      this.authManager.handleOAuthCallback(url);
    });

    // Handle protocol on Windows
    app.on('second-instance', (event, commandLine, workingDirectory) => {
      // Protocol handler for Windows
      if (process.platform === 'win32') {
        const url = commandLine.find(arg => arg.startsWith('warcraftarena://'));
        if (url) {
          this.authManager.handleOAuthCallback(url);
        }
      }
      
      // Focus the main window
      if (this.windowManager.mainWindow) {
        if (this.windowManager.mainWindow.isMinimized()) {
          this.windowManager.mainWindow.restore();
        }
        this.windowManager.mainWindow.focus();
      }
    });
  }

  registerProtocol() {
    // Register custom protocol for OAuth callbacks
    protocol.registerFileProtocol('warcraftarena', (request, callback) => {
      this.authManager.handleOAuthCallback(request.url);
    });

    // Set as default handler for our custom protocol
    app.setAsDefaultProtocolClient('warcraftarena');
  }

  async loadInitialPage() {
    const savedUser = this.store.get('user');
    if (savedUser && this.store.get('rememberLogin')) {
      // Try to validate stored user with server
      try {
        const isValid = await this.authManager.validateStoredUser(savedUser);
        if (isValid) {
          this.windowManager.loadMainApp();
          return;
        }
      } catch (error) {
        console.log('Stored user validation failed:', error.message);
        this.store.delete('user');
      }
    }
    
    this.windowManager.loadLoginPage();
  }

  setupIPC() {
    // Handle OAuth login requests from renderer
    ipcMain.handle('auth:oauth-login', async (event, provider) => {
      try {
        const user = await this.authManager.authenticateWithOAuth(provider, this.windowManager);
        this.windowManager.loadMainApp();
        return { success: true, user };
      } catch (error) {
        console.error('OAuth login error:', error);
        return { success: false, error: error.message };
      }
    });

    // Handle logout requests
    ipcMain.handle('auth:logout', async (event) => {
      try {
        await this.authManager.logout();
        this.windowManager.loadLoginPage();
        return { success: true };
      } catch (error) {
        console.error('Logout error:', error);
        return { success: false, error: error.message };
      }
    });

    // Get authentication status
    ipcMain.handle('auth:get-status', async (event) => {
      return this.authManager.getAuthStatus();
    });

    // Handle server URL changes
    ipcMain.handle('config:set-server-url', async (event, url) => {
      this.store.set('serverUrl', url);
      this.authManager.updateServerUrl(url);
      return { success: true };
    });

    // Get current configuration
    ipcMain.handle('config:get', async (event) => {
      return {
        serverUrl: this.store.get('serverUrl'),
        rememberLogin: this.store.get('rememberLogin'),
        version: app.getVersion()
      };
    });

    // Handle remember login setting
    ipcMain.handle('config:set-remember-login', async (event, remember) => {
      this.store.set('rememberLogin', remember);
      if (!remember) {
        this.store.delete('user');
      }
      return { success: true };
    });

    // Open external links
    ipcMain.handle('shell:open-external', async (event, url) => {
      shell.openExternal(url);
      return { success: true };
    });

    // Show error dialog
    ipcMain.handle('dialog:show-error', async (event, title, content) => {
      dialog.showErrorBox(title, content);
      return { success: true };
    });

    // Show message box
    ipcMain.handle('dialog:show-message', async (event, options) => {
      const result = await dialog.showMessageBox(this.windowManager.mainWindow, options);
      return result;
    });
  }
}

// Create the application
new WarcraftArenaApp();

// Handle certificate errors (for development)
app.on('certificate-error', (event, webContents, url, error, certificate, callback) => {
  if (url.startsWith('http://localhost:')) {
    event.preventDefault();
    callback(true);
  } else {
    callback(false);
  }
}); 