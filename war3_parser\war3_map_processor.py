#!/usr/bin/env python3
"""
War3 Map Processor
Processes Warcraft III map files (.w3x/.w3m) to extract metadata and generate thumbnails
"""

import os
import sys
import json
import struct
from pathlib import Path
from PIL import Image, ImageDraw
import io
import hashlib
from typing import Dict, List, Optional, Tuple
import traceback
import mpyq

class War3MapProcessor:
    def __init__(self, maps_dir: str, thumbnails_dir: str):
        self.maps_dir = Path(maps_dir)
        self.thumbnails_dir = Path(thumbnails_dir)
        self.thumbnails_dir.mkdir(exist_ok=True)
        
        # Ensure directories exist
        if not self.maps_dir.exists():
            raise FileNotFoundError(f"Maps directory not found: {maps_dir}")
    
    def process_all_maps(self) -> List[Dict]:
        """Process all .w3x and .w3m files in the maps directory"""
        results = []
        map_files = list(self.maps_dir.glob("*.w3x")) + list(self.maps_dir.glob("*.w3m"))
        
        print(f"🚀 Found {len(map_files)} War3 map files to process...")
        
        for i, map_file in enumerate(map_files, 1):
            print(f"\n📋 Processing {i}/{len(map_files)}: {map_file.name}")
            
            try:
                map_data = self.process_single_map(map_file)
                if map_data:
                    results.append(map_data)
                    print(f"✅ Successfully processed: {map_file.name}")
                else:
                    print(f"⚠️  Skipped (no data): {map_file.name}")
            except Exception as e:
                print(f"❌ Error processing {map_file.name}: {str(e)}")
                # Continue processing other maps even if one fails
                continue
        
        print(f"\n🎉 Processing complete! Successfully processed {len(results)} maps.")
        return results
    
    def process_single_map(self, map_file: Path) -> Optional[Dict]:
        """Process a single map file and extract all available data"""
        try:
            # War3 maps are MPQ archives
            with mpyq.MPQArchive(str(map_file)) as mpq:
                map_data = {
                    'filename': map_file.name,
                    'filepath': str(map_file),
                    'filesize': map_file.stat().st_size,
                    'hash': self._calculate_file_hash(map_file),
                    'name': map_file.stem,  # Default to filename without extension
                    'description': '',
                    'author': 'Unknown',
                    'players': 0,
                    'version': '1.0',
                    'category': 'Custom',
                    'thumbnail': None,
                    'extracted_files': []
                }
                
                # List all files in the archive
                file_list = mpq.files
                map_data['extracted_files'] = file_list
                print(f"   📁 Found {len(file_list)} files in archive")
                
                # Extract map info (war3map.w3i)
                if 'war3map.w3i' in file_list:
                    map_info = self._extract_map_info(mpq, 'war3map.w3i')
                    map_data.update(map_info)
                    print(f"   📄 Extracted map info: {map_data.get('name', 'Unknown')}")
                
                # Extract and process minimap (war3mapMap.blp or war3mapMap.tga)
                minimap_files = ['war3mapMap.blp', 'war3mapMap.tga', 'war3mapPreview.tga']
                for minimap_file in minimap_files:
                    if minimap_file in file_list:
                        thumbnail_path = self._extract_and_convert_minimap(
                            mpq, minimap_file, map_file.stem
                        )
                        if thumbnail_path:
                            map_data['thumbnail'] = thumbnail_path
                            print(f"   🖼️  Generated thumbnail: {thumbnail_path}")
                            break
                
                # If no minimap found, skip thumbnail generation
                if not map_data['thumbnail']:
                    print(f"   ⚠️  No minimap found, skipping thumbnail generation")
                
                return map_data
                
        except Exception as e:
            print(f"❌ Error processing {map_file.name}: {str(e)}")
            traceback.print_exc()
            return None
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """Calculate SHA-256 hash of the file"""
        hash_sha256 = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    
    def _extract_map_info(self, mpq: mpyq.MPQArchive, info_file: str) -> Dict:
        """Extract map information from war3map.w3i file"""
        try:
            data = mpq.read_file(info_file)
            
            # Parse the w3i file format (simplified)
            # This is a basic parser - the full format is quite complex
            info = {}
            
            if len(data) < 50:
                return info
            
            try:
                # Skip header and read basic info
                offset = 8  # Skip file format version
                
                # Map name (null-terminated string)
                name_start = offset
                while offset < len(data) and data[offset] != 0:
                    offset += 1
                
                if offset > name_start:
                    name = data[name_start:offset].decode('utf-8', errors='ignore').strip()
                    if name:
                        info['name'] = name
                
                # Skip to author field (approximate offset)
                offset += 1
                author_start = offset
                while offset < len(data) and data[offset] != 0:
                    offset += 1
                
                if offset > author_start:
                    author = data[author_start:offset].decode('utf-8', errors='ignore').strip()
                    if author:
                        info['author'] = author
                
                # Try to extract player count (this is approximate)
                if len(data) > 100:
                    # Player count is usually stored as a 4-byte integer
                    for i in range(50, min(len(data) - 4, 200), 4):
                        try:
                            players = struct.unpack('<I', data[i:i+4])[0]
                            if 1 <= players <= 12:  # Reasonable player count
                                info['players'] = players
                                break
                        except:
                            continue
                            
            except Exception as e:
                print(f"⚠️  Error parsing map info: {str(e)}")
            
            return info
            
        except Exception as e:
            print(f"⚠️  Could not extract map info: {str(e)}")
            return {}
    
    def _extract_and_convert_minimap(self, mpq: mpyq.MPQArchive, minimap_file: str, map_name: str) -> Optional[str]:
        """Extract and convert minimap to PNG thumbnail"""
        try:
            minimap_data = mpq.read_file(minimap_file)
            
            # Try to process as image
            try:
                # For BLP files, we'll try to process them as raw image data
                if minimap_file.endswith('.blp'):
                    thumbnail_path = self._process_blp_minimap(minimap_data, map_name)
                else:
                    # For TGA files, PIL should handle them
                    image = Image.open(io.BytesIO(minimap_data))
                    thumbnail_path = self._save_thumbnail(image, map_name)
                
                return thumbnail_path
                
            except Exception as e:
                print(f"⚠️  Could not process minimap {minimap_file}: {str(e)}")
                return None
                
        except Exception as e:
            print(f"⚠️  Could not extract minimap {minimap_file}: {str(e)}")
            return None
    
    def _process_blp_minimap(self, blp_data: bytes, map_name: str) -> Optional[str]:
        """Process BLP (Blizzard Picture) format minimap"""
        try:
            # BLP format is complex and not supported
            print(f"⚠️  BLP format detected for {map_name}, skipping thumbnail")
            return None
            
        except Exception as e:
            print(f"⚠️  Error processing BLP: {str(e)}")
            return None
    
    def _save_thumbnail(self, image: Image.Image, map_name: str) -> str:
        """Save processed image as PNG thumbnail"""
        # Resize to standard thumbnail size
        thumbnail_size = (256, 256)
        image.thumbnail(thumbnail_size, Image.Resampling.LANCZOS)
        
        # Save as PNG
        thumbnail_filename = f"{map_name}.png"
        thumbnail_path = self.thumbnails_dir / thumbnail_filename
        
        # Convert to RGB if necessary
        if image.mode in ('RGBA', 'LA', 'P'):
            # Create white background
            background = Image.new('RGB', image.size, (255, 255, 255))
            if image.mode == 'P':
                image = image.convert('RGBA')
            background.paste(image, mask=image.split()[-1] if image.mode in ('RGBA', 'LA') else None)
            image = background
        
        image.save(thumbnail_path, 'PNG', optimize=True)
        return thumbnail_filename
    


def main():
    """Main function to process all War3 maps"""
    # Set up paths
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    maps_dir = project_root / "uploads" / "war3"
    thumbnails_dir = project_root / "uploads" / "war3images"
    output_file = project_root / "uploads" / "war3_processed_maps.json"
    
    print("🚀 War3 Map Processor Starting...")
    print(f"📁 Maps directory: {maps_dir}")
    print(f"🖼️  Thumbnails directory: {thumbnails_dir}")
    print(f"📄 Output file: {output_file}")
    
    # Initialize processor
    processor = War3MapProcessor(str(maps_dir), str(thumbnails_dir))
    
    # Process all maps
    results = processor.process_all_maps()
    
    # Save results to JSON
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n✅ Results saved to: {output_file}")
    print(f"📊 Processed {len(results)} maps successfully!")
    
    # Print summary
    if results:
        print("\n📋 Processing Summary:")
        print(f"   - Total maps: {len(results)}")
        print(f"   - With thumbnails: {sum(1 for r in results if r.get('thumbnail'))}")
        print(f"   - With names: {sum(1 for r in results if r.get('name') != r.get('filename', '').replace('.w3x', '').replace('.w3m', ''))}")
        print(f"   - With authors: {sum(1 for r in results if r.get('author') != 'Unknown')}")

if __name__ == "__main__":
    main() 