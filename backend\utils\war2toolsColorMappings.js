/**
 * WAR2TOOLS COMPLETE COLOR MAPPINGS - AUTO-GENERATED
 * 
 * Extracted from war2tools/libpud/tiles.c using extract-war2tools-tiles.js
 * This file contains ALL 1556 tile mappings from the official war2tools.
 * 
 * Generated on: 2025-06-03T14:50:09.041Z
 */

// FOREST TILESET - 387 tiles
const FOREST_COLORS = {
  "0x0010": [
    4,
    56,
    117
  ],
  "0x0011": [
    4,
    56,
    117
  ],
  "0x0012": [
    4,
    56,
    117
  ],
  "0x0013": [
    4,
    56,
    117
  ],
  "0x0020": [
    4,
    52,
    113
  ],
  "0x0021": [
    4,
    52,
    113
  ],
  "0x0022": [
    4,
    52,
    113
  ],
  "0x0023": [
    4,
    52,
    113
  ],
  "0x0030": [
    109,
    65,
    0
  ],
  "0x0031": [
    117,
    69,
    4
  ],
  "0x0032": [
    109,
    65,
    0
  ],
  "0x0034": [
    81,
    48,
    0
  ],
  "0x0035": [
    109,
    65,
    0
  ],
  "0x0036": [
    109,
    65,
    0
  ],
  "0x0037": [
    109,
    65,
    0
  ],
  "0x0038": [
    109,
    65,
    0
  ],
  "0x0039": [
    97,
    56,
    0
  ],
  "0x003A": [
    109,
    65,
    0
  ],
  "0x003B": [
    109,
    65,
    0
  ],
  "0x0040": [
    97,
    56,
    0
  ],
  "0x0041": [
    97,
    56,
    0
  ],
  "0x0042": [
    109,
    65,
    0
  ],
  "0x0044": [
    97,
    56,
    0
  ],
  "0x0045": [
    97,
    56,
    0
  ],
  "0x0046": [
    97,
    56,
    0
  ],
  "0x0047": [
    97,
    56,
    0
  ],
  "0x0048": [
    97,
    56,
    0
  ],
  "0x0049": [
    81,
    48,
    0
  ],
  "0x004A": [
    97,
    56,
    0
  ],
  "0x004B": [
    97,
    56,
    0
  ],
  "0x0050": [
    40,
    85,
    12
  ],
  "0x0051": [
    40,
    85,
    12
  ],
  "0x0052": [
    40,
    85,
    12
  ],
  "0x0054": [
    36,
    73,
    4
  ],
  "0x0055": [
    40,
    85,
    12
  ],
  "0x0056": [
    36,
    73,
    4
  ],
  "0x0057": [
    44,
    93,
    16
  ],
  "0x0058": [
    36,
    73,
    4
  ],
  "0x0059": [
    65,
    44,
    0
  ],
  "0x005A": [
    36,
    73,
    4
  ],
  "0x005B": [
    40,
    85,
    12
  ],
  "0x005C": [
    36,
    73,
    4
  ],
  "0x005D": [
    40,
    85,
    12
  ],
  "0x005E": [
    36,
    73,
    4
  ],
  "0x005F": [
    40,
    85,
    12
  ],
  "0x0060": [
    36,
    73,
    4
  ],
  "0x0061": [
    36,
    73,
    4
  ],
  "0x0062": [
    36,
    73,
    4
  ],
  "0x0064": [
    36,
    73,
    4
  ],
  "0x0065": [
    73,
    73,
    73
  ],
  "0x0066": [
    36,
    73,
    4
  ],
  "0x0067": [
    36,
    73,
    4
  ],
  "0x0068": [
    36,
    73,
    4
  ],
  "0x0069": [
    36,
    73,
    4
  ],
  "0x006A": [
    36,
    73,
    4
  ],
  "0x006B": [
    73,
    73,
    73
  ],
  "0x006C": [
    36,
    73,
    4
  ],
  "0x006D": [
    73,
    73,
    73
  ],
  "0x006E": [
    36,
    73,
    4
  ],
  "0x006F": [
    73,
    73,
    73
  ],
  "0x0070": [
    0,
    77,
    0
  ],
  "0x0071": [
    0,
    77,
    0
  ],
  "0x0072": [
    0,
    77,
    0
  ],
  "0x0080": [
    24,
    24,
    24
  ],
  "0x0081": [
    73,
    73,
    73
  ],
  "0x0082": [
    60,
    60,
    60
  ],
  "0x0083": [
    60,
    60,
    60
  ],
  "0x0090": [
    81,
    81,
    81
  ],
  "0x0092": [
    73,
    73,
    73
  ],
  "0x0094": [
    117,
    117,
    117
  ],
  "0x00A0": [
    105,
    105,
    105
  ],
  "0x00A2": [
    40,
    85,
    12
  ],
  "0x00A4": [
    117,
    117,
    117
  ],
  "0x00B0": [
    150,
    150,
    150
  ],
  "0x00B2": [
    150,
    150,
    150
  ],
  "0x00B4": [
    138,
    138,
    138
  ],
  "0x00C0": [
    60,
    60,
    60
  ],
  "0x00C2": [
    138,
    97,
    24
  ],
  "0x00C4": [
    138,
    138,
    138
  ],
  "0x0100": [
    4,
    52,
    113
  ],
  "0x0101": [
    4,
    52,
    113
  ],
  "0x0110": [
    4,
    56,
    117
  ],
  "0x0111": [
    4,
    52,
    113
  ],
  "0x0120": [
    4,
    52,
    113
  ],
  "0x0121": [
    4,
    52,
    113
  ],
  "0x0122": [
    4,
    52,
    113
  ],
  "0x0130": [
    4,
    56,
    117
  ],
  "0x0131": [
    4,
    56,
    117
  ],
  "0x0140": [
    4,
    52,
    113
  ],
  "0x0141": [
    4,
    52,
    113
  ],
  "0x0142": [
    4,
    52,
    113
  ],
  "0x0150": [
    4,
    56,
    117
  ],
  "0x0151": [
    4,
    56,
    117
  ],
  "0x0160": [
    4,
    52,
    113
  ],
  "0x0161": [
    4,
    52,
    113
  ],
  "0x0170": [
    4,
    56,
    117
  ],
  "0x0171": [
    4,
    56,
    117
  ],
  "0x0180": [
    4,
    52,
    113
  ],
  "0x0181": [
    4,
    52,
    113
  ],
  "0x0190": [
    4,
    56,
    117
  ],
  "0x0191": [
    4,
    52,
    113
  ],
  "0x0192": [
    4,
    56,
    117
  ],
  "0x01A0": [
    4,
    52,
    113
  ],
  "0x01A1": [
    4,
    52,
    113
  ],
  "0x01B0": [
    4,
    56,
    117
  ],
  "0x01B1": [
    4,
    56,
    117
  ],
  "0x01B2": [
    4,
    56,
    117
  ],
  "0x01C0": [
    4,
    52,
    113
  ],
  "0x01C1": [
    4,
    52,
    113
  ],
  "0x01D0": [
    4,
    56,
    117
  ],
  "0x01D1": [
    4,
    56,
    117
  ],
  "0x0200": [
    97,
    56,
    0
  ],
  "0x0201": [
    0,
    32,
    85
  ],
  "0x0210": [
    117,
    69,
    4
  ],
  "0x0211": [
    109,
    65,
    0
  ],
  "0x0220": [
    4,
    52,
    113
  ],
  "0x0221": [
    0,
    48,
    105
  ],
  "0x0222": [
    0,
    40,
    93
  ],
  "0x0230": [
    117,
    69,
    4
  ],
  "0x0231": [
    109,
    65,
    0
  ],
  "0x0240": [
    0,
    32,
    85
  ],
  "0x0241": [
    0,
    48,
    105
  ],
  "0x0242": [
    0,
    32,
    85
  ],
  "0x0250": [
    97,
    56,
    0
  ],
  "0x0251": [
    97,
    56,
    0
  ],
  "0x0260": [
    4,
    52,
    113
  ],
  "0x0261": [
    4,
    56,
    117
  ],
  "0x0270": [
    109,
    65,
    0
  ],
  "0x0271": [
    109,
    65,
    0
  ],
  "0x0280": [
    0,
    48,
    105
  ],
  "0x0281": [
    0,
    48,
    105
  ],
  "0x0290": [
    117,
    69,
    4
  ],
  "0x0291": [
    117,
    69,
    4
  ],
  "0x0292": [
    117,
    69,
    4
  ],
  "0x02A0": [
    89,
    52,
    16
  ],
  "0x02A1": [
    97,
    56,
    0
  ],
  "0x02B0": [
    109,
    65,
    0
  ],
  "0x02B1": [
    109,
    65,
    0
  ],
  "0x02B2": [
    117,
    69,
    4
  ],
  "0x02C0": [
    0,
    48,
    105
  ],
  "0x02C1": [
    0,
    48,
    105
  ],
  "0x02D0": [
    97,
    56,
    0
  ],
  "0x02D1": [
    109,
    65,
    0
  ],
  "0x0300": [
    97,
    56,
    0
  ],
  "0x0301": [
    97,
    56,
    0
  ],
  "0x0310": [
    117,
    69,
    4
  ],
  "0x0311": [
    117,
    69,
    4
  ],
  "0x0320": [
    109,
    65,
    0
  ],
  "0x0321": [
    97,
    56,
    0
  ],
  "0x0322": [
    97,
    56,
    0
  ],
  "0x0330": [
    117,
    69,
    4
  ],
  "0x0331": [
    117,
    69,
    4
  ],
  "0x0340": [
    97,
    56,
    0
  ],
  "0x0341": [
    97,
    56,
    0
  ],
  "0x0342": [
    97,
    56,
    0
  ],
  "0x0350": [
    97,
    56,
    0
  ],
  "0x0351": [
    117,
    69,
    4
  ],
  "0x0360": [
    97,
    56,
    0
  ],
  "0x0361": [
    97,
    56,
    0
  ],
  "0x0370": [
    109,
    65,
    0
  ],
  "0x0371": [
    109,
    65,
    0
  ],
  "0x0380": [
    97,
    56,
    0
  ],
  "0x0381": [
    97,
    56,
    0
  ],
  "0x0390": [
    117,
    69,
    4
  ],
  "0x0391": [
    97,
    56,
    0
  ],
  "0x0392": [
    117,
    69,
    4
  ],
  "0x03A0": [
    109,
    65,
    0
  ],
  "0x03A1": [
    109,
    65,
    0
  ],
  "0x03B0": [
    117,
    69,
    4
  ],
  "0x03B1": [
    97,
    56,
    0
  ],
  "0x03B2": [
    109,
    65,
    0
  ],
  "0x03C0": [
    109,
    65,
    0
  ],
  "0x03C1": [
    109,
    65,
    0
  ],
  "0x03D0": [
    97,
    56,
    0
  ],
  "0x03D1": [
    97,
    56,
    0
  ],
  "0x0400": [
    81,
    81,
    81
  ],
  "0x0401": [
    125,
    125,
    125
  ],
  "0x0410": [
    60,
    60,
    60
  ],
  "0x0411": [
    117,
    117,
    117
  ],
  "0x0420": [
    125,
    125,
    125
  ],
  "0x0421": [
    105,
    105,
    105
  ],
  "0x0430": [
    97,
    56,
    0
  ],
  "0x0431": [
    105,
    105,
    105
  ],
  "0x0440": [
    48,
    48,
    48
  ],
  "0x0441": [
    60,
    60,
    60
  ],
  "0x0450": [
    60,
    60,
    60
  ],
  "0x0451": [
    60,
    60,
    60
  ],
  "0x0460": [
    117,
    117,
    117
  ],
  "0x0470": [
    109,
    65,
    0
  ],
  "0x0471": [
    109,
    65,
    0
  ],
  "0x0480": [
    105,
    105,
    105
  ],
  "0x0481": [
    81,
    81,
    81
  ],
  "0x0490": [
    73,
    73,
    73
  ],
  "0x0491": [
    48,
    48,
    48
  ],
  "0x04A0": [
    60,
    60,
    60
  ],
  "0x04B0": [
    109,
    65,
    0
  ],
  "0x04B1": [
    109,
    65,
    0
  ],
  "0x04C0": [
    60,
    60,
    60
  ],
  "0x04D0": [
    60,
    60,
    60
  ],
  "0x0500": [
    117,
    69,
    4
  ],
  "0x0501": [
    44,
    93,
    16
  ],
  "0x0510": [
    40,
    85,
    12
  ],
  "0x0511": [
    36,
    73,
    4
  ],
  "0x0520": [
    109,
    65,
    0
  ],
  "0x0521": [
    117,
    69,
    4
  ],
  "0x0522": [
    109,
    65,
    0
  ],
  "0x0530": [
    77,
    109,
    28
  ],
  "0x0531": [
    44,
    93,
    16
  ],
  "0x0540": [
    117,
    69,
    4
  ],
  "0x0541": [
    97,
    56,
    0
  ],
  "0x0542": [
    60,
    101,
    20
  ],
  "0x0550": [
    40,
    85,
    12
  ],
  "0x0551": [
    44,
    93,
    16
  ],
  "0x0560": [
    109,
    65,
    0
  ],
  "0x0561": [
    109,
    65,
    0
  ],
  "0x0570": [
    44,
    93,
    16
  ],
  "0x0571": [
    40,
    85,
    12
  ],
  "0x0580": [
    109,
    65,
    0
  ],
  "0x0581": [
    117,
    69,
    4
  ],
  "0x0590": [
    40,
    85,
    12
  ],
  "0x0591": [
    44,
    93,
    16
  ],
  "0x0592": [
    44,
    93,
    16
  ],
  "0x05A0": [
    109,
    65,
    0
  ],
  "0x05A1": [
    109,
    65,
    0
  ],
  "0x05B0": [
    81,
    48,
    0
  ],
  "0x05B1": [
    44,
    93,
    16
  ],
  "0x05B2": [
    40,
    85,
    12
  ],
  "0x05C0": [
    117,
    69,
    4
  ],
  "0x05C1": [
    109,
    65,
    0
  ],
  "0x05D0": [
    81,
    48,
    0
  ],
  "0x05D1": [
    40,
    85,
    12
  ],
  "0x0600": [
    40,
    85,
    12
  ],
  "0x0601": [
    40,
    85,
    12
  ],
  "0x0610": [
    44,
    93,
    16
  ],
  "0x0611": [
    40,
    85,
    12
  ],
  "0x0620": [
    40,
    85,
    12
  ],
  "0x0621": [
    40,
    85,
    12
  ],
  "0x0622": [
    40,
    85,
    12
  ],
  "0x0630": [
    44,
    93,
    16
  ],
  "0x0631": [
    44,
    93,
    16
  ],
  "0x0640": [
    40,
    85,
    12
  ],
  "0x0641": [
    40,
    85,
    12
  ],
  "0x0642": [
    40,
    85,
    12
  ],
  "0x0650": [
    44,
    93,
    16
  ],
  "0x0651": [
    44,
    93,
    16
  ],
  "0x0660": [
    40,
    85,
    12
  ],
  "0x0661": [
    40,
    85,
    12
  ],
  "0x0670": [
    44,
    93,
    16
  ],
  "0x0671": [
    44,
    93,
    16
  ],
  "0x0680": [
    40,
    85,
    12
  ],
  "0x0681": [
    40,
    85,
    12
  ],
  "0x0690": [
    44,
    93,
    16
  ],
  "0x0691": [
    44,
    93,
    16
  ],
  "0x0692": [
    44,
    93,
    16
  ],
  "0x06A0": [
    40,
    85,
    12
  ],
  "0x06A1": [
    40,
    85,
    12
  ],
  "0x06B0": [
    40,
    85,
    12
  ],
  "0x06B1": [
    40,
    85,
    12
  ],
  "0x06B2": [
    44,
    93,
    16
  ],
  "0x06C0": [
    40,
    85,
    12
  ],
  "0x06C1": [
    40,
    85,
    12
  ],
  "0x06D0": [
    44,
    93,
    16
  ],
  "0x06D1": [
    44,
    93,
    16
  ],
  "0x0700": [
    0,
    44,
    0
  ],
  "0x0701": [
    0,
    44,
    0
  ],
  "0x0710": [
    20,
    52,
    0
  ],
  "0x0711": [
    0,
    44,
    0
  ],
  "0x0720": [
    0,
    44,
    0
  ],
  "0x0721": [
    0,
    77,
    0
  ],
  "0x0730": [
    40,
    85,
    12
  ],
  "0x0731": [
    40,
    85,
    12
  ],
  "0x0740": [
    0,
    77,
    0
  ],
  "0x0741": [
    0,
    77,
    0
  ],
  "0x0750": [
    0,
    44,
    0
  ],
  "0x0751": [
    0,
    44,
    0
  ],
  "0x0760": [
    0,
    77,
    0
  ],
  "0x0761": [
    0,
    77,
    0
  ],
  "0x0770": [
    36,
    73,
    4
  ],
  "0x0771": [
    40,
    85,
    12
  ],
  "0x0780": [
    0,
    44,
    0
  ],
  "0x0781": [
    0,
    44,
    0
  ],
  "0x0790": [
    20,
    52,
    0
  ],
  "0x0791": [
    20,
    52,
    0
  ],
  "0x07A0": [
    0,
    77,
    0
  ],
  "0x07A1": [
    0,
    77,
    0
  ],
  "0x07B0": [
    40,
    85,
    12
  ],
  "0x07B1": [
    40,
    85,
    12
  ],
  "0x07C0": [
    0,
    77,
    0
  ],
  "0x07C1": [
    0,
    77,
    0
  ],
  "0x07D0": [
    65,
    44,
    0
  ],
  "0x07D1": [
    65,
    44,
    0
  ],
  "0x0800": [
    81,
    81,
    81
  ],
  "0x0802": [
    117,
    117,
    117
  ],
  "0x0804": [
    117,
    117,
    117
  ],
  "0x0810": [
    81,
    81,
    81
  ],
  "0x0812": [
    117,
    117,
    117
  ],
  "0x0814": [
    117,
    117,
    117
  ],
  "0x0820": [
    81,
    81,
    81
  ],
  "0x0822": [
    117,
    117,
    117
  ],
  "0x0824": [
    117,
    117,
    117
  ],
  "0x0830": [
    138,
    138,
    138
  ],
  "0x0832": [
    138,
    138,
    138
  ],
  "0x0834": [
    117,
    117,
    117
  ],
  "0x0840": [
    138,
    138,
    138
  ],
  "0x0841": [
    138,
    138,
    138
  ],
  "0x0843": [
    36,
    36,
    36
  ],
  "0x0844": [
    48,
    48,
    48
  ],
  "0x0846": [
    117,
    117,
    117
  ],
  "0x0847": [
    117,
    117,
    117
  ],
  "0x0850": [
    138,
    138,
    138
  ],
  "0x0852": [
    138,
    138,
    138
  ],
  "0x0854": [
    117,
    117,
    117
  ],
  "0x0860": [
    138,
    138,
    138
  ],
  "0x0862": [
    60,
    60,
    60
  ],
  "0x0864": [
    117,
    117,
    117
  ],
  "0x0870": [
    138,
    138,
    138
  ],
  "0x0872": [
    138,
    138,
    138
  ],
  "0x0874": [
    117,
    117,
    117
  ],
  "0x0880": [
    138,
    138,
    138
  ],
  "0x0882": [
    138,
    138,
    138
  ],
  "0x0884": [
    117,
    117,
    117
  ],
  "0x0890": [
    138,
    138,
    138
  ],
  "0x0891": [
    138,
    138,
    138
  ],
  "0x0893": [
    117,
    117,
    117
  ],
  "0x0894": [
    138,
    138,
    138
  ],
  "0x0896": [
    93,
    93,
    93
  ],
  "0x0897": [
    117,
    117,
    117
  ],
  "0x08A0": [
    138,
    138,
    138
  ],
  "0x08A2": [
    81,
    81,
    81
  ],
  "0x08A4": [
    93,
    93,
    93
  ],
  "0x08B0": [
    150,
    150,
    150
  ],
  "0x08B2": [
    150,
    150,
    150
  ],
  "0x08B4": [
    117,
    117,
    117
  ],
  "0x08C0": [
    150,
    150,
    150
  ],
  "0x08C2": [
    150,
    150,
    150
  ],
  "0x08C4": [
    117,
    117,
    117
  ],
  "0x08D0": [
    150,
    150,
    150
  ],
  "0x08D2": [
    150,
    150,
    150
  ],
  "0x08D4": [
    117,
    117,
    117
  ],
  "0x0900": [
    105,
    105,
    105
  ],
  "0x0902": [
    40,
    85,
    12
  ],
  "0x0904": [
    117,
    117,
    117
  ],
  "0x0910": [
    105,
    105,
    105
  ],
  "0x0912": [
    40,
    85,
    12
  ],
  "0x0914": [
    117,
    117,
    117
  ],
  "0x0920": [
    105,
    105,
    105
  ],
  "0x0922": [
    40,
    85,
    12
  ],
  "0x0924": [
    117,
    117,
    117
  ],
  "0x0930": [
    105,
    105,
    105
  ],
  "0x0932": [
    162,
    134,
    77
  ],
  "0x0934": [
    117,
    117,
    117
  ],
  "0x0940": [
    105,
    105,
    105
  ],
  "0x0941": [
    105,
    105,
    105
  ],
  "0x0943": [
    162,
    134,
    77
  ],
  "0x0944": [
    73,
    73,
    73
  ],
  "0x0946": [
    117,
    117,
    117
  ],
  "0x0947": [
    117,
    117,
    117
  ],
  "0x0950": [
    105,
    105,
    105
  ],
  "0x0952": [
    162,
    134,
    77
  ],
  "0x0954": [
    117,
    117,
    117
  ],
  "0x0960": [
    105,
    105,
    105
  ],
  "0x0962": [
    73,
    73,
    73
  ],
  "0x0964": [
    117,
    117,
    117
  ],
  "0x0970": [
    93,
    93,
    93
  ],
  "0x0972": [
    60,
    60,
    60
  ],
  "0x0974": [
    117,
    117,
    117
  ],
  "0x0980": [
    93,
    93,
    93
  ],
  "0x0982": [
    60,
    60,
    60
  ],
  "0x0984": [
    117,
    117,
    117
  ],
  "0x0990": [
    93,
    93,
    93
  ],
  "0x0991": [
    93,
    93,
    93
  ],
  "0x0993": [
    60,
    60,
    60
  ],
  "0x0994": [
    65,
    44,
    0
  ],
  "0x0996": [
    93,
    93,
    93
  ],
  "0x0997": [
    117,
    117,
    117
  ],
  "0x09A0": [
    93,
    93,
    93
  ],
  "0x09A2": [
    60,
    60,
    60
  ],
  "0x09A4": [
    93,
    93,
    93
  ],
  "0x09B0": [
    60,
    60,
    60
  ],
  "0x09B2": [
    138,
    97,
    24
  ],
  "0x09B4": [
    117,
    117,
    117
  ],
  "0x09C0": [
    60,
    60,
    60
  ],
  "0x09C2": [
    138,
    97,
    24
  ],
  "0x09C4": [
    117,
    117,
    117
  ],
  "0x09D0": [
    60,
    60,
    60
  ],
  "0x09D2": [
    138,
    97,
    24
  ],
  "0x09D4": [
    117,
    117,
    117
  ]
};

// WINTER TILESET - 393 tiles  
const WINTER_COLORS = {
  "0x0010": [
    4,
    56,
    117
  ],
  "0x0011": [
    4,
    56,
    117
  ],
  "0x0012": [
    4,
    56,
    117
  ],
  "0x0013": [
    4,
    56,
    117
  ],
  "0x0015": [
    97,
    154,
    203
  ],
  "0x0016": [
    4,
    56,
    117
  ],
  "0x0017": [
    4,
    56,
    117
  ],
  "0x0020": [
    4,
    52,
    113
  ],
  "0x0021": [
    4,
    52,
    113
  ],
  "0x0022": [
    4,
    52,
    113
  ],
  "0x0023": [
    4,
    52,
    113
  ],
  "0x0025": [
    97,
    154,
    203
  ],
  "0x0026": [
    4,
    52,
    113
  ],
  "0x0027": [
    4,
    52,
    113
  ],
  "0x0030": [
    24,
    85,
    138
  ],
  "0x0031": [
    24,
    85,
    138
  ],
  "0x0032": [
    24,
    85,
    138
  ],
  "0x0034": [
    24,
    85,
    138
  ],
  "0x0035": [
    105,
    109,
    134
  ],
  "0x0036": [
    24,
    85,
    138
  ],
  "0x0037": [
    24,
    85,
    138
  ],
  "0x0038": [
    24,
    85,
    138
  ],
  "0x0039": [
    24,
    85,
    138
  ],
  "0x003A": [
    24,
    85,
    138
  ],
  "0x003B": [
    24,
    85,
    138
  ],
  "0x0040": [
    20,
    77,
    142
  ],
  "0x0041": [
    20,
    77,
    142
  ],
  "0x0042": [
    20,
    77,
    142
  ],
  "0x0044": [
    20,
    77,
    142
  ],
  "0x0045": [
    105,
    109,
    134
  ],
  "0x0046": [
    20,
    77,
    142
  ],
  "0x0047": [
    20,
    77,
    142
  ],
  "0x0048": [
    20,
    77,
    142
  ],
  "0x0049": [
    20,
    77,
    142
  ],
  "0x004A": [
    20,
    77,
    142
  ],
  "0x004B": [
    20,
    77,
    142
  ],
  "0x0050": [
    142,
    142,
    158
  ],
  "0x0051": [
    150,
    150,
    162
  ],
  "0x0052": [
    142,
    142,
    158
  ],
  "0x0054": [
    142,
    142,
    158
  ],
  "0x0055": [
    142,
    142,
    158
  ],
  "0x0056": [
    142,
    142,
    158
  ],
  "0x0057": [
    150,
    150,
    162
  ],
  "0x0058": [
    150,
    150,
    162
  ],
  "0x0059": [
    142,
    142,
    158
  ],
  "0x005A": [
    142,
    142,
    158
  ],
  "0x005B": [
    142,
    142,
    158
  ],
  "0x005C": [
    142,
    142,
    158
  ],
  "0x005D": [
    142,
    142,
    158
  ],
  "0x005E": [
    142,
    142,
    158
  ],
  "0x005F": [
    150,
    150,
    162
  ],
  "0x0060": [
    134,
    134,
    154
  ],
  "0x0061": [
    142,
    142,
    158
  ],
  "0x0062": [
    134,
    134,
    154
  ],
  "0x0064": [
    134,
    134,
    154
  ],
  "0x0065": [
    134,
    134,
    154
  ],
  "0x0066": [
    134,
    134,
    154
  ],
  "0x0067": [
    142,
    142,
    158
  ],
  "0x0068": [
    142,
    142,
    158
  ],
  "0x0069": [
    134,
    134,
    154
  ],
  "0x006A": [
    134,
    134,
    154
  ],
  "0x006B": [
    134,
    134,
    154
  ],
  "0x006C": [
    134,
    134,
    154
  ],
  "0x006D": [
    134,
    134,
    154
  ],
  "0x006E": [
    142,
    142,
    158
  ],
  "0x006F": [
    142,
    142,
    158
  ],
  "0x0070": [
    32,
    89,
    101
  ],
  "0x0071": [
    32,
    89,
    101
  ],
  "0x0072": [
    32,
    89,
    101
  ],
  "0x0080": [
    77,
    85,
    113
  ],
  "0x0081": [
    162,
    162,
    166
  ],
  "0x0082": [
    73,
    40,
    32
  ],
  "0x0083": [
    73,
    40,
    32
  ],
  "0x0090": [
    77,
    85,
    113
  ],
  "0x0092": [
    65,
    73,
    105
  ],
  "0x0094": [
    113,
    117,
    142
  ],
  "0x00A0": [
    105,
    109,
    134
  ],
  "0x00A2": [
    142,
    142,
    158
  ],
  "0x00A4": [
    113,
    117,
    142
  ],
  "0x00B0": [
    150,
    150,
    162
  ],
  "0x00B2": [
    150,
    150,
    162
  ],
  "0x00B4": [
    89,
    97,
    125
  ],
  "0x00C0": [
    20,
    73,
    73
  ],
  "0x00C2": [
    138,
    97,
    77
  ],
  "0x00C4": [
    89,
    97,
    125
  ],
  "0x0100": [
    4,
    52,
    113
  ],
  "0x0101": [
    4,
    52,
    113
  ],
  "0x0110": [
    4,
    56,
    117
  ],
  "0x0111": [
    4,
    52,
    113
  ],
  "0x0120": [
    4,
    52,
    113
  ],
  "0x0121": [
    4,
    52,
    113
  ],
  "0x0122": [
    4,
    52,
    113
  ],
  "0x0130": [
    4,
    56,
    117
  ],
  "0x0131": [
    4,
    56,
    117
  ],
  "0x0140": [
    4,
    52,
    113
  ],
  "0x0141": [
    4,
    52,
    113
  ],
  "0x0142": [
    4,
    52,
    113
  ],
  "0x0150": [
    4,
    56,
    117
  ],
  "0x0151": [
    4,
    56,
    117
  ],
  "0x0160": [
    4,
    52,
    113
  ],
  "0x0161": [
    4,
    52,
    113
  ],
  "0x0170": [
    4,
    56,
    117
  ],
  "0x0171": [
    4,
    56,
    117
  ],
  "0x0180": [
    4,
    52,
    113
  ],
  "0x0181": [
    4,
    52,
    113
  ],
  "0x0190": [
    4,
    56,
    117
  ],
  "0x0191": [
    4,
    52,
    113
  ],
  "0x0192": [
    4,
    56,
    117
  ],
  "0x01A0": [
    4,
    52,
    113
  ],
  "0x01A1": [
    4,
    52,
    113
  ],
  "0x01B0": [
    4,
    56,
    117
  ],
  "0x01B1": [
    4,
    56,
    117
  ],
  "0x01B2": [
    4,
    56,
    117
  ],
  "0x01C0": [
    4,
    52,
    113
  ],
  "0x01C1": [
    4,
    52,
    113
  ],
  "0x01D0": [
    4,
    56,
    117
  ],
  "0x01D1": [
    4,
    56,
    117
  ],
  "0x0200": [
    4,
    52,
    113
  ],
  "0x0201": [
    85,
    125,
    178
  ],
  "0x0210": [
    24,
    85,
    138
  ],
  "0x0211": [
    56,
    101,
    154
  ],
  "0x0220": [
    4,
    56,
    117
  ],
  "0x0221": [
    4,
    56,
    117
  ],
  "0x0222": [
    4,
    56,
    117
  ],
  "0x0230": [
    24,
    85,
    138
  ],
  "0x0231": [
    24,
    85,
    138
  ],
  "0x0240": [
    4,
    56,
    117
  ],
  "0x0241": [
    24,
    85,
    138
  ],
  "0x0242": [
    4,
    56,
    117
  ],
  "0x0250": [
    44,
    93,
    150
  ],
  "0x0251": [
    44,
    93,
    150
  ],
  "0x0260": [
    4,
    56,
    117
  ],
  "0x0261": [
    4,
    56,
    117
  ],
  "0x0270": [
    24,
    85,
    138
  ],
  "0x0271": [
    24,
    85,
    138
  ],
  "0x0280": [
    4,
    56,
    117
  ],
  "0x0281": [
    4,
    56,
    117
  ],
  "0x0290": [
    20,
    77,
    142
  ],
  "0x0291": [
    24,
    85,
    138
  ],
  "0x0292": [
    24,
    85,
    138
  ],
  "0x02A0": [
    4,
    56,
    117
  ],
  "0x02A1": [
    4,
    56,
    117
  ],
  "0x02B0": [
    20,
    77,
    142
  ],
  "0x02B1": [
    24,
    85,
    138
  ],
  "0x02B2": [
    44,
    93,
    150
  ],
  "0x02C0": [
    4,
    56,
    117
  ],
  "0x02C1": [
    4,
    56,
    117
  ],
  "0x02D0": [
    24,
    85,
    138
  ],
  "0x02D1": [
    44,
    93,
    150
  ],
  "0x0300": [
    20,
    77,
    142
  ],
  "0x0301": [
    20,
    77,
    142
  ],
  "0x0310": [
    20,
    77,
    142
  ],
  "0x0311": [
    24,
    85,
    138
  ],
  "0x0320": [
    20,
    77,
    142
  ],
  "0x0321": [
    20,
    77,
    142
  ],
  "0x0322": [
    20,
    77,
    142
  ],
  "0x0330": [
    24,
    85,
    138
  ],
  "0x0331": [
    24,
    85,
    138
  ],
  "0x0340": [
    20,
    77,
    142
  ],
  "0x0341": [
    20,
    77,
    142
  ],
  "0x0342": [
    20,
    77,
    142
  ],
  "0x0350": [
    24,
    85,
    138
  ],
  "0x0351": [
    24,
    85,
    138
  ],
  "0x0360": [
    20,
    77,
    142
  ],
  "0x0361": [
    20,
    77,
    142
  ],
  "0x0370": [
    24,
    85,
    138
  ],
  "0x0371": [
    24,
    85,
    138
  ],
  "0x0380": [
    20,
    77,
    142
  ],
  "0x0381": [
    20,
    77,
    142
  ],
  "0x0390": [
    24,
    85,
    138
  ],
  "0x0391": [
    20,
    77,
    142
  ],
  "0x0392": [
    24,
    85,
    138
  ],
  "0x03A0": [
    20,
    77,
    142
  ],
  "0x03A1": [
    20,
    77,
    142
  ],
  "0x03B0": [
    24,
    85,
    138
  ],
  "0x03B1": [
    24,
    85,
    138
  ],
  "0x03B2": [
    24,
    85,
    138
  ],
  "0x03C0": [
    24,
    85,
    138
  ],
  "0x03C1": [
    24,
    85,
    138
  ],
  "0x03D0": [
    20,
    77,
    142
  ],
  "0x03D1": [
    20,
    77,
    142
  ],
  "0x0400": [
    162,
    162,
    166
  ],
  "0x0401": [
    142,
    142,
    158
  ],
  "0x0410": [
    73,
    40,
    32
  ],
  "0x0411": [
    125,
    85,
    73
  ],
  "0x0420": [
    134,
    134,
    154
  ],
  "0x0421": [
    117,
    73,
    60
  ],
  "0x0430": [
    89,
    97,
    125
  ],
  "0x0431": [
    97,
    101,
    130
  ],
  "0x0440": [
    60,
    36,
    32
  ],
  "0x0441": [
    73,
    40,
    32
  ],
  "0x0450": [
    105,
    109,
    134
  ],
  "0x0451": [
    60,
    36,
    32
  ],
  "0x0460": [
    125,
    85,
    73
  ],
  "0x0470": [
    24,
    85,
    138
  ],
  "0x0471": [
    8,
    69,
    121
  ],
  "0x0480": [
    117,
    73,
    60
  ],
  "0x0481": [
    162,
    162,
    166
  ],
  "0x0490": [
    113,
    117,
    142
  ],
  "0x0491": [
    24,
    85,
    138
  ],
  "0x04A0": [
    73,
    40,
    32
  ],
  "0x04B0": [
    56,
    101,
    154
  ],
  "0x04B1": [
    0,
    48,
    105
  ],
  "0x04C0": [
    73,
    40,
    32
  ],
  "0x04D0": [
    65,
    73,
    105
  ],
  "0x0500": [
    105,
    109,
    134
  ],
  "0x0501": [
    105,
    109,
    134
  ],
  "0x0510": [
    142,
    142,
    158
  ],
  "0x0511": [
    142,
    142,
    158
  ],
  "0x0520": [
    24,
    85,
    138
  ],
  "0x0521": [
    24,
    85,
    138
  ],
  "0x0522": [
    24,
    85,
    138
  ],
  "0x0530": [
    150,
    150,
    162
  ],
  "0x0531": [
    150,
    150,
    162
  ],
  "0x0540": [
    24,
    85,
    138
  ],
  "0x0541": [
    24,
    85,
    138
  ],
  "0x0542": [
    24,
    85,
    138
  ],
  "0x0550": [
    142,
    142,
    158
  ],
  "0x0551": [
    150,
    150,
    162
  ],
  "0x0560": [
    24,
    85,
    138
  ],
  "0x0561": [
    24,
    85,
    138
  ],
  "0x0570": [
    142,
    142,
    158
  ],
  "0x0571": [
    142,
    142,
    158
  ],
  "0x0580": [
    24,
    85,
    138
  ],
  "0x0581": [
    105,
    109,
    134
  ],
  "0x0590": [
    150,
    150,
    162
  ],
  "0x0591": [
    150,
    150,
    162
  ],
  "0x0592": [
    150,
    150,
    162
  ],
  "0x05A0": [
    24,
    85,
    138
  ],
  "0x05A1": [
    24,
    85,
    138
  ],
  "0x05B0": [
    142,
    142,
    158
  ],
  "0x05B1": [
    142,
    142,
    158
  ],
  "0x05B2": [
    142,
    142,
    158
  ],
  "0x05C0": [
    24,
    85,
    138
  ],
  "0x05C1": [
    24,
    85,
    138
  ],
  "0x05D0": [
    142,
    142,
    158
  ],
  "0x05D1": [
    142,
    142,
    158
  ],
  "0x0600": [
    134,
    134,
    154
  ],
  "0x0601": [
    142,
    142,
    158
  ],
  "0x0610": [
    142,
    142,
    158
  ],
  "0x0611": [
    134,
    134,
    154
  ],
  "0x0620": [
    142,
    142,
    158
  ],
  "0x0621": [
    134,
    134,
    154
  ],
  "0x0622": [
    134,
    134,
    154
  ],
  "0x0630": [
    150,
    150,
    162
  ],
  "0x0631": [
    150,
    150,
    162
  ],
  "0x0640": [
    134,
    134,
    154
  ],
  "0x0641": [
    134,
    134,
    154
  ],
  "0x0642": [
    134,
    134,
    154
  ],
  "0x0650": [
    150,
    150,
    162
  ],
  "0x0651": [
    150,
    150,
    162
  ],
  "0x0660": [
    142,
    142,
    158
  ],
  "0x0661": [
    142,
    142,
    158
  ],
  "0x0670": [
    142,
    142,
    158
  ],
  "0x0671": [
    150,
    150,
    162
  ],
  "0x0680": [
    134,
    134,
    154
  ],
  "0x0681": [
    142,
    142,
    158
  ],
  "0x0690": [
    134,
    134,
    154
  ],
  "0x0691": [
    142,
    142,
    158
  ],
  "0x0692": [
    142,
    142,
    158
  ],
  "0x06A0": [
    142,
    142,
    158
  ],
  "0x06A1": [
    142,
    142,
    158
  ],
  "0x06B0": [
    142,
    142,
    158
  ],
  "0x06B1": [
    142,
    142,
    158
  ],
  "0x06B2": [
    142,
    142,
    158
  ],
  "0x06C0": [
    150,
    150,
    162
  ],
  "0x06C1": [
    150,
    150,
    162
  ],
  "0x06D0": [
    142,
    142,
    158
  ],
  "0x06D1": [
    142,
    142,
    158
  ],
  "0x0700": [
    4,
    40,
    8
  ],
  "0x0701": [
    4,
    40,
    8
  ],
  "0x0710": [
    12,
    48,
    12
  ],
  "0x0711": [
    4,
    40,
    8
  ],
  "0x0720": [
    4,
    40,
    8
  ],
  "0x0721": [
    32,
    89,
    101
  ],
  "0x0730": [
    142,
    142,
    158
  ],
  "0x0731": [
    142,
    142,
    158
  ],
  "0x0740": [
    32,
    89,
    101
  ],
  "0x0741": [
    32,
    89,
    101
  ],
  "0x0750": [
    60,
    36,
    32
  ],
  "0x0751": [
    60,
    36,
    32
  ],
  "0x0760": [
    32,
    89,
    101
  ],
  "0x0761": [
    32,
    89,
    101
  ],
  "0x0770": [
    142,
    142,
    158
  ],
  "0x0771": [
    142,
    142,
    158
  ],
  "0x0780": [
    32,
    89,
    101
  ],
  "0x0781": [
    32,
    89,
    101
  ],
  "0x0790": [
    12,
    48,
    12
  ],
  "0x0791": [
    12,
    48,
    12
  ],
  "0x07A0": [
    32,
    89,
    101
  ],
  "0x07A1": [
    32,
    89,
    101
  ],
  "0x07B0": [
    142,
    142,
    158
  ],
  "0x07B1": [
    142,
    142,
    158
  ],
  "0x07C0": [
    32,
    89,
    101
  ],
  "0x07C1": [
    32,
    89,
    101
  ],
  "0x07D0": [
    60,
    36,
    32
  ],
  "0x07D1": [
    60,
    36,
    32
  ],
  "0x0800": [
    77,
    85,
    113
  ],
  "0x0802": [
    113,
    117,
    142
  ],
  "0x0804": [
    113,
    117,
    142
  ],
  "0x0810": [
    77,
    85,
    113
  ],
  "0x0812": [
    113,
    117,
    142
  ],
  "0x0814": [
    113,
    117,
    142
  ],
  "0x0820": [
    77,
    85,
    113
  ],
  "0x0822": [
    113,
    117,
    142
  ],
  "0x0824": [
    113,
    117,
    142
  ],
  "0x0830": [
    134,
    134,
    154
  ],
  "0x0832": [
    134,
    134,
    154
  ],
  "0x0834": [
    89,
    97,
    125
  ],
  "0x0840": [
    134,
    134,
    154
  ],
  "0x0841": [
    134,
    134,
    154
  ],
  "0x0843": [
    0,
    40,
    93
  ],
  "0x0844": [
    0,
    40,
    93
  ],
  "0x0846": [
    89,
    97,
    125
  ],
  "0x0847": [
    89,
    97,
    125
  ],
  "0x0850": [
    134,
    134,
    154
  ],
  "0x0852": [
    134,
    134,
    154
  ],
  "0x0854": [
    89,
    97,
    125
  ],
  "0x0860": [
    134,
    134,
    154
  ],
  "0x0862": [
    48,
    73,
    89
  ],
  "0x0864": [
    89,
    97,
    125
  ],
  "0x0870": [
    134,
    134,
    154
  ],
  "0x0872": [
    134,
    134,
    154
  ],
  "0x0874": [
    89,
    97,
    125
  ],
  "0x0880": [
    134,
    134,
    154
  ],
  "0x0882": [
    134,
    134,
    154
  ],
  "0x0884": [
    89,
    97,
    125
  ],
  "0x0890": [
    134,
    134,
    154
  ],
  "0x0891": [
    134,
    134,
    154
  ],
  "0x0893": [
    113,
    117,
    142
  ],
  "0x0894": [
    134,
    134,
    154
  ],
  "0x0896": [
    89,
    97,
    125
  ],
  "0x0897": [
    65,
    73,
    105
  ],
  "0x08A0": [
    134,
    134,
    154
  ],
  "0x08A2": [
    77,
    85,
    113
  ],
  "0x08A4": [
    89,
    97,
    125
  ],
  "0x08B0": [
    150,
    150,
    162
  ],
  "0x08B2": [
    150,
    150,
    162
  ],
  "0x08B4": [
    89,
    97,
    125
  ],
  "0x08C0": [
    150,
    150,
    162
  ],
  "0x08C2": [
    150,
    150,
    162
  ],
  "0x08C4": [
    89,
    97,
    125
  ],
  "0x08D0": [
    150,
    150,
    162
  ],
  "0x08D2": [
    150,
    150,
    162
  ],
  "0x08D4": [
    65,
    73,
    105
  ],
  "0x0900": [
    105,
    109,
    134
  ],
  "0x0902": [
    105,
    109,
    134
  ],
  "0x0904": [
    113,
    117,
    142
  ],
  "0x0910": [
    105,
    109,
    134
  ],
  "0x0912": [
    142,
    142,
    158
  ],
  "0x0914": [
    113,
    117,
    142
  ],
  "0x0920": [
    105,
    109,
    134
  ],
  "0x0922": [
    142,
    142,
    158
  ],
  "0x0924": [
    113,
    117,
    142
  ],
  "0x0930": [
    105,
    109,
    134
  ],
  "0x0932": [
    170,
    134,
    77
  ],
  "0x0934": [
    89,
    97,
    125
  ],
  "0x0940": [
    105,
    109,
    134
  ],
  "0x0941": [
    105,
    109,
    134
  ],
  "0x0943": [
    170,
    134,
    77
  ],
  "0x0944": [
    65,
    73,
    105
  ],
  "0x0946": [
    89,
    97,
    125
  ],
  "0x0947": [
    89,
    97,
    125
  ],
  "0x0950": [
    105,
    109,
    134
  ],
  "0x0952": [
    170,
    134,
    77
  ],
  "0x0954": [
    89,
    97,
    125
  ],
  "0x0960": [
    105,
    109,
    134
  ],
  "0x0962": [
    65,
    73,
    105
  ],
  "0x0964": [
    89,
    97,
    125
  ],
  "0x0970": [
    89,
    97,
    125
  ],
  "0x0972": [
    20,
    73,
    73
  ],
  "0x0974": [
    89,
    97,
    125
  ],
  "0x0980": [
    89,
    97,
    125
  ],
  "0x0982": [
    20,
    73,
    73
  ],
  "0x0984": [
    89,
    97,
    125
  ],
  "0x0990": [
    89,
    97,
    125
  ],
  "0x0991": [
    89,
    97,
    125
  ],
  "0x0993": [
    60,
    36,
    32
  ],
  "0x0994": [
    20,
    73,
    73
  ],
  "0x0996": [
    89,
    97,
    125
  ],
  "0x0997": [
    65,
    73,
    105
  ],
  "0x09A0": [
    89,
    97,
    125
  ],
  "0x09A2": [
    20,
    73,
    73
  ],
  "0x09A4": [
    89,
    97,
    125
  ],
  "0x09B0": [
    20,
    73,
    73
  ],
  "0x09B2": [
    138,
    97,
    77
  ],
  "0x09B4": [
    89,
    97,
    125
  ],
  "0x09C0": [
    20,
    73,
    73
  ],
  "0x09C2": [
    138,
    97,
    77
  ],
  "0x09C4": [
    89,
    97,
    125
  ],
  "0x09D0": [
    20,
    73,
    73
  ],
  "0x09D2": [
    138,
    97,
    77
  ],
  "0x09D4": [
    65,
    73,
    105
  ]
};

// WASTELAND TILESET - 393 tiles
const WASTELAND_COLORS = {
  "0x0010": [
    12,
    32,
    44
  ],
  "0x0011": [
    12,
    32,
    44
  ],
  "0x0012": [
    12,
    32,
    44
  ],
  "0x0013": [
    12,
    32,
    44
  ],
  "0x0015": [
    12,
    32,
    44
  ],
  "0x0016": [
    12,
    32,
    44
  ],
  "0x0017": [
    12,
    32,
    44
  ],
  "0x0020": [
    16,
    32,
    44
  ],
  "0x0021": [
    16,
    32,
    44
  ],
  "0x0022": [
    16,
    32,
    44
  ],
  "0x0023": [
    16,
    32,
    44
  ],
  "0x0025": [
    16,
    32,
    44
  ],
  "0x0026": [
    16,
    32,
    44
  ],
  "0x0027": [
    16,
    32,
    44
  ],
  "0x0030": [
    77,
    40,
    12
  ],
  "0x0031": [
    69,
    36,
    8
  ],
  "0x0032": [
    77,
    40,
    12
  ],
  "0x0034": [
    77,
    40,
    12
  ],
  "0x0035": [
    69,
    36,
    8
  ],
  "0x0036": [
    77,
    40,
    12
  ],
  "0x0037": [
    77,
    40,
    12
  ],
  "0x0038": [
    69,
    36,
    8
  ],
  "0x0039": [
    77,
    40,
    12
  ],
  "0x003A": [
    69,
    36,
    8
  ],
  "0x003B": [
    77,
    40,
    12
  ],
  "0x0040": [
    69,
    36,
    8
  ],
  "0x0041": [
    65,
    32,
    8
  ],
  "0x0042": [
    69,
    36,
    8
  ],
  "0x0044": [
    60,
    28,
    8
  ],
  "0x0045": [
    65,
    32,
    8
  ],
  "0x0046": [
    69,
    36,
    8
  ],
  "0x0047": [
    60,
    28,
    8
  ],
  "0x0048": [
    60,
    28,
    8
  ],
  "0x0049": [
    65,
    32,
    8
  ],
  "0x004A": [
    65,
    32,
    8
  ],
  "0x004B": [
    69,
    36,
    8
  ],
  "0x0050": [
    121,
    56,
    4
  ],
  "0x0051": [
    130,
    65,
    4
  ],
  "0x0052": [
    130,
    65,
    4
  ],
  "0x0054": [
    121,
    56,
    4
  ],
  "0x0055": [
    130,
    65,
    4
  ],
  "0x0056": [
    130,
    65,
    4
  ],
  "0x0057": [
    166,
    89,
    20
  ],
  "0x0058": [
    121,
    56,
    4
  ],
  "0x0059": [
    130,
    65,
    4
  ],
  "0x005A": [
    130,
    65,
    4
  ],
  "0x005B": [
    142,
    73,
    4
  ],
  "0x005C": [
    130,
    65,
    4
  ],
  "0x005D": [
    130,
    65,
    4
  ],
  "0x005E": [
    130,
    65,
    4
  ],
  "0x005F": [
    130,
    65,
    4
  ],
  "0x0060": [
    113,
    48,
    4
  ],
  "0x0061": [
    121,
    56,
    4
  ],
  "0x0062": [
    121,
    56,
    4
  ],
  "0x0064": [
    113,
    48,
    4
  ],
  "0x0065": [
    121,
    56,
    4
  ],
  "0x0066": [
    121,
    56,
    4
  ],
  "0x0067": [
    113,
    48,
    4
  ],
  "0x0068": [
    81,
    28,
    8
  ],
  "0x0069": [
    121,
    56,
    4
  ],
  "0x006A": [
    121,
    56,
    4
  ],
  "0x006B": [
    130,
    65,
    4
  ],
  "0x006C": [
    121,
    56,
    4
  ],
  "0x006D": [
    121,
    56,
    4
  ],
  "0x006E": [
    81,
    28,
    8
  ],
  "0x006F": [
    113,
    48,
    4
  ],
  "0x0070": [
    28,
    36,
    0
  ],
  "0x0071": [
    28,
    36,
    0
  ],
  "0x0072": [
    4,
    16,
    0
  ],
  "0x0080": [
    24,
    16,
    16
  ],
  "0x0081": [
    73,
    60,
    56
  ],
  "0x0082": [
    65,
    52,
    48
  ],
  "0x0083": [
    65,
    52,
    48
  ],
  "0x0090": [
    56,
    40,
    40
  ],
  "0x0092": [
    56,
    40,
    40
  ],
  "0x0094": [
    93,
    81,
    77
  ],
  "0x00A0": [
    85,
    69,
    69
  ],
  "0x00A2": [
    121,
    56,
    4
  ],
  "0x00A4": [
    93,
    81,
    77
  ],
  "0x00B0": [
    142,
    134,
    130
  ],
  "0x00B2": [
    121,
    109,
    105
  ],
  "0x00B4": [
    113,
    101,
    97
  ],
  "0x00C0": [
    36,
    24,
    24
  ],
  "0x00C2": [
    130,
    65,
    4
  ],
  "0x00C4": [
    113,
    101,
    97
  ],
  "0x0100": [
    16,
    32,
    44
  ],
  "0x0101": [
    16,
    32,
    44
  ],
  "0x0110": [
    12,
    32,
    44
  ],
  "0x0111": [
    12,
    32,
    44
  ],
  "0x0120": [
    16,
    32,
    44
  ],
  "0x0121": [
    16,
    32,
    44
  ],
  "0x0122": [
    16,
    32,
    44
  ],
  "0x0130": [
    12,
    32,
    44
  ],
  "0x0131": [
    12,
    32,
    44
  ],
  "0x0140": [
    16,
    32,
    44
  ],
  "0x0141": [
    12,
    32,
    44
  ],
  "0x0142": [
    16,
    32,
    44
  ],
  "0x0150": [
    12,
    32,
    44
  ],
  "0x0151": [
    12,
    32,
    44
  ],
  "0x0160": [
    16,
    32,
    44
  ],
  "0x0161": [
    16,
    32,
    44
  ],
  "0x0170": [
    12,
    32,
    44
  ],
  "0x0171": [
    12,
    32,
    44
  ],
  "0x0180": [
    16,
    32,
    44
  ],
  "0x0181": [
    16,
    32,
    44
  ],
  "0x0190": [
    12,
    32,
    44
  ],
  "0x0191": [
    12,
    32,
    44
  ],
  "0x0192": [
    12,
    32,
    44
  ],
  "0x01A0": [
    16,
    32,
    44
  ],
  "0x01A1": [
    16,
    32,
    44
  ],
  "0x01B0": [
    12,
    32,
    44
  ],
  "0x01B1": [
    12,
    32,
    44
  ],
  "0x01B2": [
    12,
    32,
    44
  ],
  "0x01C0": [
    16,
    32,
    44
  ],
  "0x01C1": [
    12,
    32,
    44
  ],
  "0x01D0": [
    12,
    32,
    44
  ],
  "0x01D1": [
    12,
    32,
    44
  ],
  "0x0200": [
    44,
    16,
    4
  ],
  "0x0201": [
    12,
    40,
    52
  ],
  "0x0210": [
    69,
    36,
    8
  ],
  "0x0211": [
    69,
    36,
    8
  ],
  "0x0220": [
    12,
    32,
    44
  ],
  "0x0221": [
    12,
    32,
    44
  ],
  "0x0222": [
    12,
    32,
    44
  ],
  "0x0230": [
    69,
    36,
    8
  ],
  "0x0231": [
    69,
    36,
    8
  ],
  "0x0240": [
    12,
    32,
    44
  ],
  "0x0241": [
    12,
    32,
    44
  ],
  "0x0242": [
    12,
    32,
    44
  ],
  "0x0250": [
    69,
    36,
    8
  ],
  "0x0251": [
    69,
    36,
    8
  ],
  "0x0260": [
    12,
    32,
    44
  ],
  "0x0261": [
    12,
    32,
    44
  ],
  "0x0270": [
    77,
    40,
    12
  ],
  "0x0271": [
    77,
    40,
    12
  ],
  "0x0280": [
    12,
    32,
    44
  ],
  "0x0281": [
    12,
    32,
    44
  ],
  "0x0290": [
    65,
    32,
    8
  ],
  "0x0291": [
    77,
    40,
    12
  ],
  "0x0292": [
    65,
    32,
    8
  ],
  "0x02A0": [
    12,
    32,
    44
  ],
  "0x02A1": [
    12,
    32,
    44
  ],
  "0x02B0": [
    69,
    36,
    8
  ],
  "0x02B1": [
    69,
    36,
    8
  ],
  "0x02B2": [
    69,
    36,
    8
  ],
  "0x02C0": [
    12,
    32,
    44
  ],
  "0x02C1": [
    12,
    32,
    44
  ],
  "0x02D0": [
    69,
    36,
    8
  ],
  "0x02D1": [
    77,
    40,
    12
  ],
  "0x0300": [
    77,
    40,
    12
  ],
  "0x0301": [
    77,
    40,
    12
  ],
  "0x0310": [
    77,
    40,
    12
  ],
  "0x0311": [
    77,
    40,
    12
  ],
  "0x0320": [
    69,
    36,
    8
  ],
  "0x0321": [
    65,
    32,
    8
  ],
  "0x0322": [
    69,
    36,
    8
  ],
  "0x0330": [
    77,
    40,
    12
  ],
  "0x0331": [
    77,
    40,
    12
  ],
  "0x0340": [
    69,
    36,
    8
  ],
  "0x0341": [
    65,
    32,
    8
  ],
  "0x0342": [
    69,
    36,
    8
  ],
  "0x0350": [
    77,
    40,
    12
  ],
  "0x0351": [
    77,
    40,
    12
  ],
  "0x0360": [
    69,
    36,
    8
  ],
  "0x0361": [
    69,
    36,
    8
  ],
  "0x0370": [
    77,
    40,
    12
  ],
  "0x0371": [
    77,
    40,
    12
  ],
  "0x0380": [
    69,
    36,
    8
  ],
  "0x0381": [
    69,
    36,
    8
  ],
  "0x0390": [
    77,
    40,
    12
  ],
  "0x0391": [
    69,
    36,
    8
  ],
  "0x0392": [
    77,
    40,
    12
  ],
  "0x03A0": [
    69,
    36,
    8
  ],
  "0x03A1": [
    69,
    36,
    8
  ],
  "0x03B0": [
    77,
    40,
    12
  ],
  "0x03B1": [
    69,
    36,
    8
  ],
  "0x03B2": [
    77,
    40,
    12
  ],
  "0x03C0": [
    69,
    36,
    8
  ],
  "0x03C1": [
    69,
    36,
    8
  ],
  "0x03D0": [
    77,
    40,
    12
  ],
  "0x03D1": [
    77,
    40,
    12
  ],
  "0x0400": [
    73,
    60,
    56
  ],
  "0x0401": [
    113,
    101,
    97
  ],
  "0x0410": [
    56,
    40,
    40
  ],
  "0x0411": [
    113,
    101,
    97
  ],
  "0x0420": [
    113,
    101,
    97
  ],
  "0x0421": [
    93,
    81,
    77
  ],
  "0x0430": [
    77,
    40,
    12
  ],
  "0x0431": [
    56,
    40,
    40
  ],
  "0x0440": [
    44,
    32,
    32
  ],
  "0x0441": [
    56,
    40,
    40
  ],
  "0x0450": [
    56,
    40,
    40
  ],
  "0x0451": [
    56,
    40,
    40
  ],
  "0x0460": [
    113,
    101,
    97
  ],
  "0x0470": [
    77,
    40,
    12
  ],
  "0x0471": [
    69,
    36,
    8
  ],
  "0x0480": [
    93,
    81,
    77
  ],
  "0x0481": [
    73,
    60,
    56
  ],
  "0x0490": [
    65,
    52,
    48
  ],
  "0x0491": [
    44,
    32,
    32
  ],
  "0x04A0": [
    56,
    40,
    40
  ],
  "0x04B0": [
    77,
    40,
    12
  ],
  "0x04B1": [
    69,
    36,
    8
  ],
  "0x04C0": [
    56,
    40,
    40
  ],
  "0x04D0": [
    56,
    40,
    40
  ],
  "0x0500": [
    81,
    28,
    8
  ],
  "0x0501": [
    77,
    40,
    12
  ],
  "0x0510": [
    121,
    56,
    4
  ],
  "0x0511": [
    130,
    65,
    4
  ],
  "0x0520": [
    77,
    40,
    12
  ],
  "0x0521": [
    69,
    36,
    8
  ],
  "0x0522": [
    77,
    40,
    12
  ],
  "0x0530": [
    130,
    65,
    4
  ],
  "0x0531": [
    121,
    56,
    4
  ],
  "0x0540": [
    77,
    40,
    12
  ],
  "0x0541": [
    69,
    24,
    8
  ],
  "0x0542": [
    69,
    36,
    8
  ],
  "0x0550": [
    130,
    65,
    4
  ],
  "0x0551": [
    121,
    56,
    4
  ],
  "0x0560": [
    77,
    40,
    12
  ],
  "0x0561": [
    77,
    40,
    12
  ],
  "0x0570": [
    121,
    56,
    4
  ],
  "0x0571": [
    130,
    65,
    4
  ],
  "0x0580": [
    77,
    40,
    12
  ],
  "0x0581": [
    81,
    28,
    8
  ],
  "0x0590": [
    130,
    65,
    4
  ],
  "0x0591": [
    121,
    56,
    4
  ],
  "0x0592": [
    130,
    65,
    4
  ],
  "0x05A0": [
    77,
    40,
    12
  ],
  "0x05A1": [
    77,
    40,
    12
  ],
  "0x05B0": [
    142,
    73,
    4
  ],
  "0x05B1": [
    121,
    56,
    4
  ],
  "0x05B2": [
    121,
    56,
    4
  ],
  "0x05C0": [
    69,
    36,
    8
  ],
  "0x05C1": [
    65,
    32,
    8
  ],
  "0x05D0": [
    130,
    65,
    4
  ],
  "0x05D1": [
    130,
    65,
    4
  ],
  "0x0600": [
    121,
    56,
    4
  ],
  "0x0601": [
    113,
    48,
    4
  ],
  "0x0610": [
    130,
    65,
    4
  ],
  "0x0611": [
    121,
    56,
    4
  ],
  "0x0620": [
    113,
    48,
    4
  ],
  "0x0621": [
    121,
    56,
    4
  ],
  "0x0622": [
    121,
    56,
    4
  ],
  "0x0630": [
    121,
    56,
    4
  ],
  "0x0631": [
    130,
    65,
    4
  ],
  "0x0640": [
    121,
    56,
    4
  ],
  "0x0641": [
    113,
    48,
    4
  ],
  "0x0642": [
    121,
    56,
    4
  ],
  "0x0650": [
    101,
    40,
    4
  ],
  "0x0651": [
    130,
    65,
    4
  ],
  "0x0660": [
    121,
    56,
    4
  ],
  "0x0661": [
    121,
    56,
    4
  ],
  "0x0670": [
    121,
    56,
    4
  ],
  "0x0671": [
    121,
    56,
    4
  ],
  "0x0680": [
    121,
    56,
    4
  ],
  "0x0681": [
    113,
    48,
    4
  ],
  "0x0690": [
    121,
    56,
    4
  ],
  "0x0691": [
    130,
    65,
    4
  ],
  "0x0692": [
    130,
    65,
    4
  ],
  "0x06A0": [
    121,
    56,
    4
  ],
  "0x06A1": [
    121,
    56,
    4
  ],
  "0x06B0": [
    121,
    56,
    4
  ],
  "0x06B1": [
    130,
    65,
    4
  ],
  "0x06B2": [
    130,
    65,
    4
  ],
  "0x06C0": [
    121,
    56,
    4
  ],
  "0x06C1": [
    121,
    56,
    4
  ],
  "0x06D0": [
    101,
    40,
    4
  ],
  "0x06D1": [
    101,
    40,
    4
  ],
  "0x0700": [
    28,
    36,
    0
  ],
  "0x0701": [
    28,
    36,
    0
  ],
  "0x0710": [
    81,
    28,
    8
  ],
  "0x0711": [
    56,
    20,
    8
  ],
  "0x0720": [
    8,
    24,
    0
  ],
  "0x0721": [
    28,
    36,
    0
  ],
  "0x0730": [
    121,
    56,
    4
  ],
  "0x0731": [
    121,
    56,
    4
  ],
  "0x0740": [
    28,
    36,
    0
  ],
  "0x0741": [
    28,
    36,
    0
  ],
  "0x0750": [
    56,
    20,
    8
  ],
  "0x0751": [
    56,
    20,
    8
  ],
  "0x0760": [
    28,
    36,
    0
  ],
  "0x0761": [
    28,
    36,
    0
  ],
  "0x0770": [
    121,
    56,
    4
  ],
  "0x0771": [
    130,
    65,
    4
  ],
  "0x0780": [
    28,
    36,
    0
  ],
  "0x0781": [
    28,
    36,
    0
  ],
  "0x0790": [
    69,
    24,
    8
  ],
  "0x0791": [
    56,
    20,
    8
  ],
  "0x07A0": [
    4,
    16,
    0
  ],
  "0x07A1": [
    28,
    36,
    0
  ],
  "0x07B0": [
    121,
    56,
    4
  ],
  "0x07B1": [
    130,
    65,
    4
  ],
  "0x07C0": [
    28,
    36,
    0
  ],
  "0x07C1": [
    4,
    16,
    0
  ],
  "0x07D0": [
    52,
    48,
    0
  ],
  "0x07D1": [
    52,
    48,
    0
  ],
  "0x0800": [
    65,
    52,
    48
  ],
  "0x0802": [
    93,
    81,
    77
  ],
  "0x0804": [
    93,
    81,
    77
  ],
  "0x0810": [
    65,
    52,
    48
  ],
  "0x0812": [
    93,
    81,
    77
  ],
  "0x0814": [
    93,
    81,
    77
  ],
  "0x0820": [
    65,
    52,
    48
  ],
  "0x0822": [
    93,
    81,
    77
  ],
  "0x0824": [
    93,
    81,
    77
  ],
  "0x0830": [
    113,
    101,
    97
  ],
  "0x0832": [
    113,
    101,
    97
  ],
  "0x0834": [
    93,
    81,
    77
  ],
  "0x0840": [
    113,
    101,
    97
  ],
  "0x0841": [
    113,
    101,
    97
  ],
  "0x0843": [
    24,
    16,
    16
  ],
  "0x0844": [
    36,
    24,
    24
  ],
  "0x0846": [
    93,
    81,
    77
  ],
  "0x0847": [
    93,
    81,
    77
  ],
  "0x0850": [
    113,
    101,
    97
  ],
  "0x0852": [
    113,
    101,
    97
  ],
  "0x0854": [
    93,
    81,
    77
  ],
  "0x0860": [
    113,
    101,
    97
  ],
  "0x0862": [
    44,
    32,
    32
  ],
  "0x0864": [
    93,
    81,
    77
  ],
  "0x0870": [
    113,
    101,
    97
  ],
  "0x0872": [
    113,
    101,
    97
  ],
  "0x0874": [
    93,
    81,
    77
  ],
  "0x0880": [
    113,
    101,
    97
  ],
  "0x0882": [
    113,
    101,
    97
  ],
  "0x0884": [
    93,
    81,
    77
  ],
  "0x0890": [
    113,
    101,
    97
  ],
  "0x0891": [
    113,
    101,
    97
  ],
  "0x0893": [
    93,
    81,
    77
  ],
  "0x0894": [
    113,
    101,
    97
  ],
  "0x0896": [
    73,
    60,
    56
  ],
  "0x0897": [
    93,
    81,
    77
  ],
  "0x08A0": [
    113,
    101,
    97
  ],
  "0x08A2": [
    65,
    52,
    48
  ],
  "0x08A4": [
    73,
    60,
    56
  ],
  "0x08B0": [
    121,
    109,
    105
  ],
  "0x08B2": [
    121,
    109,
    105
  ],
  "0x08B4": [
    93,
    81,
    77
  ],
  "0x08C0": [
    121,
    109,
    105
  ],
  "0x08C2": [
    121,
    109,
    105
  ],
  "0x08C4": [
    93,
    81,
    77
  ],
  "0x08D0": [
    121,
    109,
    105
  ],
  "0x08D2": [
    121,
    109,
    105
  ],
  "0x08D4": [
    93,
    81,
    77
  ],
  "0x0900": [
    85,
    69,
    69
  ],
  "0x0902": [
    121,
    56,
    4
  ],
  "0x0904": [
    93,
    81,
    77
  ],
  "0x0910": [
    85,
    69,
    69
  ],
  "0x0912": [
    121,
    56,
    4
  ],
  "0x0914": [
    93,
    81,
    77
  ],
  "0x0920": [
    85,
    69,
    69
  ],
  "0x0922": [
    121,
    56,
    4
  ],
  "0x0924": [
    93,
    81,
    77
  ],
  "0x0930": [
    85,
    69,
    69
  ],
  "0x0932": [
    166,
    89,
    20
  ],
  "0x0934": [
    93,
    81,
    77
  ],
  "0x0940": [
    85,
    69,
    69
  ],
  "0x0941": [
    85,
    69,
    69
  ],
  "0x0943": [
    166,
    89,
    20
  ],
  "0x0944": [
    56,
    40,
    40
  ],
  "0x0946": [
    93,
    81,
    77
  ],
  "0x0947": [
    93,
    81,
    77
  ],
  "0x0950": [
    85,
    69,
    69
  ],
  "0x0952": [
    166,
    89,
    20
  ],
  "0x0954": [
    93,
    81,
    77
  ],
  "0x0960": [
    85,
    69,
    69
  ],
  "0x0962": [
    56,
    40,
    40
  ],
  "0x0964": [
    93,
    81,
    77
  ],
  "0x0970": [
    73,
    60,
    56
  ],
  "0x0972": [
    44,
    32,
    32
  ],
  "0x0974": [
    93,
    81,
    77
  ],
  "0x0980": [
    73,
    60,
    56
  ],
  "0x0982": [
    44,
    32,
    32
  ],
  "0x0984": [
    93,
    81,
    77
  ],
  "0x0990": [
    73,
    60,
    56
  ],
  "0x0991": [
    73,
    60,
    56
  ],
  "0x0993": [
    44,
    32,
    32
  ],
  "0x0994": [
    89,
    52,
    24
  ],
  "0x0996": [
    73,
    60,
    56
  ],
  "0x0997": [
    93,
    81,
    77
  ],
  "0x09A0": [
    73,
    60,
    56
  ],
  "0x09A2": [
    44,
    32,
    32
  ],
  "0x09A4": [
    73,
    60,
    56
  ],
  "0x09B0": [
    44,
    32,
    32
  ],
  "0x09B2": [
    130,
    65,
    4
  ],
  "0x09B4": [
    93,
    81,
    77
  ],
  "0x09C0": [
    44,
    32,
    32
  ],
  "0x09C2": [
    130,
    65,
    4
  ],
  "0x09C4": [
    93,
    81,
    77
  ],
  "0x09D0": [
    44,
    32,
    32
  ],
  "0x09D2": [
    130,
    65,
    4
  ],
  "0x09D4": [
    93,
    81,
    77
  ]
};

// SWAMP TILESET - 383 tiles
const SWAMP_COLORS = {
  "0x0010": [
    24,
    32,
    8
  ],
  "0x0011": [
    24,
    32,
    8
  ],
  "0x0012": [
    24,
    32,
    8
  ],
  "0x0013": [
    24,
    32,
    8
  ],
  "0x0020": [
    20,
    28,
    8
  ],
  "0x0021": [
    20,
    28,
    8
  ],
  "0x0022": [
    20,
    28,
    8
  ],
  "0x0023": [
    20,
    28,
    8
  ],
  "0x0030": [
    97,
    52,
    4
  ],
  "0x0031": [
    97,
    52,
    4
  ],
  "0x0032": [
    89,
    44,
    0
  ],
  "0x0034": [
    105,
    60,
    8
  ],
  "0x0035": [
    73,
    36,
    0
  ],
  "0x0036": [
    97,
    52,
    4
  ],
  "0x0037": [
    97,
    52,
    4
  ],
  "0x0038": [
    97,
    52,
    4
  ],
  "0x0039": [
    117,
    77,
    16
  ],
  "0x0040": [
    89,
    44,
    0
  ],
  "0x0041": [
    89,
    44,
    0
  ],
  "0x0042": [
    85,
    40,
    0
  ],
  "0x0044": [
    85,
    40,
    0
  ],
  "0x0045": [
    89,
    44,
    0
  ],
  "0x0046": [
    89,
    44,
    0
  ],
  "0x0047": [
    89,
    44,
    0
  ],
  "0x0048": [
    109,
    69,
    12
  ],
  "0x0049": [
    36,
    52,
    8
  ],
  "0x0050": [
    69,
    44,
    28
  ],
  "0x0051": [
    81,
    56,
    40
  ],
  "0x0052": [
    81,
    56,
    40
  ],
  "0x0054": [
    69,
    44,
    28
  ],
  "0x0055": [
    69,
    44,
    28
  ],
  "0x0056": [
    81,
    56,
    40
  ],
  "0x0057": [
    81,
    56,
    40
  ],
  "0x0058": [
    48,
    28,
    20
  ],
  "0x0059": [
    81,
    56,
    40
  ],
  "0x005A": [
    81,
    56,
    40
  ],
  "0x005B": [
    24,
    32,
    8
  ],
  "0x005C": [
    69,
    44,
    28
  ],
  "0x005D": [
    36,
    52,
    8
  ],
  "0x005E": [
    40,
    20,
    12
  ],
  "0x005F": [
    69,
    44,
    28
  ],
  "0x0060": [
    56,
    36,
    24
  ],
  "0x0061": [
    69,
    44,
    28
  ],
  "0x0062": [
    69,
    44,
    28
  ],
  "0x0064": [
    56,
    36,
    24
  ],
  "0x0065": [
    56,
    36,
    24
  ],
  "0x0066": [
    56,
    40,
    40
  ],
  "0x0067": [
    69,
    44,
    28
  ],
  "0x0068": [
    69,
    44,
    28
  ],
  "0x0069": [
    56,
    36,
    24
  ],
  "0x006A": [
    69,
    44,
    28
  ],
  "0x006B": [
    69,
    44,
    28
  ],
  "0x006C": [
    69,
    44,
    28
  ],
  "0x006D": [
    24,
    32,
    8
  ],
  "0x006E": [
    56,
    36,
    24
  ],
  "0x006F": [
    36,
    52,
    8
  ],
  "0x0070": [
    65,
    40,
    12
  ],
  "0x0071": [
    125,
    89,
    65
  ],
  "0x0072": [
    65,
    40,
    12
  ],
  "0x0080": [
    65,
    52,
    48
  ],
  "0x0081": [
    73,
    60,
    56
  ],
  "0x0082": [
    24,
    16,
    16
  ],
  "0x0083": [
    56,
    40,
    40
  ],
  "0x0090": [
    56,
    40,
    40
  ],
  "0x0092": [
    65,
    52,
    48
  ],
  "0x0094": [
    113,
    101,
    97
  ],
  "0x00A0": [
    93,
    81,
    77
  ],
  "0x00A2": [
    69,
    44,
    28
  ],
  "0x00A4": [
    113,
    101,
    97
  ],
  "0x00B0": [
    134,
    121,
    117
  ],
  "0x00B2": [
    134,
    121,
    117
  ],
  "0x00B4": [
    121,
    109,
    105
  ],
  "0x00C0": [
    56,
    40,
    40
  ],
  "0x00C2": [
    125,
    85,
    24
  ],
  "0x00C4": [
    121,
    109,
    105
  ],
  "0x0100": [
    20,
    28,
    8
  ],
  "0x0101": [
    20,
    28,
    8
  ],
  "0x0110": [
    24,
    32,
    8
  ],
  "0x0111": [
    24,
    32,
    8
  ],
  "0x0120": [
    20,
    28,
    8
  ],
  "0x0121": [
    20,
    28,
    8
  ],
  "0x0122": [
    20,
    28,
    8
  ],
  "0x0130": [
    24,
    32,
    8
  ],
  "0x0131": [
    24,
    32,
    8
  ],
  "0x0140": [
    20,
    28,
    8
  ],
  "0x0141": [
    24,
    32,
    8
  ],
  "0x0142": [
    20,
    28,
    8
  ],
  "0x0150": [
    24,
    32,
    8
  ],
  "0x0151": [
    24,
    32,
    8
  ],
  "0x0160": [
    20,
    28,
    8
  ],
  "0x0161": [
    20,
    28,
    8
  ],
  "0x0170": [
    24,
    32,
    8
  ],
  "0x0171": [
    24,
    32,
    8
  ],
  "0x0180": [
    20,
    28,
    8
  ],
  "0x0181": [
    20,
    28,
    8
  ],
  "0x0190": [
    24,
    32,
    8
  ],
  "0x0191": [
    24,
    32,
    8
  ],
  "0x0192": [
    24,
    32,
    8
  ],
  "0x01A0": [
    20,
    28,
    8
  ],
  "0x01A1": [
    20,
    28,
    8
  ],
  "0x01B0": [
    24,
    32,
    8
  ],
  "0x01B1": [
    24,
    32,
    8
  ],
  "0x01B2": [
    24,
    32,
    8
  ],
  "0x01C0": [
    20,
    28,
    8
  ],
  "0x01C1": [
    24,
    32,
    8
  ],
  "0x01D0": [
    24,
    32,
    8
  ],
  "0x01D1": [
    24,
    32,
    8
  ],
  "0x0200": [
    24,
    32,
    8
  ],
  "0x0201": [
    24,
    32,
    8
  ],
  "0x0210": [
    89,
    44,
    0
  ],
  "0x0211": [
    89,
    44,
    0
  ],
  "0x0220": [
    24,
    32,
    8
  ],
  "0x0221": [
    24,
    32,
    8
  ],
  "0x0222": [
    24,
    32,
    8
  ],
  "0x0230": [
    97,
    52,
    4
  ],
  "0x0231": [
    97,
    52,
    4
  ],
  "0x0240": [
    24,
    32,
    8
  ],
  "0x0241": [
    24,
    32,
    8
  ],
  "0x0242": [
    24,
    32,
    8
  ],
  "0x0250": [
    85,
    40,
    0
  ],
  "0x0251": [
    85,
    40,
    0
  ],
  "0x0260": [
    24,
    32,
    8
  ],
  "0x0261": [
    24,
    32,
    8
  ],
  "0x0270": [
    97,
    52,
    4
  ],
  "0x0271": [
    97,
    52,
    4
  ],
  "0x0280": [
    40,
    48,
    12
  ],
  "0x0281": [
    40,
    48,
    12
  ],
  "0x0290": [
    89,
    44,
    0
  ],
  "0x0291": [
    89,
    44,
    0
  ],
  "0x0292": [
    89,
    44,
    0
  ],
  "0x02A0": [
    24,
    32,
    8
  ],
  "0x02A1": [
    24,
    32,
    8
  ],
  "0x02B0": [
    60,
    28,
    0
  ],
  "0x02B1": [
    97,
    52,
    4
  ],
  "0x02B2": [
    89,
    44,
    0
  ],
  "0x02C0": [
    24,
    32,
    8
  ],
  "0x02C1": [
    40,
    48,
    12
  ],
  "0x02D0": [
    85,
    40,
    0
  ],
  "0x02D1": [
    97,
    52,
    4
  ],
  "0x0300": [
    89,
    44,
    0
  ],
  "0x0301": [
    89,
    44,
    0
  ],
  "0x0310": [
    89,
    44,
    0
  ],
  "0x0311": [
    97,
    52,
    4
  ],
  "0x0320": [
    89,
    44,
    0
  ],
  "0x0321": [
    89,
    44,
    0
  ],
  "0x0322": [
    85,
    40,
    0
  ],
  "0x0330": [
    97,
    52,
    4
  ],
  "0x0331": [
    97,
    52,
    4
  ],
  "0x0340": [
    89,
    44,
    0
  ],
  "0x0341": [
    89,
    44,
    0
  ],
  "0x0342": [
    85,
    40,
    0
  ],
  "0x0350": [
    97,
    52,
    4
  ],
  "0x0351": [
    97,
    52,
    4
  ],
  "0x0360": [
    89,
    44,
    0
  ],
  "0x0361": [
    89,
    44,
    0
  ],
  "0x0370": [
    97,
    52,
    4
  ],
  "0x0371": [
    97,
    52,
    4
  ],
  "0x0380": [
    89,
    44,
    0
  ],
  "0x0381": [
    89,
    44,
    0
  ],
  "0x0390": [
    97,
    52,
    4
  ],
  "0x0391": [
    89,
    44,
    0
  ],
  "0x0392": [
    89,
    44,
    0
  ],
  "0x03A0": [
    89,
    44,
    0
  ],
  "0x03A1": [
    89,
    44,
    0
  ],
  "0x03B0": [
    97,
    52,
    4
  ],
  "0x03B1": [
    97,
    52,
    4
  ],
  "0x03B2": [
    89,
    44,
    0
  ],
  "0x03C0": [
    97,
    52,
    4
  ],
  "0x03C1": [
    97,
    52,
    4
  ],
  "0x03D0": [
    89,
    44,
    0
  ],
  "0x03D1": [
    89,
    44,
    0
  ],
  "0x0400": [
    56,
    40,
    40
  ],
  "0x0401": [
    36,
    24,
    24
  ],
  "0x0410": [
    97,
    52,
    4
  ],
  "0x0411": [
    73,
    60,
    56
  ],
  "0x0420": [
    56,
    40,
    40
  ],
  "0x0421": [
    65,
    52,
    48
  ],
  "0x0430": [
    97,
    52,
    4
  ],
  "0x0431": [
    56,
    40,
    40
  ],
  "0x0440": [
    24,
    16,
    16
  ],
  "0x0441": [
    85,
    69,
    69
  ],
  "0x0450": [
    65,
    52,
    48
  ],
  "0x0451": [
    97,
    52,
    4
  ],
  "0x0460": [
    65,
    52,
    48
  ],
  "0x0470": [
    97,
    52,
    4
  ],
  "0x0471": [
    109,
    69,
    12
  ],
  "0x0480": [
    65,
    52,
    48
  ],
  "0x0481": [
    56,
    40,
    40
  ],
  "0x0490": [
    97,
    52,
    4
  ],
  "0x0491": [
    44,
    32,
    32
  ],
  "0x04A0": [
    65,
    52,
    48
  ],
  "0x04B0": [
    97,
    52,
    4
  ],
  "0x04B1": [
    85,
    40,
    0
  ],
  "0x04C0": [
    65,
    52,
    48
  ],
  "0x04D0": [
    65,
    52,
    48
  ],
  "0x0500": [
    60,
    28,
    0
  ],
  "0x0501": [
    109,
    69,
    12
  ],
  "0x0510": [
    69,
    44,
    28
  ],
  "0x0511": [
    48,
    28,
    20
  ],
  "0x0520": [
    69,
    44,
    28
  ],
  "0x0521": [
    89,
    44,
    0
  ],
  "0x0522": [
    97,
    52,
    4
  ],
  "0x0530": [
    81,
    56,
    40
  ],
  "0x0531": [
    56,
    36,
    24
  ],
  "0x0540": [
    109,
    69,
    12
  ],
  "0x0541": [
    109,
    69,
    12
  ],
  "0x0542": [
    109,
    69,
    12
  ],
  "0x0550": [
    73,
    36,
    0
  ],
  "0x0551": [
    69,
    44,
    28
  ],
  "0x0560": [
    109,
    69,
    12
  ],
  "0x0561": [
    109,
    69,
    12
  ],
  "0x0570": [
    81,
    56,
    40
  ],
  "0x0571": [
    69,
    44,
    28
  ],
  "0x0580": [
    89,
    44,
    0
  ],
  "0x0581": [
    109,
    69,
    12
  ],
  "0x0590": [
    69,
    44,
    28
  ],
  "0x0591": [
    69,
    44,
    28
  ],
  "0x0592": [
    81,
    56,
    40
  ],
  "0x05A0": [
    89,
    44,
    0
  ],
  "0x05A1": [
    97,
    52,
    4
  ],
  "0x05B0": [
    69,
    44,
    28
  ],
  "0x05B1": [
    81,
    56,
    40
  ],
  "0x05B2": [
    81,
    56,
    40
  ],
  "0x05C0": [
    97,
    52,
    4
  ],
  "0x05C1": [
    97,
    52,
    4
  ],
  "0x05D0": [
    60,
    28,
    0
  ],
  "0x05D1": [
    125,
    85,
    24
  ],
  "0x0600": [
    69,
    44,
    28
  ],
  "0x0601": [
    56,
    36,
    24
  ],
  "0x0610": [
    81,
    56,
    40
  ],
  "0x0611": [
    69,
    44,
    28
  ],
  "0x0620": [
    56,
    36,
    24
  ],
  "0x0621": [
    69,
    44,
    28
  ],
  "0x0622": [
    69,
    44,
    28
  ],
  "0x0630": [
    69,
    44,
    28
  ],
  "0x0631": [
    81,
    56,
    40
  ],
  "0x0640": [
    69,
    44,
    28
  ],
  "0x0641": [
    56,
    36,
    24
  ],
  "0x0642": [
    69,
    44,
    28
  ],
  "0x0650": [
    48,
    28,
    20
  ],
  "0x0651": [
    81,
    56,
    40
  ],
  "0x0660": [
    69,
    44,
    28
  ],
  "0x0661": [
    69,
    44,
    28
  ],
  "0x0670": [
    69,
    44,
    28
  ],
  "0x0671": [
    69,
    44,
    28
  ],
  "0x0680": [
    69,
    44,
    28
  ],
  "0x0681": [
    56,
    36,
    24
  ],
  "0x0690": [
    69,
    44,
    28
  ],
  "0x0691": [
    81,
    56,
    40
  ],
  "0x0692": [
    81,
    56,
    40
  ],
  "0x06A0": [
    69,
    44,
    28
  ],
  "0x06A1": [
    69,
    44,
    28
  ],
  "0x06B0": [
    69,
    44,
    28
  ],
  "0x06B1": [
    81,
    56,
    40
  ],
  "0x06B2": [
    81,
    56,
    40
  ],
  "0x06C0": [
    69,
    44,
    28
  ],
  "0x06C1": [
    69,
    44,
    28
  ],
  "0x06D0": [
    48,
    28,
    20
  ],
  "0x06D1": [
    48,
    28,
    20
  ],
  "0x0700": [
    93,
    60,
    32
  ],
  "0x0701": [
    93,
    60,
    32
  ],
  "0x0710": [
    65,
    40,
    12
  ],
  "0x0711": [
    65,
    40,
    12
  ],
  "0x0720": [
    125,
    89,
    65
  ],
  "0x0721": [
    65,
    40,
    12
  ],
  "0x0730": [
    81,
    56,
    40
  ],
  "0x0731": [
    69,
    44,
    28
  ],
  "0x0740": [
    125,
    89,
    65
  ],
  "0x0741": [
    65,
    40,
    12
  ],
  "0x0750": [
    69,
    44,
    28
  ],
  "0x0751": [
    69,
    44,
    28
  ],
  "0x0760": [
    65,
    40,
    12
  ],
  "0x0761": [
    65,
    40,
    12
  ],
  "0x0770": [
    69,
    44,
    28
  ],
  "0x0771": [
    69,
    44,
    28
  ],
  "0x0780": [
    125,
    89,
    65
  ],
  "0x0781": [
    125,
    89,
    65
  ],
  "0x0790": [
    69,
    44,
    28
  ],
  "0x0791": [
    81,
    56,
    40
  ],
  "0x07A0": [
    65,
    40,
    12
  ],
  "0x07A1": [
    65,
    40,
    12
  ],
  "0x07B0": [
    69,
    44,
    28
  ],
  "0x07B1": [
    81,
    56,
    40
  ],
  "0x07C0": [
    125,
    89,
    65
  ],
  "0x07C1": [
    125,
    89,
    65
  ],
  "0x07D0": [
    69,
    44,
    28
  ],
  "0x07D1": [
    69,
    44,
    28
  ],
  "0x0800": [
    73,
    60,
    56
  ],
  "0x0802": [
    113,
    101,
    97
  ],
  "0x0804": [
    113,
    101,
    97
  ],
  "0x0810": [
    73,
    60,
    56
  ],
  "0x0812": [
    113,
    101,
    97
  ],
  "0x0814": [
    113,
    101,
    97
  ],
  "0x0820": [
    73,
    60,
    56
  ],
  "0x0822": [
    113,
    101,
    97
  ],
  "0x0824": [
    113,
    101,
    97
  ],
  "0x0830": [
    121,
    109,
    105
  ],
  "0x0832": [
    121,
    109,
    105
  ],
  "0x0834": [
    113,
    101,
    97
  ],
  "0x0840": [
    121,
    109,
    105
  ],
  "0x0841": [
    121,
    109,
    105
  ],
  "0x0843": [
    24,
    16,
    16
  ],
  "0x0844": [
    44,
    32,
    32
  ],
  "0x0846": [
    113,
    101,
    97
  ],
  "0x0847": [
    113,
    101,
    97
  ],
  "0x0850": [
    121,
    109,
    105
  ],
  "0x0852": [
    121,
    109,
    105
  ],
  "0x0854": [
    113,
    101,
    97
  ],
  "0x0860": [
    121,
    109,
    105
  ],
  "0x0862": [
    56,
    40,
    40
  ],
  "0x0864": [
    113,
    101,
    97
  ],
  "0x0870": [
    121,
    109,
    105
  ],
  "0x0872": [
    121,
    109,
    105
  ],
  "0x0874": [
    113,
    101,
    97
  ],
  "0x0880": [
    121,
    109,
    105
  ],
  "0x0882": [
    121,
    109,
    105
  ],
  "0x0884": [
    113,
    101,
    97
  ],
  "0x0890": [
    121,
    109,
    105
  ],
  "0x0891": [
    121,
    109,
    105
  ],
  "0x0893": [
    113,
    101,
    97
  ],
  "0x0894": [
    121,
    109,
    105
  ],
  "0x0896": [
    65,
    52,
    48
  ],
  "0x0897": [
    113,
    101,
    97
  ],
  "0x08A0": [
    121,
    109,
    105
  ],
  "0x08A2": [
    73,
    60,
    56
  ],
  "0x08A4": [
    65,
    52,
    48
  ],
  "0x08B0": [
    134,
    121,
    117
  ],
  "0x08B2": [
    134,
    121,
    117
  ],
  "0x08B4": [
    105,
    89,
    85
  ],
  "0x08C0": [
    134,
    121,
    117
  ],
  "0x08C2": [
    134,
    121,
    117
  ],
  "0x08C4": [
    113,
    101,
    97
  ],
  "0x08D0": [
    134,
    121,
    117
  ],
  "0x08D2": [
    134,
    121,
    117
  ],
  "0x08D4": [
    113,
    101,
    97
  ],
  "0x0900": [
    85,
    69,
    69
  ],
  "0x0902": [
    69,
    44,
    28
  ],
  "0x0904": [
    113,
    101,
    97
  ],
  "0x0910": [
    85,
    69,
    69
  ],
  "0x0912": [
    69,
    44,
    28
  ],
  "0x0914": [
    113,
    101,
    97
  ],
  "0x0920": [
    85,
    69,
    69
  ],
  "0x0922": [
    69,
    44,
    28
  ],
  "0x0924": [
    113,
    101,
    97
  ],
  "0x0930": [
    85,
    69,
    69
  ],
  "0x0932": [
    125,
    85,
    24
  ],
  "0x0934": [
    113,
    101,
    97
  ],
  "0x0940": [
    85,
    69,
    69
  ],
  "0x0941": [
    85,
    69,
    69
  ],
  "0x0943": [
    125,
    85,
    24
  ],
  "0x0944": [
    56,
    40,
    40
  ],
  "0x0946": [
    113,
    101,
    97
  ],
  "0x0947": [
    113,
    101,
    97
  ],
  "0x0950": [
    85,
    69,
    69
  ],
  "0x0952": [
    125,
    85,
    24
  ],
  "0x0954": [
    113,
    101,
    97
  ],
  "0x0960": [
    85,
    69,
    69
  ],
  "0x0962": [
    65,
    52,
    48
  ],
  "0x0964": [
    113,
    101,
    97
  ],
  "0x0970": [
    85,
    69,
    69
  ],
  "0x0972": [
    56,
    40,
    40
  ],
  "0x0974": [
    113,
    101,
    97
  ],
  "0x0980": [
    73,
    60,
    56
  ],
  "0x0982": [
    56,
    40,
    40
  ],
  "0x0984": [
    113,
    101,
    97
  ],
  "0x0990": [
    73,
    60,
    56
  ],
  "0x0991": [
    73,
    60,
    56
  ],
  "0x0993": [
    36,
    24,
    24
  ],
  "0x0994": [
    73,
    36,
    0
  ],
  "0x0996": [
    65,
    52,
    48
  ],
  "0x0997": [
    113,
    101,
    97
  ],
  "0x09A0": [
    73,
    60,
    56
  ],
  "0x09A2": [
    56,
    40,
    40
  ],
  "0x09A4": [
    65,
    52,
    48
  ],
  "0x09B0": [
    44,
    32,
    32
  ],
  "0x09B2": [
    125,
    85,
    24
  ],
  "0x09B4": [
    105,
    89,
    85
  ],
  "0x09C0": [
    44,
    32,
    32
  ],
  "0x09C2": [
    125,
    85,
    24
  ],
  "0x09C4": [
    113,
    101,
    97
  ],
  "0x09D0": [
    44,
    32,
    32
  ],
  "0x09D2": [
    125,
    85,
    24
  ],
  "0x09D4": [
    113,
    101,
    97
  ]
};

// Export class for tile color mapping
class War2ToolsColorMapper {
  static hasOfficialMapping(tilesetId, tileId) {
    const tileset = this.getTilesetColors(tilesetId);
    const hexKey = `0x${tileId.toString(16).padStart(4, '0').toUpperCase()}`;
    const decimalKey = tileId.toString();
    
    // Check both hex and decimal keys
    if (tileset.hasOwnProperty(hexKey) || tileset.hasOwnProperty(decimalKey)) {
      return true;
    }
    
    // SPECIAL HANDLING for critical boundary tiles that appear in all maps
    if (this.isBoundaryTile(tileId)) {
      return true;
    }
    
    return false;
  }
  
  static getOfficialColor(tilesetId, tileId) {
    const tileset = this.getTilesetColors(tilesetId);
    const hexKey = `0x${tileId.toString(16).padStart(4, '0').toUpperCase()}`;
    const decimalKey = tileId.toString();
    
    // Try exact mappings first
    let color = tileset[hexKey] || tileset[decimalKey];
    if (color) return color;
    
    // SPECIAL HANDLING for boundary/empty tiles
    if (this.isBoundaryTile(tileId)) {
      return this.getBoundaryTileColor(tilesetId, tileId);
    }
    
    return null;
  }
  
  // Handle boundary/empty tiles that don't have explicit mappings
  static isBoundaryTile(tileId) {
    return tileId === 0 || tileId === 1 || tileId === 65535 || tileId === 0xFFFF;
  }
  
  static getBoundaryTileColor(tilesetId, tileId) {
    // Return appropriate base ground color for each tileset
    switch (tilesetId) {
      case 0: // Forest - use forest grass
        return [0x28, 0x55, 0x0c]; // Forest green
      case 1: // Winter - use snow color  
        return [0xf7, 0xf7, 0xf7]; // Snow white
      case 2: // Wasteland - use desert sand
        return [0xc6, 0x9c, 0x6d]; // Desert sand
      case 3: // Swamp - use murky green
        return [0x41, 0x55, 0x34]; // Swamp green
      default:
        return [0x28, 0x55, 0x0c]; // Default forest green
    }
  }
  
  static getOfficialColorHex(tilesetId, tileId) {
    const color = this.getOfficialColor(tilesetId, tileId);
    if (!color) return null;
    
    const r = color[0].toString(16).padStart(2, '0');
    const g = color[1].toString(16).padStart(2, '0');
    const b = color[2].toString(16).padStart(2, '0');
    
    return `#${r}${g}${b}`;
  }
  
  static getTilesetColors(tilesetId) {
    switch (tilesetId) {
      case 0: return FOREST_COLORS;
      case 1: return WINTER_COLORS;
      case 2: return WASTELAND_COLORS;
      case 3: return SWAMP_COLORS;
      default: return FOREST_COLORS;
    }
  }
  
  static colorToHex(color) {
    if (!color || !Array.isArray(color) || color.length !== 3) return '#FF00FF';
    
    const r = color[0].toString(16).padStart(2, '0');
    const g = color[1].toString(16).padStart(2, '0');
    const b = color[2].toString(16).padStart(2, '0');
    
    return `#${r}${g}${b}`;
  }
}

module.exports = War2ToolsColorMapper;
