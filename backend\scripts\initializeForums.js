const mongoose = require('mongoose');
const ForumCategory = require('../models/ForumCategory');
require('dotenv').config();

// Forum categories to create
const forumCategories = [
  {
    name: 'WC Arena General Discussion',
    description: 'General discussion about WC Arena, community topics, and announcements.',
    order: 1
  },
  {
    name: 'WC Arena Feedback',
    description: 'Share your feedback, suggestions, and bug reports to help improve WC Arena.',
    order: 2
  },
  {
    name: 'WC 1/2 Strategy',
    description: 'Discuss strategies, tactics, and gameplay tips for WC: Orcs & Humans and WC II.',
    order: 3
  },
  {
    name: 'WC 3 - Orcs',
    description: 'Strategies, builds, and discussions specific to the Orc race in WC III.',
    order: 4
  },
  {
    name: 'WC 3 - Humans',
    description: 'Strategies, builds, and discussions specific to the Human race in WC III.',
    order: 5
  },
  {
    name: 'WC 3 - Night Elves',
    description: 'Strategies, builds, and discussions specific to the Night Elf race in WC III.',
    order: 6
  },
  {
    name: 'WC 3 - Undead',
    description: 'Strategies, builds, and discussions specific to the Undead race in WC III.',
    order: 7
  }
];

async function initializeForums() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/newsite';
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('✅ Connected to MongoDB');

    // Check if categories already exist
    const existingCategories = await ForumCategory.find();
    console.log(`📋 Found ${existingCategories.length} existing forum categories`);

    // Create categories that don't exist
    let createdCount = 0;
    let skippedCount = 0;

    for (const categoryData of forumCategories) {
      // Check if category with this name already exists
      const existingCategory = await ForumCategory.findOne({ name: categoryData.name });
      
      if (existingCategory) {
        console.log(`⏭️  Skipping "${categoryData.name}" - already exists`);
        skippedCount++;
      } else {
        // Create new category
        const category = new ForumCategory(categoryData);
        await category.save();
        console.log(`✅ Created forum category: "${categoryData.name}"`);
        createdCount++;
      }
    }

    console.log('\n🎉 Forum initialization complete!');
    console.log(`📊 Summary:`);
    console.log(`   - Created: ${createdCount} categories`);
    console.log(`   - Skipped: ${skippedCount} categories (already existed)`);
    console.log(`   - Total: ${createdCount + skippedCount} categories processed`);

    // List all categories
    const allCategories = await ForumCategory.find().sort({ order: 1 });
    console.log('\n📋 Current forum categories:');
    allCategories.forEach((category, index) => {
      console.log(`   ${index + 1}. ${category.name} (Order: ${category.order})`);
    });

  } catch (error) {
    console.error('❌ Error initializing forums:', error);
    process.exit(1);
  } finally {
    // Close MongoDB connection
    await mongoose.connection.close();
    console.log('\n🔌 Disconnected from MongoDB');
    process.exit(0);
  }
}

// Run the initialization
if (require.main === module) {
  console.log('🎮 Warcraft Arena Forum Initialization');
  console.log('=====================================\n');
  initializeForums();
}

module.exports = initializeForums; 