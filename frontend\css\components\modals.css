/* ==========================================================================
   WARCRAFT ARENA - STREAMLINED MODAL SYSTEM
   Universal modal styling that works across the entire project
   ========================================================================== */

/* Reset and base modal setup */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(4px);
  z-index: 1000;
  display: none;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* Active modal states */
.modal.show,
.modal.active,
.modal[data-visible="true"] {
  display: flex;
  opacity: 1;
}

/* Modal content container */
.modal-content {
  background: linear-gradient(135deg, 
    rgba(15, 23, 42, 0.98), 
    rgba(30, 41, 59, 0.95));
  border: 2px solid var(--primary-gold, #D4AF37);
  border-radius: var(--radius-xl, 16px);
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.8),
    0 0 30px rgba(212, 175, 55, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  max-width: 90vw;
  max-height: 90vh;
  width: auto;
  position: relative;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.modal.show .modal-content,
.modal.active .modal-content,
.modal[data-visible="true"] .modal-content {
  transform: scale(1);
}

/* Modal header */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem 1rem 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(135deg, 
    rgba(212, 175, 55, 0.1), 
    rgba(212, 175, 55, 0.05));
}

.modal-title {
  font-family: var(--font-display, 'Cinzel', serif);
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-gold, #D4AF37);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

/* Modal body */
.modal-body {
  padding: 2rem;
  overflow-y: auto;
  max-height: calc(90vh - 120px);
  color: var(--neutral-100, #f8fafc);
}

/* Modal footer */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1rem 2rem 1.5rem 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(135deg, 
    rgba(0, 0, 0, 0.2), 
    rgba(0, 0, 0, 0.1));
}

/* Close button */
.close-modal,
.modal-close,
.close {
  position: absolute;
  top: 15px;
  right: 20px;
  background: rgba(220, 38, 38, 0.1);
  border: 2px solid rgba(220, 38, 38, 0.3);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #dc2626;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 1001;
  text-decoration: none;
  line-height: 1;
}

.close-modal:hover,
.modal-close:hover,
.close:hover {
  background: rgba(220, 38, 38, 0.3);
  border-color: rgba(220, 38, 38, 0.6);
  color: #ffffff;
  transform: scale(1.1) rotate(90deg);
  box-shadow: 0 4px 15px rgba(220, 38, 38, 0.4);
}

/* Modal size variants */
.modal-sm .modal-content {
  max-width: 400px;
}

.modal-md .modal-content {
  max-width: 600px;
}

.modal-lg .modal-content {
  max-width: 900px;
}

.modal-xl .modal-content {
  max-width: 1200px;
}

.modal-fullscreen .modal-content {
  max-width: 95vw;
  max-height: 95vh;
  width: 95vw;
  height: 95vh;
}

/* Modal loading state */
.modal-loading {
  pointer-events: none;
}

.modal-loading .modal-content {
  opacity: 0.7;
}

.modal-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40px;
  height: 40px;
  border: 4px solid rgba(212, 175, 55, 0.3);
  border-top: 4px solid var(--primary-gold, #D4AF37);
  border-radius: 50%;
  animation: modalSpin 1s linear infinite;
  transform: translate(-50%, -50%);
  z-index: 1002;
}

@keyframes modalSpin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Prevent body scroll when modal is open */
body.modal-open {
  overflow: hidden;
}

/* Tab system for modals */
.modal-tabs {
  display: flex;
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 1.5rem;
  gap: 0.5rem;
}

.modal-tab {
  background: transparent;
  border: none;
  padding: 0.75rem 1.5rem;
  color: var(--neutral-400, #94a3b8);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
  font-size: 0.875rem;
}

.modal-tab:hover {
  color: var(--neutral-200, #e2e8f0);
  background: rgba(255, 255, 255, 0.05);
}

.modal-tab.active {
  color: var(--primary-gold, #D4AF37);
  border-bottom-color: var(--primary-gold, #D4AF37);
  background: rgba(212, 175, 55, 0.1);
}

.modal-tab-content {
  display: none;
}

.modal-tab-content.active {
  display: block;
}

/* Player Modal Specific Styles */
.player-modal-container {
  min-height: 400px;
}

.player-modal-header {
  background: linear-gradient(135deg, 
    rgba(212, 175, 55, 0.1), 
    rgba(212, 175, 55, 0.05));
  border-radius: var(--radius-lg, 12px);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(212, 175, 55, 0.2);
}

.player-info {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.player-name {
  font-family: var(--font-display, 'Cinzel', serif);
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--primary-gold, #D4AF37);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.player-rank {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.rank-icon {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.rank-name {
  font-weight: 600;
  color: var(--neutral-200, #e2e8f0);
}

.player-mmr {
  margin-left: auto;
  text-align: right;
}

.mmr-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-gold, #D4AF37);
  display: block;
}

.mmr-label {
  font-size: 0.875rem;
  color: var(--neutral-400, #94a3b8);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg, 12px);
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(212, 175, 55, 0.3);
  transform: translateY(-2px);
}

.stat-icon {
  font-size: 2rem;
  color: var(--primary-gold, #D4AF37);
  margin-bottom: 0.5rem;
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--neutral-100, #f8fafc);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--neutral-400, #94a3b8);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Matches Content */
.matches-content {
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--radius-lg, 12px);
  overflow: hidden;
}

.matches-header {
  display: grid;
  grid-template-columns: 80px 1fr 150px 120px;
  gap: 1rem;
  padding: 1rem;
  background: rgba(212, 175, 55, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-weight: 600;
  color: var(--primary-gold, #D4AF37);
  text-transform: uppercase;
  font-size: 0.875rem;
  letter-spacing: 0.5px;
}

.matches-list {
  max-height: 300px;
  overflow-y: auto;
}

.match-row {
  display: grid;
  grid-template-columns: 80px 1fr 150px 120px;
  gap: 1rem;
  padding: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
  align-items: center;
}

.match-row:hover {
  background: rgba(255, 255, 255, 0.05);
}

.match-row.win {
  border-left: 4px solid #10b981;
}

.match-row.loss {
  border-left: 4px solid #ef4444;
}

.match-result {
  font-weight: 700;
  text-align: center;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-md, 8px);
  font-size: 0.875rem;
}

.match-row.win .match-result {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.match-row.loss .match-result {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.match-opponent {
  font-weight: 500;
  color: var(--neutral-100, #f8fafc);
}

.match-map, .match-date {
  color: var(--neutral-300, #cbd5e1);
  font-size: 0.875rem;
}

/* Performance Content */
.performance-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.performance-section h4 {
  font-family: var(--font-display, 'Cinzel', serif);
  color: var(--primary-gold, #D4AF37);
  font-size: 1.25rem;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.performance-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.performance-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md, 8px);
  transition: all 0.3s ease;
}

.performance-item:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(212, 175, 55, 0.3);
}

.perf-name {
  font-weight: 600;
  color: var(--neutral-100, #f8fafc);
  text-transform: capitalize;
}

.perf-stats {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
}

.perf-games {
  color: var(--neutral-400, #94a3b8);
}

.perf-winrate {
  color: var(--primary-gold, #D4AF37);
  font-weight: 600;
}

/* Loading and Error States */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--neutral-400, #94a3b8);
  font-size: 1.125rem;
}

.loading::before {
  content: '';
  width: 24px;
  height: 24px;
  border: 3px solid rgba(212, 175, 55, 0.3);
  border-top: 3px solid var(--primary-gold, #D4AF37);
  border-radius: 50%;
  animation: modalSpin 1s linear infinite;
  margin-right: 1rem;
}

.error-message {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #ef4444;
  font-size: 1.125rem;
  text-align: center;
}

.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--neutral-400, #94a3b8);
  font-size: 1.125rem;
  text-align: center;
}

/* Modal form styling */
.modal .form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.modal .form-group {
  margin-bottom: 1rem;
}

.modal .form-group label {
  display: block;
  color: var(--primary-gold, #D4AF37);
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.modal .form-group input,
.modal .form-group textarea,
.modal .form-group select {
  width: 100%;
  background: rgba(15, 23, 42, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-md, 8px);
  padding: 0.75rem;
  color: var(--neutral-100, #f8fafc);
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.modal .form-group input:focus,
.modal .form-group textarea:focus,
.modal .form-group select:focus {
  outline: none;
  border-color: var(--primary-gold, #D4AF37);
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.2);
}

/* Modal buttons */
.modal .btn {
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-md, 8px);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  border: none;
}

.modal .btn-primary {
  background: linear-gradient(135deg, var(--primary-gold, #D4AF37), #B8860B);
  color: #000;
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
}

.modal .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
}

.modal .btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: var(--neutral-300, #cbd5e1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.modal .btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  color: var(--neutral-100, #f8fafc);
}

.modal .btn-danger {
  background: linear-gradient(135deg, #dc2626, #991b1b);
  color: #ffffff;
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

.modal .btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(220, 38, 38, 0.4);
}

/* Responsive design */
@media (max-width: 768px) {
  .modal-content {
    max-width: 95vw;
    max-height: 95vh;
    margin: 1rem;
  }
  
  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1rem;
  }
  
  .modal-title {
    font-size: 1.25rem;
  }
  
  .modal .form-grid {
    grid-template-columns: 1fr;
  }
  
  .modal-tabs {
    flex-wrap: wrap;
  }
  
  .modal-tab {
    flex: 1;
    min-width: 100px;
  }

  .player-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .player-mmr {
    margin-left: 0;
    text-align: left;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .matches-header,
  .match-row {
    grid-template-columns: 60px 1fr 100px 80px;
    gap: 0.5rem;
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .close-modal,
  .modal-close,
  .close {
    top: 10px;
    right: 15px;
    width: 32px;
    height: 32px;
    font-size: 16px;
  }
  
  .modal-footer {
    flex-direction: column;
  }
  
  .modal .btn {
    width: 100%;
    justify-content: center;
  }

  .matches-header,
  .match-row {
    grid-template-columns: 1fr 80px;
    gap: 0.5rem;
  }

  .match-map,
  .match-date {
    display: none;
  }
}

/* Animation improvements */
@media (prefers-reduced-motion: reduce) {
  .modal,
  .modal-content,
  .close-modal,
  .modal-close,
  .close {
    transition: none;
  }
  
  .modal-loading::after {
    animation: none;
  }
} 