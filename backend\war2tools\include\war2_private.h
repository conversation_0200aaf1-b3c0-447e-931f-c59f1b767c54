/*
 * Copyright (c) 2014-2016 <PERSON>'h
 *
 * Permission is hereby granted, free of charge, to any person obtaining a
 * copy of this software and associated documentation files (the "Software"),
 * to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.
 */

#ifndef _WAR2_PRIVATE_H_
#define _WAR2_PRIVATE_H_


#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <stdint.h>
#include <time.h>

#include "pud.h"
#include "pud_private.h"
#include "debug.h"
#include "war2.h"
#include "common.h"

struct _War2_Data
{
   Pud_Mmap *mem_map;

   uint32_t     magic;
   uint16_t     fid;

   uint16_t        entries_count;
   unsigned char **entries;

   Pud_Color forest[WAR2_PALETTE_SIZE];
   Pud_Color winter[WAR2_PALETTE_SIZE];
   Pud_Color wasteland[WAR2_PALETTE_SIZE];
   Pud_Color swamp[WAR2_PALETTE_SIZE];

   int verbose;
};

#define WAR2_TRAP_SETUP(W2) COMMON_TRAP_SETUP(W2->mem_map)
#define WAR2_READ8(W2) common_read8(w2->mem_map)
#define WAR2_READ16(W2) common_read16(w2->mem_map)
#define WAR2_READ32(W2) common_read32(w2->mem_map)

#define WAR2_VERBOSE(w2, lvl, msg, ...) \
   do { \
      if (w2->verbose >= lvl) { \
         fprintf(stdout, "-- " msg "\n", ## __VA_ARGS__); \
      } \
   } while (0)



#endif /* ! _WAR2_PRIVATE_H_ */
