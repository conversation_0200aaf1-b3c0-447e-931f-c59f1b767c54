/**
 * Game Tabs Functionality
 * Handles switching between different game tabs (War1, War2, War3) across pages
 */

console.log('🎮 Game Tabs JS loading...');

// Prevent redeclaration if already loaded
if (typeof window.GameTabsManager !== 'undefined') {
  console.log('🎮 GameTabsManager already loaded, skipping redeclaration');
} else {

class GameTabsManager {
  constructor() {
    this.currentGame = 'war2'; // Default to War2
    this.gameData = {
      'war2': {
        title: 'Warcraft II Ladder',
        subtitle: '',
        icon: 'fas fa-shield-alt',
        className: 'war2'
      },
      'war3': {
        title: 'Warcraft III Ladder',
        subtitle: '',
        icon: 'fas fa-dragon',
        className: 'war3'
      }
    };
    this.init();
  }

  init() {
    this.setupEventListeners();
    this.restoreSelectedGame();
  }

  setupEventListeners() {
    // Event delegation for game toggle buttons (corrected ID)
    document.addEventListener('click', (e) => {
      const gameToggleBtn = e.target.closest('#game-toggle-btn');
      if (gameToggleBtn) {
        e.preventDefault();
        this.toggleGame();
      }
    });

    // Listen for click on current game button (legacy support)
    document.addEventListener('click', (e) => {
      if (e.target.closest('#current-game-btn')) {
        e.preventDefault();
        console.log('🎮 Current game button clicked - toggling game!');
        this.toggleGame();
      }
    });

    // Listen for legacy game tab clicks (if any exist)
    document.addEventListener('click', (e) => {
      const gameTab = e.target.closest('[data-game]');
      if (gameTab) {
        e.preventDefault();
        const gameId = gameTab.dataset.game;
        console.log(`🎮 Game tab clicked: ${gameId}`);
        this.switchGame(gameId);
      }
    });

    console.log('🎮 Event listeners setup complete');
  }

  toggleGame() {
    const nextGame = this.currentGame === 'war2' ? 'war3' : 'war2';
    this.switchGame(nextGame);
  }

  switchGame(gameId) {
    console.log(`🎮 Switching to game: ${gameId}`);
    
    // Update current game
    this.currentGame = gameId;
    
    // Update UI
    this.updateGameDisplay();
    
    // Store selection
    localStorage.setItem('selectedGame', gameId);
    
    // Dispatch event for other components to listen to
    window.dispatchEvent(new CustomEvent('gameTabChanged', {
      detail: { 
        game: gameId,
        gameDisplay: this.getGameDisplayName(gameId)
      }
    }));
    
    // Trigger page-specific functionality
    this.triggerPageSpecificChanges(gameId);
  }

  updateGameDisplay() {
    const currentGameData = this.gameData[this.currentGame];
    const otherGame = this.currentGame === 'war2' ? 'war3' : 'war2';
    const otherGameData = this.gameData[otherGame];

    // Update game toggle button (corrected ID)
    const gameToggleBtn = document.getElementById('game-toggle-btn');
    const currentGameIcon = document.getElementById('current-game-icon');
    const currentGameTitle = document.getElementById('current-game-title');
    const toggleText = document.getElementById('toggle-text');
    const reportMatchText = document.getElementById('report-match-text');

    if (gameToggleBtn && currentGameData) {
      // Update button class for theming
      gameToggleBtn.className = `game-toggle-btn ${currentGameData.className}`;
      
      // Update icon
      if (currentGameIcon) {
        currentGameIcon.className = `fas ${currentGameData.icon}`;
      }
      
      // Update title
      if (currentGameTitle) {
        currentGameTitle.textContent = currentGameData.title;
      }
      
      // Update toggle text
      if (toggleText) {
        toggleText.textContent = `Click to Switch to ${otherGameData.title}`;
      }
      
      // Update report match button text
      if (reportMatchText) {
        const gameAbbrev = this.currentGame === 'war2' ? 'WC II' : 'WC III';
        reportMatchText.textContent = `REPORT ${gameAbbrev} MATCH`;
      }
      
      console.log(`🎮 Updated game display to: ${currentGameData.title}`);
    } else {
      console.warn('⚠️ Game toggle button or game data not found');
    }

    // Update any game-specific content
    this.updateGameContent();
  }

  updateGameContent() {
    // Update game-specific content based on current game
    console.log(`🎮 Updating content for game: ${this.currentGame}`);
    
    // This function can be extended to update page-specific content
    // For now, it's a placeholder to prevent the error
  }

  triggerPageSpecificChanges(gameId) {
    const currentPage = window.location.pathname;
    
    // Page-specific functionality based on game selection
    if (currentPage.includes('ladder.html')) {
      this.handleLadderGameChange(gameId);
    } else if (currentPage.includes('tournaments.html')) {
      this.handleTournamentGameChange(gameId);
    } else if (currentPage.includes('content.html')) {
      this.handleContentGameChange(gameId);
    } else if (currentPage.includes('maps.html')) {
      this.handleMapsGameChange(gameId);
    }
  }

  handleLadderGameChange(gameId) {
    console.log(`🏆 Ladder game changed to: ${gameId}`);
    // Trigger ladder data reload for specific game
    if (typeof window.loadLadderData === 'function') {
      window.loadLadderData(gameId);
    }
    
    // Update report match button text
    if (typeof window.updateReportMatchButton === 'function') {
      window.updateReportMatchButton(gameId);
    }
  }

  handleTournamentGameChange(gameId) {
    console.log(`🏆 Tournament game changed to: ${gameId}`);
    // Trigger tournament data reload for specific game
    if (typeof window.loadTournamentData === 'function') {
      window.loadTournamentData(gameId);
    }
  }

  handleContentGameChange(gameId) {
    console.log(`📺 Content game changed to: ${gameId}`);
    // Update content creator data
    if (typeof window.loadContentCreators === 'function') {
      window.loadContentCreators(gameId);
    }
  }

  handleMapsGameChange(gameId) {
    console.log(`🗺️ Maps game changed to: ${gameId}`);
    
    // Save maps page preference
    localStorage.setItem('mapsPageGame', gameId);
    console.log(`💾 Saved maps page preference: ${gameId}`);
    
    // Update maps data
    if (typeof window.loadMapsData === 'function') {
      window.loadMapsData(gameId);
    }
  }

  restoreSelectedGame() {
    const currentPage = window.location.pathname;
    let gameToRestore = this.currentGame; // Default to class default
    
    // Check for page-specific defaults
    if (currentPage.includes('ladder.html')) {
      // Ladder page should use saved game preference, but default to war2
      const savedGame = localStorage.getItem('selectedGame');
      if (savedGame && this.gameData[savedGame]) {
        gameToRestore = savedGame;
      } else {
        gameToRestore = 'war2'; // Default to Warcraft 2
      }
    } else if (currentPage.includes('maps.html')) {
      // Maps page should use saved maps preference, but default to war2
      const savedMapsGame = localStorage.getItem('mapsPageGame');
      if (savedMapsGame && this.gameData[savedMapsGame]) {
        gameToRestore = savedMapsGame;
        console.log(`🗺️ Restoring saved maps game preference: ${savedMapsGame}`);
      } else {
        gameToRestore = 'war2'; // Default to Warcraft 2
      }
    } else {
      // For other pages, use saved game if available
      const savedGame = localStorage.getItem('selectedGame');
      if (savedGame && this.gameData[savedGame]) {
        gameToRestore = savedGame;
      }
    }
    
    console.log(`🎮 Restoring saved game: ${gameToRestore}`);
    this.switchGame(gameToRestore);
  }

  getGameDisplayName(gameId) {
    return this.gameData[gameId]?.title || gameId;
  }

  getCurrentGame() {
    return this.currentGame;
  }

  // Public API for other components
  getGameTypeForAPI() {
    // Map game tab IDs to API game types
    const gameTypeMapping = {
      'war2': 'warcraft2',
      'war3': 'warcraft3'
    };
    return gameTypeMapping[this.currentGame] || 'warcraft2';
  }
}

// Make the class globally available
window.GameTabsManager = GameTabsManager;

// Initialize when DOM is ready
let gameTabsManager;

function initGameTabs() {
  // Check for new streamlined design elements OR old game tab elements
  const hasNewDesign = document.getElementById('current-game-btn') && document.getElementById('game-toggle-btn');
  const hasOldDesign = document.querySelectorAll('.game-tab').length > 0;
  
  if (!gameTabsManager && (hasNewDesign || hasOldDesign)) {
    gameTabsManager = new GameTabsManager();
    console.log('✅ Game Tabs Manager initialized');
    window.gameTabsManager = gameTabsManager; // Make globally available
  }
}

// Export for global access
window.initGameTabs = initGameTabs;

// Auto-initialize on DOM content loaded
document.addEventListener('DOMContentLoaded', initGameTabs);

// Also try to initialize after a short delay in case DOM is already loaded
setTimeout(initGameTabs, 100);

console.log('✅ Game Tabs JS loaded');

} // End of else block 