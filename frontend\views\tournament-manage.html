<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Manage Tournament - WC Arena</title>
  <link rel="stylesheet" href="/css/warcraft-app-modern.css" />
  <link rel="stylesheet" href="/css/tournaments.css" />
  <link rel="stylesheet" href="/css/bracket.css" />
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-brands-400.woff2" as="font" type="font/woff2" crossorigin>
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
  <div id="navbar-container"></div>

  <main>
    <div class="tournament-manage-container">
      <h1>Manage Tournament</h1>

      <div id="tournament-details">
        <!-- Tournament details will be loaded here -->
      </div>

      <div class="tournament-manage-tabs">
        <button class="tab-btn active" data-tab="overview">Overview</button>
        <button class="tab-btn" data-tab="participants">Participants</button>
        <button class="tab-btn" data-tab="matches">Matches</button>
        <button class="tab-btn" data-tab="brackets">Brackets</button>
        <button class="tab-btn" data-tab="settings">Settings</button>
      </div>

      <div class="tournament-manage-content">
        <!-- Overview Tab -->
        <div class="tab-content active" id="overview-tab">
          <div class="tournament-status-controls">
            <h2>Tournament Status</h2>
            <div class="status-controls">
              <select id="tournament-status">
                <option value="draft">Draft</option>
                <option value="registration">Registration Open</option>
                <option value="in_progress">In Progress</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
              <button id="update-status-btn" class="btn btn-primary">Update Status</button>
            </div>
          </div>

          <div class="tournament-stats">
            <div class="stat-card">
              <div class="stat-title">Participants</div>
              <div class="stat-value" id="participant-count">0</div>
            </div>
            <div class="stat-card">
              <div class="stat-title">Matches</div>
              <div class="stat-value" id="match-count">0</div>
            </div>
            <div class="stat-card">
              <div class="stat-title">Completed</div>
              <div class="stat-value" id="completed-match-count">0</div>
            </div>
          </div>

          <div class="tournament-actions">
            <button id="publish-tournament-btn" class="btn btn-success">Publish Tournament</button>
            <button id="start-tournament-btn" class="btn btn-primary">Start Tournament</button>
            <button id="generate-bracket-btn" class="btn btn-secondary">Generate Bracket</button>
            <button id="view-bracket-btn" class="btn btn-secondary">View Bracket</button>
          </div>
        </div>

        <!-- Participants Tab -->
        <div class="tab-content" id="participants-tab">
          <div class="participants-controls">
            <h2>Participants</h2>
            <div class="participants-actions">
              <button id="add-participant-btn" class="btn btn-primary">Add Participant</button>
              <button id="seed-participants-btn" class="btn btn-secondary">Seed Participants</button>
            </div>
          </div>

          <div class="participants-list" id="participants-list">
            <!-- Participants will be loaded here -->
            <div class="loading">Loading participants...</div>
          </div>
        </div>

        <!-- Matches Tab -->
        <div class="tab-content" id="matches-tab">
          <div class="matches-controls">
            <h2>Tournament Matches</h2>
            <div class="matches-actions">
              <button id="create-match-btn" class="btn btn-primary">Create Match</button>
              <button id="auto-generate-matches-btn" class="btn btn-secondary">Auto-Generate Matches</button>
            </div>
          </div>

          <div class="matches-list" id="matches-list">
            <!-- Matches will be loaded here -->
            <div class="loading">Loading matches...</div>
          </div>
        </div>

        <!-- Brackets Tab -->
        <div class="tab-content" id="brackets-tab">
          <div class="brackets-header">
            <h3>Tournament Bracket</h3>
          </div>
          <div id="bracket-container" class="bracket-container">
            Loading bracket...
          </div>
        </div>

        <!-- Settings Tab -->
        <div class="tab-content" id="settings-tab">
          <h2>Tournament Settings</h2>

          <form id="tournament-settings-form">
            <div class="form-group">
              <label for="tournament-name">Tournament Name</label>
              <input type="text" id="tournament-name" name="name" required>
            </div>

            <div class="form-group">
              <label for="tournament-description">Description</label>
              <textarea id="tournament-description" name="description" rows="4"></textarea>
            </div>

            <div class="form-group">
              <label for="tournament-type">Tournament Type</label>
              <select id="tournament-type" name="type">
                <option value="single_elimination">Single Elimination</option>
                <option value="double_elimination">Double Elimination</option>
                <option value="round_robin">Round Robin</option>
                <option value="swiss">Swiss</option>
              </select>
            </div>

            <div class="form-group">
              <label for="max-participants">Maximum Participants</label>
              <input type="number" id="max-participants" name="maxParticipants" min="2" max="128">
            </div>

            <div class="form-group">
              <label for="start-date">Start Date</label>
              <input type="datetime-local" id="start-date" name="startDate">
            </div>

            <div class="form-group">
              <label for="end-date">End Date</label>
              <input type="datetime-local" id="end-date" name="endDate">
            </div>

            <div class="form-actions">
              <button type="submit" class="btn btn-primary">Save Settings</button>
              <button type="button" id="delete-tournament-btn" class="btn btn-danger">Delete Tournament</button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Add Participant Modal -->
    <div id="add-participant-modal" class="modal">
      <div class="modal-content">
        <span class="close-modal">&times;</span>
        <h2>Add Participant</h2>
        <form id="add-participant-form">
          <div class="form-group">
            <label for="player-name">Player Name</label>
            <input type="text" id="player-name" name="playerName" required>
          </div>
          <button type="submit" class="btn btn-primary">Add Participant</button>
        </form>
      </div>
    </div>

    <!-- Create Match Modal -->
    <div id="create-match-modal" class="modal">
      <div class="modal-content">
        <span class="close-modal">&times;</span>
        <h2>Create Match</h2>
        <form id="create-match-form">
          <div class="form-group">
            <label for="player1">Player 1</label>
            <select id="player1" name="player1" required></select>
          </div>
          <div class="form-group">
            <label for="player2">Player 2</label>
            <select id="player2" name="player2" required></select>
          </div>
          <div class="form-group">
            <label for="match-round">Round</label>
            <input type="number" id="match-round" name="round" min="1" value="1">
          </div>
          <button type="submit" class="btn btn-primary">Create Match</button>
        </form>
      </div>
    </div>
  </main>

  <div id="footer-container"></div>

  <!-- Scripts -->
  <script src="/js/utils.js"></script>
  <script src="/js/main.js"></script>
  <script src="/js/tournaments-consolidated.js"></script>
</body>
</html>
