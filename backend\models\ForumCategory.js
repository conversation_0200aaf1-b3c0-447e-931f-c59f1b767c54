const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Forum Category Schema
 * Represents a category in the forum
 */
const ForumCategorySchema = new Schema({
  // Category name
  name: {
    type: String,
    required: true,
    trim: true
  },
  
  // Category description
  description: {
    type: String,
    required: true,
    trim: true
  },
  
  // Category order (for sorting)
  order: {
    type: Number,
    default: 0
  },
  
  // Number of topics in this category
  topicCount: {
    type: Number,
    default: 0
  },
  
  // Number of posts in this category
  postCount: {
    type: Number,
    default: 0
  },
  
  // Last post in this category
  lastPost: {
    topicId: {
      type: Schema.Types.ObjectId,
      ref: 'ForumTopic'
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    username: String,
    date: Date
  },
  
  // Creation date
  createdAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Create indexes for faster lookups
ForumCategorySchema.index({ order: 1 });

// Method to recalculate category stats
ForumCategorySchema.methods.recalculateStats = async function() {
  const ForumTopic = mongoose.model('ForumTopic');
  const ForumPost = mongoose.model('ForumPost');

  // Count topics in this category
  const topicCount = await ForumTopic.countDocuments({ category: this._id });
  
  // Count posts in topics of this category
  const topics = await ForumTopic.find({ category: this._id });
  const topicIds = topics.map(topic => topic._id);
  const postCount = await ForumPost.countDocuments({ topic: { $in: topicIds } });

  // Get the last post
  const lastPost = await ForumPost.findOne({ topic: { $in: topicIds } })
    .sort({ createdAt: -1 })
    .populate('topic', 'title')
    .populate('author.userId', 'username');

  // Update the category
  this.topicCount = topicCount;
  this.postCount = postCount;
  if (lastPost) {
    this.lastPost = {
      topicId: lastPost.topic._id,
      userId: lastPost.author.userId._id,
      username: lastPost.author.username,
      date: lastPost.createdAt
    };
  }
  
  await this.save();
  return this;
};

module.exports = mongoose.model('ForumCategory', ForumCategorySchema);
