/* ===== MODAL STYLING ===== */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  animation-duration: 0.3s;
  animation-fill-mode: both;
}

.modal.opening {
  animation-name: fadeIn;
}

.modal.closing {
  animation-name: fadeOut;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

.modal-content {
  position: relative;
  margin: 5% auto;
  width: 90%;
  max-width: 1200px;
  max-height: 80vh;
  background: rgba(20, 20, 20, 0.95);
  border-radius: 20px;
  border: 1px solid rgba(255, 215, 0, 0.2);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.8);
  overflow: hidden;
  animation-duration: 0.3s;
  animation-fill-mode: both;
}

.modal.opening .modal-content {
  animation-name: slideIn;
}

.modal.closing .modal-content {
  animation-name: slideOut;
}

@keyframes slideIn {
  from { 
    transform: translateY(-50px) scale(0.9);
    opacity: 0;
  }
  to { 
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

@keyframes slideOut {
  from { 
    transform: translateY(0) scale(1);
    opacity: 1;
  }
  to { 
    transform: translateY(-50px) scale(0.9);
    opacity: 0;
  }
}

.close-modal {
  position: absolute;
  top: 15px;
  right: 20px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 1001;
}

.close-modal:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ffd700;
  transform: rotate(90deg);
}

.modal-body {
  padding: 2rem;
  max-height: 60vh;
  overflow-y: auto;
}

/* ===== COMPACT MAP DETAILS MODAL ===== */
.map-details-epic {
  color: white;
  font-family: 'Inter', sans-serif;
  line-height: 1.4;
}

/* Compact Header Layout */
.map-details-header-epic {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
  align-items: start;
}

/* Strategic Map Image Container with Fixed Overlays */
.map-image-container {
  position: relative;
  width: 100%;
  max-width: 400px;
  border-radius: 12px;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.05));
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 215, 0, 0.2);
}

.map-details-thumbnail-epic {
  width: 100%;
  height: auto;
  display: block;
  aspect-ratio: 1;
  object-fit: contain;
  transition: all 0.3s ease;
  background: rgba(0, 0, 0, 0.8);
}

.map-details-thumbnail-epic:hover {
  transform: scale(1.01);
}

/* Enhanced Strategic Overlays with Improved Positioning */
.strategic-overlays,
.enhanced-strategic-overlays {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 15;
}

/* Enhanced Goldmine Overlays */
.enhanced-goldmine-overlay {
  position: absolute;
  border-radius: 50%;
  pointer-events: all;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 20;
  transform-origin: center;
}

.enhanced-goldmine-overlay:hover {
  z-index: 25;
}

/* Enhanced Starting Position Overlays */
.enhanced-starting-position-overlay {
  position: absolute;
  border-radius: 6px;
  pointer-events: all;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 20;
  transform-origin: center;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.enhanced-starting-position-overlay:hover {
  z-index: 25;
}

/* Enhanced Tooltip System */
.enhanced-tooltip {
  position: fixed;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(20, 20, 30, 0.95));
  backdrop-filter: blur(10px);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 12px;
  line-height: 1.4;
  z-index: 10000;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 215, 0, 0.3);
  max-width: 250px;
  animation: tooltipFadeIn 0.2s ease;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.enhanced-tooltip .tooltip-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid rgba(255, 215, 0, 0.3);
  font-weight: bold;
}

.enhanced-tooltip .tooltip-header.goldmine-header {
  color: #FFD700;
}

.enhanced-tooltip .tooltip-header.player-header {
  color: #87CEEB;
}

.enhanced-tooltip .tooltip-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.enhanced-tooltip .tooltip-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
}

.enhanced-tooltip .stat-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 11px;
}

.enhanced-tooltip .stat-value {
  font-weight: bold;
  font-size: 12px;
}

.enhanced-tooltip .gold-value {
  color: #FFD700;
}

.enhanced-tooltip .category-very-high { color: #FF00FF; }
.enhanced-tooltip .category-high { color: #FFD700; }
.enhanced-tooltip .category-medium { color: #FFA500; }
.enhanced-tooltip .category-low { color: #87CEEB; }
.enhanced-tooltip .category-very-low { color: #808080; }

.enhanced-tooltip .race-human { color: #4A90E2; }
.enhanced-tooltip .race-orc { color: #E74C3C; }
.enhanced-tooltip .race-unknown { color: #BDC3C7; }

/* Compact Map Info Panel */
.map-info-epic {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.map-title-epic {
  font-family: 'Cinzel', serif;
  font-size: 1.6rem;
  font-weight: 700;
  color: #ffd700;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
  line-height: 1.2;
}

/* Compact Meta Information Grid */
.map-meta-epic {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.meta-item-epic {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 0.85rem;
  transition: all 0.3s ease;
}

.meta-item-epic:hover {
  background: rgba(255, 215, 0, 0.1);
  border-color: rgba(255, 215, 0, 0.3);
}

.meta-item-epic i {
  color: #ffd700;
  font-size: 0.9rem;
  min-width: 14px;
}

/* Compact Rating Display */
.map-rating-epic {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.08);
  padding: 0.75rem;
  margin-bottom: 1rem;
}

.rating-section {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.75rem;
  flex-wrap: wrap;
}

.stars-epic {
  font-size: 1.1rem;
  color: #ffd700;
}

.rating-text-epic {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.85rem;
  font-weight: 500;
}

.login-hint {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.75rem;
  font-style: italic;
  margin-top: 0.25rem;
}

.stats-section {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 0.75rem;
}

.downloads-epic,
.matches-epic {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.85rem;
}

.downloads-epic i,
.matches-epic i {
  color: #ffd700;
}

/* Action Buttons Under Rating */
.map-actions-top-epic {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.map-actions-top-epic .btn-epic {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.8rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.4rem;
  font-family: 'Inter', sans-serif;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.map-actions-top-epic .btn-epic::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.map-actions-top-epic .btn-epic:hover::before {
  left: 100%;
}

.map-actions-top-epic .btn-primary-epic {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #000;
  box-shadow: 0 3px 12px rgba(255, 215, 0, 0.3);
}

.map-actions-top-epic .btn-primary-epic:hover {
  background: linear-gradient(135deg, #ffed4e, #ffd700);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
}

.map-actions-top-epic .btn-secondary-epic {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.map-actions-top-epic .btn-secondary-epic:hover {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.1));
  color: #ffd700;
  border-color: rgba(255, 215, 0, 0.3);
  transform: translateY(-1px);
}

/* Interactive Star Rating Styles */
.interactive-stars {
  display: inline-flex;
  gap: 2px;
}

.interactive-stars .rating-star {
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
}

.interactive-stars .rating-star:hover {
  color: #ffd700;
  transform: scale(1.1);
}

.interactive-stars .rating-star.fas {
  color: #ffd700;
}

.rating-stars-large .rating-star {
  font-size: 1.5rem;
}

.rating-stars-small .rating-star {
  font-size: 0.9rem;
}

/* Remove old action section styles since we moved the buttons */
.map-actions-section-epic {
  display: none;
}

/* Compact Description Section */
.map-description-section-epic {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.08);
}

.description-header-epic {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.description-header-epic h3 {
  font-family: 'Cinzel', serif;
  color: #ffd700;
  margin: 0;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.description-content-epic {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
  font-size: 0.9rem;
  max-height: 80px;
  overflow-y: auto;
}

/* Compact Strategic Analysis */
.strategic-analysis-compact {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  padding: 1rem;
  border: 1px solid rgba(255, 215, 0, 0.1);
}

.analysis-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  padding: 0.75rem;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  background: rgba(255, 215, 0, 0.08);
  border-color: rgba(255, 215, 0, 0.2);
  transform: translateY(-1px);
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background: rgba(255, 215, 0, 0.2);
  color: #ffd700;
  font-size: 0.9rem;
}

.stat-info {
  flex: 1;
  min-width: 0;
}

.stat-number {
  font-size: 1.2rem;
  font-weight: 700;
  color: #ffd700;
  font-family: 'Cinzel', serif;
  line-height: 1;
}

.stat-label {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  margin: 2px 0;
}

.stat-detail {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.2;
}

/* Terrain Details Compact */
.terrain-details-compact {
  margin: 1rem 0;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.08);
}

.terrain-details-compact h4 {
  color: #ffd700;
  font-size: 0.95rem;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.terrain-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 0.5rem;
}

.terrain-item-compact {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 0.75rem;
  text-align: center;
}

.terrain-color-indicator {
  width: 16px;
  height: 16px;
  border-radius: 3px;
  margin-bottom: 4px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.terrain-percentage-compact {
  font-weight: 600;
  color: #ffd700;
  font-size: 0.8rem;
}

.terrain-name-compact {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.7rem;
}

/* Analysis Note */
.analysis-note {
  margin-top: 1rem;
  padding: 0.75rem;
  background: rgba(255, 215, 0, 0.08);
  border-radius: 8px;
  border-left: 3px solid #ffd700;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.analysis-note i {
  color: #ffd700;
  font-size: 0.9rem;
}

/* Fullscreen Button */
.fullscreen-btn-epic {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 30;
  font-size: 0.8rem;
}

.fullscreen-btn-epic:hover {
  background: rgba(255, 215, 0, 0.9);
  color: #000;
  transform: scale(1.1);
}

/* Map Tooltip Styling */
.map-tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 10000;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.2s;
  box-shadow: rgba(0, 0, 0, 0.3) 0px 4px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Upload Modal Specific Styling */
#upload-map-modal {
  display: flex;
  align-items: center;
  justify-content: center;
}

#upload-map-modal .modal-content {
  position: relative;
  margin: 0;
  width: 90%;
  max-width: 500px;
  background: linear-gradient(135deg, rgba(26, 35, 57, 0.95), rgba(15, 25, 42, 0.95));
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 215, 0, 0.2);
  padding: 2rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.8);
}

#upload-map-modal .upload-title {
  text-align: center;
  color: #ffd700;
  font-family: 'Cinzel', serif;
  margin-bottom: 2rem;
  font-size: 1.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

#upload-map-modal .upload-form-modern .form-group {
  margin-bottom: 1.5rem;
}

#upload-map-modal .upload-form-modern label {
  display: block;
  color: #ffd700;
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
}

#upload-map-modal .file-upload-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.03);
  border: 2px dashed rgba(255, 215, 0, 0.3);
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

#upload-map-modal .file-upload-label:hover {
  border-color: #ffd700;
  background: rgba(255, 215, 0, 0.05);
}

#upload-map-modal .file-upload-label i {
  font-size: 2rem;
  color: #ffd700;
  margin-bottom: 0.5rem;
}

#upload-map-modal .upload-text {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffd700;
}

#upload-map-modal .upload-hint {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 0.25rem;
}

#upload-map-modal .upload-form-modern input[type="file"] {
  display: none;
}

#upload-map-modal .map-name-display {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: rgba(255, 215, 0, 0.1);
  border-radius: 10px;
  border-left: 3px solid #ffd700;
  display: none;
}

#upload-map-modal .map-name-display label {
  color: #ffd700;
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: block;
}

#upload-map-modal .map-name-text {
  color: #ffffff;
  font-size: 1.1rem;
  font-weight: 500;
  background: rgba(0, 0, 0, 0.2);
  padding: 0.5rem;
  border-radius: 5px;
}

#upload-map-modal .upload-form-modern textarea {
  width: 100%;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: white;
  transition: all 0.3s ease;
  resize: vertical;
  box-sizing: border-box;
}

#upload-map-modal .upload-form-modern textarea:focus {
  outline: none;
  border-color: #ffd700;
  background: rgba(255, 255, 255, 0.08);
}

#upload-map-modal .upload-info-compact {
  background: rgba(255, 215, 0, 0.1);
  padding: 1rem;
  border-radius: 10px;
  margin: 1rem 0;
  border-left: 3px solid #ffd700;
}

#upload-map-modal .upload-info-compact p {
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

#upload-map-modal .upload-submit-btn {
  width: 100%;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #1a1a1a;
  border: none;
  border-radius: 15px;
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

#upload-map-modal .upload-submit-btn:disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.4);
  cursor: not-allowed;
  opacity: 0.5;
}

#upload-map-modal .upload-submit-btn:not(:disabled):hover {
  background: linear-gradient(135deg, #ffed4e, #ffd700);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

#upload-map-modal .upload-progress {
  margin: 1rem 0;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 1rem;
  display: none;
}

#upload-map-modal .upload-progress-bar-container {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

#upload-map-modal .upload-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #ffd700, #ffed4e);
  width: 0%;
  transition: width 0.3s ease;
}

#upload-map-modal .upload-progress-text {
  color: #ffd700;
  font-size: 0.9rem;
  text-align: center;
}

#upload-map-modal.show {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Responsive Design for Modal */
@media (max-width: 768px) {
  .map-details-header-epic {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .map-image-container {
    max-width: 100%;
    margin: 0 auto;
  }
  
  .analysis-stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }
  
  .terrain-grid {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  }
  
  .map-actions-section-epic {
    justify-content: center;
  }
  
  .modal-body {
    padding: 1rem;
  }
  
  .modal-content {
    margin: 2% auto;
    width: 95%;
    max-height: 90vh;
  }
}

/* Custom scrollbar for modal */
.modal-body::-webkit-scrollbar {
  width: 8px;
}

.modal-body::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb {
  background: rgba(255, 215, 0, 0.5);
  border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 215, 0, 0.7);
}

/* Terrain Chart Section */
.terrain-chart-section {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.08);
}

/* Compact Terrain Chart Section */
.terrain-chart-compact {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.08);
}

.chart-header-compact {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.chart-header-compact h3 {
  font-family: 'Cinzel', serif;
  color: #ffd700;
  margin: 0;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.chart-and-legend-container {
  display: flex;
  gap: 1.5rem;
  align-items: center;
  justify-content: space-between;
}

.chart-container-compact {
  position: relative;
  height: 150px;
  width: 150px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-container-compact canvas {
  max-width: 100%;
  max-height: 100%;
}

.terrain-legend-compact {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  max-height: 150px;
  overflow-y: auto;
}

.legend-item-compact {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.4rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  transition: background 0.3s ease;
}

.legend-item-compact:hover {
  background: rgba(255, 255, 255, 0.1);
}

.legend-color-compact {
  width: 14px;
  height: 14px;
  border-radius: 3px;
  flex-shrink: 0;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.legend-label-compact {
  color: #f1f5f9;
  font-size: 0.9rem;
  flex: 1;
}

.legend-value-compact {
  color: #ffd700;
  font-weight: 600;
  font-size: 0.85rem;
  min-width: 45px;
  text-align: right;
}

.section-header-chart {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.section-header-chart h3 {
  font-family: 'Cinzel', serif;
  color: #ffd700;
  margin: 0;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.chart-container {
  position: relative;
  height: 250px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-container canvas {
  max-width: 100%;
  max-height: 100%;
}

/* Terrain Color Classes for Chart Legend */
.terrain-color.water { background-color: #4A90E2; }
.terrain-color.grass { background-color: #7ED321; }
.terrain-color.trees { background-color: #228B22; }
.terrain-color.rocks { background-color: #8B7355; }
.terrain-color.coast { background-color: #87CEEB; }
.terrain-color.walls { background-color: #696969; }
.terrain-color.ground { background-color: #D2B48C; }

/* Responsive Design for Compact Chart */
@media (max-width: 768px) {
  .chart-and-legend-container {
    flex-direction: column;
    gap: 1rem;
  }
  
  .chart-container-compact {
    width: 120px;
    height: 120px;
  }
  
  .terrain-legend-compact {
    max-height: none;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 0.4rem;
  }
  
  .legend-item-compact {
    flex: 0 1 calc(50% - 0.2rem);
    min-width: 140px;
  }
}

/* Enhanced Match History Modal */
.match-history-overlay {
  animation: fadeIn 0.3s ease;
}

.match-history-dialog {
  animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Enhanced Close Button Hover Effects */
.close-match-history:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ffd700;
  transform: rotate(90deg);
}

#close-fullscreen-enhanced:hover {
  background: rgba(255, 215, 0, 0.9);
  color: #000;
  transform: scale(1.1);
}

/* Terrain Chart and Balance Metrics Container */
.terrain-and-balance-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

@media (max-width: 768px) {
  .terrain-and-balance-section {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* Balance Metrics Section */
.balance-metrics-section {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  padding: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.08);
}

.balance-header h3 {
  font-family: 'Cinzel', serif;
  color: #ffd700;
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.balance-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.balance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 0.75rem;
}

.balance-metric {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  padding: 0.75rem;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.balance-metric:hover {
  background: rgba(255, 215, 0, 0.08);
  border-color: rgba(255, 215, 0, 0.2);
  transform: translateY(-1px);
}

.balance-metric .metric-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background: rgba(255, 215, 0, 0.2);
  color: #ffd700;
  font-size: 0.9rem;
}

.balance-metric .metric-info {
  flex: 1;
  min-width: 0;
}

.balance-metric .metric-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #ffd700;
  font-family: 'Cinzel', serif;
  line-height: 1;
}

.balance-metric .metric-label {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  margin-top: 2px;
}

.overall-balance {
  background: rgba(255, 215, 0, 0.1);
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: 8px;
  padding: 0.75rem;
  text-align: center;
}

.overall-rating {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.rating-label {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 600;
}

.rating-value {
  font-family: 'Cinzel', serif;
  font-weight: 700;
  font-size: 1.2rem;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
}

.rating-value.excellent {
  background: rgba(34, 197, 94, 0.2);
  color: #10b981;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.rating-value.good {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.rating-value.average {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.rating-value.poor {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.no-balance-data {
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
  text-align: center;
  padding: 1rem;
}

/* Specific Modal Fullscreen Button */
.map-details-fullscreen-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 100;
  font-size: 1.1rem;
  backdrop-filter: blur(5px);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.map-details-fullscreen-btn:hover {
  background: rgba(255, 215, 0, 0.9);
  color: #000;
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
}

.map-details-fullscreen-btn:active {
  transform: scale(0.95);
}

/* Enhanced Fullscreen Icon for Map Cards */
.fullscreen-icon {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 10;
  font-size: 1rem;
  opacity: 0;
  transform: scale(0.8);
  backdrop-filter: blur(5px);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.map-card:hover .fullscreen-icon {
  opacity: 1;
  transform: scale(1);
}

.fullscreen-icon:hover {
  background: rgba(255, 215, 0, 0.9);
  color: #000;
  transform: scale(1.15);
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
}

.fullscreen-icon:active {
  transform: scale(0.9);
}

/* Fullscreen Modal Enhanced */
#fullscreen-modal-enhanced {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(10px);
  z-index: 20000;
  display: none;
  align-items: center;
  justify-content: center;
}

#fullscreen-modal-enhanced.show {
  display: flex;
}

.fullscreen-image-container {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fullscreen-image {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

#close-fullscreen-enhanced {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: rgba(255, 255, 255, 0.8);
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  transition: all 0.3s ease;
  z-index: 20001;
  backdrop-filter: blur(5px);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

#close-fullscreen-enhanced:hover {
  background: rgba(255, 60, 60, 0.9);
  color: white;
  transform: scale(1.1);
}

/* Remove old conflicting styles */
.fullscreen-btn-epic {
  display: none;
}

/* No terrain data styling */
.no-terrain-data {
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
  text-align: center;
  padding: 1rem;
  margin: 0;
}

/* Terrain percentage fix */
.legend-value-compact {
  color: #ffd700;
  font-weight: 600;
  min-width: 45px;
  text-align: right;
}

/* Balance metrics improvements */
.no-balance-data {
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
  text-align: center;
  padding: 1rem;
  margin: 0;
}

/* Balance Metrics Text Value Styling */
.metric-value.excellent,
.rating-value.excellent {
  color: #10b981; /* Emerald green */
  font-weight: 700;
}

.metric-value.very-good {
  color: #22c55e; /* Light green */
  font-weight: 600;
}

.metric-value.good,
.rating-value.good {
  color: #65a30d; /* Lime green */
  font-weight: 600;
}

.metric-value.fair,
.rating-value.fair,
.metric-value.average,
.rating-value.average {
  color: #eab308; /* Yellow */
  font-weight: 500;
}

.metric-value.poor,
.rating-value.poor {
  color: #ef4444; /* Red */
  font-weight: 600;
}

.metric-value.very-poor {
  color: #dc2626; /* Dark red */
  font-weight: 700;
}

.metric-value.na,
.rating-value.na {
  color: #6b7280; /* Gray */
  font-style: italic;
  font-weight: 400;
}

.metric-value.neutral,
.rating-value.neutral {
  color: #9ca3af; /* Light gray */
  font-weight: 500;
}

/* WC1 Fullscreen Modal */
.war1-fullscreen-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(10px);
  z-index: 20000;
  display: none;
  align-items: center;
  justify-content: center;
}

.war1-fullscreen-modal.show {
  display: flex;
}

.war1-fullscreen-container {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.war1-fullscreen-image {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(139, 69, 19, 0.5);
  border: 2px solid rgba(218, 165, 32, 0.3);
}

.war1-title-overlay {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: rgba(139, 69, 19, 0.9);
  backdrop-filter: blur(10px);
  padding: 1rem 1.5rem;
  border-radius: 8px;
  border: 1px solid rgba(218, 165, 32, 0.3);
  z-index: 20001;
}

.war1-title-overlay h2 {
  color: #daa520;
  margin: 0 0 0.25rem 0;
  font-family: 'Cinzel', serif;
  font-size: 1.5rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.war1-title-overlay p {
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-size: 0.9rem;
  font-style: italic;
}

#close-war1-fullscreen {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(139, 69, 19, 0.8);
  color: rgba(218, 165, 32, 0.8);
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  transition: all 0.3s ease;
  z-index: 20001;
  backdrop-filter: blur(5px);
  border: 2px solid rgba(218, 165, 32, 0.3);
}

#close-war1-fullscreen:hover {
  background: rgba(218, 165, 32, 0.9);
  color: #000;
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(218, 165, 32, 0.4);
}

/* WC3 Fullscreen Modal */
.war3-fullscreen-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(10px);
  z-index: 20000;
  display: none;
  align-items: center;
  justify-content: center;
}

.war3-fullscreen-modal.show {
  display: flex;
}

.war3-fullscreen-container {
  position: relative;
  max-width: 98vw;
  max-height: 98vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.war3-fullscreen-image {
  max-width: 95vw;
  max-height: 95vh;
  min-width: 600px;
  min-height: 600px;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(59, 130, 246, 0.5);
  border: 2px solid rgba(59, 130, 246, 0.3);
  image-rendering: crisp-edges;
  image-rendering: -webkit-optimize-contrast;
  transform: scale(1.2);
}

.war3-title-overlay {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: rgba(30, 58, 138, 0.9);
  backdrop-filter: blur(10px);
  padding: 1rem 1.5rem;
  border-radius: 8px;
  border: 1px solid rgba(59, 130, 246, 0.3);
  z-index: 20001;
}

.war3-title-overlay h2 {
  color: #3b82f6;
  margin: 0 0 0.25rem 0;
  font-family: 'Cinzel', serif;
  font-size: 1.5rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.war3-title-overlay p {
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 0.75rem 0;
  font-size: 0.9rem;
  font-style: italic;
}

.war3-fullscreen-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 0.5rem;
}

.war3-fs-btn {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  color: #3b82f6;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.4rem;
  text-decoration: none;
}

.war3-fs-btn:hover {
  background: rgba(59, 130, 246, 0.2);
  border-color: #3b82f6;
  color: white;
  transform: translateY(-1px);
}

#close-war3-fullscreen {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(30, 58, 138, 0.8);
  color: rgba(59, 130, 246, 0.8);
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  transition: all 0.3s ease;
  z-index: 20001;
  backdrop-filter: blur(5px);
  border: 2px solid rgba(59, 130, 246, 0.3);
}

#close-war3-fullscreen:hover {
  background: rgba(59, 130, 246, 0.9);
  color: #000;
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
}

/* WC3 Details Modal Specific Styles */
.map-details-epic.wc3-details {
  background: linear-gradient(135deg, rgba(30, 58, 138, 0.05), rgba(59, 130, 246, 0.05));
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.wc3-minimap {
  border: 2px solid #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.wc3-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.wc3-info-item {
  color: #1e3a8a;
  font-size: 0.9rem;
}

.wc3-info-item strong {
  color: #3b82f6;
  font-weight: 600;
}

.wc3-race-stats-section {
  margin: 2rem 0;
  padding: 1.5rem;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.race-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.race-stat-item {
  background: rgba(255, 255, 255, 0.8);
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid rgba(59, 130, 246, 0.2);
  text-align: center;
  transition: all 0.3s ease;
}

.race-stat-item:hover {
  background: rgba(59, 130, 246, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.race-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: #1e3a8a;
  font-weight: 600;
}

.race-header i {
  color: #3b82f6;
}

.race-stats {
  font-size: 0.9rem;
}

.stat-value {
  color: #1e3a8a;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.win-rate {
  color: #059669;
  font-size: 0.8rem;
  font-weight: 500;
}

/* WC3 Fullscreen Button for Map Cards */
.war3-fullscreen-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(0, 0, 0, 0.8);
  color: #3b82f6;
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 10;
  font-size: 1rem;
  opacity: 0;
  transform: scale(0.8);
  backdrop-filter: blur(5px);
  border: 2px solid rgba(59, 130, 246, 0.3);
}

.war3-map-card:hover .war3-fullscreen-btn {
  opacity: 1;
  transform: scale(1);
}

.war3-fullscreen-btn:hover {
  background: rgba(59, 130, 246, 0.9);
  color: #000;
  transform: scale(1.15);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
} 
