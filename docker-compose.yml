version: '3.8'

services:
  web:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    env_file:
      - .env    ports:
      - "3000:3000"
    volumes:
      - ./backend:/app
      - ./frontend:/app/frontend
      - ./uploads:/app/uploads
      - /app/node_modules
      - ./assets:/app/assets  # Add assets volume
    environment:
      - NODE_ENV=development
      - MONGODB_URI=mongodb://mongo:27017/newsite
    depends_on:
      - mongo

  mongo:
    image: mongo:latest
    ports:
      - "27017:27017"
    volumes:
      - mongo-data:/data/db

  mongo-express:
    image: mongo-express
    container_name: mongo-express
    restart: always
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_SERVER: mongo
      ME_CONFIG_MONGODB_PORT: 27017
      ME_CONFIG_MONGODB_ENABLE_ADMIN: true
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: password

volumes:
  mongo-data:
