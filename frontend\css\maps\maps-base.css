/* ===== GLOBAL BASE STYLES ===== */
html, body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', sans-serif;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
  color: rgba(255, 255, 255, 0.9);
  min-height: 100vh;
  overflow-x: hidden;
}

* {
  box-sizing: border-box;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .maps-main {
    padding: 0 1rem 2rem;
  }
  
  .hero-title {
    font-size: clamp(2rem, 8vw, 3rem);
  }
  
  .hero-subtitle {
    font-size: clamp(0.9rem, 3vw, 1.2rem);
  }
  
  .section-title {
    font-size: 1.5rem;
  }
  
  .section-header {
    flex-direction: column;
    text-align: center;
  }
  
  .maps-controls {
    flex-direction: column;
    gap: 1rem;
  }
  
  .maps-search {
    max-width: 100%;
  }
  
  .game-tabs {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .game-tab {
    min-width: auto;
    padding: 0.75rem 1rem;
  }
  
  #maps-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    padding: 0.5rem;
    gap: 0.8rem;
  }
  
  .map-card {
    margin-bottom: 0.5rem;
  }
  
  .map-thumbnail-wrapper {
    height: 160px;
  }
  
  .modal-content {
    width: 95%;
    margin: 2% auto;
    max-height: 90vh;
  }
  
  .modal-header {
    padding: 1rem 1.5rem;
  }
  
  .modal-body {
    padding: 1.5rem;
  }
  
  .modal-title {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .maps-hero {
    min-height: 6vh;
    padding: 1rem 0;
  }
  
  .hero-stats {
    gap: 0.5rem;
  }
  
  .hero-stat {
    padding: 0.4rem 0.6rem;
    min-width: 60px;
  }
  
  .hero-stat-value {
    font-size: 1rem;
  }
  
  .hero-stat-label {
    font-size: 0.6rem;
  }
  
  .game-tabs-container {
    margin: 0.5rem auto 0.5rem;
    padding: 0.4rem;
  }
  
  .game-tab {
    padding: 0.6rem 0.8rem;
    font-size: 0.7rem;
  }
  
  #maps-grid {
    grid-template-columns: 1fr;
    padding: 0.5rem;
  }
  
  .war2-section {
    padding: 1.5rem;
    margin: 1rem auto;
  }
  
  .section-title {
    font-size: 1.3rem;
  }
  
  .map-actions {
    flex-direction: column;
    gap: 0.3rem;
  }
}

/* ===== UTILITY CLASSES ===== */
.btn {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn:hover {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.1));
  border-color: rgba(255, 215, 0, 0.3);
  color: #ffd700;
  transform: translateY(-2px);
}

.btn.primary {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #000;
  border-color: #ffd700;
}

.btn.primary:hover {
  background: linear-gradient(135deg, #ffed4e, #ffd700);
  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
}

/* ===== ANIMATIONS ===== */
.fade-in {
  animation: fadeIn 0.6s ease;
}

.slide-up {
  animation: slideUp 0.6s ease;
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Scroll animations */
.scroll-fade {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease;
}

.scroll-fade.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Loading spinner */
.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 215, 0, 0.2);
  border-top: 3px solid #ffd700;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 2rem auto;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.3), rgba(255, 215, 0, 0.6));
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.6), rgba(255, 215, 0, 0.8));
}

/* Selection styling */
::selection {
  background: rgba(255, 215, 0, 0.3);
  color: #ffd700;
}

::-moz-selection {
  background: rgba(255, 215, 0, 0.3);
  color: #ffd700;
} 
