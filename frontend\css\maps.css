/* Lazy loading optimization */
.map-thumbnail.lazy-load {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

.map-thumbnail.lazy-load.loaded {
  animation: none;
  background: none;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Optimize thumbnail dimensions for performance */
.map-thumbnail {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
  transition: transform 0.2s ease, opacity 0.3s ease;
  will-change: transform; /* Optimize for animations */
}

.map-thumbnail:hover {
  transform: scale(1.02);
}

/* Reduce layout shifts during loading */
.map-thumbnail-wrapper {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  background-color: #f3f4f6;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Performance improvements for map cards */
.map-card {
  contain: layout style paint; /* CSS containment for better performance */
  transform: translateZ(0); /* Force hardware acceleration */
} 
