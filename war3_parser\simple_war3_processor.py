#!/usr/bin/env python3
"""
Simple War3 Map Processor
Creates metadata from existing map files and thumbnails
"""

import os
import json
import hashlib
from pathlib import Path
from typing import Dict, List, Optional
import re

class SimpleWar3Processor:
    def __init__(self, maps_dir: str, thumbnails_dir: str):
        self.maps_dir = Path(maps_dir)
        self.thumbnails_dir = Path(thumbnails_dir)
        
        # Ensure directories exist
        if not self.maps_dir.exists():
            raise FileNotFoundError(f"Maps directory not found: {maps_dir}")
        if not self.thumbnails_dir.exists():
            raise FileNotFoundError(f"Thumbnails directory not found: {thumbnails_dir}")
    
    def process_all_maps(self) -> List[Dict]:
        """Process all .w3x and .w3m files and match with existing thumbnails"""
        results = []
        map_files = list(self.maps_dir.glob("*.w3x")) + list(self.maps_dir.glob("*.w3m"))
        thumbnail_files = {f.stem: f.name for f in self.thumbnails_dir.glob("*.png")}
        
        print(f"🚀 Found {len(map_files)} War3 map files to process...")
        print(f"🖼️  Found {len(thumbnail_files)} existing thumbnails...")
        
        for i, map_file in enumerate(map_files, 1):
            print(f"\n📋 Processing {i}/{len(map_files)}: {map_file.name}")
            
            try:
                map_data = self.process_single_map(map_file, thumbnail_files)
                if map_data:
                    results.append(map_data)
                    print(f"✅ Successfully processed: {map_file.name}")
                else:
                    print(f"⚠️  Skipped (no data): {map_file.name}")
            except Exception as e:
                print(f"❌ Error processing {map_file.name}: {str(e)}")
                continue
        
        print(f"\n🎉 Processing complete! Successfully processed {len(results)} maps.")
        return results
    
    def process_single_map(self, map_file: Path, thumbnail_files: Dict[str, str]) -> Optional[Dict]:
        """Process a single map file and create metadata"""
        try:
            # Extract info from filename
            filename_info = self._parse_filename(map_file.name)
            
            map_data = {
                'id': self._generate_id(map_file.name),
                'filename': map_file.name,
                'filepath': str(map_file.relative_to(map_file.parent.parent)),
                'filesize': map_file.stat().st_size,
                'hash': self._calculate_file_hash(map_file),
                'name': filename_info['name'],
                'description': f"Warcraft III custom map: {filename_info['name']}",
                'author': 'Unknown',
                'players': filename_info['players'],
                'version': filename_info['version'],
                'category': filename_info['category'],
                'thumbnail': None,
                'tags': filename_info['tags'],
                'date_added': self._get_file_date(map_file),
                'downloads': 0,
                'rating': 0,
                'source': 'local',
                'map_type': filename_info['map_type']
            }
            
            # Find matching thumbnail
            thumbnail_name = self._find_matching_thumbnail(map_file.stem, thumbnail_files)
            if thumbnail_name:
                map_data['thumbnail'] = thumbnail_name
                print(f"   🖼️  Found thumbnail: {thumbnail_name}")
            else:
                print(f"   ⚠️  No thumbnail found for: {map_file.stem}")
            
            return map_data
            
        except Exception as e:
            print(f"❌ Error processing {map_file.name}: {str(e)}")
            return None
    
    def _parse_filename(self, filename: str) -> Dict:
        """Extract information from the filename"""
        # Remove extension
        name = filename.replace('.w3x', '').replace('.w3m', '')
        
        # Initialize result
        result = {
            'name': name,
            'players': 0,
            'version': '1.0',
            'category': 'Custom',
            'tags': [],
            'map_type': 'Unknown'
        }
        
        # Extract player count from parentheses at start
        player_match = re.match(r'^\((\d+)\)', name)
        if player_match:
            result['players'] = int(player_match.group(1))
            name = name[len(player_match.group(0)):]
        
        # Extract version numbers
        version_patterns = [
            r'_v(\d+\.\d+(?:\.\d+)?)',
            r'_v(\d+)',
            r'v(\d+\.\d+(?:\.\d+)?)',
            r'(\d+\.\d+(?:\.\d+)?)$'
        ]
        
        for pattern in version_patterns:
            version_match = re.search(pattern, name)
            if version_match:
                result['version'] = version_match.group(1)
                name = re.sub(pattern, '', name)
                break
        
        # Determine map type and category from filename patterns
        if 'FFA' in name:
            result['map_type'] = 'Free For All'
            result['tags'].append('FFA')
        elif 'LV' in name:
            result['map_type'] = 'Ladder Version'
            result['tags'].append('Ladder')
        elif 'S2' in name:
            result['map_type'] = 'Season 2'
            result['tags'].append('Season 2')
        elif any(x in name.lower() for x in ['td', 'defense', 'tower']):
            result['map_type'] = 'Tower Defense'
            result['category'] = 'Tower Defense'
        elif any(x in name.lower() for x in ['dota', 'aos', 'arena']):
            result['map_type'] = 'Arena'
            result['category'] = 'Arena'
        else:
            result['map_type'] = 'Melee'
            result['category'] = 'Melee'
        
        # Clean up name
        name = re.sub(r'_+', ' ', name)  # Replace underscores with spaces
        name = re.sub(r'\s+', ' ', name)  # Normalize spaces
        name = name.strip('_- ')  # Remove trailing chars
        
        # Capitalize words
        name = ' '.join(word.capitalize() for word in name.split())
        
        result['name'] = name if name else filename.replace('.w3x', '').replace('.w3m', '')
        
        return result
    
    def _find_matching_thumbnail(self, map_stem: str, thumbnail_files: Dict[str, str]) -> Optional[str]:
        """Find matching thumbnail for a map file"""
        # Direct match
        if map_stem in thumbnail_files:
            return thumbnail_files[map_stem]
        
        # Try without common suffixes
        for suffix in ['_LV', '_S2', '_v1', '_v2', '_v3']:
            if map_stem.endswith(suffix):
                base_name = map_stem[:-len(suffix)]
                if base_name in thumbnail_files:
                    return thumbnail_files[base_name]
        
        # Try fuzzy matching
        map_stem_lower = map_stem.lower()
        for thumb_stem, thumb_name in thumbnail_files.items():
            if thumb_stem.lower() in map_stem_lower or map_stem_lower in thumb_stem.lower():
                return thumb_name
        
        return None
    
    def _generate_id(self, filename: str) -> str:
        """Generate a unique ID for the map"""
        return hashlib.md5(filename.encode()).hexdigest()[:8]
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """Calculate SHA-256 hash of the file"""
        hash_sha256 = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    
    def _get_file_date(self, file_path: Path) -> str:
        """Get file modification date"""
        import datetime
        timestamp = file_path.stat().st_mtime
        return datetime.datetime.fromtimestamp(timestamp).isoformat()

def main():
    """Main function to process all War3 maps"""
    # Set up paths
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    maps_dir = project_root / "uploads" / "war3"
    thumbnails_dir = project_root / "uploads" / "war3images"
    output_file = project_root / "uploads" / "war3_processed_maps.json"
    
    print("🚀 Simple War3 Map Processor Starting...")
    print(f"📁 Maps directory: {maps_dir}")
    print(f"🖼️  Thumbnails directory: {thumbnails_dir}")
    print(f"📄 Output file: {output_file}")
    
    # Initialize processor
    processor = SimpleWar3Processor(str(maps_dir), str(thumbnails_dir))
    
    # Process all maps
    results = processor.process_all_maps()
    
    # Save results to JSON
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n✅ Results saved to: {output_file}")
    print(f"📊 Processed {len(results)} maps successfully!")
    
    # Print summary
    if results:
        print("\n📋 Processing Summary:")
        print(f"   - Total maps: {len(results)}")
        print(f"   - With thumbnails: {sum(1 for r in results if r.get('thumbnail'))}")
        print(f"   - Player counts detected: {sum(1 for r in results if r.get('players', 0) > 0)}")
        print(f"   - Versions detected: {sum(1 for r in results if r.get('version') != '1.0')}")
        
        # Show some examples
        print("\n🎯 Sample processed maps:")
        for i, result in enumerate(results[:3]):
            print(f"   {i+1}. {result['name']} ({result['players']} players, v{result['version']})")

if __name__ == "__main__":
    main() 