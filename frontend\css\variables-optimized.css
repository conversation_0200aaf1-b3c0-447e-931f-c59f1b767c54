/* ==========================================================================
   OPTIMIZED CSS VARIABLES
   Consolidated design tokens with no redundancy
   ========================================================================== */

:root {
  /* ===== BRAND COLORS ===== */
  --primary-gold: #D4AF37;
  --primary-gold-dark: #B8941F;
  --primary-gold-light: #E8C547;
  
  --alliance-blue: #2563EB;
  --alliance-blue-dark: #1D4ED8;
  --alliance-blue-light: #3B82F6;
  
  --horde-red: #DC2626;
  --horde-red-dark: #B91C1C;
  --horde-red-light: #EF4444;
  
  /* ===== SEMANTIC COLORS ===== */
  --success-green: #10B981;
  --warning-orange: #F59E0B;
  --error-red: #EF4444;
  --info-blue: #3B82F6;
  
  /* ===== NEUTRAL PALETTE ===== */
  --neutral-50: #F8FAFC;
  --neutral-100: #F1F5F9;
  --neutral-200: #E2E8F0;
  --neutral-300: #CBD5E1;
  --neutral-400: #94A3B8;
  --neutral-500: #64748B;
  --neutral-600: #475569;
  --neutral-700: #334155;
  --neutral-800: #1E293B;
  --neutral-900: #0F172A;
  
  /* ===== BACKGROUND SYSTEM ===== */
  --bg-primary: linear-gradient(135deg, #0F172A 0%, #1E293B 50%, #334155 100%);
  --bg-secondary: linear-gradient(135deg, #1E293B 0%, #334155 100%);
  --bg-card: rgba(15, 23, 42, 0.8);
  --bg-hover: rgba(30, 41, 59, 0.9);
  --bg-tooltip: rgba(15, 23, 42, 0.95);
  
  /* ===== GLASS EFFECTS ===== */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  
  /* ===== TYPOGRAPHY ===== */
  --font-primary: 'Inter', 'Segoe UI', system-ui, sans-serif;
  --font-display: 'Cinzel', 'Inter', serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
  
  --font-xs: 0.75rem;     /* 12px */
  --font-sm: 0.875rem;    /* 14px */
  --font-base: 1rem;      /* 16px */
  --font-lg: 1.125rem;    /* 18px */
  --font-xl: 1.25rem;     /* 20px */
  --font-2xl: 1.5rem;     /* 24px */
  --font-3xl: 1.875rem;   /* 30px */
  --font-4xl: 2.25rem;    /* 36px */
  
  /* ===== SPACING SCALE ===== */
  --space-xs: 0.25rem;    /* 4px */
  --space-sm: 0.5rem;     /* 8px */
  --space-md: 1rem;       /* 16px */
  --space-lg: 1.5rem;     /* 24px */
  --space-xl: 2rem;       /* 32px */
  --space-2xl: 3rem;      /* 48px */
  --space-3xl: 4rem;      /* 64px */
  
  /* ===== BORDER RADIUS ===== */
  --radius-sm: 0.375rem;  /* 6px */
  --radius-md: 0.5rem;    /* 8px */
  --radius-lg: 0.75rem;   /* 12px */
  --radius-xl: 1rem;      /* 16px */
  --radius-full: 9999px;
  
  /* ===== SHADOWS ===== */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
  --shadow-glow: 0 0 20px rgba(212, 175, 55, 0.3);
  
  /* ===== TRANSITIONS ===== */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  
  /* ===== SEMANTIC ALIASES ===== */
  /* Primary Colors */
  --primary-color: var(--primary-gold);
  --primary-hover: var(--primary-gold-light);
  
  /* Spacing Aliases (for backward compatibility) */
  --spacing-xs: var(--space-xs);
  --spacing-sm: var(--space-sm);
  --spacing-md: var(--space-md);
  --spacing-lg: var(--space-lg);
  --spacing-xl: var(--space-xl);
  
  /* Border Colors */
  --border-primary: var(--glass-border);
  --border-hover: var(--primary-gold);
  --border-active: var(--primary-gold-light);
  
  /* Text Colors */
  --text-primary: var(--neutral-100);
  --text-secondary: var(--neutral-400);
  --text-muted: var(--neutral-500);
  --text-dark: var(--neutral-900);
  
  /* State Colors */
  --color-success: var(--success-green);
  --color-warning: var(--warning-orange);
  --color-error: var(--error-red);
  --color-info: var(--info-blue);
  
  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
} 
