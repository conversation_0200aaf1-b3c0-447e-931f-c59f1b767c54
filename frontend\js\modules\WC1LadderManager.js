/**
 * WC1LadderManager.js - Warcraft I vs AI Arena Manager
 * 
 * Specialized manager for WC1 vs AI matches with simplified interface:
 * - Auto-creates player with user's username
 * - Only vs AI matches (no other game types)
 * - Simplified map selection from 21 scenarios
 * - Integrated MMR/records system
 * - Map-specific win percentage charts
 */

export class WC1LadderManager {
  constructor() {
    this.currentUser = null;
    this.wc1Player = null;
    this.scenarios = [];
    this.initialized = false;
    this.isActive = false;
    
    // WC1 specific configuration
    this.wc1Config = {
      gameType: 'warcraft1',
      matchType: 'vsai',
      races: ['human', 'orc'],
      categories: ['forest', 'swamp', 'dungeon']
    };

    this.init();
  }

  /**
   * Initialize the WC1 ladder manager
   */
  async init() {
    console.log('🗡️ Initializing WC1 Ladder Manager...');
    
    try {
      // Load scenarios
      await this.loadScenarios();
      
      // Setup event listeners
      this.setupEventListeners();
      
      // Load current user
      await this.loadCurrentUser();
      
      this.initialized = true;
      console.log('✅ WC1 Ladder Manager initialized');
      
    } catch (error) {
      console.error('❌ Failed to initialize WC1 Ladder Manager:', error);
    }
  }

  /**
   * Load WC1 scenarios from API
   */
  async loadScenarios() {
    try {
      const response = await fetch('/api/wc1scenarios');
      if (response.ok) {
        this.scenarios = await response.json();
        console.log(`📋 Loaded ${this.scenarios.length} WC1 scenarios`);
      } else {
        // Fallback to hardcoded scenarios if API fails
        this.loadFallbackScenarios();
      }
    } catch (error) {
      console.warn('⚠️ Failed to load scenarios from API, using fallback:', error);
      this.loadFallbackScenarios();
    }
  }

  /**
   * Load fallback scenarios (hardcoded)
   */
  loadFallbackScenarios() {
    this.scenarios = [
      // Forest scenarios
      { name: 'Forest 1', category: 'forest' },
      { name: 'Forest 2', category: 'forest' },
      { name: 'Forest 3', category: 'forest' },
      { name: 'Forest 4', category: 'forest' },
      { name: 'Forest 5', category: 'forest' },
      { name: 'Forest 6', category: 'forest' },
      { name: 'Forest 7', category: 'forest' },
      
      // Swamp scenarios
      { name: 'Swamp 1', category: 'swamp' },
      { name: 'Swamp 2', category: 'swamp' },
      { name: 'Swamp 3', category: 'swamp' },
      { name: 'Swamp 4', category: 'swamp' },
      { name: 'Swamp 5', category: 'swamp' },
      { name: 'Swamp 6', category: 'swamp' },
      { name: 'Swamp 7', category: 'swamp' },
      
      // Dungeon scenarios
      { name: 'Dungeon 1', category: 'dungeon' },
      { name: 'Dungeon 2', category: 'dungeon' },
      { name: 'Dungeon 3', category: 'dungeon' },
      { name: 'Dungeon 4', category: 'dungeon' },
      { name: 'Dungeon 5', category: 'dungeon' },
      { name: 'Dungeon 6', category: 'dungeon' },
      { name: 'Dungeon 7', category: 'dungeon' }
    ];
    console.log(`📋 Loaded ${this.scenarios.length} fallback WC1 scenarios`);
  }

  /**
   * Load current user information
   */
  async loadCurrentUser() {
    try {
      const response = await fetch('/api/me', { credentials: 'include' });
      if (response.ok) {
        this.currentUser = await response.json();
        console.log(`👤 Current user loaded: ${this.currentUser.username}`);
        
        // Check if user already has a WC1 player
        await this.ensureWC1Player();
      }
    } catch (error) {
      console.error('❌ Failed to load current user:', error);
    }
  }

  /**
   * Ensure user has a WC1 player (auto-create if needed)
   */
  async ensureWC1Player() {
    if (!this.currentUser) return;

    try {
      // Check if user already has a WC1 player
      const response = await fetch(`/api/ladder/my-players?gameType=warcraft1`, {
        credentials: 'include'
      });
      
      if (response.ok) {
        const players = await response.json();
        const wc1Players = players.filter(p => p.gameType === 'warcraft1');
        
        if (wc1Players.length > 0) {
          this.wc1Player = wc1Players[0];
          console.log(`✅ Found existing WC1 player: ${this.wc1Player.playerName}`);
        } else {
          // Auto-create WC1 player with user's username
          await this.createWC1Player();
        }
      }
    } catch (error) {
      console.error('❌ Failed to check WC1 player:', error);
    }
  }

  /**
   * Auto-create WC1 player for the user
   */
  async createWC1Player() {
    if (!this.currentUser) return;

    try {
      const response = await fetch('/api/ladder/players', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          playerName: this.currentUser.username,
          gameType: 'warcraft1',
          preferredRace: 'human', // Default race
          autoCreated: true
        })
      });

      if (response.ok) {
        this.wc1Player = await response.json();
        console.log(`✅ Auto-created WC1 player: ${this.wc1Player.playerName}`);
      } else {
        console.error('❌ Failed to create WC1 player:', await response.text());
      }
    } catch (error) {
      console.error('❌ Error creating WC1 player:', error);
    }
  }

  /**
   * Setup event listeners for WC1 functionality
   */
  setupEventListeners() {
    console.log('🎧 Setting up WC1 event listeners...');
    
    // Setup report match button specifically for WC1
    document.addEventListener('click', (e) => {
      console.log('🖱️ Click detected:', e.target);
      
      const reportBtn = e.target.closest('#report-match-btn');
      if (reportBtn) {
        console.log('🎯 Report button clicked! isActive:', this.isActive);
        
        if (this.isActive) {
          console.log('✅ WC1 is active, opening modal...');
          e.preventDefault();
          e.stopPropagation();
          this.openWC1ReportModal();
        } else {
          console.log('❌ WC1 is not active, ignoring click');
        }
      }
    });

    // Setup WC1 modal events (will be available when modal exists)
    this.setupWC1ModalEvents();
    
    console.log('✅ WC1 event listeners set up');
  }

  /**
   * Activate WC1 mode (called when WC1 tab is clicked)
   */
  activateWC1Mode() {
    console.log('🗡️ Activating WC1 Mode');
    this.isActive = true;
    
    // Update active tab visually
    document.querySelectorAll('.game-tab').forEach(tab => {
      tab.classList.remove('active');
    });
    
    const wc1Tab = document.querySelector('.game-tab[data-game-type="war1"]');
    if (wc1Tab) {
      wc1Tab.classList.add('active');
    }
    
    // Show WC1 controls, hide WC2/WC3 controls
    this.toggleControls('war1');
    
    // Update report button text
    this.updateReportButton();
    
    // Update page title
    document.title = 'WC Arena - WC1 vs AI Arena';
    
    // Hide WC2-specific statistics that don't apply to WC1 vs AI
    this.hideWC2Statistics();
    
    // Load/update WC1 specific data
    this.updateWC1Stats();
    
    console.log('✅ WC1 Mode activated');
  }

  /**
   * Hide WC2-specific statistics that don't apply to WC1 vs AI
   */
  hideWC2Statistics() {
    // Hide match type statistics (1v1, 2v2, etc.) since WC1 is only vs AI
    const matchTypeCard = document.querySelector('.stats-card:has(#match-type-chart)');
    if (matchTypeCard) {
      matchTypeCard.style.display = 'none';
    }
    
    // Hide rank distribution since WC1 is just starting
    const rankCard = document.querySelector('.stats-card:has(#rank-chart)');
    if (rankCard) {
      rankCard.style.display = 'none';
    }
    
    // Hide MMR distribution for now
    const mmrCard = document.querySelector('.stats-card:has(#mmr-chart)');
    if (mmrCard) {
      mmrCard.style.display = 'none';
    }
    
    // Hide activity chart for now
    const activityCard = document.querySelector('.stats-card:has(#activity-chart)');
    if (activityCard) {
      activityCard.style.display = 'none';
    }
    
    // Update race distribution title to be WC1-specific
    const raceCard = document.querySelector('.stats-card:has(#race-chart)');
    if (raceCard) {
      const title = raceCard.querySelector('h3');
      if (title) {
        title.textContent = 'WC1 Race Distribution';
      }
    }
    
    // Update popular maps title to be WC1-specific
    const mapsCard = document.querySelector('.stats-card:has(#maps-stats-list)');
    if (mapsCard) {
      const title = mapsCard.querySelector('h3');
      if (title) {
        title.textContent = 'Popular WC1 Scenarios';
      }
    }
  }

  /**
   * Show WC2-specific statistics when switching away from WC1
   */
  showWC2Statistics() {
    // Show all hidden statistics cards
    const hiddenCards = [
      '.stats-card:has(#match-type-chart)',
      '.stats-card:has(#rank-chart)', 
      '.stats-card:has(#mmr-chart)',
      '.stats-card:has(#activity-chart)'
    ];
    
    hiddenCards.forEach(selector => {
      const card = document.querySelector(selector);
      if (card) {
        card.style.display = '';
      }
    });
    
    // Reset race distribution title
    const raceCard = document.querySelector('.stats-card:has(#race-chart)');
    if (raceCard) {
      const title = raceCard.querySelector('h3');
      if (title) {
        title.textContent = 'Race Distribution';
      }
    }
    
    // Reset popular maps title
    const mapsCard = document.querySelector('.stats-card:has(#maps-stats-list)');
    if (mapsCard) {
      const title = mapsCard.querySelector('h3');
      if (title) {
        title.textContent = 'Popular Maps';
      }
    }
  }

  /**
   * Called when deactivating WC1 mode (switching to WC2/WC3)
   */
  onDeactivate() {
    console.log('🗡️ Deactivating WC1 Mode');
    this.isActive = false;
    
    // Show WC2/WC3 controls, hide WC1 controls
    this.toggleControls('war2');
    
    // Reset report button text for WC2/WC3
    this.resetReportButton();
    
    // Show WC2-specific statistics again
    this.showWC2Statistics();
    
    console.log('✅ WC1 Mode deactivated');
  }

  /**
   * Setup event listeners for WC1 modal interactions
   */
  setupWC1ModalEvents() {
    console.log('🎭 Setting up WC1 modal events...');
    
    // Handle modal close events
    document.addEventListener('click', (e) => {
      const modal = document.getElementById('wc1-report-match-modal');
      if (!modal || !modal.classList.contains('show')) return;
      
      // Close if clicking outside modal content (on overlay)
      if (e.target === modal || e.target.classList.contains('modal-overlay')) {
        e.preventDefault();
        e.stopPropagation();
        this.closeWC1ReportModal();
      }
      
      // Close if clicking close button (X)
      if (e.target.closest('.close-modal') && modal.contains(e.target)) {
        e.preventDefault();
        e.stopPropagation();
        this.closeWC1ReportModal();
      }
      
      // Close if clicking cancel button
      if (e.target.id === 'wc1-cancel-report') {
        e.preventDefault();
        e.stopPropagation();
        this.closeWC1ReportModal();
      }
    });
    
    // Handle escape key
    document.addEventListener('keydown', (e) => {
      const modal = document.getElementById('wc1-report-match-modal');
      if (modal && modal.classList.contains('show') && e.key === 'Escape') {
        e.preventDefault();
        this.closeWC1ReportModal();
      }
    });
    
    // Handle form submission
    document.addEventListener('submit', async (e) => {
      if (e.target.id === 'wc1-report-match-form') {
        e.preventDefault();
        await this.handleWC1MatchSubmission(e.target);
      }
    });
    
    console.log('✅ WC1 modal events set up');
  }

  /**
   * Open the WC1 report match modal
   */
  openWC1ReportModal() {
    console.log('🚀 Opening WC1 report modal...');
    console.log('👤 Current user:', this.currentUser);
    
    if (!this.currentUser) {
      console.log('❌ No current user, showing alert');
      alert('Please log in to report matches.');
      return;
    }

    const modal = document.getElementById('wc1-report-match-modal');
    console.log('🎭 Modal element:', modal);
    
    if (modal) {
      console.log('✅ Modal found, showing...');
      
      // Use CSS class instead of direct style manipulation
      modal.classList.add('show');
      modal.style.display = 'flex'; // Backup for older browsers
      
      // Ensure high z-index
      modal.style.zIndex = '10000';
      
      // Reset form
      const form = document.getElementById('wc1-report-match-form');
      console.log('📝 Form element:', form);
      if (form) {
        form.reset();
        console.log('✅ Form reset');
      } else {
        console.log('⚠️ Form not found');
      }
      
      // Prevent body scrolling
      document.body.style.overflow = 'hidden';
      
      console.log('✅ WC1 modal opened successfully');
    } else {
      console.log('❌ Modal not found');
    }
  }

  /**
   * Close the WC1 report match modal
   */
  closeWC1ReportModal() {
    console.log('🚪 Closing WC1 report modal...');
    
    const modal = document.getElementById('wc1-report-match-modal');
    if (modal) {
      // Use CSS class removal
      modal.classList.remove('show');
      modal.style.display = 'none';
      
      // Restore body scrolling
      document.body.style.overflow = '';
      
      console.log('✅ WC1 modal closed');
    }
  }

  /**
   * Handle WC1 match form submission
   */
  async handleWC1MatchSubmission(form) {
    console.log('📝 Processing WC1 match submission...');
    
    try {
      // Extract form data
      const formData = new FormData(form);
      const matchData = {
        map: formData.get('map'),
        race: formData.get('race'),
        result: formData.get('result'),
        duration: formData.get('duration') || null,
        notes: formData.get('notes') || '',
        gameType: 'warcraft1',
        matchType: 'vsai',
        timestamp: new Date().toISOString()
      };
      
      console.log('📊 Match data:', matchData);
      
      // Validate required fields
      if (!matchData.map || !matchData.race || !matchData.result) {
        alert('Please fill in all required fields (Scenario, Race, and Result).');
        return;
      }
      
      // Disable submit button
      const submitBtn = form.querySelector('button[type="submit"]');
      const originalText = submitBtn.innerHTML;
      submitBtn.disabled = true;
      submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Submitting...';
      
      // Submit to backend
      const response = await fetch('/api/ladder/matches/wc1', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(matchData)
      });
      
      if (response.ok) {
        const result = await response.json();
        console.log('✅ Match submitted successfully:', result);
        
        // Show success message
        alert('Match reported successfully! Your vs AI match has been recorded.');
        
        // Close modal and refresh data
        this.closeWC1ReportModal();
        await this.updateWC1Stats();
        
      } else {
        const error = await response.json();
        console.error('❌ Match submission failed:', error);
        alert(`Failed to submit match: ${error.message || 'Unknown error'}`);
      }
      
    } catch (error) {
      console.error('❌ Error submitting match:', error);
      alert('An error occurred while submitting the match. Please try again.');
    } finally {
      // Re-enable submit button
      const submitBtn = form.querySelector('button[type="submit"]');
      if (submitBtn) {
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-robot"></i> Submit vs AI Match';
      }
    }
  }

  /**
   * Toggle controls based on game type
   */
  toggleControls(gameType) {
    const wc2wc3Controls = document.getElementById('wc2-wc3-controls');
    const wc1Controls = document.getElementById('wc1-controls');

    if (gameType === 'war1') {
      if (wc2wc3Controls) wc2wc3Controls.style.display = 'none';
      if (wc1Controls) wc1Controls.style.display = 'block';
    } else {
      if (wc2wc3Controls) wc2wc3Controls.style.display = 'flex';
      if (wc1Controls) wc1Controls.style.display = 'none';
    }
  }

  /**
   * Update report button for WC1
   */
  updateReportButton() {
    const reportBtn = document.getElementById('report-match-btn');
    if (reportBtn) {
      const textSpan = reportBtn.querySelector('#report-match-text');
      if (textSpan) {
        textSpan.textContent = 'REPORT vs AI MATCH';
      }
    }
  }

  /**
   * Reset report button to default
   */
  resetReportButton() {
    const reportBtn = document.getElementById('report-match-btn');
    if (reportBtn) {
      const textSpan = reportBtn.querySelector('#report-match-text');
      if (textSpan) {
        textSpan.textContent = 'REPORT MATCH';
      }
    }
  }

  /**
   * Get scenarios by category
   */
  getScenariosByCategory(category) {
    if (category === 'all') {
      return this.scenarios;
    }
    return this.scenarios.filter(s => s.category === category);
  }

  /**
   * Update statistics for WC1 (map win percentages)
   */
  async updateWC1Stats() {
    try {
      // Update leaderboard title
      const leaderboardTitle = document.getElementById('leaderboard-title');
      if (leaderboardTitle) {
        leaderboardTitle.textContent = 'WC1 vs AI Leaderboard';
      }

      // Load WC1 leaderboard data
      await this.loadWC1Leaderboard();
      
      // Load WC1 recent matches
      await this.loadWC1RecentMatches();
      
      // Load WC1 statistics
      const response = await fetch('/api/ladder/stats?gameType=warcraft1', {
        credentials: 'include'
      });
      
      if (response.ok) {
        const stats = await response.json();
        this.renderWC1MapStats(stats);
      }
    } catch (error) {
      console.error('❌ Failed to load WC1 stats:', error);
    }
  }

  /**
   * Load and render WC1 leaderboard
   */
  async loadWC1Leaderboard() {
    try {
      const response = await fetch('/api/ladder/rankings?gameType=warcraft1&matchType=vsai&limit=50&page=1', {
        credentials: 'include'
      });
      
      if (response.ok) {
        const data = await response.json();
        this.renderWC1Leaderboard(data.rankings || []);
      } else {
        // Show empty leaderboard with message
        this.renderWC1Leaderboard([]);
      }
    } catch (error) {
      console.error('❌ Failed to load WC1 leaderboard:', error);
      this.renderWC1Leaderboard([]);
    }
  }

  /**
   * Render WC1 leaderboard table
   */
  renderWC1Leaderboard(rankings) {
    const tbody = document.getElementById('leaderboard-body');
    if (!tbody) return;

    if (rankings.length === 0) {
      tbody.innerHTML = `
        <tr>
          <td colspan="6" class="empty-state">
            <div class="wc1-empty-state">
              <i class="fas fa-sword"></i>
              <h3>No WC1 vs AI matches yet!</h3>
              <p>Be the first to battle against the AI in classic Warcraft scenarios.</p>
              <button onclick="wc1LadderManager.openWC1ReportModal()" class="btn btn-primary">
                <i class="fas fa-trophy"></i> Report Your First Match
              </button>
            </div>
          </td>
        </tr>
      `;
      return;
    }

    tbody.innerHTML = rankings.map((player, index) => `
      <tr class="player-row" data-player-id="${player._id}">
        <td class="rank-cell">
          <div class="rank-info">
            <span class="rank-number">${index + 1}</span>
            ${player.rank ? `
              <div class="rank-badge">
                <img src="/assets/img/ranks/${player.rank.image}" alt="${player.rank.name}" class="rank-image">
                <span class="rank-name">${player.rank.name}</span>
              </div>
            ` : ''}
          </div>
        </td>
        <td class="player-cell">
          <div class="player-info" onclick="wc1LadderManager.showWC1PlayerStats('${player.playerName}')">
            <div class="player-main">
              <span class="player-name">${player.playerName}</span>
            </div>
            <div class="player-details">
              <span class="player-race ${player.preferredRace || 'human'}">${(player.preferredRace || 'human').charAt(0).toUpperCase() + (player.preferredRace || 'human').slice(1)}</span>
              <span class="vs-ai-badge">vs AI</span>
            </div>
          </div>
        </td>
        <td class="mmr-cell">
          <span class="mmr-value">${player.mmr || 1200}</span>
        </td>
        <td class="wins-cell">${player.wins || 0}</td>
        <td class="losses-cell">${player.losses || 0}</td>
        <td class="ratio-cell">
          <span class="win-rate ${this.getWinRateClass(player.winRate || 0)}">${(player.winRate || 0).toFixed(1)}%</span>
          <span class="games-count">(${(player.wins || 0) + (player.losses || 0)} games)</span>
        </td>
      </tr>
    `).join('');
  }

  /**
   * Load and render WC1 recent matches
   */
  async loadWC1RecentMatches() {
    try {
      const response = await fetch('/api/ladder/recent-matches?gameType=warcraft1&limit=10', {
        credentials: 'include'
      });
      
      if (response.ok) {
        const matches = await response.json();
        this.renderWC1RecentMatches(matches);
      } else {
        this.renderWC1RecentMatches([]);
      }
    } catch (error) {
      console.error('❌ Failed to load WC1 recent matches:', error);
      this.renderWC1RecentMatches([]);
    }
  }

  /**
   * Render WC1 recent matches
   */
  renderWC1RecentMatches(matches) {
    const container = document.getElementById('recent-matches-container');
    if (!container) return;

    if (matches.length === 0) {
      container.innerHTML = `
        <div class="empty-matches">
          <i class="fas fa-robot"></i>
          <p>No recent vs AI matches</p>
        </div>
      `;
      return;
    }

    container.innerHTML = matches.map(match => `
      <div class="match-item">
        <div class="match-header">
          <span class="match-type">vs AI</span>
          <span class="match-map">${match.map || 'Unknown Map'}</span>
        </div>
        <div class="match-players">
          <span class="match-player ${match.players?.[0]?.result === 'win' ? 'win' : 'loss'}">
            ${match.players?.[0]?.playerName || 'Player'}
          </span>
          vs
          <span class="match-player ai">AI</span>
        </div>
        <div class="match-time">${this.formatMatchTime(match.createdAt)}</div>
      </div>
    `).join('');
  }

  /**
   * Show WC1 player statistics (simplified version)
   */
  showWC1PlayerStats(playerName) {
    // For now, just show a simple alert
    // In a full implementation, this would open a detailed stats modal
    alert(`WC1 Player Stats for ${playerName}\n\nDetailed vs AI statistics coming soon!`);
  }

  /**
   * Get CSS class for win rate display
   */
  getWinRateClass(winRate) {
    if (winRate >= 70) return 'excellent';
    if (winRate >= 60) return 'good';
    if (winRate >= 50) return 'average';
    return 'below-average';
  }

  /**
   * Format match time for display
   */
  formatMatchTime(timestamp) {
    if (!timestamp) return 'Unknown';
    
    const now = new Date();
    const matchTime = new Date(timestamp);
    const diffMs = now - matchTime;
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) return `${diffDays}d ago`;
    if (diffHours > 0) return `${diffHours}h ago`;
    if (diffMins > 0) return `${diffMins}m ago`;
    return 'Just now';
  }

  /**
   * Render WC1 map-specific statistics
   */
  renderWC1MapStats(stats) {
    // This would integrate with the charts section to show
    // map win percentages instead of generic stats
    console.log('📊 WC1 Map Stats:', stats);
    
    // TODO: Update charts to show map-specific win rates
    // This would replace the generic race/match type charts
    // with WC1-specific map performance charts
  }

  /**
   * Public API - called when switching to WC1 tab
   */
  onActivate() {
    this.activateWC1Mode();
  }
}

// Auto-initialize when module loads
const wc1LadderManager = new WC1LadderManager();

// Export for global access
if (typeof window !== 'undefined') {
  window.wc1LadderManager = wc1LadderManager;
}

export default wc1LadderManager; 