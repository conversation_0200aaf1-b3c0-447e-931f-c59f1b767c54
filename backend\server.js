// API endpoint to get W3 maps
app.get('/api/w3-maps', (req, res) => {
    try {
        const query = `
            SELECT 
                id, filename, name, description, author, recommended_players,
                player_count, max_players, map_size, tileset, map_type,
                expansion, file_count, blp_count, has_script, file_size,
                is_reforged, created_at, updated_at
            FROM w3_maps 
            ORDER BY 
                CASE 
                    WHEN map_type = '1v1' THEN 1
                    WHEN map_type = 'Team' THEN 2
                    WHEN map_type = 'FFA' THEN 3
                    ELSE 4
                END,
                player_count ASC,
                name ASC
        `;
        
        db.all(query, [], (err, rows) => {
            if (err) {
                console.error('Error fetching W3 maps:', err);
                res.status(500).json({ error: 'Failed to fetch W3 maps' });
                return;
            }
            
            // Group maps by type for better organization
            const groupedMaps = {
                oneVsOne: rows.filter(map => map.map_type === '1v1'),
                team: rows.filter(map => map.map_type === 'Team'),
                ffa: rows.filter(map => map.map_type === 'FFA'),
                other: rows.filter(map => !['1v1', 'Team', 'FFA'].includes(map.map_type))
            };
            
            res.json({
                success: true,
                total: rows.length,
                maps: rows,
                grouped: groupedMaps
            });
        });
    } catch (error) {
        console.error('Error in W3 maps endpoint:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// API endpoint to get a specific W3 map details
app.get('/api/w3-maps/:id', (req, res) => {
    try {
        const mapId = req.params.id;
        const query = `
            SELECT * FROM w3_maps WHERE id = ?
        `;
        
        db.get(query, [mapId], (err, row) => {
            if (err) {
                console.error('Error fetching W3 map details:', err);
                res.status(500).json({ error: 'Failed to fetch map details' });
                return;
            }
            
            if (!row) {
                res.status(404).json({ error: 'Map not found' });
                return;
            }
            
            // Parse JSON fields
            if (row.extracted_files) {
                row.extracted_files = JSON.parse(row.extracted_files);
            }
            if (row.images_info) {
                row.images_info = JSON.parse(row.images_info);
            }
            
            res.json({
                success: true,
                map: row
            });
        });
    } catch (error) {
        console.error('Error in W3 map details endpoint:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
}); 