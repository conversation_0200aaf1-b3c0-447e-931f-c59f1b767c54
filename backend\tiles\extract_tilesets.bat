@echo off
echo Warcraft 2 Tileset Extraction Helper
echo =====================================
echo.
echo This script will help you extract tilesets from War2Dat.mpq
echo.
echo Step 1: Download WinMPQ 1.64
echo Visit: https://forum.war2.ru/index.php?topic=961.0
echo.
echo Step 2: Download the listfile from the same topic
echo.
echo Step 3: Open War2Dat.mpq with WinMPQ
echo Location: C:\Users\<USER>\OneDrive\Desktop\newsite\War2Combat\War2Dat.mpq
echo.
echo Step 4: Add the listfile (Options -> File Lists -> Add)
echo.
echo Step 5: Extract .grp files to this directory:
echo C:\Users\<USER>\OneDrive\Desktop\newsite\backend\tiles
echo.
echo Press any key to open the extraction guide...
pause
notepad "C:\Users\<USER>\OneDrive\Desktop\newsite\backend\tiles\EXTRACTION_GUIDE.md"
