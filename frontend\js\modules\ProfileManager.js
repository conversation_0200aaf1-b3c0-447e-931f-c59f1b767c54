/**
 * ProfileManager.js - Master Coordinator for Profile System
 * 
 * This is the main controller that coordinates all profile modules
 * and replaces the 126KB myprofile.js monster.
 * 
 * Coordinates:
 * - ProfileDataLoader: Data management and API calls
 * - ProfileUIManager: UI updates and visual feedback  
 * - ProfileFormHandlers: Form processing and validation
 * - ProfileTabManager: Tab switching and navigation
 * - ClanManager: Clan functionality and management
 */

import { profileDataLoader } from './ProfileDataLoader.js';
import { profileUIManager } from './ProfileUIManager.js';
import { profileFormHandlers } from './ProfileFormHandlers.js';
import { profileTabManager } from './ProfileTabManager.js';
import { ClanManager } from './ClanManager.js';
import { BarracksManager } from './BarracksManager.js';
import { avatarManager } from './AvatarManager.js';

export class ProfileManager {
  constructor() {
    this.isInitialized = false;
    this.loadingState = false;
    this.modules = {
      dataLoader: profileDataLoader,
      uiManager: profileUIManager,
      formHandlers: profileFormHandlers,
      tabManager: profileTabManager,
      clanManager: new ClanManager(),
      barracksManager: new BarracksManager(),
      avatarManager: avatarManager,
      membershipManager: window.membershipManager || null
    };
  }

  /**
   * Initialize the entire profile system
   */
  async init() {
    if (this.isInitialized) {
      console.log('⚠️ Profile Manager already initialized');
      return;
    }

    console.log('🚀 Initializing Profile Manager...');
    this.loadingState = true;

    try {
      // Initialize all modules in proper order
      console.log('📦 Initializing UI Manager...');
      this.modules.uiManager.init();
      
      console.log('📝 Initializing Form Handlers...');
      this.modules.formHandlers.init();
      
      console.log('📑 Initializing Tab Manager...');
      this.modules.tabManager.init();

      console.log('🏰 Initializing Clan Manager...');
      await this.modules.clanManager.init();

      console.log('🏰 Initializing Barracks Manager...');
      await this.modules.barracksManager.init();

      console.log('🎨 Initializing Avatar Manager...');
      await this.modules.avatarManager.init();

      console.log('🛡️ Initializing Unified Membership Manager...');
      if (this.modules.membershipManager) {
        // The unified MembershipManager is already initialized from the profile page
        console.log('✅ Unified MembershipManager already available');
      } else {
        console.warn('⚠️ MembershipManager not found on window object');
      }

      // Load initial profile data
      console.log('🔄 Loading initial profile data...');
      await this.loadInitialData();

      // Setup global event handlers
      this.setupGlobalEventHandlers();

      // Load and apply saved layout
      await this.loadSavedLayout();

      // Mark as initialized
      this.isInitialized = true;
      this.loadingState = false;

      console.log('✅ Profile Manager fully initialized!');
      
      // Trigger initial UI update
      this.refreshProfileDisplay();

    } catch (error) {
      console.error('❌ Failed to initialize Profile Manager:', error);
      this.loadingState = false;
      throw error;
    }
  }

  /**
   * Load initial data for profile page
   */
  async loadInitialData() {
    try {
      // Load all profile data in parallel for maximum performance
      const allData = await this.modules.dataLoader.loadAllProfileData();
      
      // Update UI with loaded data
      await this.updateAllUI(allData);
      
      console.log('✅ Initial data loaded and UI updated');
      return allData;
      
    } catch (error) {
      console.error('❌ Failed to load initial data:', error);
      this.showError('Failed to load profile data. Please refresh the page.');
      throw error;
    }
  }

  /**
   * Update all UI components with loaded data
   */
  async updateAllUI(profileData) {
    console.log('🎨 Updating all UI components...');

    try {
      // Update individual components in order of importance
      await Promise.all([
        this.modules.uiManager.updateUserProfile(profileData.user),
        this.modules.uiManager.updatePlayerStats(profileData.playerStats),
        this.modules.uiManager.updateCampaignStats(profileData.campaignData),
        this.modules.uiManager.updateAchievementStats(profileData.achievementData),
        this.modules.uiManager.updateTournamentActivity(profileData.tournaments),
        this.modules.uiManager.updateClanManagement(profileData.clanData)
      ]);

      // Update Barracks with player data
      if (profileData.playerNames && this.modules.barracksManager) {
        this.modules.barracksManager.updatePlayers(profileData.playerNames);
      }

      // Update points display (depends on other data)
      this.modules.uiManager.updatePointsDisplay(profileData.user);
      
      // Update experience bar (depends on points/experience)
      this.modules.uiManager.updateExperienceBar(profileData.user);
      
      // Update user level (depends on points/experience)
      this.modules.uiManager.updateUserLevel(profileData.user);
      
      // Check if achievements were awarded during this profile load
      if (profileData.user.achievementsAwarded && profileData.user.achievementsAwarded > 0) {
        console.log(`🏆 ${profileData.user.achievementsAwarded} achievement(s) detected on profile load`);
        
        // Use new unified achievement system
        if (window.achievementEngine && window.achievementUI) {
          window.achievementEngine.updateUserProgress({
            experience: profileData.user.experience,
            arenaGold: profileData.user.arenaGold,
            honor: profileData.user.honor
          });
          
          await window.achievementUI.handleAchievementAward({
            count: profileData.user.achievementsAwarded,
            achievements: profileData.user.newAchievementData || []
          });
          
          // Trigger immediate notification refresh
          if (window.notificationsManager) {
            setTimeout(() => {
              window.notificationsManager.loadNotifications();
            }, 500);
          }
        } else {
          // Fallback to old system
          this.modules.uiManager.handleAchievementUnlock(profileData.user.achievementsAwarded);
        }
      }

      // Initialize tab system (no activity data needed since we removed activity section)
      this.modules.tabManager.initializeWithData({});

      // Refresh clan system now that user data is available
      if (this.modules.clanManager && typeof this.modules.clanManager.refreshWithUserData === 'function') {
        await this.modules.clanManager.refreshWithUserData();
      }

      // Store timestamp for auto-refresh logic
      localStorage.setItem('lastProfileRefresh', Date.now().toString());

    } catch (error) {
      console.error('❌ Failed to update UI:', error);
      throw error;
    }

    console.log('✅ All UI components updated');
  }

  /**
   * Refresh entire profile display
   */
  async refreshProfileDisplay() {
    if (this.loadingState) return;

    console.log('🔄 Refreshing profile display...');
    this.loadingState = true;

    try {
      // Clear all caches
      this.modules.dataLoader.clearCache();
      this.modules.tabManager.clearAllCaches();

      // Reload and update
      await this.loadInitialData();

    } catch (error) {
      console.error('❌ Failed to refresh profile:', error);
      this.showError('Failed to refresh profile data.');
    } finally {
      this.loadingState = false;
    }
  }

  /**
   * Setup global event handlers for profile functionality
   */
  setupGlobalEventHandlers() {
    // Global refresh button
    const refreshBtn = document.getElementById('refresh-profile-btn');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => {
        this.refreshProfileDisplay();
      });
    }

    // Layout control buttons
    this.setupLayoutControls();

    // Handle all activity-tab clicks (including campaign section tabs)
    document.addEventListener('click', (e) => {
      if (e.target.closest('.activity-tab-collapse')) {
        // Handle collapse button clicks
        e.stopPropagation();
        this.handleTabCollapse(e.target.closest('.activity-tab-collapse'));
      } else if (e.target.closest('.activity-tab')) {
        // Handle regular tab clicks
        const tabButton = e.target.closest('.activity-tab');
        const tabName = tabButton.dataset.tab;
        
        if (tabName) {
          this.handleTabClick(tabButton, tabName);
        }
      }
    });

    // Handle window visibility changes (refresh when tab becomes active)
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden && this.shouldAutoRefresh()) {
        this.refreshProfileDisplay();
      }
    });

    // Handle online/offline status
    window.addEventListener('online', () => {
      console.log('🌐 Connection restored, refreshing profile...');
      this.refreshProfileDisplay();
    });

    window.addEventListener('offline', () => {
      console.log('📡 Connection lost');
      this.showError('Connection lost. Some features may not work properly.');
    });

    // Setup performance monitoring
    this.setupPerformanceMonitoring();

    console.log('✅ Global event handlers setup complete');
  }

  /**
   * Setup performance monitoring for profile system
   */
  setupPerformanceMonitoring() {
    // Monitor large data loads
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      const startTime = performance.now();
      const result = await originalFetch(...args);
      const endTime = performance.now();
      
      if (endTime - startTime > 1000) {
        console.warn(`⚠️ Slow API call: ${args[0]} took ${Math.round(endTime - startTime)}ms`);
      }
      
      return result;
    };

    // Monitor DOM updates
    let updateCount = 0;
    const originalUpdateElement = this.modules.uiManager.updateElement;
    this.modules.uiManager.updateElement = (...args) => {
      updateCount++;
      if (updateCount % 50 === 0) {
        console.log(`📊 DOM updates: ${updateCount} total`);
      }
      return originalUpdateElement.apply(this.modules.uiManager, args);
    };
  }

  /**
   * Check if auto-refresh should happen
   */
  shouldAutoRefresh() {
    // Only auto-refresh if page has been hidden for more than 5 minutes
    const lastRefresh = localStorage.getItem('lastProfileRefresh');
    if (!lastRefresh) return true;
    
    const timeSinceRefresh = Date.now() - parseInt(lastRefresh);
    return timeSinceRefresh > 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Show error message to user
   */
  showError(message) {
    // Try to use existing error display, fallback to alert
    const errorContainer = document.getElementById('profile-error-container');
    if (errorContainer) {
      errorContainer.innerHTML = `
        <div class="error-message">
          <i class="fas fa-exclamation-triangle"></i>
          <span>${message}</span>
          <button onclick="profileManager.refreshProfileDisplay()" class="btn btn-sm btn-secondary">
            <i class="fas fa-refresh"></i> Retry
          </button>
        </div>
      `;
      errorContainer.style.display = 'block';
    } else {
      console.error('Profile Error:', message);
    }
  }

  /**
   * Add a new player (called from UI)
   */
  async addPlayer(playerName) {
    try {
      console.log(`➕ Adding player: ${playerName}`);
      
      // Use form handler to add player
      await this.modules.formHandlers.setupAddPlayerForm();
      
      // Refresh player data
      this.modules.dataLoader.clearCache('player-names');
      this.modules.dataLoader.clearCache('player-stats');
      
      const players = await this.modules.dataLoader.loadPlayerStats();
      await this.modules.uiManager.updatePlayerStats(players);
      
      console.log('✅ Player added successfully');
      return true;
      
    } catch (error) {
      console.error('❌ Failed to add player:', error);
      this.showError(`Failed to add player ${playerName}`);
      return false;
    }
  }

  /**
   * Remove a player (called from UI)
   */
  async removePlayer(playerId) {
    try {
      console.log(`➖ Removing player: ${playerId}`);
      
      const response = await fetch(`/api/ladder/players/${playerId}`, {
        method: 'DELETE',
        credentials: 'include'
      });

      if (response.ok) {
        // Refresh player data
        this.modules.dataLoader.clearCache('player-names');
        this.modules.dataLoader.clearCache('player-stats');
        
        const players = await this.modules.dataLoader.loadPlayerStats();
        await this.modules.uiManager.updatePlayerStats(players);
        
        console.log('✅ Player removed successfully');
        return true;
      } else {
        throw new Error('Failed to remove player');
      }
      
    } catch (error) {
      console.error('❌ Failed to remove player:', error);
      this.showError('Failed to remove player');
      return false;
    }
  }

  /**
   * Switch tab (called from UI)
   */
  switchTab(tabName) {
    return this.modules.tabManager.switchToTab(tabName);
  }

  /**
   * Handle tab click events for both activity tabs and campaign section tabs
   */
  async handleTabClick(tabButton, tabName) {
    // Check if this is a campaign section tab
    const campaignSection = tabButton.closest('#section-campaign');
    
    if (campaignSection) {
      // Handle campaign section tabs
      this.handleCampaignSectionTab(tabButton, tabName);
    } else {
      // Handle main activity tabs
      this.modules.tabManager.switchToTab(tabName);
    }
  }

  /**
   * Handle campaign section tab switching
   */
  async handleCampaignSectionTab(tabButton, tabName) {
    const campaignSection = tabButton.closest('#section-campaign');
    if (!campaignSection) return;

    // Update tab button states
    const allTabs = campaignSection.querySelectorAll('.activity-tab');
    const allContents = campaignSection.querySelectorAll('.activity-content');

    allTabs.forEach(tab => tab.classList.remove('active'));
    allContents.forEach(content => content.style.display = 'none');

    tabButton.classList.add('active');

    // Show the corresponding content
    const targetContent = campaignSection.querySelector(`#${tabName}-tab`);
    if (targetContent) {
      targetContent.style.display = 'block';

      // Load campaign achievements if switching to that tab
      if (tabName === 'campaign-achievements') {
        await this.loadCampaignAchievements();
      }
    }
  }

  /**
   * Load and display campaign achievements
   */
  async loadCampaignAchievements() {
    try {
      const campaignAchievements = await this.modules.dataLoader.loadCampaignAchievements();
      this.modules.uiManager.updateCampaignAchievements(campaignAchievements);
    } catch (error) {
      console.error('❌ Failed to load campaign achievements:', error);
      this.showError('Failed to load campaign achievements.');
    }
  }

  /**
   * Get system status for debugging
   */
  getSystemStatus() {
    const status = {
      initialized: this.isInitialized,
      loading: this.loadingState,
      modules: {
        dataLoader: !!this.modules.dataLoader,
        uiManager: !!this.modules.uiManager,
        formHandlers: !!this.modules.formHandlers,
        tabManager: !!this.modules.tabManager
      },
      cacheStats: {
        dataLoaderCache: this.modules.dataLoader.cache.size,
        tabManagerCache: this.modules.tabManager.tabContent.size
      },
      performance: {
        userAgent: navigator.userAgent,
        online: navigator.onLine,
        memory: performance.memory ? {
          used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + 'MB',
          total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + 'MB'
        } : 'not available'
      }
    };

    console.log('📊 Profile System Status:', status);
    return status;
  }

  /**
   * Emergency reset (clear all data and reload)
   */
  async emergencyReset() {
    console.log('🚨 Emergency reset initiated...');
    
    try {
      // Clear all caches and local storage
      this.modules.dataLoader.clearCache();
      this.modules.tabManager.clearAllCaches();
      localStorage.removeItem('lastProfileRefresh');
      
      // Reset all modules
      this.isInitialized = false;
      
      // Reinitialize
      await this.init();
      
      console.log('✅ Emergency reset completed');
      this.showSuccess('Profile system reset successfully');
      
    } catch (error) {
      console.error('❌ Emergency reset failed:', error);
      this.showError('Reset failed. Please refresh the page manually.');
    }
  }

  /**
   * Show success message
   */
  showSuccess(message) {
    console.log('✅', message);
    // Could implement UI success notification here
  }

  /**
   * Setup layout control handlers
   */
  setupLayoutControls() {
    console.log('⚙️ Setting up layout controls...');
    
    const saveLayoutBtn = document.getElementById('save-layout');
    const minimizeAllBtn = document.getElementById('minimize-all');
    const maximizeAllBtn = document.getElementById('maximize-all');
    
    if (saveLayoutBtn) {
      saveLayoutBtn.addEventListener('click', (e) => {
        e.preventDefault();
        this.saveLayout();
      });
      console.log('✅ Save layout button connected');
    } else {
      console.warn('⚠️ Save layout button not found');
    }
    
    if (minimizeAllBtn) {
      minimizeAllBtn.addEventListener('click', (e) => {
        e.preventDefault();
        console.log('🔽 Minimize All button clicked');
        
        // Use the draggable grid's minimize all method
        if (window.draggableGrid && window.draggableGrid.minimizeAll) {
          window.draggableGrid.minimizeAll();
        } else {
          console.error('❌ draggableGrid not found or missing minimizeAll method');
        }
      });
      console.log('✅ Minimize all button connected');
    } else {
      console.warn('⚠️ Minimize all button not found');
    }
    
    if (maximizeAllBtn) {
      maximizeAllBtn.addEventListener('click', (e) => {
        e.preventDefault();
        console.log('🔼 Maximize All button clicked');
        
        // Use the draggable grid's maximize all method
        if (window.draggableGrid && window.draggableGrid.maximizeAll) {
          window.draggableGrid.maximizeAll();
        } else {
          console.error('❌ draggableGrid not found or missing maximizeAll method');
        }
      });
      console.log('✅ Maximize all button connected');
    } else {
      console.warn('⚠️ Maximize all button not found');
    }
  }

  /**
   * Save current layout to database
   */
  async saveLayout() {
    console.log('💾 Saving profile layout...');
    
    try {
      // Collect current layout data
      const layoutData = this.getCurrentLayoutData();
      
      // Save to database via API
      const response = await fetch('/api/users/profile/layout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(layoutData)
      });
      
      if (response.ok) {
        console.log('✅ Layout saved successfully');
        this.showLayoutMessage('Layout saved successfully!', 'success');
      } else {
        console.error('❌ Failed to save layout:', response.status);
        this.showLayoutMessage('Failed to save layout', 'error');
      }
    } catch (error) {
      console.error('❌ Error saving layout:', error);
      this.showLayoutMessage('Error saving layout', 'error');
    }
  }

  /**
   * Load saved layout from database
   */
  async loadSavedLayout() {
    console.log('📖 Loading saved profile layout...');
    
    try {
      const response = await fetch('/api/users/profile/layout', {
        method: 'GET',
        credentials: 'include'
      });
      
      if (response.ok) {
        const layoutData = await response.json();
        if (layoutData && layoutData.sections) {
          console.log('✅ Layout data found, applying...');
          this.applySavedLayout(layoutData);
        } else {
          console.log('ℹ️ No saved layout found, using default');
        }
      } else {
        console.log('ℹ️ No saved layout available');
      }
    } catch (error) {
      console.error('❌ Error loading layout:', error);
    }
  }

  /**
   * Collect current layout data from the grid
   */
  getCurrentLayoutData() {
    const sections = document.querySelectorAll('.draggable-section');
    const layoutData = {
      sections: [],
      timestamp: new Date().toISOString()
    };
    
    sections.forEach(section => {
      const sectionData = {
        id: section.id,
        sectionType: section.dataset.section,
        position: parseInt(section.dataset.position) || 0,
        isMinimized: section.classList.contains('minimized'),
        gridColumn: section.style.gridColumn || '',
        gridRow: section.style.gridRow || ''
      };
      
      layoutData.sections.push(sectionData);
      console.log(`📊 Captured section: ${sectionData.id}, pos: ${sectionData.position}, minimized: ${sectionData.isMinimized}`);
    });
    
    // Sort by position to maintain order
    layoutData.sections.sort((a, b) => a.position - b.position);
    
    console.log('📊 Layout data collected:', layoutData);
    return layoutData;
  }

  /**
   * Apply saved layout data to the grid
   */
  applySavedLayout(layoutData) {
    console.log('🎨 Applying saved layout data...');
    
    try {
      layoutData.sections.forEach(sectionData => {
        const section = document.getElementById(sectionData.id);
        if (section) {
          // Apply position
          section.dataset.position = sectionData.position;
          
          // Apply grid positioning if available
          if (sectionData.gridColumn) {
            section.style.gridColumn = sectionData.gridColumn;
          }
          if (sectionData.gridRow) {
            section.style.gridRow = sectionData.gridRow;
          }
          
          // Apply minimize/maximize state
          const btn = section.querySelector('.minimize-toggle');
          const content = section.querySelector('.section-content');
          const btnText = btn?.querySelector('.btn-text');
          const indicator = section.querySelector('.grid-position-indicator');
          
          if (sectionData.isMinimized) {
            // Set to minimized state
            section.classList.add('minimized');
            if (content) content.style.display = 'none';
            if (btnText) btnText.textContent = 'Restore';
            if (btn) btn.title = 'Show section content';
            if (indicator) {
              indicator.textContent = indicator.textContent.replace(' (Min)', '') + ' (Min)';
            }
          } else {
            // Set to maximized state
            section.classList.remove('minimized');
            if (content) content.style.display = 'block';
            if (btnText) btnText.textContent = 'Minimize';
            if (btn) btn.title = 'Hide section content';
            if (indicator) {
              indicator.textContent = indicator.textContent.replace(' (Min)', '');
            }
          }
          
          console.log(`🎨 Applied layout to ${sectionData.id}: pos ${sectionData.position}, minimized: ${sectionData.isMinimized}`);
        } else {
          console.warn(`⚠️ Section not found: ${sectionData.id}`);
        }
      });
      
      // Trigger a reflow to ensure proper positioning
      if (window.draggableGrid && window.draggableGrid.reflowSections) {
        window.draggableGrid.reflowSections();
      }
      
      console.log('✅ Saved layout applied successfully');
    } catch (error) {
      console.error('❌ Error applying saved layout:', error);
    }
  }

  /**
   * Show layout save/load message to user
   */
  showLayoutMessage(message, type = 'info') {
    // Create or update message element
    let messageEl = document.getElementById('layout-message');
    if (!messageEl) {
      messageEl = document.createElement('div');
      messageEl.id = 'layout-message';
      messageEl.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        color: white;
        font-weight: 500;
        z-index: 1000;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
      `;
      document.body.appendChild(messageEl);
    }
    
    // Set message and style based on type
    messageEl.textContent = message;
    messageEl.style.background = type === 'success' ? 'rgba(34, 197, 94, 0.9)' : 
                                 type === 'error' ? 'rgba(239, 68, 68, 0.9)' : 
                                 'rgba(59, 130, 246, 0.9)';
    
    // Show message
    requestAnimationFrame(() => {
      messageEl.style.opacity = '1';
      messageEl.style.transform = 'translateX(0)';
    });
    
    // Hide message after 3 seconds
    setTimeout(() => {
      messageEl.style.opacity = '0';
      messageEl.style.transform = 'translateX(100%)';
    }, 3000);
  }

  /**
   * Cleanup before page unload
   */
  cleanup() {
    console.log('🧹 Cleaning up Profile Manager...');
    
    // Store last refresh time
    localStorage.setItem('lastProfileRefresh', Date.now().toString());
    
    // Cancel any pending operations
    this.loadingState = false;
    
    // Clear timers from tab manager
    this.modules.tabManager.clearAllCaches();
  }

  /**
   * Initialize event listeners and basic setup
   */
  initializeEventListeners() {
    // Tab switching
    document.addEventListener('click', (e) => {
      if (e.target.closest('.activity-tab') && !e.target.closest('.activity-tab-collapse')) {
        const tabButton = e.target.closest('.activity-tab');
        const tabName = tabButton.dataset.tab;
        this.handleTabClick(tabButton, tabName);
      }
    });

    // War Table tab collapse functionality
    document.addEventListener('click', (e) => {
      if (e.target.closest('.activity-tab-collapse')) {
        e.stopPropagation(); // Prevent tab switching
        this.handleTabCollapse(e.target.closest('.activity-tab-collapse'));
      }
    });

    // Setup layout controls
    this.setupLayoutControls();
  }

  /**
   * Handle War Table tab collapse/expand
   */
  handleTabCollapse(collapseButton) {
    const tabName = collapseButton.dataset.collapse;
    const tab = collapseButton.closest('.activity-tab');
    const content = document.getElementById(`${tabName}-tab`);
    const icon = collapseButton.querySelector('i');

    if (!content || !tab) return;

    if (tab.classList.contains('collapsed')) {
      // Expand
      tab.classList.remove('collapsed');
      content.classList.remove('collapsed');
      content.style.display = 'block';
      icon.classList.remove('fa-chevron-down');
      icon.classList.add('fa-chevron-up');
    } else {
      // Collapse
      tab.classList.add('collapsed');
      content.classList.add('collapsed');
      content.style.display = 'none';
      icon.classList.remove('fa-chevron-up');
      icon.classList.add('fa-chevron-down');
    }
  }
}

// Create and export singleton instance
export const profileManager = new ProfileManager();

// Global cleanup on page unload
window.addEventListener('beforeunload', () => {
  profileManager.cleanup();
});

// Expose to global scope for debugging and UI callbacks
window.profileManager = profileManager; 