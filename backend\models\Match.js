const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Match Schema
 *
 * Represents a match between players, including match details, results, and verification status.
 */
const MatchSchema = new Schema({
  // Game type (warcraft2 or warcraft3)
  gameType: {
    type: String,
    enum: ['warcraft1', 'warcraft2', 'warcraft3'],
    default: 'warcraft2',
    index: true
  },

  // Match type (1v1, 2v2, 3v3, 4v4, ffa, vsai)
  matchType: {
    type: String,
    required: true,
    enum: ['1v1', '2v2', '3v3', '4v4', 'ffa', 'vsai']
  },

  // Map information
  map: {
    // Map name (required)
    name: {
      type: String,
      required: true
    },
    // Optional image of the map (mainly for war2 maps)
    image: {
      type: String,
      default: null
    },
    // Reference to Map model for war2 maps (optional)
    mapId: {
      type: Schema.Types.ObjectId,
      ref: 'Map',
      default: null
    },
    // Reference to War3Map model for war3 maps (optional)
    war3MapId: {
      type: Schema.Types.ObjectId,
      ref: 'War3Map',
      default: null
    }
  },

  // Resource level (high, medium, low)
  resourceLevel: {
    type: String,
    required: true,
    enum: ['high', 'medium', 'low']
  },

  // Players involved in the match
  players: [{
    playerId: {
      type: Schema.Types.ObjectId,
      ref: 'Player',
      required: true
    },
    name: String, // For quick reference without population
    team: {
      type: Number,
      default: 0 // 0 for FFA, 1 or 2 for team matches
    },
    placement: Number, // For FFA matches (1st, 2nd, 3rd, etc.)
    race: {
      type: String,
      enum: ['human', 'orc', 'undead', 'night_elf', 'random'],
      default: 'random'
    },
    isAI: {
      type: Boolean,
      default: false
    },
    mmrBefore: Number,
    mmrAfter: Number,
    mmrChange: Number,
    rankBefore: String,
    rankAfter: String,
    rankChanged: Boolean
  }],

  // Winner information
  winner: {
    // For team matches: team number that won (1 or 2)
    // For 1v1: player ID that won
    // For FFA: player ID that got 1st place
    type: Schema.Types.Mixed,
    required: true
  },

  // Match date and time
  date: {
    type: Date,
    default: Date.now
  },

  // Match duration in minutes
  duration: {
    type: Number,
    default: 0
  },

  // Screenshots for verification
  screenshots: [{
    url: {
      type: String,
      required: true
    },
    uploadedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    uploadDate: {
      type: Date,
      default: Date.now
    }
  }],

  // Match verification status
  verification: {
    status: {
      type: String,
      enum: ['pending', 'verified', 'rejected', 'disputed'],
      default: 'pending'
    },
    verifiedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      default: null
    },
    verifiedAt: {
      type: Date,
      default: null
    },
    rejectionReason: {
      type: String,
      default: null
    }
  },

  // Match report information
  report: {
    reportedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    reportedAt: {
      type: Date,
      default: Date.now
    },
    notes: {
      type: String,
      default: ''
    }
  },

  // Disputes
  disputes: [{
    disputedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    playerName: {
      type: String,
      required: true
    },
    reason: {
      type: String,
      required: true
    },
    status: {
      type: String,
      enum: ['open', 'resolved', 'rejected'],
      default: 'open'
    },
    resolvedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      default: null
    },
    resolution: {
      type: String,
      default: null
    },
    evidence: {
      type: String,
      default: null
    },
    createdAt: {
      type: Date,
      default: Date.now
    },
    resolvedAt: {
      type: Date,
      default: null
    }
  }],

  // Edit proposals for match modification
  editProposals: [{
    proposedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    proposedChanges: {
      map: String,
      resourceLevel: String,
      opponent: String,
      outcome: String,
      matchType: String,
      race: String,
      notes: String
    },
    editReason: {
      type: String,
      required: true
    },
    status: {
      type: String,
      enum: ['pending', 'approved', 'rejected'],
      default: 'pending'
    },
    reviewedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      default: null
    },
    reviewedAt: {
      type: Date,
      default: null
    },
    reviewNotes: {
      type: String,
      default: null
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],

  // Whether MMR has been calculated and applied for this match
  mmrCalculated: {
    type: Boolean,
    default: false
  },

  // Whether this match counts towards ladder rankings
  countsForLadder: {
    type: Boolean,
    default: true
  },

  // Season information
  season: {
    type: Number,
    default: 1
  },

  // Uneven teams flag (for MMR adjustment)
  unevenTeams: {
    type: Boolean,
    default: false
  },

  // AI game options (for vsai matches)
  aiGameOptions: {
    type: mongoose.Schema.Types.Mixed,
    default: null
  }
}, {
  timestamps: true
});

// Indexes for faster lookups
MatchSchema.index({ matchType: 1 });
MatchSchema.index({ date: -1 });
MatchSchema.index({ 'verification.status': 1 });
MatchSchema.index({ 'players.playerId': 1 });
MatchSchema.index({ season: 1 });

// Static method to get recent matches
MatchSchema.statics.getRecentMatches = function(limit = 20) {
  return this.find({ 'verification.status': 'verified' })
    .sort({ date: -1 })
    .limit(limit)
    .populate('players.playerId', 'name mmr rank')
    .populate('report.reportedBy', 'username displayName avatar');
};

// Static method to get matches for a player
MatchSchema.statics.getPlayerMatches = function(playerId, limit = 20) {
  return this.find({
    'players.playerId': playerId,
    'verification.status': 'verified'
  })
    .sort({ date: -1 })
    .limit(limit)
    .populate('players.playerId', 'name mmr rank')
    .populate('report.reportedBy', 'username displayName avatar');
};

// Static method to get disputed matches
MatchSchema.statics.getDisputedMatches = function(limit = 20) {
  return this.find({ 'verification.status': 'disputed' })
    .sort({ date: -1 })
    .limit(limit)
    .populate('players.playerId', 'name mmr rank')
    .populate('report.reportedBy', 'username displayName avatar')
    .populate('disputes.disputedBy', 'username displayName avatar');
};

module.exports = mongoose.model('Match', MatchSchema);
