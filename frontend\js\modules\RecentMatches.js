/**
 * RecentMatches Module
 * Handles recent matches display, pagination, and filtering
 */

class RecentMatches {
  constructor() {
    this.ladderAPI = null;
    this.currentGameType = 'warcraft2';
    this.currentMatchType = 'all';
    this.currentPage = 1;
    this.totalPages = 1;
    this.matches = [];
    this.itemsPerPage = 10;
  }

  /**
   * Initialize the recent matches system
   */
  init() {
    this.ladderAPI = window.ladderAPI;
    this.setupGlobalFunctions();
    console.log('📊 RecentMatches module initialized');
  }

  /**
   * Set current game type
   */
  setGameType(gameType) {
    this.currentGameType = gameType;
  }

  /**
   * Set up global functions for external access
   */
  setupGlobalFunctions() {
    window.loadRecentMatches = (matchType = 'all', showLoading = true, gameType = null) => 
      this.loadRecentMatches(matchType, showLoading, gameType);
    window.refreshRecentMatches = () => this.refreshMatches();
  }

  /**
   * Load recent matches
   * @param {string} matchType - Match type filter ('all', '1v1', '2v2', etc.)
   * @param {boolean} showLoading - Whether to show loading indicator
   * @param {string} gameType - Game type filter
   */
  async loadRecentMatches(matchType = 'all', showLoading = true, gameType = null) {
    try {
      console.log('📊 Loading recent matches:', { matchType, showLoading, gameType });

      const targetGameType = gameType || this.currentGameType;
      this.currentMatchType = matchType;
      this.currentPage = 1; // Reset to first page when changing filters

      // Show loading state
      if (showLoading) {
        this.showLoading();
      }

      // Load data from API
      let data;
      if (this.ladderAPI && typeof this.ladderAPI.loadRecentMatches === 'function') {
        data = await this.ladderAPI.loadRecentMatches(matchType, this.currentPage, targetGameType);
      } else {
        // Fallback to direct API call
        data = await this.fetchRecentMatchesDirect(matchType, this.currentPage, targetGameType);
      }

      // Store matches data
      this.matches = Array.isArray(data) ? data : (data.matches || []);
      this.totalPages = data.pagination?.totalPages || 1;

      // Display the matches
      this.displayRecentMatches();

      // Update pagination if container exists
      this.updatePagination();

      console.log('✅ Recent matches loaded successfully:', this.matches.length, 'matches');
      return data;
    } catch (error) {
      console.error('❌ Error loading recent matches:', error);
      this.showErrorMessage('Failed to load recent matches. Please try again later.');
      throw error;
    }
  }

  /**
   * Direct API call fallback
   */
  async fetchRecentMatchesDirect(matchType, page, gameType) {
    const params = new URLSearchParams({
      limit: this.itemsPerPage.toString(),
      page: page.toString()
    });

    if (matchType && matchType !== 'all') {
      params.append('matchType', matchType);
    }

    if (gameType && gameType !== 'all') {
      params.append('gameType', gameType);
    }

    const response = await fetch(`/api/ladder/matches?${params}`);
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  }

  /**
   * Display recent matches in the container
   */
  displayRecentMatches() {
    const container = this.getContainer();
    if (!container) return;

    if (this.matches.length === 0) {
      this.showEmptyState();
      return;
    }

    // Clear container
    container.innerHTML = '';

    // Create match cards
    this.matches.forEach(match => {
      const matchCard = this.createMatchCard(match);
      container.appendChild(matchCard);
    });

    console.log('📊 Displayed', this.matches.length, 'recent matches');
  }

  /**
   * Create a match card element
   */
  createMatchCard(match) {
    const card = document.createElement('div');
    card.className = 'match-card';

    // Format date
    const matchDate = new Date(match.date || match.createdAt);
    const timeAgo = this.formatTimeAgo(matchDate);

    // Determine match result display
    const { winnerDisplay, loserDisplay } = this.formatMatchResult(match);

    // Get map name
    const mapName = match.map?.name || match.map || 'Unknown Map';

    // Create match type badge
    const matchTypeBadge = this.createMatchTypeBadge(match.matchType);

    card.innerHTML = `
      <div class="match-header">
        ${matchTypeBadge}
        <span class="match-date">${timeAgo}</span>
      </div>
      <div class="match-details">
        <div class="match-players">
          <div class="match-result winners">
            <span class="result-label">Winners:</span>
            <span class="player-names">${winnerDisplay}</span>
          </div>
          <div class="match-result losers">
            <span class="result-label">Losers:</span>
            <span class="player-names">${loserDisplay}</span>
          </div>
        </div>
        <div class="match-map">
          <i class="fas fa-map"></i>
          <span>${mapName}</span>
        </div>
      </div>
      <div class="match-actions">
        <button class="btn btn-sm btn-secondary" onclick="viewMatchDetails('${match._id || match.id}')">
          <i class="fas fa-eye"></i> Details
        </button>
        ${match.screenshots && match.screenshots.length > 0 ? `
          <button class="btn btn-sm btn-secondary" onclick="viewScreenshots('${match._id || match.id}')">
            <i class="fas fa-images"></i> Screenshots
          </button>
        ` : ''}
      </div>
    `;

    return card;
  }

  /**
   * Format match result for display
   */
  formatMatchResult(match) {
    const winners = [];
    const losers = [];

    if (match.players && Array.isArray(match.players)) {
      match.players.forEach(player => {
        const playerName = player.name || player.playerId?.name || 'Unknown';
        if (player.result === 'win') {
          winners.push(playerName);
        } else {
          losers.push(playerName);
        }
      });
    }

    const winnerDisplay = winners.length > 0 ? winners.join(', ') : 'Unknown';
    const loserDisplay = losers.length > 0 ? losers.join(', ') : 'Unknown';

    return { winnerDisplay, loserDisplay };
  }

  /**
   * Create match type badge
   */
  createMatchTypeBadge(matchType) {
    const typeClass = matchType ? matchType.toLowerCase() : 'unknown';
    return `<span class="match-type ${typeClass}">${matchType || 'Unknown'}</span>`;
  }

  /**
   * Format time ago string
   */
  formatTimeAgo(date) {
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    
    return date.toLocaleDateString();
  }

  /**
   * Show loading state
   */
  showLoading() {
    const container = this.getContainer();
    if (!container) return;

    container.innerHTML = `
      <div class="loading-matches">
        <div class="loading-spinner"></div>
        <span>Loading recent matches...</span>
      </div>
    `;
  }

  /**
   * Show empty state
   */
  showEmptyState() {
    const container = this.getContainer();
    if (!container) return;

    container.innerHTML = `
      <div class="empty-matches">
        <i class="fas fa-inbox"></i>
        <h3>No Recent Matches</h3>
        <p>No matches found for the selected filters.</p>
        <button class="btn btn-primary" onclick="refreshRecentMatches()">
          <i class="fas fa-refresh"></i> Refresh
        </button>
      </div>
    `;
  }

  /**
   * Show error message
   */
  showErrorMessage(message) {
    const container = this.getContainer();
    if (!container) return;

    container.innerHTML = `
      <div class="error-matches">
        <i class="fas fa-exclamation-triangle"></i>
        <h3>Error Loading Matches</h3>
        <p>${message}</p>
        <button class="btn btn-primary" onclick="refreshRecentMatches()">
          <i class="fas fa-retry"></i> Try Again
        </button>
      </div>
    `;
  }

  /**
   * Get the recent matches container
   */
  getContainer() {
    return document.getElementById('recent-matches-container') || 
           document.getElementById('recent-matches');
  }

  /**
   * Update pagination controls
   */
  updatePagination() {
    const paginationContainer = document.getElementById('recent-matches-pagination');
    if (!paginationContainer || this.totalPages <= 1) {
      if (paginationContainer) paginationContainer.style.display = 'none';
      return;
    }

    paginationContainer.style.display = 'flex';
    
    const prevDisabled = this.currentPage <= 1 ? 'disabled' : '';
    const nextDisabled = this.currentPage >= this.totalPages ? 'disabled' : '';

    paginationContainer.innerHTML = `
      <button class="btn btn-sm" ${prevDisabled} onclick="recentMatches.loadPage(${this.currentPage - 1})">
        <i class="fas fa-chevron-left"></i> Previous
      </button>
      <span class="pagination-info">
        Page ${this.currentPage} of ${this.totalPages}
      </span>
      <button class="btn btn-sm" ${nextDisabled} onclick="recentMatches.loadPage(${this.currentPage + 1})">
        Next <i class="fas fa-chevron-right"></i>
      </button>
    `;
  }

  /**
   * Load a specific page
   */
  async loadPage(page) {
    if (page < 1 || page > this.totalPages) return;
    
    this.currentPage = page;
    await this.loadRecentMatches(this.currentMatchType, true, this.currentGameType);
  }

  /**
   * Refresh current matches
   */
  async refreshMatches() {
    await this.loadRecentMatches(this.currentMatchType, true, this.currentGameType);
  }

  /**
   * Filter matches by type
   */
  async filterByType(matchType) {
    await this.loadRecentMatches(matchType, true, this.currentGameType);
  }

  /**
   * Get current matches data
   */
  getCurrentMatches() {
    return this.matches;
  }

  /**
   * Get current pagination state
   */
  getPaginationState() {
    return {
      currentPage: this.currentPage,
      totalPages: this.totalPages,
      matchType: this.currentMatchType,
      gameType: this.currentGameType
    };
  }

  /**
   * Set up match type filter event listeners
   */
  setupMatchTypeFilters() {
    const filterButtons = document.querySelectorAll('.recent-matches-filters .filter-btn');
    
    filterButtons.forEach(button => {
      button.addEventListener('click', async () => {
        // Remove active class from all buttons
        filterButtons.forEach(btn => btn.classList.remove('active'));
        
        // Add active class to clicked button
        button.classList.add('active');
        
        // Get match type and filter
        const matchType = button.getAttribute('data-match-type') || 'all';
        await this.filterByType(matchType);
      });
    });
  }

  /**
   * Initialize recent matches for a specific container
   */
  async initializeForContainer(containerId, options = {}) {
    const container = document.getElementById(containerId);
    if (!container) {
      console.warn(`Recent matches container ${containerId} not found`);
      return;
    }

    // Set options
    if (options.itemsPerPage) this.itemsPerPage = options.itemsPerPage;
    if (options.gameType) this.currentGameType = options.gameType;
    if (options.matchType) this.currentMatchType = options.matchType;

    // Load initial data
    await this.loadRecentMatches(this.currentMatchType, true, this.currentGameType);

    // Set up filters if they exist
    this.setupMatchTypeFilters();

    console.log(`📊 Recent matches initialized for container: ${containerId}`);
  }
}

// Export for use in other modules
window.RecentMatches = RecentMatches;

// Auto-initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  if (!window.recentMatches) {
    window.recentMatches = new RecentMatches();
  }
}); 