/* ==========================================================================
   COMPREHENSIVE BLACK THEME - WARCRAFT ARENA
   Black backgrounds, silver outlines, gold hover/active states
   No JavaScript required - Pure CSS solution
   ========================================================================== */

/* Import Warcraft-style fonts for enhanced styling */
@import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700&family=Grenze:wght@400;500;600;700&display=swap');

/* ==========================================================================
   ROOT VARIABLES - BLACK THEME SYSTEM
   ========================================================================== */
:root {
  /* Core Theme Colors */
  --theme-black: #000000;
  --theme-silver: #C0C0C0;
  --theme-silver-dark: #808080;
  --theme-silver-light: #E0E0E0;
  --theme-gold: #D4AF37;
  --theme-gold-dark: #B8941F;
  --theme-gold-light: #E8C547;
  
  /* Background Variations */
  --bg-primary: #000000;
  --bg-secondary: #0a0a0a;
  --bg-tertiary: #1a1a1a;
  --bg-overlay: rgba(0, 0, 0, 0.95);
  
  /* Silver Outline System */
  --border-silver: 1px solid var(--theme-silver);
  --border-silver-thick: 2px solid var(--theme-silver);
  --border-silver-heavy: 3px solid var(--theme-silver);
  
  /* Gold Active/Hover System */
  --border-gold: 1px solid var(--theme-gold);
  --border-gold-thick: 2px solid var(--theme-gold);
  --border-gold-heavy: 3px solid var(--theme-gold);
  
  /* Shadows and Glows */
  --shadow-silver: 0 0 8px rgba(192, 192, 192, 0.3);
  --shadow-gold: 0 0 12px rgba(212, 175, 55, 0.5);
  --glow-gold: 0 0 20px rgba(212, 175, 55, 0.6);
  
  /* Text Shadows */
  --text-shadow-base: 1px 1px 2px rgba(0, 0, 0, 0.8);
  --text-shadow-gold: 1px 1px 2px rgba(0, 0, 0, 0.8), 0 0 8px rgba(212, 175, 55, 0.4);
  
  /* Fonts */
  --font-primary: 'Cinzel', serif;
  --font-secondary: 'Grenze', serif;
  
  /* Transitions */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* ==========================================================================
   GLOBAL RESETS AND BASE STYLING
   ========================================================================== */
* {
  box-sizing: border-box;
}

html, body {
  background: var(--theme-black);
  color: var(--theme-silver);
  font-family: var(--font-primary);
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

/* ==========================================================================
   UNIVERSAL CONTAINER STYLING
   All containers get black background with silver outlines
   ========================================================================== */
div, section, article, main, header, footer, nav, aside,
.container, .wrapper, .content, .panel, .box {
  background: var(--theme-black);
  border: var(--border-silver);
  color: var(--theme-silver);
  box-shadow: var(--shadow-silver);
  transition: all var(--transition-normal);
}

/* Remove borders from specific layout containers to avoid over-bordering */
body, html, main, .hero-section {
  border: none;
  box-shadow: none;
}

/* ==========================================================================
   TYPOGRAPHY - ALL TEXT ELEMENTS
   ========================================================================== */
h1, h2, h3, h4, h5, h6, p, span, label, a, li, td, th, 
.text, .title, .subtitle, .caption {
  color: var(--theme-silver);
  text-shadow: var(--text-shadow-base);
  font-family: var(--font-primary);
  transition: color var(--transition-fast);
}

/* Headers get enhanced styling */
h1, h2, h3 {
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Links base styling */
a {
  text-decoration: none;
  color: var(--theme-silver);
  transition: all var(--transition-fast);
}

/* ==========================================================================
   BUTTON STYLING - ALL INTERACTIVE BUTTONS
   ========================================================================== */
button, .btn, input[type="button"], input[type="submit"], 
.button, .action-btn, .download-btn, .support-link {
  background: var(--theme-black);
  color: var(--theme-silver);
  border: var(--border-silver-thick);
  font-family: var(--font-primary);
  font-weight: 600;
  text-shadow: var(--text-shadow-base);
  box-shadow: var(--shadow-silver);
  padding: 10px 20px;
  cursor: pointer;
  transition: all var(--transition-normal);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* ==========================================================================
   FORM ELEMENTS - INPUTS, TEXTAREAS, SELECTS
   ========================================================================== */
input, textarea, select {
  background: var(--theme-black);
  color: var(--theme-silver);
  border: var(--border-silver);
  font-family: var(--font-primary);
  text-shadow: var(--text-shadow-base);
  box-shadow: var(--shadow-silver);
  padding: 8px 12px;
  transition: all var(--transition-fast);
}

/* Special input types */
input[type="checkbox"], input[type="radio"] {
  accent-color: var(--theme-gold);
}

/* Select options */
select option {
  background: var(--theme-black);
  color: var(--theme-silver);
}

/* ==========================================================================
   TABLE STYLING
   ========================================================================== */
table {
  background: var(--theme-black);
  border: var(--border-silver-thick);
  border-collapse: separate;
  border-spacing: 0;
  box-shadow: var(--shadow-silver);
}

table th, table td {
  background: var(--theme-black);
  border: var(--border-silver);
  color: var(--theme-silver);
  text-shadow: var(--text-shadow-base);
  padding: 12px;
  transition: all var(--transition-fast);
}

table th {
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* ==========================================================================
   CARD AND MODAL STYLING
   ========================================================================== */
.card, .modal, .modal-content, .dropdown-menu, .tooltip,
.poll-container, .donation-goal-container, .support-section {
  background: var(--theme-black);
  border: var(--border-silver-thick);
  color: var(--theme-silver);
  box-shadow: var(--shadow-silver);
  transition: all var(--transition-normal);
}

/* Modal overlays */
.modal-overlay, .backdrop {
  background: var(--bg-overlay);
}

/* ==========================================================================
   NAVIGATION STYLING
   ========================================================================== */
nav, .navbar, .nav, .navigation, .menu {
  background: var(--theme-black);
  border-bottom: var(--border-silver-thick);
  box-shadow: var(--shadow-silver);
}

.nav-link, .navbar-link, .menu-item {
  color: var(--theme-silver);
  border: var(--border-silver);
  background: var(--theme-black);
  transition: all var(--transition-fast);
}

/* ==========================================================================
   SPECIAL UI COMPONENTS
   ========================================================================== */
/* Progress bars */
.progress, .progress-bar-container {
  background: var(--bg-secondary);
  border: var(--border-silver);
  box-shadow: var(--shadow-silver);
}

.progress-bar, .progress-fill {
  background: linear-gradient(90deg, var(--theme-gold-dark), var(--theme-gold-light));
  box-shadow: var(--shadow-gold);
}

/* Badges and labels */
.badge, .label, .tag, .chip {
  background: var(--theme-black);
  color: var(--theme-silver);
  border: var(--border-silver);
  text-shadow: var(--text-shadow-base);
}

/* Icons */
i, .icon, .fa, .fas, .far, .fab {
  color: var(--theme-silver);
  text-shadow: var(--text-shadow-base);
  transition: color var(--transition-fast);
}

/* ==========================================================================
   HOVER AND ACTIVE STATES - GOLD THEME
   All interactive elements turn gold on hover/active
   ========================================================================== */

/* Button hover states */
button:hover, .btn:hover, input[type="button"]:hover, input[type="submit"]:hover,
.button:hover, .action-btn:hover, .download-btn:hover, .support-link:hover {
  background: var(--theme-black);
  color: var(--theme-gold);
  border: var(--border-gold-thick);
  text-shadow: var(--text-shadow-gold);
  box-shadow: var(--shadow-gold);
  transform: translateY(-2px);
}

/* Link hover states */
a:hover, .nav-link:hover, .navbar-link:hover, .menu-item:hover {
  color: var(--theme-gold);
  text-shadow: var(--text-shadow-gold);
}

/* Container hover states for interactive containers */
.card:hover, .poll-option:hover, .feature-item:hover,
.support-link:hover, .download-features:hover {
  border: var(--border-gold);
  box-shadow: var(--shadow-gold);
  color: var(--theme-gold);
}

/* Input focus states */
input:focus, textarea:focus, select:focus {
  border: var(--border-gold-thick);
  box-shadow: var(--shadow-gold);
  outline: none;
  color: var(--theme-gold);
}

/* Table row hover */
tr:hover, .table-row:hover {
  background: var(--bg-secondary);
}

tr:hover td, .table-row:hover td {
  color: var(--theme-gold);
  text-shadow: var(--text-shadow-gold);
}

/* Icon hover states */
i:hover, .icon:hover, .fa:hover, .fas:hover, .far:hover, .fab:hover {
  color: var(--theme-gold);
  text-shadow: var(--text-shadow-gold);
}

/* Active and selected states */
.active, .selected, .current, button.active, .btn.active,
.nav-link.active, .navbar-link.active, .menu-item.active {
  background: var(--theme-black);
  color: var(--theme-gold);
  border: var(--border-gold-heavy);
  text-shadow: var(--text-shadow-gold);
  box-shadow: var(--shadow-gold);
}

/* ==========================================================================
   SCROLLBAR STYLING
   ========================================================================== */
::-webkit-scrollbar {
  width: 12px;
  background: var(--theme-black);
  border: var(--border-silver);
}

::-webkit-scrollbar-track {
  background: var(--theme-black);
  border: var(--border-silver);
}

::-webkit-scrollbar-thumb {
  background: var(--bg-tertiary);
  border: var(--border-silver);
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--theme-gold);
  border: var(--border-gold);
  box-shadow: var(--shadow-gold);
}

/* ==========================================================================
   SPECIFIC COMPONENT OVERRIDES
   Targeting specific components that might need special handling
   ========================================================================== */

/* Hero section */
.hero-section {
  background: var(--theme-black);
  border: none;
  box-shadow: none;
}

/* Download section */
.download-hero-section, .hero-download-container {
  background: var(--theme-black);
  border: var(--border-silver-thick);
  box-shadow: var(--shadow-silver);
}

/* Support section styling */
.support-section {
  background: var(--theme-black);
  border: var(--border-silver-thick);
  box-shadow: var(--shadow-silver);
}

/* Poll section */
.poll-section, .poll-container {
  background: var(--theme-black);
  border: var(--border-silver-thick);
  box-shadow: var(--shadow-silver);
}

.poll-option {
  background: var(--theme-black);
  border: var(--border-silver);
  color: var(--theme-silver);
  transition: all var(--transition-normal);
}

.poll-option:hover {
  border: var(--border-gold);
  color: var(--theme-gold);
  box-shadow: var(--shadow-gold);
}

/* Donation progress bars */
.donation-goal-progress, .poll-option-bar {
  background: var(--bg-secondary);
  border: var(--border-silver);
}

.donation-goal-bar, .poll-option-progress {
  background: linear-gradient(90deg, var(--theme-gold-dark), var(--theme-gold-light));
  box-shadow: var(--shadow-gold);
}

/* ==========================================================================
   RESPONSIVE DESIGN CONSIDERATIONS
   ========================================================================== */
@media (max-width: 768px) {
  /* Maintain theme consistency on mobile */
  .container, .card, .modal {
    border-width: 1px;
  }
  
  button, .btn {
    padding: 8px 16px;
  }
}

/* ==========================================================================
   UTILITY CLASSES
   ========================================================================== */
.text-gold {
  color: var(--theme-gold);
  text-shadow: var(--text-shadow-gold);
}

.border-gold {
  border: var(--border-gold-thick);
  box-shadow: var(--shadow-gold);
}

.glow-gold {
  box-shadow: var(--glow-gold);
}

.no-border {
  border: none;
  box-shadow: none;
}

.no-background {
  background: transparent;
}

/* ==========================================================================
   ANIMATION ENHANCEMENTS
   ========================================================================== */
@keyframes goldPulse {
  0%, 100% { 
    box-shadow: var(--shadow-gold);
  }
  50% { 
    box-shadow: var(--glow-gold);
  }
}

.pulse-gold {
  animation: goldPulse 2s ease-in-out infinite;
}

/* Subtle shimmer effect for enhanced elements */
@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.shimmer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(192, 192, 192, 0.1),
    transparent
  );
  background-size: 200% 100%;
  animation: shimmer 3s infinite;
  pointer-events: none;
} 
