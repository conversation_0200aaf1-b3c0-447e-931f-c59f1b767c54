/**
 * Admin Control Panel Styles
 * Comprehensive styling for the centralized admin interface
 */

/* Admin Dashboard Styles */
.admin-dashboard {
  padding: 0;
}

.demo-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.demo-banner i {
  font-size: 1.2rem;
}

/* No Records Found State */
.no-records-found {
  text-align: center;
  padding: var(--spacing-xl) var(--spacing-lg);
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  border: 2px dashed var(--border-color);
}

.no-records-icon {
  font-size: 4rem;
  color: var(--text-muted);
  margin-bottom: var(--spacing-md);
}

.no-records-found h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  font-size: 1.5rem;
}

.no-records-found p {
  color: var(--text-muted);
  margin-bottom: var(--spacing-lg);
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.dashboard-header h1 {
  margin: 0;
  color: var(--text-primary);
  font-size: 2rem;
  font-weight: 600;
}

.dashboard-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.metric-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: all 0.2s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.metric-icon {
  width: 60px;
  height: 60px;
  background: var(--primary-color);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.metric-content h3 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
}

.metric-content p {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--text-secondary);
  font-weight: 500;
}

.metric-change {
  font-size: 0.875rem;
  font-weight: 600;
}

.metric-change.positive {
  color: var(--success-color);
}

.metric-change.negative {
  color: var(--danger-color);
}

.metric-detail {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Dashboard Sections */
.dashboard-section {
  margin-bottom: var(--spacing-xl);
}

.dashboard-section h2 {
  margin: 0 0 var(--spacing-lg) 0;
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 600;
}

/* Quick Actions */
.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.quick-action-btn {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  text-decoration: none;
  color: var(--text-primary);
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
}

.quick-action-btn:hover {
  background: var(--bg-secondary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.quick-action-btn i {
  font-size: 2rem;
  color: var(--primary-color);
}

.quick-action-btn .badge {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  background: var(--danger-color);
  color: white;
  font-size: 0.75rem;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

/* Activity Feed */
.activity-feed {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  max-height: 400px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  background: var(--bg-secondary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
}

.activity-description {
  color: var(--text-primary);
  font-weight: 500;
}

.activity-time {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.no-activity {
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);
}

/* System Status */
.system-status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.status-item {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.status-indicator.healthy {
  background: var(--success-color);
}

.status-indicator.warning {
  background: var(--warning-color);
}

.status-indicator.error {
  background: var(--danger-color);
}

.status-value {
  margin-left: auto;
  font-weight: 600;
  color: var(--primary-color);
}

/* Admin Sections */
.admin-section {
  padding: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.section-header h1 {
  margin: 0;
  color: var(--text-primary);
  font-size: 2rem;
  font-weight: 600;
}

.section-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* Filters Bar */
.filters-bar {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  min-width: 150px;
}

.filter-group label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary);
}

.filter-group select,
.filter-group input {
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--bg-secondary);
  color: var(--text-primary);
}

/* Bulk Actions */
.bulk-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
}

.bulk-actions label {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-weight: 500;
  color: var(--text-primary);
}

/* Data Tables */
.data-table-container {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background: var(--bg-secondary);
  padding: var(--spacing-md);
  text-align: left;
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-color);
}

.data-table td {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
}

.data-table tr:hover {
  background: var(--bg-secondary);
}

/* User Info */
.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.user-name {
  font-weight: 600;
  color: var(--text-primary);
}

.user-id {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Match Info */
.match-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.match-type {
  font-weight: 600;
  color: var(--text-primary);
}

.match-id {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.match-players {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.match-players .player {
  padding: 2px 6px;
  background: var(--bg-secondary);
  border-radius: var(--border-radius-sm);
  font-size: 0.875rem;
}

.match-players .player.winner {
  background: var(--success-color);
  color: white;
}

/* Badges */
.role-badge,
.status-badge,
.type-badge,
.priority-badge {
  padding: 2px 8px;
  border-radius: var(--border-radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.role-badge.admin {
  background: var(--danger-color);
  color: white;
}

.role-badge.moderator {
  background: var(--warning-color);
  color: white;
}

.role-badge.user {
  background: var(--info-color);
  color: white;
}

.status-badge.active,
.status-badge.verified {
  background: var(--success-color);
  color: white;
}

.status-badge.pending {
  background: var(--warning-color);
  color: white;
}

.status-badge.suspended,
.status-badge.disputed,
.status-badge.rejected {
  background: var(--danger-color);
  color: white;
}

.status-badge.banned {
  background: #000;
  color: white;
}

.priority-badge.high {
  background: var(--danger-color);
  color: white;
}

.priority-badge.medium {
  background: var(--warning-color);
  color: white;
}

.priority-badge.low {
  background: var(--info-color);
  color: white;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: var(--spacing-xs);
}

.action-buttons .btn {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 0.875rem;
}

/* Permission Management */
.permissions-grid {
  display: grid;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.permission-item {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  transition: all 0.2s ease;
}

.permission-item:hover {
  background: var(--bg-primary);
  border-color: var(--primary-color);
}

.permission-label {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  cursor: pointer;
  margin: 0;
}

.permission-label input[type="checkbox"] {
  margin-top: 2px;
  width: 18px;
  height: 18px;
  accent-color: var(--primary-color);
}

.permission-title {
  font-weight: 600;
  color: var(--text-primary);
  display: block;
  margin-bottom: var(--spacing-xs);
}

.permission-desc {
  font-size: 0.875rem;
  color: var(--text-secondary);
  display: block;
  line-height: 1.4;
}

/* Screenshot Count */
.screenshot-count {
  font-weight: 600;
  color: var(--primary-color);
}

/* Maps Admin Grid */
.maps-admin-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.map-admin-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  transition: all 0.2s ease;
}

.map-admin-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.map-selection {
  position: absolute;
  top: var(--spacing-sm);
  left: var(--spacing-sm);
  z-index: 10;
}

.map-thumbnail {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.map-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.map-placeholder {
  width: 100%;
  height: 100%;
  background: var(--bg-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  font-size: 3rem;
}

.map-info {
  padding: var(--spacing-md);
}

.map-info h4 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
}

.map-details {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.map-type,
.map-size {
  padding: 2px 6px;
  background: var(--bg-secondary);
  border-radius: var(--border-radius-sm);
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.map-stats {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.map-status {
  margin-bottom: var(--spacing-sm);
}

.map-actions {
  display: flex;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-secondary);
}

/* Settings Container */
.settings-container {
  max-width: 800px;
}

.settings-section {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.settings-section h3 {
  margin: 0 0 var(--spacing-lg) 0;
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: var(--spacing-sm);
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.setting-item label {
  font-weight: 600;
  color: var(--text-primary);
}

.setting-item input,
.setting-item textarea,
.setting-item select {
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--bg-secondary);
  color: var(--text-primary);
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--border-color);
  transition: 0.4s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: var(--primary-color);
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

/* Dispute Info */
.dispute-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.dispute-id {
  font-weight: 600;
  color: var(--text-primary);
}

.dispute-summary {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.match-link {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

/* Match Report Wizard */
.match-report-wizard {
  max-width: 800px;
  margin: 0 auto;
}

.wizard-steps {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-xl);
  padding: 0 var(--spacing-lg);
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  flex: 1;
  position: relative;
}

.step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 15px;
  left: 60%;
  right: -40%;
  height: 2px;
  background: var(--border-color);
  z-index: 1;
}

.step.completed:not(:last-child)::after {
  background: var(--success-color);
}

.step-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: var(--border-color);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  position: relative;
  z-index: 2;
}

.step.active .step-number {
  background: var(--primary-color);
  color: white;
}

.step.completed .step-number {
  background: var(--success-color);
  color: white;
}

.step-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  text-align: center;
}

.step.active .step-label {
  color: var(--primary-color);
  font-weight: 600;
}

.wizard-step {
  display: none;
}

.wizard-step.active {
  display: block;
}

.wizard-navigation {
  display: flex;
  justify-content: space-between;
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
}

/* Map Selection */
.map-selection-container {
  display: flex;
  gap: var(--spacing-sm);
}

.map-selection-container input {
  flex: 1;
}

.selected-map-preview {
  margin-top: var(--spacing-md);
}

.selected-map {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
}

.map-thumbnail {
  width: 80px;
  height: 80px;
  border-radius: var(--border-radius);
  overflow: hidden;
}

.map-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Screenshot Upload */
.screenshot-upload-area {
  margin-bottom: var(--spacing-lg);
}

.upload-dropzone {
  border: 2px dashed var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-xl);
  text-align: center;
  transition: all 0.2s ease;
  cursor: pointer;
}

.upload-dropzone:hover,
.upload-dropzone.dragover {
  border-color: var(--primary-color);
  background: var(--bg-secondary);
}

.upload-dropzone i {
  font-size: 3rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
}

.upload-dropzone h4 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
}

.upload-dropzone p {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-secondary);
}

.screenshot-previews {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.screenshot-preview {
  position: relative;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.screenshot-preview img {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.screenshot-info {
  padding: var(--spacing-sm);
}

.screenshot-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
  margin-bottom: var(--spacing-xs);
}

.screenshot-size {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.remove-screenshot {
  position: absolute;
  top: var(--spacing-xs);
  right: var(--spacing-xs);
  background: var(--danger-color);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 0.75rem;
}

.screenshot-requirements {
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
}

.screenshot-requirements h4 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
}

.screenshot-requirements ul {
  margin: 0;
  padding-left: var(--spacing-lg);
  color: var(--text-secondary);
}

/* Player Input Groups */
.player-input-group {
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.player-input-group h4 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
}

.player-input-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

/* Report Summary */
.report-summary {
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
}

.summary-section {
  margin-bottom: var(--spacing-lg);
}

.summary-section:last-child {
  margin-bottom: 0;
}

.summary-section h4 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: var(--spacing-sm);
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
}

.summary-item label {
  font-weight: 600;
  color: var(--text-secondary);
}

.summary-item span {
  color: var(--text-primary);
}

.players-summary {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.player-summary {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-sm);
  background: var(--bg-primary);
  border-radius: var(--border-radius);
}

.player-name {
  font-weight: 600;
  color: var(--text-primary);
}

.player-race,
.player-team {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.screenshots-summary {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.screenshot-thumbnails {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.summary-thumbnail {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

/* Responsive Design */
/* Media query removed - Phase 3 CSS optimization */
  
  .quick-actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
}

/* Media query removed - Phase 3 CSS optimization */
  .section-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: flex-start;
  }
  
  .filters-bar {
    flex-direction: column;
  }
  
  .bulk-actions {
    flex-wrap: wrap;
  }
  
  .data-table-container {
    overflow-x: auto;
  }
  
  .data-table {
    min-width: 600px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .maps-admin-grid {
    grid-template-columns: 1fr;
  }
  
  .settings-grid {
    grid-template-columns: 1fr;
  }
  
  .wizard-steps {
    padding: 0;
  }
  
  .step-label {
    display: none;
  }
  
  .player-input-row {
    grid-template-columns: 1fr;
  }
  
  .summary-grid {
    grid-template-columns: 1fr;
  }
}

/* Media query removed - Phase 3 CSS optimization */
  
  .quick-actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .screenshot-previews {
    grid-template-columns: 1fr;
  }
}

/* Analytics Dashboard Styles */
.analytics-dashboard {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.analytics-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.analytics-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
}

.analytics-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.analytics-card .card-header h3 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin: 0;
}

.analytics-card .card-header i {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.analytics-card .card-content {
  text-align: center;
}

.analytics-card .metric-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.analytics-card .metric-change {
  font-size: 0.875rem;
  font-weight: 500;
}

.analytics-card .metric-change.positive {
  color: var(--success-color);
}

.analytics-card .metric-change.negative {
  color: var(--danger-color);
}

.charts-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-lg);
}

.chart-container {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
}

.chart-container h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.chart-placeholder {
  height: 300px;
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
}

.reports-section {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
}

.reports-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.reports-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.report-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
}

.report-info .report-name {
  font-weight: 600;
  color: var(--text-primary);
}

.report-info .report-date {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.report-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* Moderation Tools Styles */
.moderation-queue {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.moderation-queue h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.queue-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-md);
}

.queue-stat {
  text-align: center;
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
}

.queue-stat .stat-label {
  display: block;
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.queue-stat .stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
}

.moderation-actions {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.moderation-actions h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.action-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  color: var(--text-primary);
}

.action-card:hover {
  background: var(--bg-tertiary);
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

.action-card i {
  font-size: 2rem;
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
}

.action-card span {
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
}

.action-card .action-count {
  background: var(--primary-color);
  color: white;
  font-size: 0.75rem;
  padding: 2px 8px;
  border-radius: 12px;
  margin: 0;
}

.moderation-rules {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.moderation-rules h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.rules-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.rule-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
}

.rule-info .rule-name {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.rule-info .rule-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.rule-status {
  margin: 0 var(--spacing-md);
}

.rule-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.moderation-activity {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
}

.moderation-activity h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
}

.activity-icon {
  width: 40px;
  height: 40px;
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-description {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.activity-meta {
  display: flex;
  gap: var(--spacing-md);
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Badge Styles */
.type-badge, .category-badge, .difficulty-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.type-badge.single_elimination,
.type-badge.double_elimination,
.type-badge.round_robin {
  background: var(--info-color);
  color: white;
}

.category-badge.matches {
  background: var(--primary-color);
  color: white;
}

.category-badge.tournaments {
  background: var(--warning-color);
  color: white;
}

.category-badge.social {
  background: var(--success-color);
  color: white;
}

.category-badge.special {
  background: var(--danger-color);
  color: white;
}

.difficulty-badge.easy {
  background: var(--success-color);
  color: white;
}

.difficulty-badge.medium {
  background: var(--warning-color);
  color: white;
}

.difficulty-badge.hard {
  background: var(--danger-color);
  color: white;
}

.difficulty-badge.legendary {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #333;
}

/* Additional Mobile Responsiveness for New Sections */
/* Media query removed - Phase 3 CSS optimization */

  .charts-section {
    grid-template-columns: 1fr;
  }

  .action-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .queue-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .rule-item,
  .report-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .rule-actions,
  .report-actions {
    width: 100%;
    justify-content: flex-end;
  }
}

/* Image Cache Management Styles */
.cache-stats-section {
  margin-bottom: 2rem;
}

.cache-stats-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 20px;
}

.cache-stats-card h3 {
  color: white;
  margin-bottom: 1rem;
}

.stat-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 15px;
  margin: 10px 0;
}

.stat-item h6 {
  color: white;
  margin-bottom: 0.5rem;
}

.stat-item p {
  margin-bottom: 0.25rem;
  color: rgba(255, 255, 255, 0.9);
}

.platform-badge {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.8em;
  font-weight: bold;
  margin-right: 10px;
}

.platform-youtube {
  background-color: #ff0000;
  color: white;
}

.platform-twitch {
  background-color: #9146ff;
  color: white;
}

#image-cache-activity-log {
  max-height: 400px;
  overflow-y: auto;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
}

#image-cache-activity-log .d-flex {
  padding: 0.5rem;
  border-bottom: 1px solid #e9ecef;
}

#image-cache-activity-log .d-flex:last-child {
  border-bottom: none;
}

/* Additional responsive styles for image cache */
@media (max-width: 768px) {
  .cache-stats-card {
    padding: 15px;
  }

  .stat-item {
    padding: 10px;
  }
}
