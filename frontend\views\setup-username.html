<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Set Your Username - WC Arena</title>
  <link rel="stylesheet" href="/css/warcraft-app-modern.css" />
  <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700&display=swap" rel="stylesheet">
  <style>
    .setup-container {
      max-width: 500px;
      margin: 50px auto;
      padding: 30px;
      background-color: rgba(0, 0, 0, 0.7);
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
      color: #d8dee9;
      text-align: center;
    }

    .setup-title {
      margin-bottom: 20px;
      color: #81a1c1;
    }

    .setup-description {
      margin-bottom: 25px;
      font-size: 16px;
      line-height: 1.5;
    }

    .username-form {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .input-group {
      position: relative;
    }

    .username-input {
      width: 100%;
      padding: 12px 15px;
      border: 2px solid #4c566a;
      border-radius: 4px;
      background-color: #2e3440;
      color: #eceff4;
      font-size: 16px;
      transition: border-color 0.3s;
    }

    .username-input:focus {
      border-color: #81a1c1;
      outline: none;
    }

    .username-requirements {
      margin-top: 5px;
      font-size: 12px;
      color: #aaa;
      text-align: left;
    }

    .submit-btn {
      padding: 12px;
      background-color: #81a1c1;
      color: #2e3440;
      border: none;
      border-radius: 4px;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      transition: background-color 0.3s;
    }

    .submit-btn:hover {
      background-color: #88c0d0;
    }

    .submit-btn:disabled {
      background-color: #4c566a;
      cursor: not-allowed;
    }

    .error-message {
      color: #bf616a;
      margin-top: 15px;
      font-size: 14px;
    }

    .success-message {
      color: #a3be8c;
      margin-top: 15px;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <div class="setup-container">
    <h1 class="setup-title">Welcome to WC Arena!</h1>
    <p class="setup-description">
      Before you continue, please choose a username for your account.
      This will be your identity across the site.
    </p>

    <form id="username-form" class="username-form">
      <div class="input-group">
        <input
          type="text"
          id="username"
          class="username-input"
          placeholder="Choose a username"
          pattern="[a-zA-Z0-9_-]{3,20}"
          required
          autocomplete="off"
          minlength="3"
          maxlength="20"
        />
        <div class="username-requirements">
          Username must be 3-20 characters and can only contain letters, numbers, underscores, and hyphens.
        </div>
      </div>

      <button type="submit" id="submit-btn" class="submit-btn" disabled>Create Username</button>
    </form>

    <div id="error" class="error-message"></div>
    <div id="success" class="success-message"></div>
  </div>

  <script>
    // Get user info to check for suggested username
    async function loadUserInfo() {
      try {
        const res = await fetch('/api/me', { credentials: 'include' });
        if (!res.ok) return null;
        return await res.json();
      } catch (err) {
        console.error('Error loading user info:', err);
        return null;
      }
    }

    // Validate username as user types
    const usernameInput = document.getElementById('username');
    const submitBtn = document.getElementById('submit-btn');
    const errorMsg = document.getElementById('error');

    usernameInput.addEventListener('input', () => {
      const username = usernameInput.value.trim();
      const isValid = /^[a-zA-Z0-9_-]{3,20}$/.test(username);

      submitBtn.disabled = !isValid;

      if (username.length < 3) {
        errorMsg.textContent = 'Username must be at least 3 characters';
      } else if (username.length > 20) {
        errorMsg.textContent = 'Username must be no more than 20 characters';
      } else if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
        errorMsg.textContent = 'Username can only contain letters, numbers, underscores, and hyphens';
      } else {
        errorMsg.textContent = '';
      }
    });

    // Handle form submission
    document.getElementById('username-form').addEventListener('submit', async (e) => {
      e.preventDefault();
      const username = usernameInput.value.trim();
      submitBtn.disabled = true;
      submitBtn.textContent = 'Setting up...';
      errorMsg.textContent = '';

      try {
        const res = await fetch('/auth/setup-username', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify({ username })
        });

        const data = await res.json();

        if (!res.ok) {
          errorMsg.textContent = data.error || 'Failed to set username';
          submitBtn.disabled = false;
          submitBtn.textContent = 'Create Username';
        } else {
          document.getElementById('success').textContent = 'Username set successfully! Redirecting...';
          setTimeout(() => {
            window.location.href = '/'; // Go to homepage after setting username
          }, 1500);
        }
      } catch (err) {
        errorMsg.textContent = 'Network error. Please try again.';
        submitBtn.disabled = false;
        submitBtn.textContent = 'Create Username';
      }
    });

    // On page load, check for suggested username
    window.addEventListener('DOMContentLoaded', async () => {
      const user = await loadUserInfo();
      if (user && user.suggestedUsername) {
        usernameInput.value = user.suggestedUsername;
        usernameInput.dispatchEvent(new Event('input')); // Trigger validation
      }
    });
  </script>
</body>
</html>
