/**
 * Service Worker for Chat Notifications
 * Handles background notifications when the page is not active
 */

const CACHE_NAME = 'wc-chat-v2-' + Date.now();

// Install event
self.addEventListener('install', (event) => {
  console.log('🔧 Service Worker installing...');
  self.skipWaiting();
});

// Activate event
self.addEventListener('activate', (event) => {
  console.log('✅ Service Worker activated');
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheName !== CACHE_NAME) {
            console.log('🗑️ Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => self.clients.claim())
  );
});

// Push event for notifications
self.addEventListener('push', (event) => {
  console.log('📥 Push notification received:', event);
  
  const options = {
    body: event.data ? event.data.text() : 'New chat message',
    icon: '/assets/img/chat-icon.png',
    badge: '/assets/img/chat-badge.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: '1'
    },
    actions: [
      {
        action: 'open-chat',
        title: 'Open Chat',
        icon: '/assets/img/icons/chat.png'
      },
      {
        action: 'close',
        title: 'Close',
        icon: '/assets/img/icons/close.png'
      }
    ]
  };

  event.waitUntil(
    self.registration.showNotification('WC Chat', options)
  );
});

// Notification click event
self.addEventListener('notificationclick', (event) => {
  console.log('🔔 Notification clicked:', event.notification.tag);
  
  event.notification.close();

  if (event.action === 'open-chat') {
    // Open the chat
    event.waitUntil(
      self.clients.openWindow('/?chat=open')
    );
  } else if (event.action === 'close') {
    // Just close the notification
    return;
  } else {
    // Default action - focus the window
    event.waitUntil(
      self.clients.matchAll({ type: 'window' }).then((clientList) => {
        for (const client of clientList) {
          if (client.url === '/' && 'focus' in client) {
            return client.focus();
          }
        }
        if (self.clients.openWindow) {
          return self.clients.openWindow('/');
        }
      })
    );
  }
});

// Message event for communication with main thread
self.addEventListener('message', (event) => {
  console.log('💬 Service Worker received message:', event.data);
  
  if (event.data && event.data.type === 'CHAT_NOTIFICATION') {
    // Handle chat notification from main thread
    const { title, body, icon } = event.data.payload;
    
    self.registration.showNotification(title || 'New Chat Message', {
      body: body,
      icon: icon || '/assets/img/chat-icon.png',
      badge: '/assets/img/chat-badge.png',
      tag: 'chat-message'
    });
  }
}); 