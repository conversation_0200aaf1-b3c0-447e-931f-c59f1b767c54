/**
 * COMPLETE TILE MAPPINGS - 100% COVERAGE
 * 
 * This file provides mappings for ALL 321 tiles found in maps.
 * Generated by create-complete-tile-mapping.js
 */

const War2ToolsColorMapper = require('./war2toolsColorMappings');

// Additional mappings for tiles not in war2tools
const ADDITIONAL_TILE_MAPPINGS = {
  21: [4, 54, 115], // Tile 21(0x15)
  22: [4, 54, 115], // Tile 22(0x16)
  23: [4, 54, 115], // Tile 23(0x17)
  37: [4, 54, 115], // Tile 37(0x25)
  38: [4, 54, 115], // Tile 38(0x26)
  39: [4, 54, 115], // Tile 39(0x27)
};

class CompleteTileMapper extends War2ToolsColorMapper {
  static hasOfficialMapping(tilesetId, tileId) {
    // First check war2tools
    if (super.hasOfficialMapping(tilesetId, tileId)) {
      return true;
    }
    
    // Then check additional mappings
    const key = tileId.toString();
    return ADDITIONAL_TILE_MAPPINGS.hasOwnProperty(key);
  }
  
  static getOfficialColor(tilesetId, tileId) {
    // First try war2tools
    const war2Color = super.getOfficialColor(tilesetId, tileId);
    if (war2Color) return war2Color;
    
    // Then try additional mappings with tileset variation
    const key = tileId.toString();
    if (ADDITIONAL_TILE_MAPPINGS[key]) {
      return this.adaptColorForTileset(ADDITIONAL_TILE_MAPPINGS[key], tilesetId);
    }
    
    return null;
  }
  
  static adaptColorForTileset(baseColor, tilesetId) {
    // Adapt base color for different tilesets
    const [r, g, b] = baseColor;
    
    switch (tilesetId) {
      case 1: // Winter - make cooler/whiter
        return [
          Math.min(255, r + 50),
          Math.min(255, g + 50), 
          Math.min(255, b + 80)
        ];
      case 2: // Wasteland - make warmer/sandier
        return [
          Math.min(255, r + 40),
          Math.min(255, g + 20),
          Math.max(0, b - 20)
        ];
      case 3: // Swamp - make murkier/greener
        return [
          Math.max(0, r - 20),
          Math.min(255, g + 10),
          Math.max(0, b - 30)
        ];
      default: // Forest
        return baseColor;
    }
  }
}

module.exports = CompleteTileMapper;
