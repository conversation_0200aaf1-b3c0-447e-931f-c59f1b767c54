/* =============================================================================
   ANIMATIONS COMPONENT STYLES
   ============================================================================= */

/* ===== KEYFRAME ANIMATIONS ===== */

/* Spin animation for loading indicators */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Fade in animation */
@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

/* Fade in from bottom */
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Fade in from top */
@keyframes fadeInDown {
  0% {
    opacity: 0;
    transform: translateY(-30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Slide down with fade */
@keyframes slideDownFade {
  0% {
    opacity: 0;
    transform: translateY(-20px) scale(0.9);
    visibility: visible;
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    visibility: visible;
  }
}

/* Slide in from right */
@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(100%);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Slide in from left */
@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-100%);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Zoom in animation */
@keyframes zoomIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: scale(1);
  }
}

/* Bounce animation */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

/* Pulse animation */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Glow pulse animation */
@keyframes glowPulse {
  0%, 100% {
    box-shadow: 0 0 5px rgba(212, 175, 55, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.8);
  }
}

/* Shake animation for errors */
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-10px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(10px);
  }
}

/* Flip animation */
@keyframes flip {
  0% {
    transform: perspective(400px) rotateY(0);
    animation-timing-function: ease-out;
  }
  40% {
    transform: perspective(400px) translateZ(150px) rotateY(170deg);
    animation-timing-function: ease-out;
  }
  50% {
    transform: perspective(400px) translateZ(150px) rotateY(190deg) scale(1);
    animation-timing-function: ease-in;
  }
  80% {
    transform: perspective(400px) rotateY(360deg) scale(0.95);
    animation-timing-function: ease-in;
  }
  100% {
    transform: perspective(400px) scale(1);
    animation-timing-function: ease-in;
  }
}

/* Loading dots animation */
@keyframes loadingDots {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* Progress bar animation */
@keyframes progressBar {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}

/* Typewriter animation */
@keyframes typewriter {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

/* ===== ANIMATION UTILITY CLASSES ===== */

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out;
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fadeInDown {
  animation: fadeInDown 0.6s ease-out;
}

.animate-slideInRight {
  animation: slideInRight 0.5s ease-out;
}

.animate-slideInLeft {
  animation: slideInLeft 0.5s ease-out;
}

.animate-zoomIn {
  animation: zoomIn 0.5s ease-out;
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

.animate-flip {
  animation: flip 1s ease-in-out;
}

.animate-glow {
  animation: glowPulse 2s ease-in-out infinite;
}

/* ===== HOVER ANIMATIONS ===== */

.hover-lift {
  transition: transform var(--transition-normal);
}

.hover-lift:hover {
  transform: translateY(-4px);
}

.hover-scale {
  transition: transform var(--transition-normal);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: box-shadow var(--transition-normal);
}

.hover-glow:hover {
  box-shadow: var(--shadow-glow);
}

.hover-rotate {
  transition: transform var(--transition-normal);
}

.hover-rotate:hover {
  transform: rotate(5deg);
}

.hover-skew {
  transition: transform var(--transition-normal);
}

.hover-skew:hover {
  transform: skew(-5deg);
}

/* ===== LOADING ANIMATIONS ===== */

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--glass-border);
  border-top: 4px solid var(--warcraft-gold);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-dots {
  display: inline-flex;
  gap: 4px;
}

.loading-dots .dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--warcraft-gold);
  animation: loadingDots 1.4s infinite ease-in-out both;
}

.loading-dots .dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots .dot:nth-child(2) {
  animation-delay: -0.16s;
}

.loading-progress {
  width: 100%;
  height: 4px;
  background: var(--glass-border);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.loading-progress::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent,
    var(--warcraft-gold),
    transparent
  );
  animation: progressBar 2s linear infinite;
}

/* ===== ENTRANCE ANIMATIONS ===== */

.entrance-delay-1 {
  animation-delay: 0.1s;
}

.entrance-delay-2 {
  animation-delay: 0.2s;
}

.entrance-delay-3 {
  animation-delay: 0.3s;
}

.entrance-delay-4 {
  animation-delay: 0.4s;
}

.entrance-delay-5 {
  animation-delay: 0.5s;
}

/* ===== SCROLL ANIMATIONS ===== */

.scroll-reveal {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.6s ease-out;
}

.scroll-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

.scroll-reveal-left {
  opacity: 0;
  transform: translateX(-50px);
  transition: all 0.6s ease-out;
}

.scroll-reveal-left.revealed {
  opacity: 1;
  transform: translateX(0);
}

.scroll-reveal-right {
  opacity: 0;
  transform: translateX(50px);
  transition: all 0.6s ease-out;
}

.scroll-reveal-right.revealed {
  opacity: 1;
  transform: translateX(0);
}

/* ===== NOTIFICATION ANIMATIONS ===== */

.notification-enter {
  animation: slideInRight 0.3s ease-out;
}

.notification-exit {
  animation: slideInRight 0.3s ease-out reverse;
}

/* ===== MODAL ANIMATIONS ===== */

.modal-enter {
  animation: fadeIn 0.3s ease-out,
             zoomIn 0.3s ease-out;
}

.modal-exit {
  animation: fadeIn 0.2s ease-out reverse;
}

/* ===== BUTTON ANIMATIONS ===== */

.btn-loading {
  position: relative;
  color: transparent;
}

.btn-loading::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  top: 50%;
  left: 50%;
  margin-left: -8px;
  margin-top: -8px;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  color: inherit;
}

/* ===== PAGE TRANSITION ANIMATIONS ===== */

.page-transition-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.3s ease-out, transform 0.3s ease-out;
}

.page-transition-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 0.2s ease-out, transform 0.2s ease-out;
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */

.gpu-accelerated {
  will-change: transform;
  transform: translateZ(0);
}

.reduce-motion {
  animation-duration: 0.01ms;
  animation-iteration-count: 1;
  transition-duration: 0.01ms;
}

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms;
    animation-iteration-count: 1;
    transition-duration: 0.01ms;
    scroll-behavior: auto;
  }
}

/* ===== STAGGER ANIMATIONS ===== */

.stagger-children > * {
  animation-delay: calc(var(--stagger-delay, 0.1s) * var(--stagger-index, 0));
}

.stagger-children > *:nth-child(1) { --stagger-index: 1; }
.stagger-children > *:nth-child(2) { --stagger-index: 2; }
.stagger-children > *:nth-child(3) { --stagger-index: 3; }
.stagger-children > *:nth-child(4) { --stagger-index: 4; }
.stagger-children > *:nth-child(5) { --stagger-index: 5; }
.stagger-children > *:nth-child(6) { --stagger-index: 6; }
.stagger-children > *:nth-child(7) { --stagger-index: 7; }
.stagger-children > *:nth-child(8) { --stagger-index: 8; }
.stagger-children > *:nth-child(9) { --stagger-index: 9; }
.stagger-children > *:nth-child(10) { --stagger-index: 10; } 
