const express = require('express');
const passport = require('passport');
const { availableStrategies } = require('../config/passport');
const router = express.Router();
const authController = require('../controllers/authController');
const jwt = require('jsonwebtoken');

// Debug middleware for auth routes
router.use((req, res, next) => {
  console.log('Auth Route Debug:', {
    path: req.path,
    method: req.method,
    sessionID: req.sessionID,
    isAuthenticated: req.isAuthenticated && req.isAuthenticated(),
    user: req.user ? {
      id: req.user._id,
      username: req.user.username
    } : null,
    isElectron: req.query.electron === 'true' || req.headers['x-electron-client'] === 'true'
  });
  next();
});

// Helper middleware to check if strategy is available
function checkStrategy(strategyName) {
  return (req, res, next) => {
    if (!availableStrategies[strategyName]) {
      return res.status(503).json({
        error: `${strategyName.charAt(0).toUpperCase() + strategyName.slice(1)} OAuth is not configured`,
        message: `Please configure ${strategyName.toUpperCase()}_CLIENT_ID and ${strategyName.toUpperCase()}_CLIENT_SECRET environment variables to enable ${strategyName} authentication.`,
        availableStrategies: Object.keys(availableStrategies).filter(key => availableStrategies[key])
      });
    }
    next();
  };
}

// OAuth login redirects
router.get('/google', checkStrategy('google'), (req, res, next) => {
  const isElectron = req.query.electron === 'true';
  const state = req.query.state;
  
  // Store electron flag and state in session for callback
  if (isElectron) {
    req.session.isElectron = true;
    req.session.oauthState = state;
  }
  
  passport.authenticate('google', { 
    scope: ['openid', 'profile', 'email'],
    prompt: 'select_account',
    state: state
  })(req, res, next);
});

router.get('/discord', checkStrategy('discord'), (req, res, next) => {
  const isElectron = req.query.electron === 'true';
  const state = req.query.state;
  
  if (isElectron) {
    req.session.isElectron = true;
    req.session.oauthState = state;
  }
  
  passport.authenticate('discord', { state: state })(req, res, next);
});

router.get('/twitch', checkStrategy('twitch'), (req, res, next) => {
  const isElectron = req.query.electron === 'true';
  const state = req.query.state;
  
  if (isElectron) {
    req.session.isElectron = true;
    req.session.oauthState = state;
  }
  
  passport.authenticate('twitch', { state: state })(req, res, next);
});

// OAuth callbacks
router.get('/google/callback',
  checkStrategy('google'),
  passport.authenticate('google', { 
    failureRedirect: '/login?error=google_auth_failed',
    failureMessage: true
  }),
  authController.handleOAuthCallback
);

router.get('/discord/callback',
  checkStrategy('discord'),
  passport.authenticate('discord', { 
    failureRedirect: '/login?error=discord_auth_failed',
    failureMessage: true
  }),
  authController.handleOAuthCallback
);

router.get('/twitch/callback',
  checkStrategy('twitch'),
  passport.authenticate('twitch', { 
    failureRedirect: '/login?error=twitch_auth_failed',
    failureMessage: true
  }),
  authController.handleOAuthCallback
);

// Electron-specific routes
router.get('/validate-user', async (req, res) => {
  try {
    const userId = req.headers['x-user-id'];
    const isElectron = req.headers['x-electron-client'] === 'true';
    
    if (!userId || !isElectron) {
      return res.status(400).json({ success: false, error: 'Invalid request' });
    }
    
    const User = require('../models/User');
    const user = await User.findById(userId).select('-password');
    
    if (!user) {
      return res.status(404).json({ success: false, error: 'User not found' });
    }
    
    // Update last login
    user.lastLogin = new Date();
    await user.save();
    
    res.json({ 
      success: true, 
      user: {
        _id: user._id,
        id: user._id,
        username: user.username,
        displayName: user.displayName,
        email: user.email,
        avatar: user.avatar,
        role: user.role || 'user',
        isUsernameDefined: user.isUsernameDefined
      }
    });
  } catch (error) {
    console.error('User validation error:', error);
    res.status(500).json({ success: false, error: 'Server error' });
  }
});

router.get('/electron/verify', async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    const isElectron = req.headers['x-electron-client'] === 'true';
    
    if (!token || !isElectron) {
      return res.status(400).json({ success: false, error: 'Invalid request' });
    }
    
    // Verify the token (implement your token verification logic)
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    
    const User = require('../models/User');
    const user = await User.findById(decoded.userId).select('-password');
    
    if (!user) {
      return res.status(404).json({ success: false, error: 'User not found' });
    }
    
    res.json({ 
      success: true, 
      user: {
        _id: user._id,
        id: user._id,
        username: user.username,
        displayName: user.displayName,
        email: user.email,
        avatar: user.avatar,
        role: user.role || 'user',
        isUsernameDefined: user.isUsernameDefined
      }
    });
  } catch (error) {
    console.error('Token verification error:', error);
    res.status(401).json({ success: false, error: 'Invalid token' });
  }
});

router.post('/electron/logout', (req, res) => {
  try {
    const isElectron = req.headers['x-electron-client'] === 'true';
    
    if (!isElectron) {
      return res.status(400).json({ success: false, error: 'Invalid request' });
    }
    
    // Perform any server-side logout cleanup if needed
    console.log('Electron client logout notification received');
    
    res.json({ success: true, message: 'Logout notification received' });
  } catch (error) {
    console.error('Electron logout error:', error);
    res.status(500).json({ success: false, error: 'Server error' });
  }
});

// Auth configuration endpoint - tells frontend which OAuth methods are available
router.get('/config', (req, res) => {
  res.json({
    availableStrategies: availableStrategies,
    message: 'Available OAuth authentication methods'
  });
});

// Auth status check
router.get('/me', (req, res) => {
  console.log('Auth /me Debug:', {
    sessionID: req.sessionID,
    isAuthenticated: req.isAuthenticated && req.isAuthenticated(),
    user: req.user ? {
      id: req.user._id,
      username: req.user.username
    } : null
  });

  if (req.isAuthenticated && req.isAuthenticated()) {
    // Return complete user data that might be needed by the frontend
    const userData = {
      _id: req.user._id,
      id: req.user._id, // Include both _id and id for compatibility
      username: req.user.username,
      displayName: req.user.displayName,
      email: req.user.email,
      avatar: req.user.avatar,
      isUsernameDefined: req.user.isUsernameDefined,
      role: req.user.role || 'user',
      // Include any other fields that might be needed
      ...(req.user.socialLinks && { socialLinks: req.user.socialLinks }),
      ...(req.user.bio && { bio: req.user.bio }),
      ...(req.user.createdAt && { createdAt: req.user.createdAt })
    };
    
    console.log('Sending user data to client:', userData);
    res.json(userData);
  } else {
    console.log('User not authenticated');
    res.status(401).json({ error: 'Not authenticated' });
  }
});

// POST username setup
router.post('/setup-username', authController.setupUsername);

// Logout route
router.get('/logout', (req, res) => {
  console.log('Logout Debug:', {
    sessionID: req.sessionID,
    isAuthenticated: req.isAuthenticated && req.isAuthenticated(),
    user: req.user ? {
      id: req.user._id,
      username: req.user.username
    } : null
  });

  req.logout((err) => {
    if (err) {
      console.error('Logout error:', err);
      return res.status(500).json({ error: 'Failed to logout' });
    }
    req.session.destroy((err) => {
      if (err) {
        console.error('Session destroy error:', err);
      }
      res.clearCookie('sessionId');
      res.redirect('/views/login.html');
    });
  });
});

module.exports = router;
