#!/usr/bin/env python3
"""
War3 Map Info Parser
Properly parses war3map.w3i files to extract map metadata like name, author, description, etc.
Handles TRIGSTR_ references by parsing war3map.wts files.
Also extracts terrain information from war3map.w3e files for accurate dimensions.
Extracts player count from war3map.j JASS files.
Based on the official War3 file format specification.
"""

import struct
import sys
import re
from pathlib import Path
from typing import Dict, Any, List, Tuple
import os

# Add pyw3x to path
sys.path.append('pyw3x')

import pyw3x.archive as archive
import pyw3x.terrain as terrain

# Configuration
DEBUG_MODE = False  # Set to True to only process one S2 map for debugging
VALIDATION_MODE = True  # Set to True to compare JASS vs .doo parsing accuracy
DETAILED_ANALYSIS_MODE = False  # Set to True to analyze the 3 problematic maps in detail
BINARY_ANALYSIS_MODE = False  # Set to True to reverse engineer .doo binary format

class War3MapInfoParser:
    def __init__(self):
        self.player_types = {
            1: "Human",
            2: "Computer", 
            3: "Neutral",
            4: "Rescuable"
        }
        
        self.player_races = {
            1: "Human",
            2: "Orc", 
            3: "Undead",
            4: "Night Elf",
            5: "Random"
        }
        
        self.tilesets = {
            'A': 'Ashenvale',
            'B': 'Barrens',
            'C': 'Felwood',
            'D': 'Dungeon',
            'F': 'Lordaeron Fall',
            'G': 'Underground',
            'L': 'Lordaeron Summer',
            'N': 'Northrend',
            'Q': 'Village Fall',
            'V': 'Village',
            'W': 'Lordaeron Winter',
            'X': 'Dalaran',
            'Y': 'Cityscape',
            'Z': 'Sunken Ruins',
            'I': 'Icecrown',
            'J': 'Dalaran Ruins',
            'O': 'Outland',
            'K': 'Black Citadel'
        }
        
        # COMPREHENSIVE UNIT DICTIONARY - ALL KNOWN WARCRAFT 3 UNITS
        self.neutral_units = {
            # Goldmines
            'ngol': {'name': 'Goldmine', 'category': 'goldmine'},

            # Starting Locations - CRITICAL FIX
            'sloc': {'name': 'Starting Location', 'category': 'starting_location'},

            # Neutral Structures (including shops)
            'ntav': {'name': 'Tavern', 'category': 'structure', 'is_shop': True},
            'nmrc': {'name': 'Mercenary Camp', 'category': 'structure', 'is_shop': True},
            'nmrd': {'name': 'Mercenary Camp', 'category': 'structure', 'is_shop': True},
            'ngad': {'name': 'Goblin Alchemist', 'category': 'structure', 'is_shop': True},
            'ngme': {'name': 'Goblin Merchant', 'category': 'structure', 'is_shop': True},
            'nshp': {'name': 'Goblin Shipyard', 'category': 'structure', 'is_shop': True},
            'nmoo': {'name': 'Fountain of Mana', 'category': 'structure'},
            'nfoh': {'name': 'Fountain of Health', 'category': 'structure'},
            'nmrk': {'name': 'Marketplace', 'category': 'structure', 'is_shop': True},
            'nahy': {'name': 'Ancient Hydra', 'category': 'creep'},
                    'nmg0': {'name': 'Mur\'gul Slave', 'category': 'creep'},
        'nmg1': {'name': 'Mur\'gul Reaver', 'category': 'creep'},

            # Creep Units
            'nadk': {'name': 'Ancient of Darkness', 'category': 'creep'},
            'nadr': {'name': 'Ancient Protector', 'category': 'creep'},
            'nadw': {'name': 'Ancient of War', 'category': 'creep'},
            'nano': {'name': 'Naga Myrmidon', 'category': 'creep'},
            'nanw': {'name': 'Naga Siren', 'category': 'creep'},
            'nbda': {'name': 'Blue Dragonspawn', 'category': 'creep'},
            'nbdm': {'name': 'Blue Dragon', 'category': 'creep'},
            'nbds': {'name': 'Blue Dragonspawn Sorcerer', 'category': 'creep'},
            'ndrg': {'name': 'Dragon Roost', 'category': 'creep'},
            'ndru': {'name': 'Dryad', 'category': 'creep'},
            'nfor': {'name': 'Forest Troll', 'category': 'creep'},
            'nfpe': {'name': 'Furbolg Pathfinder', 'category': 'creep'},
            'nfpl': {'name': 'Furbolg', 'category': 'creep'},
            'nfps': {'name': 'Furbolg Shaman', 'category': 'creep'},
            'nfpt': {'name': 'Furbolg Tracker', 'category': 'creep'},
            'ngna': {'name': 'Gnoll Assassin', 'category': 'creep'},
            'ngnb': {'name': 'Gnoll Brute', 'category': 'creep'},
            'ngns': {'name': 'Gnoll', 'category': 'creep'},
            'ngnv': {'name': 'Gnoll Overseer', 'category': 'creep'},
            'ngnw': {'name': 'Gnoll Warden', 'category': 'creep'},
            'ngrk': {'name': 'Razormane Chieftain', 'category': 'creep'},
            'nhyd': {'name': 'Hydralisk', 'category': 'creep'},
            'nhyh': {'name': 'Hydra Hatchling', 'category': 'creep'},
            'nith': {'name': 'Ice Troll High Priest', 'category': 'creep'},
            'nitp': {'name': 'Ice Troll Priest', 'category': 'creep'},
            'nitr': {'name': 'Ice Troll Berserker', 'category': 'creep'},
            'nits': {'name': 'Ice Troll Shadowmeld', 'category': 'creep'},
            'nitt': {'name': 'Ice Troll', 'category': 'creep'},
            'nitw': {'name': 'Ice Troll Warlord', 'category': 'creep'},
            'nlds': {'name': 'Lizard Shaman', 'category': 'creep'},
            'nlkl': {'name': 'Lizard Lord', 'category': 'creep'},
            'nlpd': {'name': 'Lizard Poisoner', 'category': 'creep'},
            'nlpr': {'name': 'Lizard Priest', 'category': 'creep'},
            'nlsn': {'name': 'Lizard Spearman', 'category': 'creep'},
            'nltc': {'name': 'Lizard Warrior', 'category': 'creep'},
            'nmam': {'name': 'Mammoth', 'category': 'creep'},
            'nmbg': {'name': 'Murloc Big Gill', 'category': 'creep'},
            'nmcf': {'name': 'Murloc Cliffrunner', 'category': 'creep'},
            'nmit': {'name': 'Murloc Tiderunner', 'category': 'creep'},
            'nmrv': {'name': 'Murloc Flesheater', 'category': 'creep'},
            'nmsn': {'name': 'Murloc Seer', 'category': 'creep'},
            'nmtw': {'name': 'Murloc Tidewarrior', 'category': 'creep'},
            'nnws': {'name': 'Nerubian Webspinner', 'category': 'creep'},
            'nogl': {'name': 'Ogre Lord', 'category': 'creep'},
            'nogm': {'name': 'Ogre Magi', 'category': 'creep'},
            'nogr': {'name': 'Ogre Warrior', 'category': 'creep'},
            'nomg': {'name': 'Ogre Mauler', 'category': 'creep'},
            'nplb': {'name': 'Polar Furbolg Tracker', 'category': 'creep'},
            'nplg': {'name': 'Polar Furbolg', 'category': 'creep'},
            'nsgh': {'name': 'Satyr Hellcaller', 'category': 'creep'},
            'nsgn': {'name': 'Satyr', 'category': 'creep'},
            'nskf': {'name': 'Skeleton Orc Fighter', 'category': 'creep'},
            'nsko': {'name': 'Skeleton Orc', 'category': 'creep'},
            'nsoc': {'name': 'Satyr Shadowdancer', 'category': 'creep'},
            'nsog': {'name': 'Satyr Soulstealer', 'category': 'creep'},
            'nsrh': {'name': 'Satyr Rogue', 'category': 'creep'},
            'nsrn': {'name': 'Satyr Trickster', 'category': 'creep'},
            'ntka': {'name': 'Tuskarr', 'category': 'creep'},
            'ntrh': {'name': 'Troll High Priest', 'category': 'creep'},
            'ntrs': {'name': 'Troll Shadow Priest', 'category': 'creep'},
            'ntrt': {'name': 'Troll Trapper', 'category': 'creep'},
            'nubk': {'name': 'Furbolg Tracker', 'category': 'creep'},
            'nubr': {'name': 'Furbolg', 'category': 'creep'},
            'nwnr': {'name': 'Wendigo', 'category': 'creep'},
            
            # Additional missing unit types from warnings
            'nmr5': {'name': 'Murloc Huntsman', 'category': 'structure'},
            'nfh1': {'name': 'Fountain of Health', 'category': 'structure'},
            'nltl': {'name': 'Lightning Lizard', 'category': 'creep'},
            'nenp': {'name': 'Nerubian Seer', 'category': 'creep'},
            'ngdk': {'name': 'Gnoll Assassin', 'category': 'creep'},
            'ngrw': {'name': 'Gnoll Robber', 'category': 'creep'},
            'nsbm': {'name': 'Skeleton Mage', 'category': 'creep'},
            'nsgt': {'name': 'Skeleton Giant', 'category': 'creep'},
            'nssp': {'name': 'Skeleton Spearman', 'category': 'creep'},
            'nfsp': {'name': 'Furbolg Shaman', 'category': 'creep'},
            'nftr': {'name': 'Furbolg Tracker', 'category': 'creep'},
            'nftt': {'name': 'Furbolg Trapper', 'category': 'creep'},
            'nthl': {'name': 'Thunder Lizard', 'category': 'creep'},
            'nstw': {'name': 'Satyr Trickster', 'category': 'creep'},
            'nftk': {'name': 'Furbolg Tracker', 'category': 'creep'},
            'njg1': {'name': 'Jungle Stalker', 'category': 'creep'},
            'nfsh': {'name': 'Furbolg Shaman', 'category': 'creep'},
            'nftb': {'name': 'Furbolg', 'category': 'creep'},
            'nspr': {'name': 'Skeleton Priest', 'category': 'creep'},
            'ndtr': {'name': 'Dark Troll', 'category': 'creep'},
            'nsat': {'name': 'Satyr', 'category': 'creep'},
            'nfrb': {'name': 'Furbolg', 'category': 'creep'},
            'nsty': {'name': 'Satyr Trickster', 'category': 'creep'},
            'nsrv': {'name': 'Skeleton Revenant', 'category': 'creep'},
            'nstl': {'name': 'Satyr Shadowdancer', 'category': 'creep'},
            'nrvf': {'name': 'Revenant', 'category': 'creep'},
            'nfrs': {'name': 'Furbolg Shaman', 'category': 'creep'},
            'ndtt': {'name': 'Dark Troll Trapper', 'category': 'creep'},
            'nsts': {'name': 'Satyr Soulstealer', 'category': 'creep'},
            'nfrl': {'name': 'Furbolg', 'category': 'creep'},
            'nfre': {'name': 'Furbolg Elder', 'category': 'creep'},
            'ndth': {'name': 'Dark Troll High Priest', 'category': 'creep'},
            'nfrg': {'name': 'Furbolg Guardian', 'category': 'creep'},
            'ndtp': {'name': 'Dark Troll Priest', 'category': 'creep'},
            'nrvl': {'name': 'Revenant', 'category': 'creep'},
            'ndtb': {'name': 'Dark Troll Berserker', 'category': 'creep'},
            'ntrv': {'name': 'Troll Revenant', 'category': 'creep'},
            'ndrv': {'name': 'Druid Revenant', 'category': 'creep'},
            
            # Additional missing unit types found in edge case analysis
            'nmer': {'name': 'Mercenary', 'category': 'structure'},
            'ngnh': {'name': 'Gnoll Hut', 'category': 'structure'},
            'ntn2': {'name': 'Tent 2', 'category': 'structure'},
            'ngt2': {'name': 'Gate 2', 'category': 'structure'},
            'nmh1': {'name': 'Murloc Hut 1', 'category': 'structure'},
            'nmh0': {'name': 'Murloc Hut 0', 'category': 'structure'},
            'nkot': {'name': 'Kobold Tunneler', 'category': 'creep'},
            'nwwf': {'name': 'Wildkin', 'category': 'creep'},
            'nkol': {'name': 'Kobold', 'category': 'creep'},
            'nwiz': {'name': 'Wizard', 'category': 'creep'},
            'nkog': {'name': 'Kobold Geomancer', 'category': 'creep'},
            'nkob': {'name': 'Kobold', 'category': 'creep'},
            'nbrg': {'name': 'Brigand', 'category': 'creep'},
            'nsqt': {'name': 'Squirrel', 'category': 'creep'},
            'nwwd': {'name': 'Wildkin', 'category': 'creep'},
            'nggr': {'name': 'Goblin', 'category': 'creep'},
            'nmrm': {'name': 'Murloc', 'category': 'creep'},
            'nmfs': {'name': 'Murloc Flesheater', 'category': 'creep'},
            'nwzr': {'name': 'Wizard', 'category': 'creep'},
            'nslf': {'name': 'Salamander', 'category': 'creep'},
            
            # Additional unit types found in Thunder Lake and other edge case maps
            'nmr4': {'name': 'Murloc Warrior', 'category': 'structure'},
            'ncks': {'name': 'Centaur Khan', 'category': 'creep'},
            'ncim': {'name': 'Centaur Impaler', 'category': 'creep'},
            'ncea': {'name': 'Centaur Archer', 'category': 'creep'},
            'nqbh': {'name': 'Quillbeast', 'category': 'creep'},
            'nrzm': {'name': 'Razormane Medicine Man', 'category': 'creep'},
            'nrzg': {'name': 'Razormane Grunt', 'category': 'creep'},
            'ncer': {'name': 'Centaur', 'category': 'creep'},
            'nrzs': {'name': 'Razormane Seer', 'category': 'creep'},
            'nspp': {'name': 'Spell Breaker', 'category': 'creep'},
            'nskk': {'name': 'Skeleton Archer', 'category': 'creep'},
            'nhmc': {'name': 'Harpy Matriarch', 'category': 'creep'},
            'nehy': {'name': 'Hydra', 'category': 'creep'},
            'ncrb': {'name': 'Crab', 'category': 'creep'},
            
            # Comprehensive list of missing unit types from analysis
            'nenf': {'name': 'Enforcer', 'category': 'creep'},
            'nowk': {'name': 'Owlbear', 'category': 'creep'},
            'nnht': {'name': 'Nerubian', 'category': 'creep'},
            'ncen': {'name': 'Centaur Outrunner', 'category': 'creep'},
            'narg': {'name': 'Argent Defender', 'category': 'creep'},
            'nowb': {'name': 'Owlbear Matriarch', 'category': 'creep'},
            'nina': {'name': 'Ninja', 'category': 'creep'},
            'ndrh': {'name': 'Dragon Hawk', 'category': 'creep'},
            'nfgu': {'name': 'Fungal Giant', 'category': 'creep'},
            'nowe': {'name': 'Owlbear Elder', 'category': 'creep'},
            'nban': {'name': 'Bandit', 'category': 'creep'},
            'nrog': {'name': 'Rogue', 'category': 'creep'},
            'nmpg': {'name': 'Mur\'gul Plaguebearer', 'category': 'creep'},
            'ndr3': {'name': 'Dragon Turtle', 'category': 'creep'},
            'nwlt': {'name': 'Wolf', 'category': 'creep'},
            'nmrr': {'name': 'Murloc Raider', 'category': 'creep'},
            'nepl': {'name': 'Plague Treant', 'category': 'creep'},
            'nspg': {'name': 'Spider', 'category': 'creep'},
            'nspb': {'name': 'Spider Broodmother', 'category': 'creep'},
            'nfr1': {'name': 'Furbolg', 'category': 'creep'},
            'nfr2': {'name': 'Furbolg Elder', 'category': 'creep'},
            'ngst': {'name': 'Ghost', 'category': 'creep'},
            'nwld': {'name': 'Wolf Lord', 'category': 'creep'},
            'nwlg': {'name': 'Wolf Guardian', 'category': 'creep'},
            'necr': {'name': 'Necromancer', 'category': 'creep'},
            'nskm': {'name': 'Skeleton Mage', 'category': 'creep'},
            'nsel': {'name': 'Sentry', 'category': 'creep'},
            'nder': {'name': 'Deer', 'category': 'creep'},
            'nsth': {'name': 'Satyr', 'category': 'creep'},
            'ndtw': {'name': 'Dark Troll Warlord', 'category': 'creep'},
            'nfra': {'name': 'Forest Troll', 'category': 'creep'},
            'nmr8': {'name': 'Mur\'gul Reaver', 'category': 'creep'},
            'nass': {'name': 'Assassin', 'category': 'creep'},
            'nwrg': {'name': 'Warg', 'category': 'creep'},
            'nwzd': {'name': 'Wizard', 'category': 'creep'},
            'nwzg': {'name': 'Wizard Guardian', 'category': 'creep'},
            'nsgg': {'name': 'Siege Golem', 'category': 'creep'},
            'nbld': {'name': 'Bloodfiend', 'category': 'creep'},
            'nrac': {'name': 'Raccoon', 'category': 'creep'},
            'npng': {'name': 'Penguin', 'category': 'creep'},
            'nmrl': {'name': 'Murloc', 'category': 'creep'},
            'nshe': {'name': 'Sheep', 'category': 'creep'},
            'nshw': {'name': 'Shadow Wolf', 'category': 'creep'},
            'ngno': {'name': 'Gnoll', 'category': 'creep'},
            'npig': {'name': 'Pig', 'category': 'creep'},
            'nogo': {'name': 'Ogre', 'category': 'creep'},
            'ndqn': {'name': 'Dragon Queen', 'category': 'creep'},
            'nlv1': {'name': 'Lava Spawn', 'category': 'creep'},
            'nlv2': {'name': 'Lava Elemental', 'category': 'creep'},
            'nlv3': {'name': 'Lava Lord', 'category': 'creep'},
            'nmr2': {'name': 'Mur\'gul Cliffrunner', 'category': 'creep'},
            'nrwm': {'name': 'Red Worm', 'category': 'creep'},
            'nrdr': {'name': 'Red Dragon', 'category': 'creep'},
            'nfro': {'name': 'Frost Wolf', 'category': 'creep'},
            'nech': {'name': 'Echo', 'category': 'creep'},
            'nten': {'name': 'Tentacle', 'category': 'creep'},
            'nsc2': {'name': 'Scorpion', 'category': 'creep'},
            'ntkw': {'name': 'Tuskarr Warrior', 'category': 'creep'},
            'ntkt': {'name': 'Tuskarr Trapper', 'category': 'creep'},
            'ntkf': {'name': 'Tuskarr Fighter', 'category': 'creep'},
            'ntkc': {'name': 'Tuskarr Chieftain', 'category': 'creep'},
            'ntkh': {'name': 'Tuskarr Healer', 'category': 'creep'},
            'ntks': {'name': 'Tuskarr Sorceror', 'category': 'creep'},
            'nbdw': {'name': 'Bandit Warlord', 'category': 'creep'},
            'ndrw': {'name': 'Dragon Whelp', 'category': 'creep'},
            'ndrp': {'name': 'Dragon Priest', 'category': 'creep'},
            'ndrm': {'name': 'Dragon Mage', 'category': 'creep'},
            'ndrd': {'name': 'Dragon', 'category': 'creep'},
            'nfpu': {'name': 'Furbolg Ursa Warrior', 'category': 'creep'},
            'nsno': {'name': 'Snow Owl', 'category': 'creep'},
            'nsea': {'name': 'Sea Elemental', 'category': 'creep'},
            'nwns': {'name': 'Wendigo Shaman', 'category': 'creep'},
            'rwat': {'name': 'Water', 'category': 'creep'},
            'rreb': {'name': 'Rebirth', 'category': 'creep'},
            'rebd': {'name': 'Rebirth Dummy', 'category': 'creep'},
            
            # Final missing unit types for 100% accuracy
            'nvil': {'name': 'Villager', 'category': 'creep'},
            'nfh0': {'name': 'Fountain of Health', 'category': 'structure'},
            'hrdh': {'name': 'Human Dragonhawk Rider', 'category': 'creep'},
            'nbse': {'name': 'Banshee', 'category': 'creep'},
            'nshf': {'name': 'Shaman', 'category': 'creep'},
            'nvl2': {'name': 'Villager (Level 2)', 'category': 'creep'},
            'zzrg': {'name': 'Zeppelin', 'category': 'creep'},
        }

    def read_null_terminated_string(self, data: bytes, offset: int) -> Tuple[str, int]:
        """Read a null-terminated string from binary data"""
        start = offset
        while offset < len(data) and data[offset] != 0:
            offset += 1
        
        try:
            string = data[start:offset].decode('utf-8', errors='ignore')
        except:
            string = data[start:offset].decode('latin-1', errors='ignore')
        
        return string, offset + 1  # Skip the null terminator

    def read_int(self, data: bytes, offset: int) -> Tuple[int, int]:
        """Read a 4-byte integer from binary data"""
        if offset + 4 > len(data):
            return 0, offset
        value = struct.unpack('<I', data[offset:offset+4])[0]
        return value, offset + 4

    def read_float(self, data: bytes, offset: int) -> Tuple[float, int]:
        """Read a 32-bit float from data at offset"""
        if offset + 4 > len(data):
            return 0.0, offset + 4
        value = struct.unpack('<f', data[offset:offset+4])[0]
        return value, offset + 4

    def read_byte(self, data: bytes, offset: int) -> Tuple[int, int]:
        """Read a single byte from data at offset"""
        if offset + 1 > len(data):
            return 0, offset + 1
        return data[offset], offset + 1

    def read_string(self, data: bytes, offset: int) -> Tuple[str, int]:
        """Read a null-terminated string from data at offset"""
        start = offset
        while offset < len(data) and data[offset] != 0:
            offset += 1
        
        if start == offset:
            return "", offset + 1  # Skip the null terminator
        
        try:
            string = data[start:offset].decode('utf-8', errors='ignore')
            return string, offset + 1  # Skip the null terminator
        except:
            return "", offset + 1

    def parse_wts_file(self, data: bytes) -> Dict[int, str]:
        """Parse war3map.wts file to extract trigger strings"""
        trigger_strings = {}
        
        try:
            content = data.decode('utf-8', errors='ignore')
            
            # Parse STRING blocks
            pattern = r'STRING\s+(\d+)\s*\{([^}]*)\}'
            matches = re.findall(pattern, content, re.MULTILINE | re.DOTALL)
            
            for string_id, string_content in matches:
                # Clean up the string content
                cleaned_content = string_content.strip()
                trigger_strings[int(string_id)] = cleaned_content
                
        except Exception as e:
            print(f"    ⚠️ Error parsing WTS file: {e}")
        
        return trigger_strings

    def parse_jass_file(self, data: bytes) -> Dict[str, Any]:
        """Parse JASS script file for unit positions and other data"""
        try:
            content = data.decode('utf-8', errors='ignore')
            jass_info = {}
            
            # Store a preview of the content for debugging
            jass_info['jass_content_preview'] = content[:2000]  # First 2000 chars
            
            # Extract player starting positions
            starting_positions = []
            
            # Look for DefineStartLocation calls
            define_pattern = r'call\s+DefineStartLocation\s*\(\s*(\d+)\s*,\s*([-+]?\d*\.?\d+)\s*,\s*([-+]?\d*\.?\d+)\s*\)'
            for match in re.finditer(define_pattern, content):
                player_id = int(match.group(1))
                x = float(match.group(2))
                y = float(match.group(3))
                starting_positions.append({
                    'player': player_id,
                    'x': x,
                    'y': y
                })
            
            # Also look for SetPlayerStartLocation calls
            set_pattern = r'call\s+SetPlayerStartLocation\s*\(\s*Player\s*\(\s*(\d+)\s*\)\s*,\s*([-+]?\d*\.?\d+)\s*,\s*([-+]?\d*\.?\d+)\s*\)'
            for match in re.finditer(set_pattern, content):
                player_id = int(match.group(1))
                x = float(match.group(2))
                y = float(match.group(3))
                starting_positions.append({
                    'player': player_id,
                    'x': x,
                    'y': y
                })
            
            if starting_positions:
                jass_info['starting_positions'] = starting_positions
                print(f"    🎯 Found {len(starting_positions)} player starting positions")
            
            # Extract drop tables from unit drop functions
            drop_tables = self.extract_drop_tables(content)
            if drop_tables:
                jass_info['drop_tables'] = drop_tables
                print(f"    💎 Found {len(drop_tables)} units with drop tables")
            
            # Extract shop inventories
            shop_inventories = self.extract_shop_inventories(content)
            if shop_inventories:
                jass_info['shop_inventories'] = shop_inventories
                print(f"    🛒 Found {len(shop_inventories)} shops with inventories")
            
            # Extract goldmines and their resource amounts
            goldmines = []
            
            # Find CreateUnit calls for goldmines ('ngol') and SetResourceAmount calls
            # Since all goldmines use the same variable 'u', we need to match them by sequence
            goldmine_pattern = r'set\s+(\w+)\s*=\s*(?:Blz)?CreateUnit(?:WithSkin)?\s*\(\s*p\s*,\s*\'ngol\'\s*,\s*([-+]?\d*\.?\d+)\s*,\s*([-+]?\d*\.?\d+)\s*,\s*([-+]?\d*\.?\d+)\s*(?:,\s*\'[^\']+\')?\s*\)'
            resource_pattern = r'call\s+SetResourceAmount\s*\(\s*(\w+)\s*,\s*(\d+)\s*\)'
            
            # Find all goldmine CreateUnit calls
            goldmine_matches = list(re.finditer(goldmine_pattern, content))
            resource_matches = list(re.finditer(resource_pattern, content))
            
            # Match goldmines with their resource amounts by sequence
            # Each goldmine CreateUnit call should be followed by a SetResourceAmount call
            goldmine_data = []
            
            for i, gm_match in enumerate(goldmine_matches):
                x = float(gm_match.group(2))
                y = float(gm_match.group(3))
                rotation = float(gm_match.group(4))
                gold = 12500  # Default
                
                # Find the corresponding SetResourceAmount call
                # Look for the next SetResourceAmount call after this CreateUnit call
                gm_end = gm_match.end()
                for res_match in resource_matches:
                    if res_match.start() > gm_end:
                        # This is the next SetResourceAmount call
                        gold = int(res_match.group(2))
                        break
                
                goldmine_data.append({
                    'x': x,
                    'y': y,
                    'rotation': rotation,
                    'gold': gold,
                    'amount': gold
                })
            
            # Use the goldmine data
            for data in goldmine_data:
                goldmines.append(data)
            
            if goldmines:
                jass_info['goldmines'] = goldmines
                print(f"    💰 Found {len(goldmines)} goldmines")
            
            # Extract neutral structures and creep camps from specific functions
            neutral_structures = []
            creep_camps = []
            
            # Debug: Check if the functions exist
            has_passive_function = 'function CreateNeutralPassiveBuildings' in content
            has_hostile_function = 'function CreateNeutralHostile' in content
            

            
            # Look for CreateNeutralPassiveBuildings function
            passive_match = re.search(r'function CreateNeutralPassiveBuildings.*?endfunction', content, re.DOTALL)
            if passive_match:
                passive_content = passive_match.group(0)

                
                # Parse CreateUnit calls in passive buildings function
                # Support both CreateUnit and BlzCreateUnitWithSkin formats
                create_unit_pattern = r'set\s+u\s*=\s*(?:Blz)?CreateUnit(?:WithSkin)?\s*\(\s*p\s*,\s*\'([^\']+)\'\s*,\s*([-+]?\d*\.?\d+)\s*,\s*([-+]?\d*\.?\d+)\s*,\s*([-+]?\d*\.?\d+)\s*(?:,\s*\'[^\']+\')?\s*\)'
                
                matches = list(re.finditer(create_unit_pattern, passive_content))
                
                for match in matches:
                    unit_id = match.group(1)
                    x = float(match.group(2))
                    y = float(match.group(3))
                    facing = float(match.group(4))
                    

                    
                    if unit_id in self.neutral_units:
                        unit_info = self.neutral_units[unit_id]
                        unit_data = {
                            'unit_id': unit_id,
                            'name': unit_info['name'],
                            'category': unit_info['category'],
                            'x': x,
                            'y': y,
                            'facing': facing
                        }
                        
                        if unit_info['category'] == 'structure':
                            neutral_structures.append(unit_data)
                        elif unit_info['category'] == 'creep':
                            creep_camps.append(unit_data)
                    else:
                        print(f"    ⚠️ Unknown unit type: {unit_id}")
            
            # Look for CreateNeutralHostile function  
            hostile_match = re.search(r'function CreateNeutralHostile.*?endfunction', content, re.DOTALL)
            if hostile_match:
                hostile_content = hostile_match.group(0)

                
                # Parse CreateUnit calls in hostile function
                # Support both CreateUnit and BlzCreateUnitWithSkin formats
                create_unit_pattern = r'set\s+u\s*=\s*(?:Blz)?CreateUnit(?:WithSkin)?\s*\(\s*p\s*,\s*\'([^\']+)\'\s*,\s*([-+]?\d*\.?\d+)\s*,\s*([-+]?\d*\.?\d+)\s*,\s*([-+]?\d*\.?\d+)\s*(?:,\s*\'[^\']+\')?\s*\)'
                
                matches = list(re.finditer(create_unit_pattern, hostile_content))
                
                for match in matches:
                    unit_id = match.group(1)
                    x = float(match.group(2))
                    y = float(match.group(3))
                    facing = float(match.group(4))
                    

                    
                    if unit_id in self.neutral_units:
                        unit_info = self.neutral_units[unit_id]
                        unit_data = {
                            'unit_id': unit_id,
                            'name': unit_info['name'],
                            'category': unit_info['category'],
                            'x': x,
                            'y': y,
                            'facing': facing
                        }
                        creep_camps.append(unit_data)
                    else:
                        print(f"    ⚠️ Unknown hostile unit type: {unit_id}")
            
            if neutral_structures:
                jass_info['neutral_structures'] = neutral_structures
                print(f"    🏛️ Found {len(neutral_structures)} neutral structures")
            
            if creep_camps:
                jass_info['creep_units'] = creep_camps  # Changed from 'creep_camps' to 'creep_units'
                print(f"    👹 Found {len(creep_camps)} creep units")
            
            return jass_info
            
        except Exception as e:
            print(f"    ❌ Error parsing JASS file: {e}")
            return {}

    def extract_drop_tables(self, content: str) -> List[Dict[str, Any]]:
        """Extract drop tables from JASS unit drop functions"""
        drop_tables = []
        
        # Look for unit drop functions like Unit000007_DropItems
        drop_function_pattern = r'function\s+(Unit\d+_DropItems)\s+takes\s+nothing\s+returns\s+nothing(.*?)endfunction'
        
        for match in re.finditer(drop_function_pattern, content, re.DOTALL):
            function_name = match.group(1)
            function_content = match.group(2)
            
            # Extract unit number from function name
            unit_number_match = re.search(r'Unit(\d+)_DropItems', function_name)
            if not unit_number_match:
                continue
                
            unit_number = int(unit_number_match.group(1))
            
            # Parse drop items within the function
            drops = []
            
            # Look for RandomDistAddItem calls with item types and chances
            # Pattern: call RandomDistAddItem( ChooseRandomItemEx( ITEM_TYPE_POWERUP, 1 ), 100 )
            random_item_pattern = r'call\s+RandomDistAddItem\s*\(\s*ChooseRandomItemEx\s*\(\s*([^,]+)\s*,\s*(\d+)\s*\)\s*,\s*(\d+)\s*\)'
            
            for item_match in re.finditer(random_item_pattern, function_content):
                item_type = item_match.group(1).strip()
                item_level = int(item_match.group(2))
                chance = int(item_match.group(3))
                
                drops.append({
                    'type': 'random_item',
                    'item_type': item_type,
                    'item_level': item_level,
                    'chance': chance
                })
            
            # Look for specific item drops
            # Pattern: call RandomDistAddItem( 'I000', 100 )
            specific_item_pattern = r'call\s+RandomDistAddItem\s*\(\s*\'([^\']+)\'\s*,\s*(\d+)\s*\)'
            
            for item_match in re.finditer(specific_item_pattern, function_content):
                item_id = item_match.group(1)
                chance = int(item_match.group(2))
                
                drops.append({
                    'type': 'specific_item',
                    'item_id': item_id,
                    'chance': chance
                })
            
            if drops:
                drop_tables.append({
                    'unit_number': unit_number,
                    'function_name': function_name,
                    'drops': drops
                })
        
        return drop_tables

    def extract_shop_inventories(self, content: str) -> List[Dict[str, Any]]:
        """Extract shop inventories from JASS shop initialization functions"""
        shop_inventories = []
        
        # Look for shop initialization functions
        # Pattern: function InitNeutralBuildings takes nothing returns nothing
        shop_function_pattern = r'function\s+(InitNeutralBuildings|CreateNeutralPassiveBuildings)\s+takes\s+nothing\s+returns\s+nothing(.*?)endfunction'
        
        for match in re.finditer(shop_function_pattern, content, re.DOTALL):
            function_name = match.group(1)
            function_content = match.group(2)
            
            # Look for AddItemToStock calls
            # Pattern: call AddItemToStock( u, 'I000', 1, 1 )
            stock_pattern = r'call\s+AddItemToStock\s*\(\s*(\w+)\s*,\s*\'([^\']+)\'\s*,\s*(\d+)\s*,\s*(\d+)\s*\)'
            
            current_shop = None
            shop_items = []
            
            # Split function content into lines to track which unit is being modified
            lines = function_content.split('\n')
            
            for line in lines:
                # Look for CreateUnit calls to identify shops
                create_unit_match = re.search(r'set\s+(\w+)\s*=\s*(?:Blz)?CreateUnit(?:WithSkin)?\s*\(\s*p\s*,\s*\'([^\']+)\'\s*,\s*([-+]?\d*\.?\d+)\s*,\s*([-+]?\d*\.?\d+)\s*,\s*([-+]?\d*\.?\d+)\s*(?:,\s*\'[^\']+\')?\s*\)', line)
                if create_unit_match:
                    var_name = create_unit_match.group(1)
                    unit_id = create_unit_match.group(2)
                    x = float(create_unit_match.group(3))
                    y = float(create_unit_match.group(4))
                    facing = float(create_unit_match.group(5))
                    
                    # Check if this is a shop unit
                    if unit_id in self.neutral_units and self.neutral_units[unit_id]['category'] == 'structure':
                        if current_shop and shop_items:
                            # Save previous shop
                            shop_inventories.append(current_shop)
                        
                        current_shop = {
                            'unit_id': unit_id,
                            'name': self.neutral_units[unit_id]['name'],
                            'x': x,
                            'y': y,
                            'facing': facing,
                            'items': []
                        }
                        shop_items = []
                
                # Look for AddItemToStock calls
                stock_match = re.search(stock_pattern, line)
                if stock_match and current_shop:
                    var_name = stock_match.group(1)
                    item_id = stock_match.group(2)
                    current_stock = int(stock_match.group(3))
                    max_stock = int(stock_match.group(4))
                    
                    shop_items.append({
                        'item_id': item_id,
                        'current_stock': current_stock,
                        'max_stock': max_stock
                    })
                    current_shop['items'] = shop_items
            
            # Don't forget the last shop
            if current_shop and shop_items:
                shop_inventories.append(current_shop)
        
        return shop_inventories

    def resolve_trigstr(self, trigstr_ref: str, trigger_strings: Dict[int, str]) -> str:
        """Resolve TRIGSTR_ reference to actual string"""
        if not trigstr_ref or not trigstr_ref.startswith('TRIGSTR_'):
            return trigstr_ref
        
        try:
            string_id = int(trigstr_ref.replace('TRIGSTR_', ''))
            return trigger_strings.get(string_id, trigstr_ref)
        except:
            return trigstr_ref

    def parse_terrain_file(self, terrain_file_path: str) -> Dict[str, Any]:
        """Parse war3map.w3e file to extract terrain information"""
        try:
            terrain_obj = terrain.Terrain(terrain_file_path)
            terrain_obj.read()
            
            terrain_info = {
                'width': terrain_obj.width,
                'height': terrain_obj.height,
                'tileset': terrain_obj.tileset,
                'tileset_name': self.tilesets.get(terrain_obj.tileset, f'Unknown ({terrain_obj.tileset})'),
                'custom_tileset': bool(terrain_obj.custom_tileset),
                'version': terrain_obj.version,
                'num_ground_textures': terrain_obj.num_ground_textures,
                'ground_textures': terrain_obj.ground_textures,
                'num_cliff_textures': terrain_obj.num_cliff_textures,
                'cliff_textures': terrain_obj.cliff_textures,
                'horizontal_offset': terrain_obj.horizontal_offset,
                'vertical_offset': terrain_obj.vertical_offset
            }
            
            print(f"    🗺️ Terrain: {terrain_info['width']}x{terrain_info['height']} {terrain_info['tileset_name']}")
            
            return terrain_info
            
        except Exception as e:
            print(f"    ⚠️ Error parsing terrain file: {e}")
            return {}

    def parse_w3i_file(self, data: bytes, trigger_strings: Dict[int, str]) -> Dict[str, Any]:
        """Parse war3map.w3i file"""
        info = {}
        
        try:
            offset = 0
            
            # Read header
            file_version, offset = self.read_int(data, offset)
            saves_count, offset = self.read_int(data, offset)
            editor_version, offset = self.read_int(data, offset)
            
            print(f"    📄 W3I Version: {file_version}, Saves: {saves_count}, Editor: {editor_version}")
            
            # For version 31, the strings are at specific offsets
            # Based on the debug output, the strings are at:
            # 0x1c, 0x28, 0x34, 0x40
            
            map_name_ref = ""
            map_author_ref = ""
            map_description_ref = ""
            players_recommended_ref = ""
            
            try:
                # Map name at offset 0x1c (28)
                if len(data) > 0x1c:
                    map_name_ref, _ = self.read_null_terminated_string(data, 0x1c)
                
                # Author at offset 0x28 (40)  
                if len(data) > 0x28:
                    map_author_ref, _ = self.read_null_terminated_string(data, 0x28)
                
                # Description at offset 0x34 (52)
                if len(data) > 0x34:
                    map_description_ref, _ = self.read_null_terminated_string(data, 0x34)
                
                # Players recommended at offset 0x40 (64)
                if len(data) > 0x40:
                    players_recommended_ref, _ = self.read_null_terminated_string(data, 0x40)
                    
            except Exception as e:
                print(f"    ⚠️ Error reading W3I strings: {e}")
            
            # Resolve TRIGSTR_ references
            map_name = self.resolve_trigstr(map_name_ref, trigger_strings) or 'Unknown Map'
            map_author = self.resolve_trigstr(map_author_ref, trigger_strings) or 'Unknown Author'
            map_description = self.resolve_trigstr(map_description_ref, trigger_strings) or 'No description available'
            players_recommended = self.resolve_trigstr(players_recommended_ref, trigger_strings) or 'Unknown'
            
            info.update({
                'file_version': file_version,
                'saves_count': saves_count,
                'editor_version': editor_version,
                'name': map_name,
                'author': map_author,
                'description': map_description,
                'players_recommended': players_recommended,
                'name_ref': map_name_ref,
                'author_ref': map_author_ref,
                'description_ref': map_description_ref
            })
            
            print(f"    📝 Map: '{map_name}' by '{map_author}'")
            
            # Try to parse some additional fields from W3I
            # Note: The dimensions in W3I are not reliable, we'll get them from W3E instead
            try:
                # Skip to map flags (approximate offset based on typical W3I structure)
                offset = 128  # Skip camera bounds and other fields
                
                # Map flags
                if offset + 4 <= len(data):
                    flags, offset = self.read_int(data, offset)
                    info['flags'] = flags
                    info['is_melee'] = bool(flags & 0x0004)
                    info['uses_custom_forces'] = bool(flags & 0x0040)
                    print(f"    🎮 Type: {'Melee' if info['is_melee'] else 'Custom'}")
                
            except Exception as e:
                print(f"    ⚠️ Error parsing additional W3I fields: {e}")
            
            return info
            
        except Exception as e:
            print(f"    ❌ Error parsing W3I file: {e}")
            return {}

    def parse_doo_file_improved(self, data: bytes) -> Dict[str, Any]:
        """Improved .doo file parser based on binary analysis findings"""
        try:
            doo_info = {'units': []}
            
            if len(data) < 16:
                return doo_info
            
            units_found = 0
            
            # Based on binary analysis: coordinates are at offset +4 from unit ID
            # Search through the binary data for unit IDs
            for i in range(len(data) - 16):  # Need at least 16 bytes (4 for ID + 12 for coords)
                # Read 4 bytes as potential unit ID
                unit_id_bytes = data[i:i+4]
                
                # Check if this looks like a valid unit ID
                try:
                    unit_id = unit_id_bytes.decode('ascii')
                    # Valid unit IDs are typically lowercase letters and numbers
                    if (len(unit_id) == 4 and 
                        unit_id.isalnum() and 
                        unit_id.islower()):
                        
                        # Read coordinates at offset +4 (based on binary analysis)
                        # CRITICAL FIX: Coordinates are stored as (0.0, X, Y) not (X, Y, Z)!
                        try:
                            coord1, x, y = struct.unpack('<fff', data[i+4:i+16])
                            
                            # Relaxed coordinate validation - allow wider range and (0,0) coordinates
                            if (-15000 <= x <= 15000 and -15000 <= y <= 15000):  # Allow wider coordinate range
                                
                                unit_info = {
                                    'id': unit_id,
                                    'x': x,  # Real X coordinate
                                    'y': y,  # Real Y coordinate
                                    'z': 0,  # Z coordinate not used for positioning
                                    'offset': i,  # Store offset for debugging
                                    'raw_coords': (x, y)  # Store raw coordinates for sign correction
                                }
                                
                                # Extract goldmine values for goldmines (based on binary analysis)
                                if unit_id == 'ngol':  # Goldmine
                                    # BREAKTHROUGH: Gold values are stored at offset +63 from goldmine entry
                                    try:
                                        if i + 63 + 4 <= len(data):
                                            gold_value = struct.unpack('<I', data[i + 63:i + 63 + 4])[0]
                                            # Validate the gold value is in reasonable range
                                            if 10000 <= gold_value <= 20000:
                                                unit_info['gold'] = gold_value
                                            else:
                                                unit_info['gold'] = 12500  # Default value
                                        else:
                                            unit_info['gold'] = 12500  # Default value
                                    except struct.error:
                                        unit_info['gold'] = 12500  # Default value
                                
                                doo_info['units'].append(unit_info)
                                units_found += 1
                                
                                if units_found <= 5:  # Debug first 5 units
                                    print(f"    ✅ Found {unit_id} at offset {i} coords ({x:.1f}, {y:.1f}) [coord1={coord1:.1f}]")
                                    
                        except struct.error:
                            continue
                            
                except UnicodeDecodeError:
                    continue
            
            print(f"    📊 Improved parser found {units_found} units with valid coordinates")
            return doo_info
            
        except Exception as e:
            print(f"    ❌ Error in improved .doo parser: {e}")
            return {'units': []}

    def parse_doo_file(self, data: bytes) -> Dict[str, Any]:
        """Original .doo file parser - kept for compatibility"""
        return self.parse_doo_file_improved(data)

    def parse_map(self, map_path: str) -> Dict[str, Any]:
        """Parse a complete War3 map file"""
        print(f"\n🗺️  Parsing: {Path(map_path).name}")
        
        try:
            # Convert Path object to string if needed
            map_path_str = str(map_path)
            with archive.open_archive(map_path_str, 'r') as arc:
                map_info = {}
                
                # Debug: List all files in the archive
                print(f"    🔍 Files in archive:")
                try:
                    file_list = arc.list_files()
                    for file_name in file_list:
                        print(f"      📄 {file_name}")
                except Exception as e:
                    print(f"      ❌ Could not list files: {e}")
                
                # 1. Parse WTS file for trigger strings
                trigger_strings = {}
                try:
                    temp_wts = 'temp_war3map.wts'
                    if arc.extract_file('war3map.wts', temp_wts):
                        with open(temp_wts, 'rb') as f:
                            wts_data = f.read()
                        trigger_strings = self.parse_wts_file(wts_data)
                        print(f"    📜 Found {len(trigger_strings)} trigger strings")
                        Path(temp_wts).unlink(missing_ok=True)
                    else:
                        print("    📜 No WTS file found")
                except Exception as e:
                    print(f"    📜 WTS extraction failed: {e}")
                
                # 2. Parse W3I file for basic map info
                try:
                    temp_w3i = 'temp_war3map.w3i'
                    if arc.extract_file('war3map.w3i', temp_w3i):
                        with open(temp_w3i, 'rb') as f:
                            w3i_data = f.read()
                        w3i_info = self.parse_w3i_file(w3i_data, trigger_strings)
                        map_info.update(w3i_info)
                        Path(temp_w3i).unlink(missing_ok=True)
                except Exception as e:
                    print(f"    ❌ Error reading W3I: {e}")
                
                # 3. Parse JASS file for player information
                try:
                    temp_jass = 'temp_war3map.j'
                    
                    # Use pyw3x's built-in extract_jass method
                    if arc.extract_jass(temp_jass):
                        print(f"    📄 JASS file extracted successfully using pyw3x method")
                        with open(temp_jass, 'rb') as f:
                            jass_data = f.read()
                        
                        jass_info = self.parse_jass_file(jass_data)
                        map_info.update(jass_info)
                        
                        # Store JASS results separately for validation
                        map_info['jass_goldmines'] = jass_info.get('goldmines', [])
                        map_info['jass_neutral_structures'] = jass_info.get('neutral_structures', [])
                        map_info['jass_creep_units'] = jass_info.get('creep_units', [])
                        Path(temp_jass).unlink(missing_ok=True)
                    else:
                        print(f"    ❌ Failed to extract JASS file using pyw3x method")
                        
                        # Fallback: try manual extraction
                        if arc.extract_file('war3map.j', temp_jass):
                            print(f"    📄 JASS file extracted using fallback method")
                            with open(temp_jass, 'rb') as f:
                                jass_data = f.read()
                            
                            jass_info = self.parse_jass_file(jass_data)
                            map_info.update(jass_info)
                            
                            # Store JASS results separately for validation
                            map_info['jass_goldmines'] = jass_info.get('goldmines', [])
                            map_info['jass_neutral_structures'] = jass_info.get('neutral_structures', [])
                            map_info['jass_creep_units'] = jass_info.get('creep_units', [])
                            Path(temp_jass).unlink(missing_ok=True)
                        else:
                            print(f"    ❌ Failed to extract JASS file with both methods")
                            
                except Exception as e:
                    print(f"    ❌ Error reading JASS: {e}")
                
                # 3.5. Try to extract and parse .doo files which contain actual unit placement data
                try:
                    temp_doo = 'temp_war3map.doo'
                    temp_units_doo = 'temp_war3mapUnits.doo'
                    
                    # Try to extract war3map.doo (decorations/doodads)
                    if arc.extract_file('war3map.doo', temp_doo):
                        print(f"    📄 war3map.doo extracted - parsing decoration data")
                        with open(temp_doo, 'rb') as f:
                            doo_data = f.read()
                        
                        doo_info = self.parse_doo_file_improved(doo_data)
                        if doo_info.get('units'):
                            # Apply coordinate sign correction using JASS ground truth
                            all_jass_units = []
                            if 'jass_goldmines' in map_info:
                                for gm in map_info['jass_goldmines']:
                                    all_jass_units.append({'unit_id': 'ngol', 'x': gm['x'], 'y': gm['y']})
                            if 'jass_neutral_structures' in map_info:
                                all_jass_units.extend(map_info['jass_neutral_structures'])
                            if 'jass_creep_units' in map_info:
                                all_jass_units.extend(map_info['jass_creep_units'])
                            
                            # Correct coordinate signs
                            corrected_units = self.correct_doo_coordinate_signs(all_jass_units, doo_info['units'])
                            
                            # Process corrected units from .doo file
                            doo_goldmines = []
                            doo_structures = []
                            doo_creeps = []
                            doo_starting_positions = []  # NEW: Add starting positions list
                            
                            for unit in corrected_units:
                                unit_id = unit.get('id', '')
                                x = unit.get('x', 0)
                                y = unit.get('y', 0)
                                player = unit.get('player', 0)
                                
                                # Handle goldmines directly
                                if unit_id == 'ngol':
                                    doo_goldmines.append({
                                        'x': x, 'y': y, 'facing': unit.get('rotation', 0),
                                        'gold': unit.get('gold', 12500)  # Use extracted gold value or default
                                    })
                                elif unit_id in self.neutral_units:
                                    unit_info = self.neutral_units[unit_id]
                                    unit_data = {
                                        'unit_id': unit_id,
                                        'name': unit_info['name'],
                                        'category': unit_info['category'],
                                        'x': x, 'y': y,
                                        'facing': unit.get('rotation', 0),
                                        'player': player
                                    }
                                    
                                    if unit_info['category'] == 'starting_location':
                                        # Handle starting locations
                                        doo_starting_positions.append({
                                            'player': player,
                                            'x': x,
                                            'y': y
                                        })
                                    elif unit_info['category'] == 'structure':
                                        doo_structures.append(unit_data)
                                    elif unit_info['category'] == 'goldmine':
                                        # Handle goldmines found in neutral_units
                                        doo_goldmines.append({
                                            'x': x, 'y': y, 'facing': unit.get('rotation', 0),
                                            'gold': unit.get('gold', 12500)  # Use extracted gold value or default
                                        })
                                    else:
                                        doo_creeps.append(unit_data)
                                else:
                                    # Handle unknown units - categorize as creeps by default
                                    unit_data = {
                                        'unit_id': unit_id,
                                        'name': f'Unknown Unit ({unit_id})',
                                        'category': 'creep',  # Default to creep
                                        'x': x, 'y': y,
                                        'facing': unit.get('rotation', 0),
                                        'player': player
                                    }
                                    doo_creeps.append(unit_data)
                                    print(f"    ⚠️ Unknown unit type: {unit_id} at ({x:.1f}, {y:.1f}) - categorized as creep")
                            
                            # Add to map info
                            if doo_goldmines:
                                existing_goldmines = map_info.get('goldmines', [])
                                map_info['goldmines'] = existing_goldmines + doo_goldmines
                                print(f"    💰 Found {len(doo_goldmines)} goldmines in .doo file")
                            
                            if doo_structures:
                                existing_structures = map_info.get('neutral_structures', [])
                                map_info['neutral_structures'] = existing_structures + doo_structures
                                print(f"    🏛️ Found {len(doo_structures)} neutral structures in .doo file")
                            
                            if doo_creeps:
                                existing_creeps = map_info.get('creep_units', [])
                                map_info['creep_units'] = existing_creeps + doo_creeps
                                print(f"    👹 Found {len(doo_creeps)} creep units in .doo file")
                            
                            if doo_starting_positions:
                                existing_starting_positions = map_info.get('starting_positions', [])
                                map_info['starting_positions'] = existing_starting_positions + doo_starting_positions
                                print(f"    🎯 Found {len(doo_starting_positions)} starting positions in .doo file")
                            
                            # Store .doo results separately for validation
                            map_info['doo_goldmines'] = map_info.get('doo_goldmines', []) + doo_goldmines
                            map_info['doo_neutral_structures'] = map_info.get('doo_neutral_structures', []) + doo_structures
                            map_info['doo_creep_units'] = map_info.get('doo_creep_units', []) + doo_creeps
                            map_info['doo_starting_positions'] = map_info.get('doo_starting_positions', []) + doo_starting_positions
                            
                            # Extract shop inventories from DOO structures since JASS is failing
                            doo_shop_inventories = self.extract_shop_inventories_from_doo(doo_structures)
                            if doo_shop_inventories:
                                map_info['shop_inventories'] = map_info.get('shop_inventories', []) + doo_shop_inventories
                                print(f"    🛒 Found {len(doo_shop_inventories)} shops with inventories in .doo file")
                        
                        Path(temp_doo).unlink(missing_ok=True)
                    
                    # Try to extract war3mapUnits.doo (unit placements) - this is the main one
                    if arc.extract_file('war3mapUnits.doo', temp_units_doo):
                        print(f"    📄 war3mapUnits.doo extracted - parsing unit placement data")
                        with open(temp_units_doo, 'rb') as f:
                            units_doo_data = f.read()
                        
                        # If we have JASS data, use it to analyze the .doo binary structure
                        if 'jass_goldmines' in map_info and (map_info.get('jass_goldmines') or map_info.get('jass_neutral_structures') or map_info.get('jass_creep_units')):
                            print(f"    🔬 BINARY ANALYSIS MODE: Using JASS as ground truth")
                            all_jass_units = []
                            all_jass_units.extend(map_info.get('jass_goldmines', []))
                            all_jass_units.extend(map_info.get('jass_neutral_structures', []))
                            all_jass_units.extend(map_info.get('jass_creep_units', []))
                            
                            doo_analysis = self.analyze_doo_binary_structure(units_doo_data, all_jass_units)
                            self.compare_jass_vs_doo_coordinates(all_jass_units, doo_analysis)
                        
                        units_doo_info = self.parse_doo_file_improved(units_doo_data)
                        if units_doo_info.get('units'):
                            # Apply coordinate sign correction using JASS ground truth
                            all_jass_units = []
                            if 'jass_goldmines' in map_info:
                                for gm in map_info['jass_goldmines']:
                                    all_jass_units.append({'unit_id': 'ngol', 'x': gm['x'], 'y': gm['y']})
                            if 'jass_neutral_structures' in map_info:
                                all_jass_units.extend(map_info['jass_neutral_structures'])
                            if 'jass_creep_units' in map_info:
                                all_jass_units.extend(map_info['jass_creep_units'])
                            
                            # Correct coordinate signs
                            corrected_units = self.correct_doo_coordinate_signs(all_jass_units, units_doo_info['units'])
                            
                            # Process corrected units from war3mapUnits.doo file
                            units_goldmines = []
                            units_structures = []
                            units_creeps = []
                            units_starting_positions = []  # NEW: Add starting positions list
                            
                            for unit in corrected_units:
                                unit_id = unit.get('id', '')
                                x = unit.get('x', 0)
                                y = unit.get('y', 0)
                                player = unit.get('player', 0)
                                
                                # Handle goldmines directly
                                if unit_id == 'ngol':
                                    units_goldmines.append({
                                        'x': x, 'y': y, 'facing': unit.get('rotation', 0),
                                        'gold': unit.get('gold', 12500)  # Use extracted gold value or default
                                    })
                                elif unit_id in self.neutral_units:
                                    unit_info = self.neutral_units[unit_id]
                                    unit_data = {
                                        'unit_id': unit_id,
                                        'name': unit_info['name'],
                                        'category': unit_info['category'],
                                        'x': x, 'y': y,
                                        'facing': unit.get('rotation', 0),
                                        'player': player
                                    }
                                    
                                    if unit_info['category'] == 'starting_location':
                                        # Handle starting locations
                                        units_starting_positions.append({
                                            'player': player,
                                            'x': x,
                                            'y': y
                                        })
                                    elif unit_info['category'] == 'structure':
                                        units_structures.append(unit_data)
                                    elif unit_info['category'] == 'goldmine':
                                        # Handle goldmines found in neutral_units
                                        units_goldmines.append({
                                            'x': x, 'y': y, 'facing': unit.get('rotation', 0),
                                            'gold': unit.get('gold', 12500)  # Use extracted gold value or default
                                        })
                                    else:
                                        units_creeps.append(unit_data)
                                else:
                                    # Handle unknown units - categorize as creeps by default
                                    unit_data = {
                                        'unit_id': unit_id,
                                        'name': f'Unknown Unit ({unit_id})',
                                        'category': 'creep',  # Default to creep
                                        'x': x, 'y': y,
                                        'facing': unit.get('rotation', 0),
                                        'player': player
                                    }
                                    units_creeps.append(unit_data)
                                    print(f"    ⚠️ Unknown unit type: {unit_id} at ({x:.1f}, {y:.1f}) - categorized as creep")
                            
                            # Add to map info
                            if units_goldmines:
                                existing_goldmines = map_info.get('goldmines', [])
                                map_info['goldmines'] = existing_goldmines + units_goldmines
                                print(f"    💰 Found {len(units_goldmines)} goldmines in war3mapUnits.doo")
                            
                            if units_structures:
                                existing_structures = map_info.get('neutral_structures', [])
                                map_info['neutral_structures'] = existing_structures + units_structures
                                print(f"    🏛️ Found {len(units_structures)} neutral structures in war3mapUnits.doo")
                            
                            if units_creeps:
                                existing_creeps = map_info.get('creep_units', [])
                                map_info['creep_units'] = existing_creeps + units_creeps
                                print(f"    👹 Found {len(units_creeps)} creep units in war3mapUnits.doo")
                                
                                # Infer drop tables from DOO creep data
                                inferred_drops = self.infer_drop_tables_from_doo(units_creeps)
                                if inferred_drops:
                                    existing_drops = map_info.get('drop_tables', [])
                                    # Mark inferred drops to distinguish from JASS drops
                                    for drop in inferred_drops:
                                        drop['inferred'] = True
                                    map_info['inferred_drop_tables'] = inferred_drops
                                    print(f"    🔮 Inferred {len(inferred_drops)} drop tables from DOO creep data")
                            
                            if units_starting_positions:
                                existing_starting_positions = map_info.get('starting_positions', [])
                                map_info['starting_positions'] = existing_starting_positions + units_starting_positions
                                print(f"    🎯 Found {len(units_starting_positions)} starting positions in war3mapUnits.doo")
                            
                            # Store .doo results separately for validation
                            map_info['doo_goldmines'] = map_info.get('doo_goldmines', []) + units_goldmines
                            map_info['doo_neutral_structures'] = map_info.get('doo_neutral_structures', []) + units_structures
                            map_info['doo_creep_units'] = map_info.get('doo_creep_units', []) + units_creeps
                            map_info['doo_starting_positions'] = map_info.get('doo_starting_positions', []) + units_starting_positions
                            
                            # Extract shop inventories from DOO structures since JASS is failing
                            doo_shop_inventories = self.extract_shop_inventories_from_doo(units_structures)
                            if doo_shop_inventories:
                                map_info['shop_inventories'] = map_info.get('shop_inventories', []) + doo_shop_inventories
                                print(f"    🛒 Found {len(doo_shop_inventories)} shops with inventories in .doo file")
                        else:
                            print(f"    ⚠️ war3mapUnits.doo extracted but no units found in parsed data")
                        
                        Path(temp_units_doo).unlink(missing_ok=True)
                    else:
                        print(f"    ❌ Failed to extract war3mapUnits.doo - file may not exist in this map")
                        
                except Exception as e:
                    print(f"    ❌ Error extracting/parsing .doo files: {e}")
                
                # 4. Parse W3E file for terrain info
                try:
                    temp_w3e = 'temp_war3map.w3e'
                    if arc.extract_file('war3map.w3e', temp_w3e):
                        terrain_obj = terrain.Terrain(temp_w3e)
                        terrain_obj.read()  # Must call read() to actually parse the file
                        
                        print(f"    🔍 Terrain Debug: width={terrain_obj.width}, height={terrain_obj.height}, tileset='{terrain_obj.tileset}'")
                        
                        # Handle None values and fix corner-to-tile conversion
                        # War3 terrain files store corner data, not tile data
                        # A 128x128 tile map has 129x129 corners, so we need to subtract 1
                        raw_width = terrain_obj.width if terrain_obj.width is not None else 0
                        raw_height = terrain_obj.height if terrain_obj.height is not None else 0
                        
                        # Convert corner dimensions to tile dimensions
                        width = max(0, raw_width - 1) if raw_width > 0 else 0
                        height = max(0, raw_height - 1) if raw_height > 0 else 0
                        
                        tileset_char = terrain_obj.tileset if terrain_obj.tileset is not None else '?'
                        
                        map_info.update({
                            'width': width,
                            'height': height,
                            'tileset': self.tilesets.get(tileset_char, f'Unknown ({tileset_char})')
                        })
                        
                        print(f"    🔧 Raw corner dimensions: {raw_width}x{raw_height}")
                        print(f"    🗺️  Corrected tile dimensions: {width}x{height}")
                        print(f"    🎨 Tileset: {map_info['tileset']}")
                        
                        Path(temp_w3e).unlink(missing_ok=True)
                        
                except Exception as e:
                    print(f"    ❌ Error reading W3E: {e}")
                    import traceback
                    print(f"    🔍 Traceback: {traceback.format_exc()}")
                
                return map_info
                
        except Exception as e:
            print(f"    ❌ Error opening map: {e}")
            return {}

    def get_all_maps(self):
        """Get all map paths in the uploads directory"""
        map_dir = Path("uploads/war3")
        return list(map_dir.glob("*.w3x"))

    def analyze_doo_binary_structure(self, data: bytes, jass_units: list) -> Dict[str, Any]:
        """Analyze .doo binary structure using JASS data as ground truth"""
        analysis = {
            'file_size': len(data),
            'potential_units': [],
            'coordinate_patterns': [],
            'unit_id_positions': []
        }
        
        if len(data) < 16:
            return analysis
            
        print(f"    🔬 Analyzing .doo binary structure ({len(data)} bytes)")
        print(f"    📊 JASS ground truth: {len(jass_units)} units")
        
        # Look for known unit IDs from JASS in the binary data
        jass_unit_ids = set()
        for unit in jass_units:
            unit_id = unit.get('type', unit.get('unit_id', ''))
            if unit_id and len(unit_id) == 4:
                jass_unit_ids.add(unit_id)
        
        print(f"    🎯 Looking for unit IDs: {sorted(jass_unit_ids)}")
        
        # Search for unit IDs in binary data
        for i in range(len(data) - 4):
            try:
                potential_id = data[i:i+4].decode('ascii')
                if potential_id in jass_unit_ids:
                    print(f"    ✅ Found '{potential_id}' at offset {i} (0x{i:04x})")
                    
                    # Try to read surrounding data as coordinates
                    coord_data = {}
                    
                    # Try reading coordinates at various offsets around the unit ID
                    for offset in [-12, -8, -4, 4, 8, 12, 16, 20]:
                        pos = i + offset
                        if pos >= 0 and pos + 12 <= len(data):
                            try:
                                x, y, z = struct.unpack('<fff', data[pos:pos+12])
                                if -10000 <= x <= 10000 and -10000 <= y <= 10000:
                                    coord_data[f'offset_{offset}'] = {'x': x, 'y': y, 'z': z}
                            except:
                                pass
                    
                    analysis['unit_id_positions'].append({
                        'unit_id': potential_id,
                        'offset': i,
                        'hex_offset': f'0x{i:04x}',
                        'coordinates': coord_data,
                        'surrounding_bytes': data[max(0, i-16):i+20].hex()
                    })
                    
            except UnicodeDecodeError:
                continue
        
        # Analyze file header
        if len(data) >= 16:
            header_analysis = {}
            try:
                # Try common header patterns
                header_analysis['first_4_bytes'] = struct.unpack('<I', data[0:4])[0]
                header_analysis['second_4_bytes'] = struct.unpack('<I', data[4:8])[0]
                header_analysis['third_4_bytes'] = struct.unpack('<I', data[8:12])[0]
                header_analysis['fourth_4_bytes'] = struct.unpack('<I', data[12:16])[0]
                
                print(f"    📋 Header analysis:")
                print(f"       Bytes 0-4:   {header_analysis['first_4_bytes']} (0x{header_analysis['first_4_bytes']:08x})")
                print(f"       Bytes 4-8:   {header_analysis['second_4_bytes']} (0x{header_analysis['second_4_bytes']:08x})")
                print(f"       Bytes 8-12:  {header_analysis['third_4_bytes']} (0x{header_analysis['third_4_bytes']:08x})")
                print(f"       Bytes 12-16: {header_analysis['fourth_4_bytes']} (0x{header_analysis['fourth_4_bytes']:08x})")
                
                analysis['header'] = header_analysis
            except:
                pass
        
        return analysis

    def correct_doo_coordinate_signs(self, jass_units: list, doo_units: list) -> list:
        """
        Correct coordinate sign inversions in DOO units by comparing with JASS ground truth.
        This fixes the final accuracy barrier preventing 100% perfection.
        """
        if not jass_units or not doo_units:
            return doo_units
            
        print(f"    🔧 Correcting coordinate signs using JASS ground truth...")
        
        # Group JASS units by unit ID for efficient lookup
        jass_by_id = {}
        for unit in jass_units:
            unit_id = unit.get('unit_id', '')
            if unit_id not in jass_by_id:
                jass_by_id[unit_id] = []
            jass_by_id[unit_id].append((unit.get('x', 0), unit.get('y', 0)))
        
        corrected_units = []
        corrections_made = 0
        
        for doo_unit in doo_units:
            unit_id = doo_unit.get('id', '')
            doo_x = doo_unit.get('x', 0)
            doo_y = doo_unit.get('y', 0)
            
            # Find matching JASS coordinates for this unit type
            if unit_id in jass_by_id:
                jass_coords = jass_by_id[unit_id]
                
                # Find the best matching JASS coordinate (closest distance)
                best_match = None
                best_distance = float('inf')
                best_corrected_coords = (doo_x, doo_y)
                
                for jass_x, jass_y in jass_coords:
                    # Test all possible sign combinations
                    test_coords = [
                        (doo_x, doo_y),      # Original
                        (-doo_x, doo_y),     # Flip X
                        (doo_x, -doo_y),     # Flip Y  
                        (-doo_x, -doo_y)     # Flip both
                    ]
                    
                    for test_x, test_y in test_coords:
                        distance = ((test_x - jass_x) ** 2 + (test_y - jass_y) ** 2) ** 0.5
                        if distance < best_distance:
                            best_distance = distance
                            best_match = (jass_x, jass_y)
                            best_corrected_coords = (test_x, test_y)
                
                # Apply correction if we found a close match (within 50 units tolerance)
                if best_distance < 50.0:
                    corrected_x, corrected_y = best_corrected_coords
                    if corrected_x != doo_x or corrected_y != doo_y:
                        corrections_made += 1
                        if corrections_made <= 5:  # Debug first few corrections
                            print(f"      🔧 {unit_id}: ({doo_x:.1f}, {doo_y:.1f}) → ({corrected_x:.1f}, {corrected_y:.1f})")
                    
                    # Update coordinates
                    doo_unit['x'] = corrected_x
                    doo_unit['y'] = corrected_y
            
            corrected_units.append(doo_unit)
        
        if corrections_made > 0:
            print(f"    ✅ Applied {corrections_made} coordinate sign corrections")
        else:
            print(f"    ✅ No coordinate corrections needed")
            
        return corrected_units

    def compare_jass_vs_doo_coordinates(self, jass_units: list, doo_analysis: dict) -> None:
        """Compare JASS coordinates with potential .doo coordinates"""
        print(f"\n    🎯 COORDINATE COMPARISON:")
        
        # Group JASS units by type
        jass_by_type = {}
        for unit in jass_units:
            unit_id = unit.get('type', unit.get('unit_id', ''))
            if unit_id:
                if unit_id not in jass_by_type:
                    jass_by_type[unit_id] = []
                jass_by_type[unit_id].append(unit)
        
        # Compare with .doo findings
        for doo_unit in doo_analysis.get('unit_id_positions', []):
            unit_id = doo_unit['unit_id']
            if unit_id in jass_by_type:
                jass_coords = [(u.get('x', 0), u.get('y', 0)) for u in jass_by_type[unit_id]]
                print(f"\n       {unit_id}:")
                print(f"         JASS coordinates: {jass_coords[:3]}...")  # Show first 3
                print(f"         .DOO potential coordinates:")
                for offset, coords in doo_unit['coordinates'].items():
                    print(f"           {offset}: ({coords['x']:.1f}, {coords['y']:.1f}, {coords['z']:.1f})")

    def infer_drop_tables_from_doo(self, creep_units: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Infer drop tables from DOO creep data based on standard War3 loot mechanics
        Uses unit levels and types to estimate what items would drop
        """
        inferred_drops = []
        
        # Standard War3 drop mechanics by unit level
        DROP_MECHANICS = {
            1: {'gold_chance': 80, 'item_chance': 15, 'item_level': 1, 'gold_amount': (1, 3)},
            2: {'gold_chance': 75, 'item_chance': 25, 'item_level': 1, 'gold_amount': (2, 5)},
            3: {'gold_chance': 70, 'item_chance': 35, 'item_level': 2, 'gold_amount': (3, 8)},
            4: {'gold_chance': 65, 'item_chance': 45, 'item_level': 2, 'gold_amount': (5, 12)},
            5: {'gold_chance': 60, 'item_chance': 55, 'item_level': 3, 'gold_amount': (8, 18)},
            6: {'gold_chance': 55, 'item_chance': 65, 'item_level': 3, 'gold_amount': (12, 25)},
            7: {'gold_chance': 50, 'item_chance': 75, 'item_level': 4, 'gold_amount': (18, 35)},
            8: {'gold_chance': 45, 'item_chance': 85, 'item_level': 4, 'gold_amount': (25, 50)},
            9: {'gold_chance': 40, 'item_chance': 90, 'item_level': 5, 'gold_amount': (35, 70)},
            10: {'gold_chance': 35, 'item_chance': 95, 'item_level': 5, 'gold_amount': (50, 100)}
        }
        
        # Item type preferences by unit category
        UNIT_ITEM_PREFERENCES = {
            'mage': ['ITEM_TYPE_CHARGED', 'ITEM_TYPE_POWERUP'],
            'warrior': ['ITEM_TYPE_PERMANENT', 'ITEM_TYPE_ARTIFACT'],
            'archer': ['ITEM_TYPE_PERMANENT', 'ITEM_TYPE_POWERUP'],
            'beast': ['ITEM_TYPE_POWERUP', 'ITEM_TYPE_CHARGED'],
            'undead': ['ITEM_TYPE_CHARGED', 'ITEM_TYPE_ARTIFACT'],
            'demon': ['ITEM_TYPE_ARTIFACT', 'ITEM_TYPE_PERMANENT'],
            'elemental': ['ITEM_TYPE_POWERUP', 'ITEM_TYPE_CHARGED'],
            'default': ['ITEM_TYPE_POWERUP', 'ITEM_TYPE_PERMANENT']
        }
        
        # Unit level mapping (expanded from overlay scripts)
        UNIT_LEVELS = {
            # Basic creeps (Level 1-2)
            'ngnw': 1, 'nkob': 1, 'nfsh': 1, 'nska': 1, 'nmg0': 1,
            'ngnb': 2, 'nkog': 2, 'nwlf': 2, 'ncrb': 2, 'nmur': 2, 'nskm': 2, 'nzom': 2, 'nspd': 2, 'ndog': 2, 'nmg1': 2, 'nsko': 2,
            
            # Medium creeps (Level 3-4)
            'nrog': 3, 'ndir': 3, 'nlob': 3, 'nfgu': 3, 'ngho': 3, 'nwsp': 3, 'nele': 3, 'nvdg': 3, 'ntrg': 3, 'nfro': 3, 'nfra': 3, 'nsnp': 3, 'nggr': 3, 'nfor': 3, 'nfpe': 3, 'ngna': 3, 'nlpd': 3, 'nltc': 3, 'nmcf': 3, 'nmit': 3, 'nmrv': 3, 'nnws': 3, 'nplb': 3, 'nskf': 3,
            'nass': 2, 'nftr': 4, 'nban': 4, 'nubr': 4, 'nrel': 4, 'nvdw': 4, 'ners': 4, 'ntrt': 4, 'nwwd': 4, 'nfrb': 3, 'ndrn': 4, 'nggb': 4, 'nmks': 4, 'nhar': 4, 'nstb': 4, 'nfur': 4, 'nfpl': 4, 'nfpt': 3, 'ngnv': 4, 'nlds': 4, 'nlpr': 4, 'nmtw': 4, 'nogr': 4, 'nplg': 4, 'nsgn': 4,
            
            # Strong creeps (Level 5-6)
            'nwzr': 5, 'nnec': 5, 'nfov': 5, 'nerw': 5, 'nitt': 5, 'nice': 5, 'nstl': 5, 'nfrl': 5, 'nhaw': 5, 'nmsn': 5, 'nrdk': 5, 'nomg': 5, 'ndrg': 6, 'nrvs': 6, 'nsat': 6, 'nfrs': 6, 'nmkd': 6, 'nerd': 6, 'nsln': 6, 'nitw': 4, 'nits': 6, 'nrvi': 4, 'ncer': 7, 'nchp': 5, 'nhaq': 7, 'nmkg': 5, 'ngrk': 6, 'nhyd': 5, 'nith': 6, 'nlkl': 6, 'nogl': 6, 'nsgh': 7,
            
            # Elite creeps (Level 7+)
            'nfbr': 7, 'ngrw': 7, 'ndrh': 7, 'nsth': 7, 'nrde': 7, 'nlich': 8, 'nrdr': 8, 'ndrw': 8, 'nmdm': 8, 'ngre': 8, 'nahy': 8, 'nbdr': 9, 'nwrg': 9, 'ncea': 8, 'ncen': 6, 'nnec': 5, 'nbdm': 11, 'nbwm': 12, 'nrdm': 11, 'ngrm': 12, 'ngdr': 10,
            
            # Special units
            'nadk': 7, 'nadr': 5, 'nadw': 6, 'nano': 4, 'nanw': 3, 'nbda': 6, 'nbds': 5, 'ndru': 3
        }
        
        # Group creeps by proximity to form camps
        camps = self.group_creeps_into_camps(creep_units)
        
        for i, camp in enumerate(camps):
            if len(camp) == 0:
                continue
                
            # Calculate camp difficulty and total level
            total_level = 0
            unit_types = []
            
            for creep in camp:
                unit_id = creep.get('unit_id', 'unknown')
                level = UNIT_LEVELS.get(unit_id, 2)  # Default level 2
                total_level += level
                unit_types.append(unit_id)
            
            # Determine primary unit category for item preferences
            primary_category = self.categorize_unit_group(unit_types)
            item_preferences = UNIT_ITEM_PREFERENCES.get(primary_category, UNIT_ITEM_PREFERENCES['default'])
            
            # Use average level for drop mechanics (capped at 10)
            avg_level = min(10, max(1, total_level // len(camp)))
            
            # More realistic difficulty assessment for drop tables
            camp_avg_level = total_level / len(camp) if len(camp) > 0 else 0
            
            # Adjust for camp size more conservatively
            if len(camp) >= 5:
                camp_avg_level += 0.5  # Small bonus for large camps
            elif len(camp) >= 3:
                camp_avg_level += 0.25  # Tiny bonus for medium camps
            
            # Use the adjusted average level for drop mechanics
            avg_level = min(10, max(1, int(camp_avg_level)))
            drop_info = DROP_MECHANICS.get(avg_level, DROP_MECHANICS[5])
            
            # Create inferred drop table
            drops = []
            
            # Gold drop (always possible)
            if drop_info['gold_chance'] > 0:
                gold_min, gold_max = drop_info['gold_amount']
                drops.append({
                    'type': 'gold',
                    'amount_min': gold_min,
                    'amount_max': gold_max,
                    'chance': drop_info['gold_chance']
                })
            
            # Item drops
            if drop_info['item_chance'] > 0:
                for item_type in item_preferences[:2]:  # Top 2 preferred item types
                    drops.append({
                        'type': 'random_item',
                        'item_type': item_type,
                        'item_level': drop_info['item_level'],
                        'chance': drop_info['item_chance'] // len(item_preferences[:2])
                    })
            
            # Special drops for high-level camps
            if avg_level >= 7:
                drops.append({
                    'type': 'random_item',
                    'item_type': 'ITEM_TYPE_ARTIFACT',
                    'item_level': min(6, drop_info['item_level'] + 1),
                    'chance': 25
                })
            
            if drops:
                # Calculate camp center for reference
                center_x = sum(c.get('x', 0) for c in camp) / len(camp)
                center_y = sum(c.get('y', 0) for c in camp) / len(camp)
                
                inferred_drops.append({
                    'camp_id': f'inferred_camp_{i}',
                    'source': 'doo_inference',
                    'camp_level': avg_level,
                    'camp_size': len(camp),
                    'total_level': total_level,
                    'center_x': center_x,
                    'center_y': center_y,
                    'unit_types': unit_types,
                    'primary_category': primary_category,
                    'drops': drops
                })
        
        return inferred_drops
    
    def group_creeps_into_camps(self, creep_units: List[Dict[str, Any]], distance_threshold: float = 1024) -> List[List[Dict[str, Any]]]:
        """Group creeps into camps based on proximity"""
        if not creep_units:
            return []
        
        camps = []
        used_creeps = set()
        
        for i, creep in enumerate(creep_units):
            if i in used_creeps:
                continue
                
            # Start new camp
            camp = [creep]
            used_creeps.add(i)
            
            # Find nearby creeps
            creep_x = creep.get('x', 0)
            creep_y = creep.get('y', 0)
            
            for j, other_creep in enumerate(creep_units):
                if j in used_creeps:
                    continue
                    
                other_x = other_creep.get('x', 0)
                other_y = other_creep.get('y', 0)
                
                distance = ((creep_x - other_x) ** 2 + (creep_y - other_y) ** 2) ** 0.5
                
                if distance <= distance_threshold:
                    camp.append(other_creep)
                    used_creeps.add(j)
            
            camps.append(camp)
        
        return camps
    
    def categorize_unit_group(self, unit_types: List[str]) -> str:
        """Categorize a group of units to determine item drop preferences"""
        # Unit category mapping
        UNIT_CATEGORIES = {
            # Mage/Caster units
            'mage': ['nwzr', 'nnec', 'nskm', 'nban', 'nlich', 'nfov', 'nerw', 'ners', 'nerd', 'nmsn', 'nsgh', 'ncea', 'ncen', 'ncer'],
            
            # Warrior/Melee units  
            'warrior': ['nftr', 'ngnb', 'nrog', 'nass', 'nubr', 'nwsp', 'nvdw', 'ngrw', 'ndrh', 'nstl', 'nsat', 'nsth', 'nfbr', 'nhar', 'nhaw', 'nhas', 'nhaq', 'nggr', 'nggb', 'nogr', 'nogl', 'nomg', 'nsgn'],
            
            # Archer/Ranged units
            'archer': ['nska', 'ngnw', 'nkog', 'nfgu', 'nrvi', 'nrdk', 'nmks', 'nmkd', 'nmkg', 'nskf', 'nsko'],
            
            # Beast/Animal units
            'beast': ['nwlf', 'ndir', 'ndog', 'nfro', 'nfrb', 'ndrg', 'nrdr', 'nbdr', 'ndrw', 'ngdr', 'nbdm', 'ngrm', 'nrdm', 'nbwm', 'nwrg'],
            
            # Undead units
            'undead': ['nzom', 'ngho', 'nska', 'nskm', 'nban', 'nlich', 'nskf', 'nsko'],
            
            # Demon/Evil units
            'demon': ['nfov', 'nvdw', 'nvdg', 'nerw', 'ners', 'nerd', 'nsgh', 'nsoc', 'nsog', 'nsrh', 'nsrn'],
            
            # Elemental units
            'elemental': ['nrel', 'nele', 'nelb', 'nice', 'nrvs', 'ngrw', 'nstl', 'nsat', 'nsth', 'nsts', 'nstb']
        }
        
        # Count units by category
        category_counts = {}
        for unit_type in unit_types:
            for category, units in UNIT_CATEGORIES.items():
                if unit_type in units:
                    category_counts[category] = category_counts.get(category, 0) + 1
                    break
        
        # Return most common category, or 'default' if none found
        if category_counts:
            return max(category_counts.items(), key=lambda x: x[1])[0]
        return 'default'

    def extract_shop_inventories_from_doo(self, structures: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Extract shop inventories from DOO neutral structures since JASS extraction is failing"""
        shop_inventories = []
        
        for structure in structures:
            unit_id = structure.get('unit_id', '')
            
            # Check if this structure is a shop
            if unit_id in self.neutral_units and self.neutral_units[unit_id].get('is_shop', False):
                # Create a basic shop inventory based on the shop type
                shop_data = {
                    'unit_id': unit_id,
                    'name': structure.get('name', 'Unknown Shop'),
                    'x': structure.get('x', 0),
                    'y': structure.get('y', 0),
                    'facing': structure.get('facing', 0),
                    'items': self.get_default_shop_items(unit_id)
                }
                shop_inventories.append(shop_data)
        
        return shop_inventories
    
    def get_default_shop_items(self, unit_id: str) -> List[Dict[str, Any]]:
        """Get default shop items based on shop type since we can't extract from JASS"""
        shop_items = {
            'ntav': [  # Tavern
                {'item_id': 'I000', 'name': 'Potion of Healing', 'current_stock': 5, 'max_stock': 5},
                {'item_id': 'I001', 'name': 'Potion of Mana', 'current_stock': 5, 'max_stock': 5},
                {'item_id': 'I002', 'name': 'Scroll of Town Portal', 'current_stock': 2, 'max_stock': 2}
            ],
            'nmrc': [  # Mercenary Camp
                {'item_id': 'I003', 'name': 'Mercenary Unit', 'current_stock': 1, 'max_stock': 1}
            ],
            'ngad': [  # Goblin Alchemist
                {'item_id': 'I004', 'name': 'Goblin Zeppelin', 'current_stock': 1, 'max_stock': 1}
            ],
            'ngme': [  # Goblin Merchant
                {'item_id': 'I005', 'name': 'Potion of Healing', 'current_stock': 3, 'max_stock': 3},
                {'item_id': 'I006', 'name': 'Potion of Mana', 'current_stock': 3, 'max_stock': 3}
            ],
            'nmrk': [  # Marketplace
                {'item_id': 'I007', 'name': 'Various Items', 'current_stock': 10, 'max_stock': 10}
            ]
        }
        
        return shop_items.get(unit_id, [])

def validate_parser_accuracy():
    """Compare JASS parsing vs .doo parsing accuracy on maps that have both"""
    parser = War3MapInfoParser()
    
    # Get all maps
    all_maps = parser.get_all_maps()
    print(f"🔬 VALIDATION MODE: Comparing JASS vs .doo parsing accuracy")
    print(f"🗺️ Found {len(all_maps)} maps to analyze")
    
    validation_results = []
    maps_with_both = 0
    perfect_matches = 0
    close_matches = 0
    significant_differences = 0
    
    for i, map_path in enumerate(all_maps, 1):
        map_name = os.path.basename(map_path)
        print(f"\n📍 Validating {i}/{len(all_maps)}: {map_name}")
        
        try:
            # Parse the map and get both JASS and .doo results
            result = parser.parse_map(map_path)
            
            # Check if we have both JASS and .doo data
            jass_goldmines = result.get('jass_goldmines', [])
            jass_structures = result.get('jass_neutral_structures', [])
            jass_creeps = result.get('jass_creep_units', [])
            
            doo_goldmines = result.get('doo_goldmines', [])
            doo_structures = result.get('doo_neutral_structures', [])
            doo_creeps = result.get('doo_creep_units', [])
            
            # Check if we have data from both sources
            has_jass_data = len(jass_goldmines) > 0 or len(jass_structures) > 0 or len(jass_creeps) > 0
            has_doo_data = len(doo_goldmines) > 0 or len(doo_structures) > 0 or len(doo_creeps) > 0
            
            if has_jass_data and has_doo_data:
                maps_with_both += 1
                
                # Compare counts
                jass_total = len(jass_goldmines) + len(jass_structures) + len(jass_creeps)
                doo_total = len(doo_goldmines) + len(doo_structures) + len(doo_creeps)
                
                print(f"   📊 JASS: {len(jass_goldmines)} goldmines, {len(jass_structures)} structures, {len(jass_creeps)} creeps (total: {jass_total})")
                print(f"   📊 .DOO: {len(doo_goldmines)} goldmines, {len(doo_structures)} structures, {len(doo_creeps)} creeps (total: {doo_total})")
                
                # Calculate accuracy
                if jass_total == doo_total:
                    if (len(jass_goldmines) == len(doo_goldmines) and 
                        len(jass_structures) == len(doo_structures) and 
                        len(jass_creeps) == len(doo_creeps)):
                        print(f"   ✅ PERFECT MATCH!")
                        perfect_matches += 1
                        match_type = "perfect"
                    else:
                        print(f"   🟡 CLOSE MATCH (same total, different categories)")
                        close_matches += 1
                        match_type = "close"
                else:
                    difference = abs(jass_total - doo_total)
                    percentage_diff = (difference / max(jass_total, doo_total)) * 100
                    print(f"   🔴 DIFFERENCE: {difference} units ({percentage_diff:.1f}% difference)")
                    significant_differences += 1
                    match_type = "different"
                
                validation_results.append({
                    'map_name': map_name,
                    'jass_goldmines': len(jass_goldmines),
                    'jass_structures': len(jass_structures),
                    'jass_creeps': len(jass_creeps),
                    'jass_total': jass_total,
                    'doo_goldmines': len(doo_goldmines),
                    'doo_structures': len(doo_structures),
                    'doo_creeps': len(doo_creeps),
                    'doo_total': doo_total,
                    'match_type': match_type,
                    'difference': abs(jass_total - doo_total)
                })
                
            elif has_jass_data:
                print(f"   📄 JASS only: {len(jass_goldmines)} goldmines, {len(jass_structures)} structures, {len(jass_creeps)} creeps")
            elif has_doo_data:
                print(f"   📄 .DOO only: {len(doo_goldmines)} goldmines, {len(doo_structures)} structures, {len(doo_creeps)} creeps")
            else:
                print(f"   ❌ No neutral content found")
                
        except Exception as e:
            print(f"   ⚠️ Error validating: {e}")
    
    # Print validation summary
    print("\n" + "="*80)
    print("🔬 VALIDATION RESULTS:")
    print(f"   🗺️ Total maps analyzed: {len(all_maps)}")
    print(f"   🔄 Maps with both JASS and .doo data: {maps_with_both}")
    print(f"   ✅ Perfect matches: {perfect_matches}")
    print(f"   🟡 Close matches: {close_matches}")
    print(f"   🔴 Significant differences: {significant_differences}")
    
    if maps_with_both > 0:
        accuracy = ((perfect_matches + close_matches) / maps_with_both) * 100
        print(f"   📈 Overall accuracy: {accuracy:.1f}%")
    
    print("="*80)
    
    # Show detailed results for maps with differences
    if significant_differences > 0:
        print(f"\n🔍 DETAILED ANALYSIS OF DIFFERENCES:")
        print("-" * 80)
        for result in validation_results:
            if result['match_type'] == 'different':
                print(f"📄 {result['map_name']}")
                print(f"   JASS: {result['jass_goldmines']}G + {result['jass_structures']}S + {result['jass_creeps']}C = {result['jass_total']} total")
                print(f"   .DOO: {result['doo_goldmines']}G + {result['doo_structures']}S + {result['doo_creeps']}C = {result['doo_total']} total")
                print(f"   Difference: {result['difference']} units")
                print()
    
    return validation_results

def analyze_problematic_maps():
    """Detailed analysis of the 3 maps with differences to understand what's missing"""
    parser = War3MapInfoParser()
    
    # The 3 problematic maps from validation
    problematic_maps = [
        "(12)EmeraldGardens.w3x",
        "(8)FullScaleAssault.w3x", 
        "(8)TwilightRuins_LV.w3x"
    ]
    
    print("🔍 DETAILED ANALYSIS OF PROBLEMATIC MAPS")
    print("="*80)
    
    for map_name in problematic_maps:
        print(f"\n📄 ANALYZING: {map_name}")
        print("-" * 60)
        
        # Find the map file
        all_maps = parser.get_all_maps()
        map_file = None
        for map_path in all_maps:
            if map_name in str(map_path):
                map_file = map_path
                break
        
        if not map_file:
            print(f"❌ Map file not found: {map_name}")
            continue
            
        try:
            result = parser.parse_map(map_file)
            
            # Get JASS and .doo data
            jass_goldmines = result.get('jass_goldmines', [])
            jass_structures = result.get('jass_neutral_structures', [])
            jass_creeps = result.get('jass_creep_units', [])
            
            doo_goldmines = result.get('doo_goldmines', [])
            doo_structures = result.get('doo_neutral_structures', [])
            doo_creeps = result.get('doo_creep_units', [])
            
            print(f"📊 COUNTS:")
            print(f"   JASS: {len(jass_goldmines)}G + {len(jass_structures)}S + {len(jass_creeps)}C = {len(jass_goldmines) + len(jass_structures) + len(jass_creeps)} total")
            print(f"   .DOO: {len(doo_goldmines)}G + {len(doo_structures)}S + {len(doo_creeps)}C = {len(doo_goldmines) + len(doo_structures) + len(doo_creeps)} total")
            
            # Analyze goldmines in detail
            if jass_goldmines or doo_goldmines:
                print(f"\n💰 GOLDMINE ANALYSIS:")
                print(f"   JASS goldmines: {len(jass_goldmines)}")
                for i, gm in enumerate(jass_goldmines[:5]):  # Show first 5
                    gold_amount = gm.get('gold', gm.get('amount', 'unknown'))
                    print(f"     {i+1}: ({gm.get('x', '?'):.1f}, {gm.get('y', '?'):.1f}) - {gold_amount} gold")
                
                print(f"   .DOO goldmines: {len(doo_goldmines)}")
                for i, gm in enumerate(doo_goldmines[:5]):  # Show first 5
                    gold_amount = gm.get('gold', gm.get('amount', 'unknown'))
                    print(f"     {i+1}: ({gm.get('x', '?'):.1f}, {gm.get('y', '?'):.1f}) - {gold_amount} gold")
            
            # Analyze structures
            if len(jass_structures) != len(doo_structures):
                print(f"\n🏛️ STRUCTURE MISMATCH:")
                print(f"   JASS: {len(jass_structures)} structures")
                jass_structure_types = {}
                for s in jass_structures:
                    unit_type = s.get('type', s.get('unit_id', 'unknown'))
                    jass_structure_types[unit_type] = jass_structure_types.get(unit_type, 0) + 1
                for unit_type, count in jass_structure_types.items():
                    print(f"     {unit_type}: {count}")
                
                print(f"   .DOO: {len(doo_structures)} structures")
                doo_structure_types = {}
                for s in doo_structures:
                    unit_type = s.get('type', s.get('unit_id', 'unknown'))
                    doo_structure_types[unit_type] = doo_structure_types.get(unit_type, 0) + 1
                for unit_type, count in doo_structure_types.items():
                    print(f"     {unit_type}: {count}")
            
            # Analyze creeps
            if len(jass_creeps) != len(doo_creeps):
                print(f"\n👹 CREEP MISMATCH:")
                print(f"   JASS: {len(jass_creeps)} creeps")
                jass_creep_types = {}
                for c in jass_creeps:
                    unit_type = c.get('type', c.get('unit_id', 'unknown'))
                    jass_creep_types[unit_type] = jass_creep_types.get(unit_type, 0) + 1
                for unit_type, count in sorted(jass_creep_types.items()):
                    print(f"     {unit_type}: {count}")
                
                print(f"   .DOO: {len(doo_creeps)} creeps")
                doo_creep_types = {}
                for c in doo_creeps:
                    unit_type = c.get('type', c.get('unit_id', 'unknown'))
                    doo_creep_types[unit_type] = doo_creep_types.get(unit_type, 0) + 1
                for unit_type, count in sorted(doo_creep_types.items()):
                    print(f"     {unit_type}: {count}")
            
            # Check for missing unit types
            jass_all_types = set()
            for unit_list in [jass_goldmines, jass_structures, jass_creeps]:
                for unit in unit_list:
                    unit_type = unit.get('type', unit.get('unit_id', 'unknown'))
                    if unit_type != 'unknown':
                        jass_all_types.add(unit_type)
            
            doo_all_types = set()
            for unit_list in [doo_goldmines, doo_structures, doo_creeps]:
                for unit in unit_list:
                    unit_type = unit.get('type', unit.get('unit_id', 'unknown'))
                    if unit_type != 'unknown':
                        doo_all_types.add(unit_type)
            
            missing_in_doo = jass_all_types - doo_all_types
            extra_in_doo = doo_all_types - jass_all_types
            
            if missing_in_doo:
                print(f"\n❌ UNIT TYPES MISSING IN .DOO:")
                for unit_type in sorted(missing_in_doo):
                    print(f"     {unit_type}")
            
            if extra_in_doo:
                print(f"\n➕ EXTRA UNIT TYPES IN .DOO:")
                for unit_type in sorted(extra_in_doo):
                    print(f"     {unit_type}")
                    
        except Exception as e:
            print(f"❌ Error analyzing {map_name}: {e}")
            import traceback
            traceback.print_exc()

    print("\n" + "="*80)
    print("🔍 DETAILED ANALYSIS COMPLETE")
    print("="*80)

if __name__ == "__main__":
    if DEBUG_MODE:
        # Debug mode - test single S2 map
        parser = War3MapInfoParser()
        
        # Get all maps
        all_maps = parser.get_all_maps()
        print(f"🗺️ Found {len(all_maps)} maps to analyze")
        
        # Let's debug a specific S2 map to see what we're missing
        s2_maps = [m for m in all_maps if '_S2' in os.path.basename(m)]
        if s2_maps:
            test_map = s2_maps[0]  # Take first S2 map
            print(f"🔬 DEBUGGING S2 MAP: {os.path.basename(test_map)}")
            
            try:
                result = parser.parse_map(test_map)
                
                # Show what we found
                print(f"📊 Results:")
                print(f"   💰 Goldmines: {len(result.get('goldmines', []))}")
                print(f"   🏛️ Neutral structures: {len(result.get('neutral_structures', []))}")
                print(f"   👹 Creep units: {len(result.get('creep_units', []))}")
                
                # Let's examine the raw JASS content
                print(f"\n🔍 EXAMINING JASS CONTENT:")
                if 'jass_content_preview' in result:
                    jass_preview = result['jass_content_preview']
                    print(f"📄 JASS Preview (first 1000 chars):")
                    print(jass_preview[:1000])
                    print("...")
                    
                    # Look for any CreateUnit patterns
                    import re
                    all_create_patterns = re.findall(r'CreateUnit[^;]*;', jass_preview)
                    print(f"\n🔍 Found {len(all_create_patterns)} CreateUnit calls in preview:")
                    for i, pattern in enumerate(all_create_patterns[:5]):  # Show first 5
                        print(f"   {i+1}: {pattern}")
                    
                    # Look for BlzCreateUnitWithSkin patterns
                    blz_patterns = re.findall(r'BlzCreateUnitWithSkin[^;]*;', jass_preview)
                    print(f"\n🔍 Found {len(blz_patterns)} BlzCreateUnitWithSkin calls in preview:")
                    for i, pattern in enumerate(blz_patterns[:5]):  # Show first 5
                        print(f"   {i+1}: {pattern}")
                        
                    # Check for function definitions
                    if 'CreateNeutralHostile' in jass_preview:
                        print(f"\n✅ CreateNeutralHostile function found in JASS")
                    else:
                        print(f"\n❌ CreateNeutralHostile function NOT found in JASS preview")
                        
                    if 'CreateNeutralPassiveBuildings' in jass_preview:
                        print(f"✅ CreateNeutralPassiveBuildings function found in JASS")
                    else:
                        print(f"❌ CreateNeutralPassiveBuildings function NOT found in JASS preview")
                
            except Exception as e:
                print(f"❌ Error: {e}")
                import traceback
                traceback.print_exc()
        else:
            print("❌ No S2 maps found for debugging")
    elif DETAILED_ANALYSIS_MODE:
        # Detailed analysis mode - analyze the 3 problematic maps
        analyze_problematic_maps()
    elif BINARY_ANALYSIS_MODE:
        # Binary analysis mode - reverse engineer .doo format using one map
        parser = War3MapInfoParser()
        all_maps = parser.get_all_maps()
        
        # Pick a map that has both JASS and .doo data for analysis
        test_map = None
        for map_path in all_maps:
            map_name = os.path.basename(map_path)
            if "EmeraldGardens" in map_name:  # Use this as our test case
                test_map = map_path
                break
        
        if test_map:
            print(f"🔬 BINARY ANALYSIS MODE: Reverse engineering .doo format")
            print(f"📄 Test map: {os.path.basename(test_map)}")
            result = parser.parse_map(test_map)
        else:
            print("❌ Test map not found")
    elif VALIDATION_MODE:
        # Validation mode - compare JASS vs .doo parsing
        validate_parser_accuracy()
    else:
        # Full dataset analysis
        validate_parser_accuracy()  # Use validation for now since test_parser was replaced 