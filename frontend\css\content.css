/* Content Page Styles - LEGENDARY EDITION */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@400;600;700&display=swap');

/* Root spacing fixes - clean approach */
.content-main {
    margin: 0;
    padding: 0;
}

.content-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1rem 2rem 2rem 2rem; /* Reduced top padding from 2rem */
    background: 
        radial-gradient(circle at 20% 50%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(255, 69, 0, 0.1) 0%, transparent 50%),
        linear-gradient(135deg, #0a0a1a 0%, #1a1a2e 50%, #16213e 100%);
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

/* Animated background particles */
.content-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(2px 2px at 20px 30px, rgba(255, 215, 0, 0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(138, 43, 226, 0.3), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(255, 69, 0, 0.3), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255, 215, 0, 0.3), transparent);
    background-repeat: repeat;
    background-size: 150px 100px;
    animation: particle-float 20s linear infinite;
    pointer-events: none;
    opacity: 0.4;
}

@keyframes particle-float {
    0% { transform: translateY(0px) translateX(0px); }
    33% { transform: translateY(-10px) translateX(10px); }
    66% { transform: translateY(5px) translateX(-5px); }
    100% { transform: translateY(0px) translateX(0px); }
}

.content-title {
    font-family: 'Orbitron', sans-serif;
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 900;
    background: linear-gradient(45deg, #ffd700, #ffed4e, #ffd700, #fff59d);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
    text-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
    animation: gradient-shine 3s ease-in-out infinite;
    position: relative;
    text-align: center;
}

.content-subtitle {
    font-family: 'Rajdhani', sans-serif;
    font-size: 1.3rem;
    font-weight: 600;
    color: #b8c5d6;
    text-align: center;
    margin-bottom: 2rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

@keyframes gradient-shine {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Content Sections */
.content-section {
    display: none;
}

.content-section.active {
    display: block;
    animation: section-fade-in 0.5s ease-out;
}

@keyframes section-fade-in {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Enhanced Content Count */
.content-count {
    text-align: center;
    margin: 2rem 0;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.05);
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.content-count span {
    font-family: 'Rajdhani', sans-serif;
    font-size: 1.1rem;
    font-weight: 600;
    color: #b8c5d6;
}

/* Enhanced Loading */
.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    text-align: center;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 215, 0, 0.3);
    border-top: 4px solid #ffd700;
    border-radius: 50%;
    animation: epic-spin 1s linear infinite;
    margin-bottom: 1.5rem;
}

@keyframes epic-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading p {
    font-family: 'Rajdhani', sans-serif;
    font-size: 1.2rem;
    color: #b8c5d6;
    animation: loading-pulse 2s ease-in-out infinite;
}

@keyframes loading-pulse {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

/* Epic Content List - Reduced card size by 30% */
.content-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(245px, 1fr)); /* Reduced from 350px to 245px (30% smaller) */
    gap: 1.5rem; /* Slightly reduced gap */
    padding: 1rem 0;
}

/* Legendary Channel Cards - Smaller and more compact */
.channel-card {
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(51, 65, 85, 0.95) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
    margin-bottom: 1.5rem;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    position: relative; /* IMPORTANT: Ensures live indicator positioning works */
}

.channel-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, 
        rgba(255, 215, 0, 0.1) 0%, 
        transparent 50%, 
        rgba(138, 43, 226, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
    pointer-events: none;
}

.channel-card:hover::before {
    opacity: 1;
}

.channel-card:hover {
    transform: translateY(-6px) rotateX(1deg); /* Reduced hover effect */
    border-color: rgba(255, 215, 0, 0.6);
    box-shadow: 
        0 12px 48px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 215, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Channel Banner - Full width spanning entire card */
.channel-banner {
    width: 100%;
    height: 100px; /* Reduced from 120px */
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #4a5568 0%, #718096 100%);
    border-radius: 0; /* Remove border radius to span full width */
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2; /* Banner below live indicator */
}

.channel-banner img {
    width: 100%; /* Full width */
    height: 100%;
    object-fit: cover; /* Changed back to cover for full banner effect */
    object-position: center;
    transition: transform 0.3s ease;
    background: transparent;
}

.channel-banner img.error-fallback {
    opacity: 0.7;
    filter: grayscale(0.3);
    object-fit: cover;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.channel-card:hover .channel-banner img {
    transform: scale(1.05); /* Slightly more scale since we're using cover */
}

/* Add subtle pattern for empty banners */
.channel-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
}

.channel-banner img {
    position: relative;
    z-index: 1;
}

/* Enhanced Live Indicator - More prominent and visible */
.live-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
    background: linear-gradient(135deg, #ff4444 0%, #cc0000 100%);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    font-family: 'Rajdhani', sans-serif;
    font-size: 0.8rem;
    font-weight: 700;
    z-index: 10; /* Higher z-index to ensure it's visible */
    box-shadow: 
        0 2px 10px rgba(255, 68, 68, 0.5),
        0 0 0 2px rgba(255, 255, 255, 0.2);
    animation: pulse-live 2s infinite;
    border: 1px solid rgba(255, 255, 255, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    min-width: 60px;
    text-align: center;
}

.live-indicator.pulsing {
    animation: pulse-live-enhanced 2s infinite;
}

@keyframes pulse-live-enhanced {
    0%, 100% { 
        transform: scale(1);
        box-shadow: 
            0 2px 10px rgba(255, 68, 68, 0.5),
            0 0 0 2px rgba(255, 255, 255, 0.2);
        background: linear-gradient(135deg, #ff4444 0%, #cc0000 100%);
    }
    25% { 
        transform: scale(1.02);
        box-shadow: 
            0 4px 15px rgba(255, 68, 68, 0.7),
            0 0 0 2px rgba(255, 255, 255, 0.3);
    }
    50% { 
        transform: scale(1.05);
        box-shadow: 
            0 4px 20px rgba(255, 68, 68, 0.8),
            0 0 0 3px rgba(255, 255, 255, 0.4);
        background: linear-gradient(135deg, #ff6666 0%, #ee0000 100%);
    }
    75% { 
        transform: scale(1.02);
        box-shadow: 
            0 4px 15px rgba(255, 68, 68, 0.7),
            0 0 0 2px rgba(255, 255, 255, 0.3);
    }
}

/* Channel Info Section - Adjusted for smaller cards */
.channel-info {
    padding: 1rem; /* Reduced from 1.5rem */
    display: flex;
    gap: 0.75rem; /* Reduced gap */
    align-items: flex-start;
}

/* Channel Avatar - Smaller for compact design */
.channel-avatar {
    position: relative;
    flex-shrink: 0;
    width: 48px; /* Reduced from 60px */
    height: 48px;
}

.channel-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    object-position: center;
    border: 2px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.1);
}

.channel-avatar img.error-fallback {
    opacity: 0.8;
    border-color: rgba(255, 215, 0, 0.3);
}

.channel-card:hover .channel-avatar img {
    border-color: rgba(255, 215, 0, 0.5);
    transform: scale(1.05);
}

/* Platform Icon - Adjusted size */
.platform-icon {
    position: absolute;
    bottom: -1px;
    right: -1px;
    width: 16px; /* Reduced from 20px */
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.65rem; /* Smaller icon */
    border: 2px solid #1a1a2e;
    transition: all 0.3s ease;
}

.platform-icon.youtube {
    background: linear-gradient(45deg, #ff0000 0%, #cc0000 100%);
    box-shadow: 0 2px 8px rgba(255, 0, 0, 0.3);
}

.platform-icon.twitch {
    background: linear-gradient(45deg, #9146ff 0%, #6441a3 100%);
    box-shadow: 0 2px 8px rgba(145, 70, 255, 0.3);
}

.platform-icon i {
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Channel Details - Adjusted for smaller cards */
.channel-details {
    flex: 1;
    min-width: 0;
}

.channel-title {
    font-family: 'Rajdhani', sans-serif;
    font-size: 1rem; /* Reduced from 1.2rem */
    font-weight: 700;
    color: #ffd700;
    margin: 0 0 0.4rem 0;
    line-height: 1.2;
    word-wrap: break-word;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.channel-description {
    color: #b8c5d6;
    font-size: 0.8rem; /* Reduced from 0.9rem */
    line-height: 1.3;
    margin: 0 0 0.75rem 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Game Tags with improved styling */
.channel-games {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.game-tag {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.15) 0%, rgba(255, 215, 0, 0.25) 100%);
    color: #ffd700;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 1px solid rgba(255, 215, 0, 0.2);
    transition: all 0.3s ease;
}

.game-tag.no-games {
    background: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.4);
    border-color: rgba(255, 255, 255, 0.1);
    font-style: italic;
    text-transform: none;
}

.game-tag:hover {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.25) 0%, rgba(255, 215, 0, 0.35) 100%);
    border-color: rgba(255, 215, 0, 0.4);
    transform: translateY(-1px);
}

/* Channel Actions - Adjusted for smaller cards */
.channel-actions {
    padding: 0 1rem 1rem 1rem; /* Reduced padding */
}

.channel-link {
    display: inline-flex;
    align-items: center;
    gap: 0.4rem; /* Reduced gap */
    padding: 0.6rem 1.2rem; /* Reduced padding */
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 215, 0, 0.2) 100%);
    color: #ffd700;
    text-decoration: none;
    border-radius: 20px;
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    font-size: 0.85rem; /* Reduced font size */
    border: 1px solid rgba(255, 215, 0, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    width: 100%;
    justify-content: center;
}

.channel-link:hover {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.2) 0%, rgba(255, 215, 0, 0.3) 100%);
    border-color: rgba(255, 215, 0, 0.6);
    color: #fff;
    transform: translateY(-1px); /* Reduced transform */
    box-shadow: 0 3px 12px rgba(255, 215, 0, 0.2);
}

.channel-link i {
    font-size: 0.9rem;
}

/* Live Channel Special Styling */
.channel-card[data-live="true"] {
    border-color: rgba(255, 68, 68, 0.4);
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(255, 68, 68, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.channel-card[data-live="true"]:hover {
    border-color: rgba(255, 68, 68, 0.6);
    box-shadow: 
        0 16px 64px rgba(0, 0, 0, 0.4),
        0 0 30px rgba(255, 68, 68, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* No Channels Message */
.no-channels-message,
.error-message {
    text-align: center;
    padding: 4rem 2rem;
    background: rgba(45, 55, 72, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    border: 2px solid rgba(255, 215, 0, 0.2);
    margin: 2rem 0;
}

.no-channels-message i,
.error-message i {
    font-size: 4rem;
    color: #ffd700;
    margin-bottom: 1.5rem;
    animation: float-icon 3s ease-in-out infinite;
}

@keyframes float-icon {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.no-channels-message h3,
.error-message h3 {
    font-family: 'Orbitron', sans-serif;
    font-size: 1.5rem;
    color: #ffd700;
    margin-bottom: 1rem;
}

.no-channels-message p,
.error-message p {
    font-family: 'Rajdhani', sans-serif;
    font-size: 1.1rem;
    color: #b8c5d6;
    line-height: 1.6;
    margin-bottom: 0.5rem;
}

.retry-btn {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #1a1a2e;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-family: 'Rajdhani', sans-serif;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.retry-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
}

/* Media Queries */
@media (max-width: 768px) {
    .content-container {
        padding: 1rem;
    }
    
    .content-list {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .filters-container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .filter-group {
        justify-content: center;
    }
    
    .filter-btn {
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .content-container {
        padding: 0.5rem;
    }
    
    .content-title {
        font-size: 2rem;
    }
    
    .channel-info {
        flex-direction: column;
    }
    
    .channel-actions {
        margin-top: 1rem;
    }
}

/* ===== ULTRA COMPACT FILTERS ===== */
.filters-container {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    gap: 0.5rem;
    background: rgba(45, 55, 72, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 0.5rem;
    border: 1px solid rgba(255, 215, 0, 0.2);
}

.filter-btn {
    background: transparent;
    border: none;
    color: #e2e8f0;
    padding: 0.75rem 1rem;
    border-radius: 10px;
    cursor: pointer;
    font-family: 'Rajdhani', sans-serif;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 80px;
    justify-content: center;
}

.filter-btn i {
    font-size: 1rem;
}

.filter-btn:hover {
    background: rgba(255, 215, 0, 0.1);
    color: #ffd700;
    transform: translateY(-2px);
}

.filter-btn:hover i {
    transform: scale(1.1);
}

.filter-btn.active {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #1a1a2e;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.filter-btn.active i {
    color: #1a1a2e;
}

.filter-btn span {
    font-size: 0.85rem;
}

/* Platform icon fixes */
.platform-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: white;
    margin-right: 8px;
}

.platform-icon.youtube {
    background: #ff0000;
}

.platform-icon.twitch {
    background: #9146ff;
}

/* Refresh Button Styles */
.refresh-btn {
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(212, 175, 55, 0.2) 100%);
    border: 1px solid rgba(212, 175, 55, 0.3);
    color: var(--primary-gold);
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-left: 1rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.refresh-btn:hover {
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.2) 0%, rgba(212, 175, 55, 0.3) 100%);
    border-color: var(--primary-gold);
    transform: translateY(-1px);
}

.refresh-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.refresh-btn i {
    font-size: 0.875rem;
}

/* ===== Rating System Styles ===== */

.channel-rating {
  margin-top: 1rem;
  padding: 1rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, rgba(255, 255, 255, 0.05) 100%);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
}

.channel-rating:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.08) 100%);
  border-color: rgba(255, 215, 0, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(255, 215, 0, 0.1);
}

.rating-display {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  flex-wrap: wrap;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.rating-main {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 0 0 auto;
  min-width: 0;
}

.rating-display .stars {
  color: #ffd700;
  font-size: 1rem;
  display: flex;
  gap: 0.1rem;
  filter: drop-shadow(0 0 4px rgba(255, 215, 0, 0.3));
}

.rating-display .stars i {
  transition: all 0.2s ease;
}

.rating-display .stars i.fas {
  color: #ffd700;
  text-shadow: 0 0 8px rgba(255, 215, 0, 0.6);
}

.rating-display .stars i.far {
  color: rgba(255, 255, 255, 0.2);
}

.rating-text {
  font-size: 1.1rem;
  color: var(--primary-gold);
  font-weight: 700;
  font-family: 'Rajdhani', sans-serif;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  padding: 0.25rem 0.75rem;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 215, 0, 0.2) 100%);
  border: 1px solid rgba(255, 215, 0, 0.2);
  border-radius: 20px;
  min-width: 50px;
  text-align: center;
}

.rating-text::after {
  content: ' ★';
  opacity: 0.6;
  font-size: 0.8em;
}

.view-reviews-inline {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.12) 100%);
  border: 1px solid rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.8);
  padding: 0.4rem 0.9rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.view-reviews-inline::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.view-reviews-inline:hover::before {
  left: 100%;
}

.view-reviews-inline:hover {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.15) 0%, rgba(255, 215, 0, 0.25) 100%);
  color: var(--primary-gold);
  border-color: rgba(255, 215, 0, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 215, 0, 0.2);
}

.no-reviews {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.4);
  font-style: italic;
  padding: 0.4rem 0.9rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  text-align: center;
}

.rating-actions {
  display: flex;
  justify-content: center;
  margin-top: 0.75rem;
}

.rate-btn {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 215, 0, 0.2) 100%);
  border: 2px solid rgba(255, 215, 0, 0.3);
  color: var(--primary-gold);
  padding: 0.7rem 1.4rem;
  border-radius: 25px;
  font-size: 0.85rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 0.6rem;
  font-family: 'Rajdhani', sans-serif;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.1);
}

.rate-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.2), transparent);
  transition: left 0.6s ease;
}

.rate-btn:hover::before {
  left: 100%;
}

.rate-btn:hover {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.25) 0%, rgba(255, 215, 0, 0.4) 100%);
  border-color: var(--primary-gold);
  color: #fff;
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 24px rgba(255, 215, 0, 0.3);
}

.rate-btn:active {
  transform: translateY(-1px) scale(0.98);
}

.rate-btn i {
  font-size: 1rem;
  filter: drop-shadow(0 0 4px currentColor);
}

/* Enhanced Rating Section for Live Channels */
.channel-card[data-live="true"] .channel-rating {
  background: linear-gradient(135deg, rgba(255, 68, 68, 0.05) 0%, rgba(255, 215, 0, 0.05) 100%);
  border-color: rgba(255, 68, 68, 0.2);
  box-shadow: 0 0 20px rgba(255, 68, 68, 0.1);
}

.channel-card[data-live="true"] .channel-rating:hover {
  border-color: rgba(255, 68, 68, 0.4);
  box-shadow: 0 4px 16px rgba(255, 68, 68, 0.2);
}

/* Platform-specific Rating Enhancements */
.channel-card[data-platform="youtube"] .rating-display .stars {
  filter: drop-shadow(0 0 6px rgba(255, 0, 0, 0.3));
}

.channel-card[data-platform="twitch"] .rating-display .stars {
  filter: drop-shadow(0 0 6px rgba(145, 70, 255, 0.3));
}

.channel-card[data-platform="youtube"] .rate-btn:hover {
  box-shadow: 0 8px 24px rgba(255, 0, 0, 0.2);
}

.channel-card[data-platform="twitch"] .rate-btn:hover {
  box-shadow: 0 8px 24px rgba(145, 70, 255, 0.2);
}

/* Rating Modal Animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Success notification animation */
@keyframes fadeInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes starGlow {
  0%, 100% {
    filter: drop-shadow(0 0 4px rgba(255, 215, 0, 0.3));
  }
  50% {
    filter: drop-shadow(0 0 12px rgba(255, 215, 0, 0.8));
  }
}

.rating-display .stars:hover {
  animation: starGlow 2s ease-in-out infinite;
}

/* Responsive rating elements */
@media (max-width: 768px) {
  .channel-rating {
    padding: 0.75rem;
    margin-top: 0.75rem;
  }
  
  .rating-display {
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    text-align: center;
  }
  
  .rating-main {
    justify-content: center;
    width: 100%;
  }
  
  .rating-text {
    font-size: 1rem;
  }
  
  .view-reviews-inline {
    font-size: 0.75rem;
    padding: 0.35rem 0.8rem;
  }
  
  .rate-btn {
    font-size: 0.8rem;
    padding: 0.6rem 1.2rem;
    width: 100%;
    justify-content: center;
  }
  
  .rate-btn i {
    font-size: 0.9rem;
  }
  
  .no-reviews {
    font-size: 0.75rem;
    width: 100%;
  }
}

/* Enhanced hover effects for rating stars */
.rating-star-input:hover {
  transform: scale(1.2);
  text-shadow: 0 0 12px currentColor;
  transition: all 0.2s ease;
}

/* Ultra-smooth animations for rating interactions */
.channel-rating * {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Empty state styling improvements */
.rating-display:has(.no-reviews) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.rating-display:has(.no-reviews) .rating-main {
  opacity: 0.7;
}

/* Rating section spacing improvements */
.channel-rating:last-child {
  margin-bottom: 0;
}

.channel-actions + .channel-rating {
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  margin-top: 1rem;
  padding-top: 1rem;
}

/* Filter group spacing for new sort filters */
.filter-group {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.filter-group:not(:last-child) {
  margin-right: 1rem;
  padding-right: 1rem;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

/* Make sure filter buttons have consistent styling */
.filter-btn[data-sort] {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.filter-btn[data-sort].active {
  background: linear-gradient(135deg, var(--primary-gold), #d4ac0d);
  color: #0f172a;
  border-color: var(--primary-gold);
}

.filter-btn[data-sort]:hover:not(.active) {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 215, 0, 0.3);
} 
