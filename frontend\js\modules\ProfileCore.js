/**
 * ProfileCore.js - Core profile functionality
 * Handles profile UI, bio management, layout controls, and user data
 * Note: Profile notification bell was removed - only navbar notifications remain active
 */

export class ProfileCore {
  constructor() {
    this.currentLayout = null;
    this.isInitialized = false;
    this.eventListeners = [];
  }

  /**
   * Initialize the profile core functionality
   */
  async initialize() {
    console.log('🎯 Initializing ProfileCore...');
    
    try {
      // Load user data first
      await this.loadUserData();
      
      // Initialize UI components
      this.initializeUIComponents();
      
      // Setup profile management features
      this.setupBioManagement();
      this.setupContentCreatorManagement();
      
      // Update highest rank image from linked players
      await this.updateHighestRankImage();
      
      // Load and apply saved layout
      await this.loadSavedLayout();
      
      this.isInitialized = true;
      console.log('✅ ProfileCore initialization complete');
      
    } catch (error) {
      console.error('❌ ProfileCore initialization failed:', error);
    }
  }

  /**
   * Load user data from the API
   */
  async loadUserData() {
    try {
      const response = await fetch('/api/me', { credentials: 'include' });
      if (response.ok) {
        this.userData = await response.json();
        await this.loadUserProfile();
      }
    } catch (error) {
      console.error('Error loading user data:', error);
    }
  }

  /**
   * Initialize UI components
   */
  initializeUIComponents() {
    this.setupLayoutControls();
    this.setupUsernameChangeForm();
    this.initializeButtonStates();
  }

  /**
   * Load and display user profile data
   */
  async loadUserProfile() {
    if (!this.userData) return;
    
    try {
      this.updateUserProfileUI(this.userData);
      this.updateBioAndSocialLinks(this.userData);
      this.updatePointsDisplay(this.userData);
      this.updateContentCreatorSections(this.userData);
      this.loadTopAlliesAndEnemies();
    } catch (error) {
      console.error('Error loading user profile:', error);
    }
  }

  /**
   * Setup bio editing functionality
   */
  setupBioManagement() {
    this.setupBioEdit();
    this.setupInlineEditButtons();
  }

  /**
   * Setup content creator management
   */
  setupContentCreatorManagement() {
    this.setupContentCreatorEdit();
    this.setupDescriptionHandlers();
  }

  // Removed redundant avatar methods - now handled by AvatarUtils

  /**
   * Setup layout control handlers
   */
  setupLayoutControls() {
    console.log('⚙️ Setting up layout controls...');
    
    const saveLayoutBtn = document.getElementById('save-layout');
    const minimizeAllBtn = document.getElementById('minimize-all');
    const maximizeAllBtn = document.getElementById('maximize-all');
    
    if (saveLayoutBtn) {
      saveLayoutBtn.addEventListener('click', (e) => {
        e.preventDefault();
        this.saveLayout();
      });
    }
    
    if (minimizeAllBtn) {
      console.log('✅ Found minimize all button, setting up listener');
      minimizeAllBtn.addEventListener('click', (e) => {
        e.preventDefault();
        console.log('🔽 Minimize All button clicked');
        console.log('Window draggableGrid:', window.draggableGrid);
        
        // Use the draggable grid's minimize all method
        if (window.draggableGrid && window.draggableGrid.minimizeAll) {
          console.log('📞 Calling draggableGrid.minimizeAll()');
          window.draggableGrid.minimizeAll();
        } else {
          console.error('❌ draggableGrid not found or missing minimizeAll method');
        }
      });
    } else {
      console.warn('⚠️ Minimize all button not found');
    }
    
    if (maximizeAllBtn) {
      console.log('✅ Found maximize all button, setting up listener');
      maximizeAllBtn.addEventListener('click', (e) => {
        e.preventDefault();
        console.log('🔼 Maximize All button clicked');
        console.log('Window draggableGrid:', window.draggableGrid);
        
        // Use the draggable grid's maximize all method
        if (window.draggableGrid && window.draggableGrid.maximizeAll) {
          console.log('📞 Calling draggableGrid.maximizeAll()');
          window.draggableGrid.maximizeAll();
        } else {
          console.error('❌ draggableGrid not found or missing maximizeAll method');
        }
      });
    } else {
      console.warn('⚠️ Maximize all button not found');
    }
  }

  /**
   * Update user profile UI elements
   */
  updateUserProfileUI(user) {
    this.updateElement('profile-username', user.username || user.displayName || 'Unknown User');
    this.updateElement('email-text', user.email || 'No email provided');
    this.updateAvatar(user.profileImage);
  }

  /**
   * Update element text content
   */
  updateElement(elementId, value, fallback = '') {
    const element = document.getElementById(elementId);
    if (element) {
      element.textContent = value || fallback;
    }
  }

  // Removed redundant updateAvatar method - use AvatarUtils.updateAllAvatarInstances()

  /**
   * Save current layout to database
   */
  async saveLayout() {
    console.log('💾 Saving profile layout...');
    
    try {
      // Collect current layout data
      const layoutData = this.getCurrentLayoutData();
      
      // Save to database via API
      const response = await fetch('/api/users/profile/layout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(layoutData)
      });
      
      if (response.ok) {
        console.log('✅ Layout saved successfully');
        this.showLayoutMessage('Layout saved successfully!', 'success');
      } else {
        console.error('❌ Failed to save layout:', response.status);
        this.showLayoutMessage('Failed to save layout', 'error');
      }
    } catch (error) {
      console.error('❌ Error saving layout:', error);
      this.showLayoutMessage('Error saving layout', 'error');
    }
  }

  /**
   * Load saved layout from database
   */
  async loadSavedLayout() {
    console.log('📖 Loading saved profile layout...');
    
    try {
      const response = await fetch('/api/users/profile/layout', {
        method: 'GET',
        credentials: 'include'
      });
      
      if (response.ok) {
        const layoutData = await response.json();
        if (layoutData && layoutData.sections) {
          console.log('✅ Layout data found, applying...');
          this.applySavedLayout(layoutData);
        } else {
          console.log('ℹ️ No saved layout found, using default');
        }
      } else {
        console.log('ℹ️ No saved layout available');
      }
    } catch (error) {
      console.error('❌ Error loading layout:', error);
    }
  }

  /**
   * Collect current layout data from the grid
   */
  getCurrentLayoutData() {
    const sections = document.querySelectorAll('.draggable-section');
    const layoutData = {
      sections: [],
      timestamp: new Date().toISOString()
    };
    
    sections.forEach(section => {
      const sectionData = {
        id: section.id,
        sectionType: section.dataset.section,
        position: parseInt(section.dataset.position) || 0,
        isMinimized: section.classList.contains('minimized'),
        gridColumn: section.style.gridColumn || '',
        gridRow: section.style.gridRow || ''
      };
      
      layoutData.sections.push(sectionData);
      console.log(`📊 Captured section: ${sectionData.id}, pos: ${sectionData.position}, minimized: ${sectionData.isMinimized}`);
    });
    
    // Sort by position to maintain order
    layoutData.sections.sort((a, b) => a.position - b.position);
    
    console.log('📊 Layout data collected:', layoutData);
    return layoutData;
  }

  /**
   * Apply saved layout data to the grid
   */
  applySavedLayout(layoutData) {
    console.log('🎨 Applying saved layout data...');
    
    try {
      layoutData.sections.forEach(sectionData => {
        const section = document.getElementById(sectionData.id);
        if (section) {
          // Apply position
          section.dataset.position = sectionData.position;
          
          // Apply grid positioning if available
          if (sectionData.gridColumn) {
            section.style.gridColumn = sectionData.gridColumn;
          }
          if (sectionData.gridRow) {
            section.style.gridRow = sectionData.gridRow;
          }
          
          // Apply minimize/maximize state
          const btn = section.querySelector('.minimize-toggle');
          const content = section.querySelector('.section-content');
          const btnText = btn?.querySelector('.btn-text');
          const indicator = section.querySelector('.grid-position-indicator');
          
          if (sectionData.isMinimized) {
            // Set to minimized state
            section.classList.add('minimized');
            if (content) content.style.display = 'none';
            if (btnText) btnText.textContent = 'Restore';
            if (btn) btn.title = 'Show section content';
            if (indicator) {
              indicator.textContent = indicator.textContent.replace(' (Min)', '') + ' (Min)';
            }
          } else {
            // Set to maximized state
            section.classList.remove('minimized');
            if (content) content.style.display = 'block';
            if (btnText) btnText.textContent = 'Minimize';
            if (btn) btn.title = 'Hide section content';
            if (indicator) {
              indicator.textContent = indicator.textContent.replace(' (Min)', '');
            }
          }
          
          console.log(`🎨 Applied layout to ${sectionData.id}: pos ${sectionData.position}, minimized: ${sectionData.isMinimized}`);
        } else {
          console.warn(`⚠️ Section not found: ${sectionData.id}`);
        }
      });
      
      // Trigger a reflow to ensure proper positioning
      if (window.draggableGrid && window.draggableGrid.reflowSections) {
        window.draggableGrid.reflowSections();
      }
      
      console.log('✅ Saved layout applied successfully');
    } catch (error) {
      console.error('❌ Error applying saved layout:', error);
    }
  }

  /**
   * Show layout save/load message to user
   */
  showLayoutMessage(message, type = 'info') {
    // Create or update message element
    let messageEl = document.getElementById('layout-message');
    if (!messageEl) {
      messageEl = document.createElement('div');
      messageEl.id = 'layout-message';
      messageEl.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        color: white;
        font-weight: 500;
        z-index: 1000;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
      `;
      document.body.appendChild(messageEl);
    }
    
    // Set message and style based on type
    messageEl.textContent = message;
    messageEl.style.background = type === 'success' ? 'rgba(34, 197, 94, 0.9)' : 
                                 type === 'error' ? 'rgba(239, 68, 68, 0.9)' : 
                                 'rgba(59, 130, 246, 0.9)';
    
    // Show message
    requestAnimationFrame(() => {
      messageEl.style.opacity = '1';
      messageEl.style.transform = 'translateX(0)';
    });
    
    // Hide message after 3 seconds
    setTimeout(() => {
      messageEl.style.opacity = '0';
      messageEl.style.transform = 'translateX(100%)';
    }, 3000);
  }
} 