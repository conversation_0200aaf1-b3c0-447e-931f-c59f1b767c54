#!/usr/bin/env pwsh

Write-Host "🔄 Restarting development server..." -ForegroundColor Blue

# Stop existing Node.js processes
$nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
if ($nodeProcesses) {
    Write-Host "⏹️ Stopping existing Node.js processes..." -ForegroundColor Yellow
    $nodeProcesses | Stop-Process -Force
    Start-Sleep -Seconds 3
}

# Check if port 3000 is still in use
$portInUse = netstat -ano | findstr :3000
if ($portInUse) {
    Write-Host "⚠️ Port 3000 still in use, waiting for cleanup..." -ForegroundColor Yellow
    Start-Sleep -Seconds 2
}

# Change to project directory
$projectRoot = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $projectRoot

# Start the server
Write-Host "🚀 Starting the development server..." -ForegroundColor Green
npm start 