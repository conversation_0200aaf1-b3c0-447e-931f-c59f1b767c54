#!/usr/bin/env python3
"""
Create strategic overlays using real War3 thumbnails with comprehensive strategic data
Features:
- Proper aspect ratio preservation for non-square maps
- Advanced two-phase creep camp clustering algorithm
- Starting positions from both JASS and DOO sources
- Drop tables and shop inventories visualization
- NPC structure markers
- Realistic camp difficulty distribution
- Uses actual War3 icons from overlay_icons directory
"""

import os
import sys
import math
import random
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
from collections import defaultdict
from pymongo import MongoClient
import numpy as np

# War3 creep difficulty thresholds (based on total camp level/power)
CREEP_DIFFICULTY_THRESHOLDS = {
    'easy': (1, 4),      # Level 1-4 camps
    'medium': (5, 8),    # Level 5-8 camps  
    'hard': (9, 15)      # Level 9+ camps
}

# Advanced clustering parameters
TIGHT_DISTANCE = 1024      # Phase 1: Very close creeps (same camp core)
MAX_CAMP_SIZE = 6          # Maximum realistic camp size
MIN_CAMP_SIZE = 2          # Minimum creeps to form a camp
SOLO_CREEP_LEVEL = 6       # Solo creeps need level 6+ to be own camp

# Icon paths - using your overlay_icons directory
ICON_DIR = "uploads/war3images/overlay_icons"
ICON_PATHS = {
    'goldmine': f"{ICON_DIR}/goldmine.png",
    'creep_easy': f"{ICON_DIR}/creep_easy.png", 
    'creep_medium': f"{ICON_DIR}/creep_medium.png",
    'creep_hard': f"{ICON_DIR}/creep_hard.png",
    'starting_position': f"{ICON_DIR}/starting_position.png",
    'npc_structure': f"{ICON_DIR}/npc_structure.png",
    'drop_table': f"{ICON_DIR}/drop_table.png",
    'shop': f"{ICON_DIR}/shop.png"
}

def load_icons():
    """Load War3 icons from overlay_icons directory"""
    icons = {}
    print("🎨 Loading War3 icons...")
    
    for icon_name, icon_path in ICON_PATHS.items():
        try:
            if os.path.exists(icon_path):
                icons[icon_name] = Image.open(icon_path).convert("RGBA")
                print(f"   ✅ Loaded icon: {icon_name}")
            else:
                raise FileNotFoundError(f"Required icon not found: {icon_path}")
        except Exception as e:
            print(f"   ❌ Error loading {icon_name}: {e}")
            raise
    
    return icons

def calculate_camp_difficulty(camp):
    """Calculate camp difficulty based on total level/power with more realistic thresholds"""
    if not camp:
        return 'easy'
    
    # Unit level database (same as in parser)
    UNIT_LEVELS = {
        # Basic creeps (Level 1-2)
        'ngnw': 1, 'nkob': 1, 'nfsh': 1, 'nska': 1, 'nwsp': 1, 'nfro': 1,
        'ngnb': 2, 'nkog': 2, 'nwlf': 2, 'ncrb': 2, 'nfps': 2, 'nftr': 2,
        
        # Medium creeps (Level 3-4)
        'nkot': 3, 'ngnv': 3, 'nwzg': 3, 'nfrl': 3, 'nftb': 3, 'nftt': 3,
        'nkol': 4, 'nwzd': 4, 'nftk': 4, 'nfrs': 4, 'nfra': 4, 'nftc': 4,
        
        # Strong creeps (Level 5-6)
        'nkog': 5, 'nwwd': 5, 'nfgb': 5, 'nfsp': 5, 'nfsh': 5, 'nftr': 5,
        'nkot': 6, 'nwzg': 6, 'nfrl': 6, 'nftb': 6, 'nftt': 6, 'nfrs': 6,
        
        # Elite creeps (Level 7+)
        'nkol': 7, 'nwzd': 7, 'nftk': 7, 'nfra': 7, 'nftc': 7, 'nfgb': 7,
        'nwwd': 8, 'nfsp': 8, 'nfsh': 8, 'nftr': 8, 'nkog': 8, 'nwzg': 8,
        'nfrl': 9, 'nftb': 9, 'nftt': 9, 'nfrs': 9, 'nkot': 9, 'nwzd': 9,
        'nftk': 10, 'nfra': 10, 'nftc': 10, 'nfgb': 10, 'nwwd': 10, 'nfsp': 10
    }
    
    total_level = 0
    for creep in camp:
        unit_id = creep.get('unit_id', '')
        level = UNIT_LEVELS.get(unit_id, 2)  # Default level 2 for unknown units
        total_level += level
    
    # Calculate average level for the camp
    avg_level = total_level / len(camp) if len(camp) > 0 else 0
    
    # More conservative difficulty assessment
    if avg_level <= 2.5:
        return 'easy'
    elif avg_level <= 4.0:
        return 'medium'
    else:
        return 'hard'

def group_creeps_into_camps(creeps):
    """Group nearby creeps into realistic camps using two-phase clustering"""
    if not creeps:
        return []
    
    # Phase 1: Tight initial clustering
    camps = []
    used_creeps = set()
    
    for i, creep in enumerate(creeps):
        if i in used_creeps:
            continue
            
        # Start new camp with this creep
        camp = [creep]
        used_creeps.add(i)
        
        # Find nearby creeps for this camp
        for j, other_creep in enumerate(creeps):
            if j in used_creeps or len(camp) >= MAX_CAMP_SIZE:
                continue
                
            distance = math.sqrt(
                (creep['x'] - other_creep['x'])**2 + 
                (creep['y'] - other_creep['y'])**2
            )
            
            if distance <= TIGHT_DISTANCE:
                camp.append(other_creep)
                used_creeps.add(j)
        
        # Only add camps that meet minimum size or are high-level solo creeps
        if len(camp) >= MIN_CAMP_SIZE:
            camps.append(camp)
        elif len(camp) == 1:
            # Check if solo creep is high level enough
            unit_id = camp[0].get('unit_id', '')
            UNIT_LEVELS = {
                'ngnw': 1, 'nkob': 1, 'nfsh': 1, 'nska': 1, 'nwsp': 1, 'nfro': 1,
                'ngnb': 2, 'nkog': 2, 'nwlf': 2, 'ncrb': 2, 'nfps': 2, 'nftr': 2,
                'nkot': 3, 'ngnv': 3, 'nwzg': 3, 'nfrl': 3, 'nftb': 3, 'nftt': 3,
                'nkol': 4, 'nwzd': 4, 'nftk': 4, 'nfrs': 4, 'nfra': 4, 'nftc': 4,
            }
            level = UNIT_LEVELS.get(unit_id, 2)
            if level >= SOLO_CREEP_LEVEL:
                camps.append(camp)
    
    return camps

def resize_with_aspect_ratio(image, base_size=512):
    """Resize image while preserving aspect ratio"""
    width, height = image.size
    
    if width == height:
        return image.resize((base_size, base_size), Image.Resampling.LANCZOS)
    
    # Calculate proportional dimensions
    if width > height:
        new_width = base_size
        new_height = int((height * base_size) / width)
    else:
        new_height = base_size
        new_width = int((width * base_size) / height)
    
    return image.resize((new_width, new_height), Image.Resampling.LANCZOS)

def create_strategic_overlay(map_data, icons):
    """Create strategic overlay with War3 icons"""
    map_name = map_data.get('name', 'Unknown Map')
    print(f"🎨 Creating strategic overlay for: {map_name}")
    
    # Load and resize thumbnail - thumbnails are directly in war3images folder
    filename = map_data.get('filename', '')
    thumbnail_path = f"uploads/war3images/{filename.replace('.w3x', '.png')}"
    
    if not os.path.exists(thumbnail_path):
        print(f"   📸 No thumbnail found: {thumbnail_path}")
        return None
    
    try:
        thumbnail = Image.open(thumbnail_path).convert("RGBA")
        print(f"   📸 Loaded thumbnail: {thumbnail.size}")
        
        # Resize with proper aspect ratio
        overlay = resize_with_aspect_ratio(thumbnail)
        print(f"   📐 Resized to: {overlay.size}")
        
        # Get map dimensions for coordinate scaling
        map_width = map_data.get('mapWidth', 256)
        map_height = map_data.get('mapHeight', 256)
        print(f"   📏 Map dimensions: {map_width}x{map_height}")
        
        draw = ImageDraw.Draw(overlay)
        overlay_width, overlay_height = overlay.size
        
        # Get strategic data from both JASS and DOO sources
        strategic_data = map_data.get('strategicData', {})
        
        # Combine goldmines from both sources
        jass_goldmines = strategic_data.get('jassGoldmines', [])
        doo_goldmines = strategic_data.get('dooGoldmines', [])
        all_goldmines = jass_goldmines + doo_goldmines
        
        # Remove duplicates based on coordinates (within 100 units)
        unique_goldmines = []
        for gm in all_goldmines:
            is_duplicate = False
            for existing in unique_goldmines:
                if (abs(gm.get('x', 0) - existing.get('x', 0)) < 100 and 
                    abs(gm.get('y', 0) - existing.get('y', 0)) < 100):
                    is_duplicate = True
                    break
            if not is_duplicate:
                unique_goldmines.append(gm)
        
        # Combine creeps from both sources
        jass_creeps = strategic_data.get('jassCreepUnits', [])
        doo_creeps = strategic_data.get('dooCreepUnits', [])
        all_creeps = jass_creeps + doo_creeps
        
        # Remove duplicate creeps
        unique_creeps = []
        for creep in all_creeps:
            is_duplicate = False
            for existing in unique_creeps:
                if (abs(creep.get('x', 0) - existing.get('x', 0)) < 50 and 
                    abs(creep.get('y', 0) - existing.get('y', 0)) < 50):
                    is_duplicate = True
                    break
            if not is_duplicate:
                unique_creeps.append(creep)
        
        print(f"      Using combined goldmines: {len(unique_goldmines)}")
        print(f"      Using combined creeps: {len(unique_creeps)}")
        
        # Draw goldmines with values
        print(f"   💰 Drawing {len(unique_goldmines)} goldmines with values")
        for goldmine in unique_goldmines:
            x = goldmine.get('x', 0)
            y = goldmine.get('y', 0)
            gold_amount = goldmine.get('gold', 12500)  # Default War3 goldmine amount
            
            # Convert War3 coordinates to overlay coordinates
            overlay_x = int((x + map_width * 16) / (map_width * 32) * overlay_width)
            overlay_y = int((map_height * 16 - y) / (map_height * 32) * overlay_height)
            
            # Ensure coordinates are within bounds
            overlay_x = max(16, min(overlay_width - 16, overlay_x))
            overlay_y = max(16, min(overlay_height - 16, overlay_y))
            
            # Draw goldmine icon
            if 'goldmine' in icons:
                icon = icons['goldmine'].resize((32, 32), Image.Resampling.LANCZOS)
                overlay.paste(icon, (overlay_x - 16, overlay_y - 16), icon)
                
                # Add gold value text below the icon
                try:
                    font = ImageFont.truetype("arial.ttf", 10)
                except:
                    font = ImageFont.load_default()
                
                gold_text = f"{gold_amount//1000}k" if gold_amount >= 1000 else str(gold_amount)
                text_bbox = draw.textbbox((0, 0), gold_text, font=font)
                text_width = text_bbox[2] - text_bbox[0]
                text_height = text_bbox[3] - text_bbox[1]
                
                # Draw text background
                text_x = overlay_x - text_width // 2
                text_y = overlay_y + 18
                draw.rectangle([text_x - 2, text_y - 1, text_x + text_width + 2, text_y + text_height + 1], 
                             fill=(0, 0, 0, 180))
                draw.text((text_x, text_y), gold_text, fill=(255, 215, 0, 255), font=font)
        
        # Group creeps into camps and draw them
        camps = group_creeps_into_camps(unique_creeps)
        print(f"   👹 Grouped {len(unique_creeps)} creeps into {len(camps)} camps")
        
        # Count camps by difficulty
        easy_camps = medium_camps = hard_camps = 0
        
        for camp in camps:
            if not camp:
                continue
                
            # Calculate camp center
            center_x = sum(creep.get('x', 0) for creep in camp) / len(camp)
            center_y = sum(creep.get('y', 0) for creep in camp) / len(camp)
            
            # Convert to overlay coordinates
            overlay_x = int((center_x + map_width * 16) / (map_width * 32) * overlay_width)
            overlay_y = int((map_height * 16 - center_y) / (map_height * 32) * overlay_height)
            
            # Ensure coordinates are within bounds
            overlay_x = max(16, min(overlay_width - 16, overlay_x))
            overlay_y = max(16, min(overlay_height - 16, overlay_y))
            
            # Determine camp difficulty with enhanced visualization
            difficulty = calculate_camp_difficulty(camp)
            camp_size = len(camp)
            
            # Count by difficulty
            if difficulty == 'easy':
                easy_camps += 1
                icon_key = 'creep_easy'
                border_color = (0, 255, 0, 255)  # Green border
            elif difficulty == 'medium':
                medium_camps += 1
                icon_key = 'creep_medium'
                border_color = (255, 165, 0, 255)  # Orange border
            else:
                hard_camps += 1
                icon_key = 'creep_hard'
                border_color = (255, 0, 0, 255)  # Red border
            
            # Draw camp icon with enhanced border
            if icon_key in icons:
                icon = icons[icon_key].resize((24, 24), Image.Resampling.LANCZOS)
                
                # Draw colored border around camp
                border_size = 28
                draw.ellipse([overlay_x - border_size//2, overlay_y - border_size//2, 
                             overlay_x + border_size//2, overlay_y + border_size//2], 
                            outline=border_color, width=2)
                
                overlay.paste(icon, (overlay_x - 12, overlay_y - 12), icon)
                
                # Add camp size indicator
                if camp_size > 1:
                    try:
                        font = ImageFont.truetype("arial.ttf", 8)
                    except:
                        font = ImageFont.load_default()
                    
                    size_text = str(camp_size)
                    text_bbox = draw.textbbox((0, 0), size_text, font=font)
                    text_width = text_bbox[2] - text_bbox[0]
                    text_height = text_bbox[3] - text_bbox[1]
                    
                    # Draw size indicator in top-right corner of icon
                    size_x = overlay_x + 8
                    size_y = overlay_y - 12
                    draw.ellipse([size_x - 6, size_y - 6, size_x + 6, size_y + 6], 
                               fill=(0, 0, 0, 200), outline=(255, 255, 255, 255))
                    draw.text((size_x - text_width//2, size_y - text_height//2), 
                             size_text, fill=(255, 255, 255, 255), font=font)
        
        print(f"      🟢 Easy camps: {easy_camps}")
        print(f"      🟡 Medium camps: {medium_camps}")
        print(f"      🔴 Hard camps: {hard_camps}")
        
        # Draw starting positions
        jass_starting = strategic_data.get('jassStartingPositions', [])
        doo_starting = strategic_data.get('dooStartingPositions', [])
        all_starting = jass_starting + doo_starting
        
        # Remove duplicates
        unique_starting = []
        for start in all_starting:
            is_duplicate = False
            for existing in unique_starting:
                if (abs(start.get('x', 0) - existing.get('x', 0)) < 100 and 
                    abs(start.get('y', 0) - existing.get('y', 0)) < 100):
                    is_duplicate = True
                    break
            if not is_duplicate:
                unique_starting.append(start)
        
        print(f"   🏠 Drawing {len(unique_starting)} starting positions")
        for start_pos in unique_starting:
            x = start_pos.get('x', 0)
            y = start_pos.get('y', 0)
            
            overlay_x = int((x + map_width * 16) / (map_width * 32) * overlay_width)
            overlay_y = int((map_height * 16 - y) / (map_height * 32) * overlay_height)
            
            overlay_x = max(20, min(overlay_width - 20, overlay_x))
            overlay_y = max(20, min(overlay_height - 20, overlay_y))
            
            if 'starting_position' in icons:
                icon = icons['starting_position'].resize((40, 40), Image.Resampling.LANCZOS)
                overlay.paste(icon, (overlay_x - 20, overlay_y - 20), icon)
        
        # Draw drop tables and shop inventories
        drop_tables = strategic_data.get('dropTables', []) + strategic_data.get('inferredDropTables', [])
        shop_inventories = strategic_data.get('shopInventories', [])
        
        if drop_tables:
            print(f"   💎 Drawing {len(drop_tables)} drop table locations")
            for drop_table in drop_tables:
                x = drop_table.get('x', 0)
                y = drop_table.get('y', 0)
                
                # Convert to overlay coordinates
                overlay_x = int((x + map_width * 16) / (map_width * 32) * overlay_width)
                overlay_y = int((map_height * 16 - y) / (map_height * 32) * overlay_height)
                
                # Ensure coordinates are within bounds
                overlay_x = max(12, min(overlay_width - 12, overlay_x))
                overlay_y = max(12, min(overlay_height - 12, overlay_y))
                
                # Draw drop table icon
                if 'drop_table' in icons:
                    icon = icons['drop_table'].resize((20, 20), Image.Resampling.LANCZOS)
                    overlay.paste(icon, (overlay_x - 10, overlay_y - 10), icon)
        
        if shop_inventories:
            print(f"   🛒 Drawing {len(shop_inventories)} shop locations")
            for shop in shop_inventories:
                x = shop.get('x', 0)
                y = shop.get('y', 0)
                
                # Convert to overlay coordinates
                overlay_x = int((x + map_width * 16) / (map_width * 32) * overlay_width)
                overlay_y = int((map_height * 16 - y) / (map_height * 32) * overlay_height)
                
                # Ensure coordinates are within bounds
                overlay_x = max(12, min(overlay_width - 12, overlay_x))
                overlay_y = max(12, min(overlay_height - 12, overlay_y))
                
                # Draw shop icon
                if 'shop' in icons:
                    icon = icons['shop'].resize((22, 22), Image.Resampling.LANCZOS)
                    overlay.paste(icon, (overlay_x - 11, overlay_y - 11), icon)
        
        # Draw NPC structures
        npc_structures = strategic_data.get('dooNeutralStructures', []) + strategic_data.get('jassNeutralStructures', [])
        if npc_structures:
            print(f"   🏛️ Drawing {len(npc_structures)} NPC structures")
            for structure in npc_structures:
                x = structure.get('x', 0)
                y = structure.get('y', 0)
                
                # Convert to overlay coordinates
                overlay_x = int((x + map_width * 16) / (map_width * 32) * overlay_width)
                overlay_y = int((map_height * 16 - y) / (map_height * 32) * overlay_height)
                
                # Ensure coordinates are within bounds
                overlay_x = max(12, min(overlay_width - 12, overlay_x))
                overlay_y = max(12, min(overlay_height - 12, overlay_y))
                
                # Draw NPC structure icon
                if 'npc_structure' in icons:
                    icon = icons['npc_structure'].resize((20, 20), Image.Resampling.LANCZOS)
                    overlay.paste(icon, (overlay_x - 10, overlay_y - 10), icon)
        
        return overlay
        
    except Exception as e:
        print(f"   ❌ Error creating overlay: {e}")
        return None

def main():
    """Main function to create strategic overlays with War3 icons"""
    print("🎮 Creating Strategic Overlays with Real Thumbnails + War3 Icons")
    
    # Load icons first
    icons = load_icons()
    
    if not icons:
        print("❌ No icons loaded! Please check your overlay_icons directory.")
        return
    
    # Connect to MongoDB
    try:
        client = MongoClient('mongodb://localhost:27017/')
        db = client['newsite']
        collection = db['war3maps']
        print("✅ Connected to MongoDB")
    except Exception as e:
        print(f"❌ Failed to connect to MongoDB: {e}")
        return
    
    # Create output directory
    output_dir = "uploads/war3images/strategic_overlays_with_thumbnails"
    os.makedirs(output_dir, exist_ok=True)
    
    # Get all maps from database
    maps = list(collection.find({}))
    print(f"📊 Found {len(maps)} maps in database")
    
    created_count = 0
    skipped_count = 0
    no_thumbnail_count = 0
    no_data_count = 0
    
    for i, map_data in enumerate(maps, 1):
        map_name = map_data.get('name', 'Unknown Map')
        filename = map_data.get('filename', '')
        
        # Create safe filename for overlay
        safe_name = "".join(c for c in map_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_name = safe_name.replace(' ', '_')
        overlay_filename = f"{safe_name}_strategic_overlay.png"
        overlay_path = os.path.join(output_dir, overlay_filename)
        
        print(f"🗺️  [{i}/{len(maps)}] Processing: {map_name}")
        
        # Skip if overlay already exists
        if os.path.exists(overlay_path):
            print(f"🔄 Progress: {created_count + skipped_count}/{len(maps)} - Skipping existing: {map_name}")
            skipped_count += 1
            continue
        
        # Create overlay
        overlay = create_strategic_overlay(map_data, icons)
        
        if overlay:
            try:
                overlay.save(overlay_path, "PNG")
                print(f"   ✅ Saved overlay: {overlay_path}")
                created_count += 1
            except Exception as e:
                print(f"   ❌ Failed to save overlay: {e}")
        else:
            # Check why overlay creation failed
            thumbnail_path = f"uploads/war3images/thumbnails/{filename.replace('.w3x', '.png')}"
            if not os.path.exists(thumbnail_path):
                no_thumbnail_count += 1
            else:
                strategic_data = map_data.get('strategicData', {})
                if not any([
                    strategic_data.get('jassGoldmines', []),
                    strategic_data.get('dooGoldmines', []),
                    strategic_data.get('jassCreepUnits', []),
                    strategic_data.get('dooCreepUnits', [])
                ]):
                    no_data_count += 1
        
        # Progress update every 10 maps
        if i % 10 == 0:
            print(f"📊 Progress: {created_count} overlays created, {skipped_count} skipped")
    
    print("🎉 Strategic overlay creation complete!")
    print(f"✅ Successfully created: {created_count}/{len(maps)} overlays")
    print(f"⏭️  Skipped existing: {skipped_count} overlays")
    print(f"📸 No thumbnail: {no_thumbnail_count} maps")
    print(f"📊 No strategic data: {no_data_count} maps")
    print(f"📁 Overlays saved to: {output_dir}")

if __name__ == "__main__":
    main() 