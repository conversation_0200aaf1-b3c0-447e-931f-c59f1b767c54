/* =============================================================================
   CHARTS COMPONENT STYLES
   ============================================================================= */

.chart-container {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  box-shadow: var(--glass-shadow);
  margin-bottom: var(--space-6);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--glass-border);
}

.chart-title {
  color: var(--warcraft-gold);
  font-family: var(--font-display);
  font-size: var(--text-xl);
  font-weight: 600;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.chart-filters {
  display: flex;
  gap: var(--space-2);
  align-items: center;
}

.chart-filter {
  padding: var(--space-1) var(--space-3);
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-sm);
  color: var(--neutral-300);
  font-size: var(--text-xs);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.chart-filter:hover,
.chart-filter.active {
  background: var(--warcraft-gold);
  color: var(--neutral-900);
  border-color: var(--warcraft-gold);
}

.chart-canvas {
  position: relative;
  height: 400px;
  width: 100%;
}

.chart-canvas canvas {
  border-radius: var(--radius-md);
}

/* Chart loading state */
.chart-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: var(--neutral-400);
}

.chart-loading .spinner {
  width: 50px;
  height: 50px;
  border: 4px solid var(--glass-border);
  border-top: 4px solid var(--warcraft-gold);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

.chart-loading-text {
  font-size: var(--text-sm);
  font-weight: 500;
}

/* Chart empty state */
.chart-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: var(--neutral-400);
  text-align: center;
}

.chart-empty i {
  font-size: var(--text-4xl);
  margin-bottom: var(--space-4);
  opacity: 0.5;
}

.chart-empty-title {
  font-size: var(--text-lg);
  font-weight: 600;
  margin-bottom: var(--space-2);
}

.chart-empty-message {
  font-size: var(--text-sm);
  opacity: 0.8;
}

/* Chart legend customization */
.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-4);
  justify-content: center;
  margin-top: var(--space-4);
  padding-top: var(--space-4);
  border-top: 1px solid var(--glass-border);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
  color: var(--neutral-300);
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: var(--radius-sm);
  border: 1px solid var(--glass-border);
}

/* Stats cards for charts */
.chart-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.stat-card {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-md);
  padding: var(--space-4);
  text-align: center;
  transition: all var(--transition-normal);
}

.stat-card:hover {
  background: var(--glass-border);
  transform: translateY(-2px);
  box-shadow: var(--shadow-glow);
}

.stat-value {
  font-family: var(--font-display);
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--warcraft-gold);
  margin-bottom: var(--space-1);
  line-height: 1;
}

.stat-label {
  font-size: var(--text-xs);
  color: var(--neutral-400);
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
}

.stat-change {
  font-size: var(--text-xs);
  font-weight: 600;
  margin-top: var(--space-1);
}

.stat-change.positive {
  color: var(--success);
}

.stat-change.negative {
  color: var(--error);
}

.stat-change.neutral {
  color: var(--neutral-400);
}

/* Chart types specific styling */
.doughnut-chart {
  max-width: 300px;
  margin: 0 auto;
}

.bar-chart {
  height: 350px;
}

.line-chart {
  height: 300px;
}

.radar-chart {
  max-width: 400px;
  margin: 0 auto;
}

/* Race distribution chart specific */
.race-chart-container {
  display: flex;
  align-items: center;
  gap: var(--space-6);
}

.race-chart {
  flex: 1;
  max-width: 300px;
}

.race-stats {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.race-stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-2) var(--space-3);
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.race-stat-item:hover {
  background: var(--glass-border);
}

.race-name {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-weight: 600;
  color: var(--neutral-100);
}

.race-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 1px solid var(--glass-border);
}

.race-percentage {
  font-family: var(--font-mono);
  font-weight: 700;
  color: var(--warcraft-gold);
}

/* Win rate trend chart */
.winrate-trend {
  position: relative;
  height: 250px;
}

.trend-indicator {
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 600;
}

.trend-up {
  color: var(--success);
}

.trend-down {
  color: var(--error);
}

.trend-stable {
  color: var(--neutral-400);
}

/* Responsive chart layouts */
@media (max-width: 768px) {
  .chart-container {
    padding: var(--space-4);
  }
  
  .chart-header {
    flex-direction: column;
    gap: var(--space-3);
    align-items: flex-start;
  }
  
  .chart-filters {
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .chart-canvas {
    height: 300px;
  }
  
  .chart-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-3);
  }
  
  .race-chart-container {
    flex-direction: column;
    text-align: center;
  }
  
  .chart-legend {
    gap: var(--space-2);
  }
  
  .legend-item {
    font-size: var(--text-xs);
  }
}

@media (max-width: 480px) {
  .chart-stats {
    grid-template-columns: 1fr;
  }
  
  .chart-canvas {
    height: 250px;
  }
  
  .stat-card {
    padding: var(--space-3);
  }
  
  .stat-value {
    font-size: var(--text-xl);
  }
  
  .chart-title {
    font-size: var(--text-lg);
  }
}

/* Chart animations */
@keyframes chartFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.chart-container.loaded {
  animation: chartFadeIn 0.5s ease-out;
}

/* Chart tooltips - overrides for Chart.js */
.chartjs-tooltip {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-md);
  backdrop-filter: blur(20px);
  box-shadow: var(--glass-shadow);
  color: var(--neutral-100);
  font-family: var(--font-primary);
  font-size: var(--text-sm);
}

.chartjs-tooltip-key {
  background: var(--warcraft-gold);
  border-radius: var(--radius-sm);
} 
