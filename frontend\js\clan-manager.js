/**
 * CLAN MANAGEMENT SYSTEM
 * Handles clan operations, rendering, and interactions
 * Extracted from myprofile.html for better maintainability
 */

class ClanManager {
  constructor() {
    this.currentClan = null;
    this.availableClans = [];
    this.init();
  }

  init() {
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.initialize());
    } else {
      this.initialize();
    }
  }

  async initialize() {
    try {
      await this.loadUserClan();
      console.log('✅ Clan management initialized');
    } catch (error) {
      console.error('❌ Error initializing clan management:', error);
    }
  }

  /**
   * Load user's current clan
   */
  async loadUserClan() {
    try {
      const response = await fetch('/api/clans/user', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (response.ok) {
        const clans = await response.json();
        this.currentClan = clans && clans.length > 0 ? clans[0] : null;
        this.renderClanSection(this.currentClan);
      } else if (response.status === 404 || response.status === 401) {
        // User not in any clan or not authenticated
        this.currentClan = null;
        this.renderClanSection(null);
      } else {
        console.error('Failed to load user clan:', response.status);
        this.renderClanSection(null);
      }
    } catch (error) {
      console.error('Error loading user clan:', error);
      this.renderClanSection(null);
    }
  }

  /**
   * Render the clan section based on user's clan status
   */
  renderClanSection(clan) {
    const container = document.getElementById('clan-container');
    if (!container) return;

    if (clan) {
      // User is in a clan
      container.innerHTML = `
        <div class="clan-info-card">
          <div class="clan-header">
            <div class="clan-logo-container">
              <div class="clan-logo">
                <i class="fas fa-shield-alt"></i>
              </div>
              <div class="clan-tag">[${clan.tag}]</div>
            </div>
            <div class="clan-basic-info">
              <h3 class="clan-name">${clan.name}</h3>
              <p class="clan-role">${this.getUserRoleInClan(clan)}</p>
              <p class="clan-game">
                <i class="fas fa-gamepad"></i>
                ${this.getGameDisplayName(clan.gameType)}
              </p>
            </div>
          </div>
          
          ${clan.description ? `
            <div class="clan-description">
              <p>${clan.description}</p>
            </div>
          ` : ''}
          
          <div class="clan-stats">
            <div class="clan-stat">
              <div class="clan-stat-value">${clan.members ? clan.members.length : 0}</div>
              <div class="clan-stat-label">Members</div>
            </div>
            <div class="clan-stat">
              <div class="clan-stat-value">${clan.level || 1}</div>
              <div class="clan-stat-label">Level</div>
            </div>
            <div class="clan-stat">
              <div class="clan-stat-value">${clan.rating || 1500}</div>
              <div class="clan-stat-label">Rating</div>
            </div>
            <div class="clan-stat">
              <div class="clan-stat-value">${clan.wins || 0}/${clan.losses || 0}</div>
              <div class="clan-stat-label">W/L</div>
            </div>
          </div>
          
          <div class="clan-actions">
            <button class="epic-btn" onclick="window.clanManager.viewClanDetails('${clan._id}')">
              <i class="fas fa-eye"></i>
              View Details
            </button>
            ${this.isUserClanLeader(clan) ? `
              <button class="epic-btn" onclick="window.clanManager.manageClan('${clan._id}')">
                <i class="fas fa-cog"></i>
                Manage
              </button>
            ` : ''}
            <button class="edit-btn" onclick="window.clanManager.leaveClan('${clan._id}')">
              <i class="fas fa-sign-out-alt"></i>
              Leave Clan
            </button>
          </div>
        </div>
      `;
    } else {
      // User is not in a clan
      container.innerHTML = `
        <div class="no-clan-message">
          <i class="fas fa-shield-alt"></i>
          <h4>No Clan</h4>
          <p>You're not currently a member of any clan. Join or create one to connect with other players!</p>
          
          <div class="clan-actions">
            <button class="epic-btn" onclick="window.clanManager.showCreateClanModal()">
              <i class="fas fa-plus"></i>
              Create Clan
            </button>
            <button class="epic-btn" onclick="window.clanManager.showFindClansModal()">
              <i class="fas fa-search"></i>
              Find Clans
            </button>
          </div>
        </div>
      `;
    }
  }

  /**
   * Get user's role in the clan
   */
  getUserRoleInClan(clan) {
    if (!clan || !clan.members) return 'Member';
    
    const currentUserId = this.getCurrentUserId();
    if (!currentUserId) return 'Member';
    
    const member = clan.members.find(m => m.user === currentUserId || m.user._id === currentUserId);
    if (!member) return 'Member';
    
    return member.role.charAt(0).toUpperCase() + member.role.slice(1);
  }

  /**
   * Check if user is clan leader
   */
  isUserClanLeader(clan) {
    if (!clan) return false;
    const currentUserId = this.getCurrentUserId();
    return clan.leader === currentUserId || clan.leader._id === currentUserId;
  }

  /**
   * Get current user ID
   */
  getCurrentUserId() {
    return window.currentUser?.id || window.currentUser?._id;
  }

  /**
   * Get display name for game type
   */
  getGameDisplayName(gameType) {
    const gameNames = {
      'warcraft1': 'Warcraft: Orcs & Humans',
      'warcraft2': 'Warcraft II',
      'warcraft3': 'Warcraft III',
      'wc12': 'Warcraft 1 & 2',
      'wc3': 'Warcraft 3'
    };
    return gameNames[gameType] || gameType;
  }

  /**
   * Show create clan modal
   */
  async showCreateClanModal() {
    const modal = document.getElementById('create-clan-modal');
    if (modal) {
      modal.style.display = 'block';
      
      // Load user's players for the clan creation
      try {
        const players = await this.getUserPlayers();
        console.log('Available players for clan creation:', players);
      } catch (error) {
        console.error('Error loading players:', error);
      }
    }
  }

  /**
   * Show find clans modal
   */
  async showFindClansModal() {
    const modal = document.getElementById('find-clans-modal');
    if (modal) {
      modal.style.display = 'block';
      await this.loadAvailableClans();
    }
  }

  /**
   * Load available clans to join
   */
  async loadAvailableClans() {
    try {
      const response = await fetch('/api/clans?status=active&limit=20', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (response.ok) {
        const clans = await response.json();
        this.availableClans = clans;
        this.renderAvailableClans(clans);
      } else {
        console.error('Failed to load clans:', response.status);
      }
    } catch (error) {
      console.error('Error loading clans:', error);
    }
  }

  /**
   * Render available clans list
   */
  renderAvailableClans(clans) {
    const container = document.getElementById('clans-list');
    if (!container) return;

    if (clans.length === 0) {
      container.innerHTML = `
        <div class="empty-state">
          <i class="fas fa-search"></i>
          <h4>No Clans Found</h4>
          <p>No clans match your search criteria.</p>
        </div>
      `;
      return;
    }

    container.innerHTML = clans.map(clan => `
      <div class="clan-search-result">
        <div class="clan-result-header">
          <div class="clan-result-tag">[${clan.tag}]</div>
          <div class="clan-result-game">
            <i class="fas fa-gamepad"></i>
            ${this.getGameDisplayName(clan.gameType)}
          </div>
        </div>
        <div class="clan-result-content">
          <h4 class="clan-result-name">${clan.name}</h4>
          <p class="clan-result-description">${clan.description || 'No description provided'}</p>
          <div class="clan-result-stats">
            <span><i class="fas fa-users"></i> ${clan.memberCount || 0} members</span>
            <span><i class="fas fa-star"></i> Level ${clan.level || 1}</span>
            <span><i class="fas fa-trophy"></i> ${clan.rating || 1500} rating</span>
          </div>
        </div>
        <div class="clan-result-actions">
          <button class="epic-btn" onclick="window.clanManager.showJoinClanModal('${clan._id}')">
            <i class="fas fa-handshake"></i>
            Apply to Join
          </button>
        </div>
      </div>
    `).join('');
  }

  /**
   * Show join clan modal with clan details
   */
  async showJoinClanModal(clanId) {
    try {
      // Get clan details
      const clanResponse = await fetch(`/api/clans/${clanId}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!clanResponse.ok) {
        throw new Error('Failed to load clan details');
      }

      const clan = await clanResponse.json();
      
      // Get user's players
      const players = await this.getUserPlayers();
      
      if (players.length === 0) {
        this.showFeedback('You need to add a player to your profile before joining a clan.');
        return;
      }

      // Show the join clan modal
      const modal = document.getElementById('join-clan-modal');
      if (modal) {
        // Update clan details
        const detailsContainer = document.getElementById('join-clan-details');
        if (detailsContainer) {
          detailsContainer.innerHTML = `
            <div class="join-clan-info">
              <h3>[${clan.tag}] ${clan.name}</h3>
              <p>${clan.description || 'No description provided'}</p>
              <div class="join-clan-stats">
                <span><i class="fas fa-users"></i> ${clan.memberCount || 0} members</span>
                <span><i class="fas fa-gamepad"></i> ${this.getGameDisplayName(clan.gameType)}</span>
              </div>
            </div>
          `;
        }
        
        modal.style.display = 'block';
        modal.dataset.clanId = clanId;
      }
    } catch (error) {
      console.error('Error showing join clan modal:', error);
      this.showFeedback('Failed to load clan information.');
    }
  }

  /**
   * Get user's players
   */
  async getUserPlayers() {
    try {
      const response = await fetch('/api/players/user', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (response.ok) {
        return await response.json();
      } else {
        console.error('Failed to load user players:', response.status);
        return [];
      }
    } catch (error) {
      console.error('Error loading user players:', error);
      return [];
    }
  }

  /**
   * View clan details (redirect to clan page)
   */
  async viewClanDetails(clanId) {
    window.location.href = `/clans/${clanId}`;
  }

  /**
   * Manage clan (redirect to management page)
   */
  async manageClan(clanId) {
    window.location.href = `/clans/${clanId}/manage`;
  }

  /**
   * Leave current clan
   */
  async leaveClan(clanId) {
    if (!confirm('Are you sure you want to leave this clan?')) {
      return;
    }

    try {
      const response = await fetch(`/api/clans/${clanId}/leave`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (response.ok) {
        this.showFeedback('Successfully left the clan.');
        await this.loadUserClan(); // Refresh the clan section
      } else {
        const error = await response.json();
        this.showFeedback(`Failed to leave clan: ${error.message}`);
      }
    } catch (error) {
      console.error('Error leaving clan:', error);
      this.showFeedback('Failed to leave clan. Please try again.');
    }
  }

  /**
   * Join a clan
   */
  async joinClan(clanId, playerId) {
    try {
      const response = await fetch(`/api/clans/${clanId}/join`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ playerId })
      });

      if (response.ok) {
        this.showFeedback('Application submitted successfully!');
        await this.loadUserClan(); // Refresh the clan section
        
        // Close the join modal
        const modal = document.getElementById('join-clan-modal');
        if (modal) {
          modal.style.display = 'none';
        }
      } else {
        const error = await response.json();
        this.showFeedback(`Failed to join clan: ${error.message}`);
      }
    } catch (error) {
      console.error('Error joining clan:', error);
      this.showFeedback('Failed to join clan. Please try again.');
    }
  }

  /**
   * Create a new clan
   */
  async createClan(clanData) {
    try {
      const response = await fetch('/api/clans', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(clanData)
      });

      if (response.ok) {
        const newClan = await response.json();
        this.showFeedback('Clan created successfully!');
        await this.loadUserClan(); // Refresh the clan section
        
        // Close the create modal
        const modal = document.getElementById('create-clan-modal');
        if (modal) {
          modal.style.display = 'none';
        }
        
        return newClan;
      } else {
        const error = await response.json();
        this.showFeedback(`Failed to create clan: ${error.message}`);
        throw new Error(error.message);
      }
    } catch (error) {
      console.error('Error creating clan:', error);
      this.showFeedback('Failed to create clan. Please try again.');
      throw error;
    }
  }

  /**
   * Show feedback message to user
   */
  showFeedback(message) {
    // Check if global showFeedback function exists, otherwise create our own
    if (window.showFeedback) {
      window.showFeedback(message);
      return;
    }

    // Create a temporary feedback element
    const feedback = document.createElement('div');
    feedback.textContent = message;
    feedback.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(26, 26, 46, 0.95);
      border: 2px solid #ffd700;
      border-radius: 12px;
      padding: 1rem 2rem;
      color: #ffd700;
      font-weight: 600;
      z-index: 10000;
      backdrop-filter: blur(20px);
      animation: fadeInOut 3s ease-in-out;
    `;
    
    document.body.appendChild(feedback);
    setTimeout(() => feedback.remove(), 3000);
  }
}

// Global instance and exports
window.ClanManager = ClanManager;
const clanManager = new ClanManager();
window.clanManager = clanManager;

// Export functions for backward compatibility
window.initClanManagement = () => clanManager.initialize();
window.loadUserClan = () => clanManager.loadUserClan();
window.showCreateClanModal = () => clanManager.showCreateClanModal();
window.showFindClansModal = () => clanManager.showFindClansModal();
window.showJoinClanModal = (clanId) => clanManager.showJoinClanModal(clanId);
window.viewClanDetails = (clanId) => clanManager.viewClanDetails(clanId);
window.manageClan = (clanId) => clanManager.manageClan(clanId);
window.leaveClan = (clanId) => clanManager.leaveClan(clanId);

console.log('⚔️ Clan Manager loaded successfully'); 