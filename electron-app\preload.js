const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose safe APIs to the renderer process
contextBridge.exposeInMainWorld('electronAPI', {
  // Authentication methods
  auth: {
    login: (provider) => ipcRenderer.invoke('auth:oauth-login', provider),
    logout: () => ipcRenderer.invoke('auth:logout'),
    getStatus: () => ipcRenderer.invoke('auth:get-status')
  },

  // Configuration methods
  config: {
    get: () => ipcRenderer.invoke('config:get'),
    setServerUrl: (url) => ipcRenderer.invoke('config:set-server-url', url),
    setRememberLogin: (remember) => ipcRenderer.invoke('config:set-remember-login', remember)
  },

  // System methods
  shell: {
    openExternal: (url) => ipcRenderer.invoke('shell:open-external', url)
  },

  // Dialog methods
  dialog: {
    showError: (title, content) => ipcRenderer.invoke('dialog:show-error', title, content),
    showMessage: (options) => ipcRenderer.invoke('dialog:show-message', options)
  },

  // Window focus callback
  onWindowFocus: null,

  // Set authentication data (used internally)
  setAuthData: (authData) => {
    window.electronAuth = authData;
    
    // Trigger a custom event that the frontend can listen to
    const event = new CustomEvent('electronAuthUpdate', { detail: authData });
    window.dispatchEvent(event);
  },

  // Check if running in Electron
  isElectron: true,

  // Platform information
  platform: process.platform,

  // Version information
  versions: {
    node: process.versions.node,
    chrome: process.versions.chrome,
    electron: process.versions.electron
  }
});

// Initialize electron-specific features when DOM is ready
window.addEventListener('DOMContentLoaded', () => {
  // Add electron class to body for CSS targeting
  document.body.classList.add('electron-app');
  
  // Add platform-specific class
  document.body.classList.add(`platform-${process.platform}`);
  
  // Override console methods to include Electron prefix
  const originalLog = console.log;
  const originalError = console.error;
  const originalWarn = console.warn;
  
  console.log = (...args) => originalLog('[Electron Renderer]', ...args);
  console.error = (...args) => originalError('[Electron Renderer]', ...args);
  console.warn = (...args) => originalWarn('[Electron Renderer]', ...args);
  
  console.log('Electron preload script loaded successfully');
});

// Handle uncaught errors in the renderer process
window.addEventListener('error', (event) => {
  console.error('Uncaught error in renderer:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection in renderer:', event.reason);
}); 