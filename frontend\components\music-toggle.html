<!-- Music Toggle Component -->
<div class="music-toggle">
  <div class="btn-group" role="group" aria-label="Music selection">
    <input type="radio" class="btn-check" name="music-option" id="music-mute" autocomplete="off" checked>
    <label class="btn btn-outline-primary" for="music-mute">
      <i class="fas fa-volume-mute"></i>
    </label>

    <input type="radio" class="btn-check" name="music-option" id="music-orc" autocomplete="off">
    <label class="btn btn-outline-primary" for="music-orc">
      <i class="fas fa-drum"></i>
    </label>

    <input type="radio" class="btn-check" name="music-option" id="music-human" autocomplete="off">
    <label class="btn btn-outline-primary" for="music-human">
      <i class="fas fa-music"></i>
    </label>
  </div>
  <audio id="orc-music" loop>
    <source src="/assets/AUDIO/ORC/orcmusic.mp3" type="audio/mpeg">
  </audio>
  <audio id="human-music" loop>
    <source src="/assets/AUDIO/HUMAN/humanmusic.mp3" type="audio/mpeg">
  </audio>
</div>

<style>
.music-toggle {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.7);
  padding: 10px;
  border-radius: 5px;
}

.music-toggle .btn-group {
  display: flex;
  gap: 5px;
}

.music-toggle .btn {
  padding: 8px 12px;
  color: #fff;
  border-color: #fff;
}

.music-toggle .btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.music-toggle .btn-check:checked + .btn {
  background-color: #007bff;
  border-color: #007bff;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const orcMusic = document.getElementById('orc-music');
  const humanMusic = document.getElementById('human-music');
  const musicOptions = document.querySelectorAll('input[name="music-option"]');

  musicOptions.forEach(option => {
    option.addEventListener('change', function() {
      // Stop all music first
      orcMusic.pause();
      humanMusic.pause();
      orcMusic.currentTime = 0;
      humanMusic.currentTime = 0;

      // Play selected music
      if (this.id === 'music-orc') {
        orcMusic.play();
      } else if (this.id === 'music-human') {
        humanMusic.play();
      }
      // If mute is selected, no music will play
    });
  });
});
</script> 