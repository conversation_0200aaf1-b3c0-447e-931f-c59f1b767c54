/* War3 Dynamic Overlay System Styles */

.war3-canvas-container {
  position: relative;
  width: 100%;
  max-width: 512px;
  margin: 0 auto;
  border-radius: 12px;
  overflow: hidden;
  background: #1a1a1a;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.war3-map-canvas {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 12px;
  cursor: default;
}

.overlay-controls {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 10;
}

.overlay-controls-top-left {
  position: absolute;
  top: 12px;
  left: 12px;
  z-index: 10;
}

.overlay-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(0, 0, 0, 0.8);
  padding: 8px 12px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 215, 0, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.overlay-toggle:hover {
  background: rgba(0, 0, 0, 0.9);
  border-color: rgba(255, 215, 0, 0.5);
  transform: scale(1.02);
}

.overlay-toggle input[type="checkbox"] {
  display: none;
}

.toggle-slider {
  position: relative;
  width: 40px;
  height: 20px;
  background: #333;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.toggle-slider::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 16px;
  height: 16px;
  background: #666;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.overlay-toggle input[type="checkbox"]:checked + .toggle-slider {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
}

.overlay-toggle input[type="checkbox"]:checked + .toggle-slider::before {
  transform: translateX(20px);
  background: #1a1a1a;
}

.toggle-label {
  color: white;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

/* Strategic Elements Grid */
.strategic-summary-epic {
  margin: 1.5rem 0;
  padding: 1.5rem;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.05), rgba(255, 215, 0, 0.02));
  border: 1px solid rgba(255, 215, 0, 0.2);
  border-radius: 12px;
}

.strategic-summary-epic h3 {
  color: #ffd700;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.strategic-grid-epic {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.strategic-stat-epic {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.strategic-stat-epic:hover {
  background: rgba(255, 215, 0, 0.05);
  border-color: rgba(255, 215, 0, 0.3);
  transform: translateY(-2px);
}

.strategic-stat-epic .strategic-icon {
  font-size: 1.2rem;
  color: #ffd700;
  width: 20px;
  text-align: center;
}

.strategic-stat-epic .stat-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.strategic-stat-epic .stat-number {
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
}

.strategic-stat-epic .stat-label {
  font-size: 0.8rem;
  color: #a0a0a0;
  font-weight: 400;
}

/* War3 Map Cards */
.war3-map-card {
  border: 1px solid rgba(255, 215, 0, 0.2);
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.03), rgba(255, 215, 0, 0.01));
}

.war3-map-card:hover {
  border-color: rgba(255, 215, 0, 0.4);
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(255, 215, 0, 0.15);
}

.war3-tileset-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #1a1a1a;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.strategic-items {
  display: flex;
  gap: 12px;
  margin: 8px 0;
  flex-wrap: wrap;
}

.strategic-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  font-size: 0.8rem;
}

.strategic-item .strategic-icon {
  font-size: 0.9rem;
  color: #ffd700;
}

.strategic-item span {
  color: white;
  font-weight: 500;
}

/* Tooltip Styles */
.war3-overlay-tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.95);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  pointer-events: none;
  z-index: 1000;
  display: none;
  max-width: 200px;
  border: 1px solid rgba(255, 215, 0, 0.3);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.war3-overlay-tooltip strong {
  color: #ffd700;
  display: block;
  margin-bottom: 4px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .strategic-grid-epic {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.8rem;
  }
  
  .strategic-stat-epic {
    padding: 10px;
    gap: 10px;
  }
  
  .strategic-stat-epic .strategic-icon {
    font-size: 1rem;
  }
  
  .strategic-stat-epic .stat-number {
    font-size: 1rem;
  }
  
  .strategic-stat-epic .stat-label {
    font-size: 0.75rem;
  }
  
  .overlay-controls {
    top: 8px;
    right: 8px;
  }
  
  .overlay-toggle {
    padding: 6px 10px;
  }
  
  .toggle-label {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .strategic-grid-epic {
    grid-template-columns: 1fr;
  }
  
  .strategic-items {
    gap: 8px;
  }
  
  .strategic-item {
    padding: 3px 6px;
    font-size: 0.75rem;
  }
} 