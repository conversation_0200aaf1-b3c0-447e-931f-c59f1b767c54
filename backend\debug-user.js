const mongoose = require('mongoose');
const User = require('./models/User');

async function checkTurtlemanUser() {
  try {
    // Connect to MongoDB
    await mongoose.connect('mongodb://localhost:27017/newsite');
    console.log('✅ Connected to MongoDB');

    // Find TURTLEMAN user
    const user = await User.findOne({ 
      $or: [
        { username: 'TURTLEMAN' },
        { username: 'turtleman' }
      ]
    });

    if (user) {
      console.log('🐢 TURTLEMAN found in database:');
      console.log({
        _id: user._id,
        username: user.username,
        email: user.email,
        role: user.role,
        roleType: typeof user.role,
        isUsernameDefined: user.isUsernameDefined,
        createdAt: user.registeredAt
      });

      // Check if role is admin
      console.log('\n🔍 Admin check:');
      console.log('user.role === "admin":', user.role === 'admin');
      console.log('user.role === "user":', user.role === 'user');
      console.log('Role value:', JSON.stringify(user.role));

      // Update to admin if not already
      if (user.role !== 'admin') {
        console.log('\n🔧 Updating role to admin...');
        user.role = 'admin';
        await user.save();
        console.log('✅ Role updated to admin');
        
        // Verify the update
        const updatedUser = await User.findById(user._id);
        console.log('✅ Verified role after update:', updatedUser.role);
      } else {
        console.log('✅ User is already admin');
      }

    } else {
      console.log('❌ TURTLEMAN not found in database');
      
      // List all users to see what's available
      const allUsers = await User.find({}, 'username email role').limit(10);
      console.log('\n📋 Available users:');
      allUsers.forEach(u => {
        console.log(`- ${u.username} (${u.email}) - role: ${u.role}`);
      });
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
    process.exit(0);
  }
}

checkTurtlemanUser();
