/**
 * Modern Map Details Modal Styling
 * Enhanced design with single interactive map and smooth hover effects
 */

/* Modern Modal Container */
.map-details-modern {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
  background: transparent;
}

/* Interactive Map Showcase */
.interactive-map-showcase {
  position: relative;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.95));
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
}

.map-container-interactive {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 20px;
}

.main-map-image {
  max-width: 100%;
  max-height: 500px;
  border-radius: 12px;
  box-shadow: 
    0 10px 30px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.main-map-image:hover {
  transform: scale(1.02);
  box-shadow: 
    0 15px 40px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.2);
}

/* Map Info Overlay */
.map-info-overlay {
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  pointer-events: none;
  z-index: 5;
}

.map-title-section {
  flex: 1;
}

.map-title-large {
  font-size: 2.5rem;
  font-weight: 700;
  color: #f1f5f9;
  margin: 0 0 10px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
  letter-spacing: -0.5px;
}

.map-meta-badges {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.meta-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
}

.type-badge { background: linear-gradient(135deg, #8b5cf6, #6366f1); }
.size-badge { background: linear-gradient(135deg, #06b6d4, #0891b2); }
.players-badge { background: linear-gradient(135deg, #10b981, #059669); }

/* Rating Section */
.map-rating-section {
  text-align: right;
}

.stars-display {
  font-size: 1.5rem;
  margin-bottom: 5px;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.7));
}

.rating-info {
  color: #cbd5e1;
  font-size: 0.9rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
}

/* Interactive Legend */
.interactive-legend {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: rgba(15, 23, 42, 0.9);
  backdrop-filter: blur(10px);
  padding: 12px;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  pointer-events: none;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
  font-size: 0.8rem;
  color: #cbd5e1;
}

.legend-item:last-of-type {
  margin-bottom: 8px;
}

.legend-icon {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid #FFD700;
}

.goldmine-icon {
  background: radial-gradient(circle, #FFD700, #FFA500);
}

.player-icon {
  background: linear-gradient(135deg, #FF4444, #CC0000);
  border-radius: 3px;
}

.legend-hint {
  font-size: 0.7rem;
  color: #94a3b8;
  font-style: italic;
}

/* Content Sections */
.map-content-sections {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

/* Quick Info Panel */
.quick-info-panel {
  flex: 1;
  min-width: 300px;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.95));
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(51, 65, 85, 0.5);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.info-item:hover {
  background: rgba(51, 65, 85, 0.8);
  transform: translateY(-2px);
}

.info-item i {
  font-size: 1.2rem;
  color: #d4af37;
  width: 20px;
  text-align: center;
}

.info-content {
  flex: 1;
}

.info-label {
  display: block;
  font-size: 0.8rem;
  color: #94a3b8;
  margin-bottom: 2px;
}

.info-value {
  font-size: 1rem;
  font-weight: 600;
  color: #f1f5f9;
}

/* Action Buttons */
.action-buttons-modern {
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-width: 200px;
}

.btn-modern {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.btn-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-modern:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.btn-secondary {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  color: white;
  box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
}

.btn-danger {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

/* Expandable Sections */
.expandable-sections {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.section-card {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.95));
  border-radius: 12px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.section-card:hover {
  border-color: rgba(212, 175, 55, 0.3);
}

.section-header {
  padding: 16px 20px;
  background: rgba(51, 65, 85, 0.5);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
}

.section-header:hover {
  background: rgba(51, 65, 85, 0.8);
}

.section-header.expanded {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.section-header h3 {
  margin: 0;
  color: #f1f5f9;
  font-size: 1.2rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.section-header h3 i {
  color: #d4af37;
  font-size: 1.1rem;
}

.toggle-icon {
  color: #94a3b8;
  transition: transform 0.3s ease;
}

.section-content {
  display: none;
  padding: 20px;
  color: #e2e8f0;
  line-height: 1.6;
}

.description-text {
  margin: 0;
  font-size: 1rem;
  line-height: 1.6;
}

/* Strategic Analysis Compact */
.strategic-analysis-compact {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.analysis-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: rgba(51, 65, 85, 0.5);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  background: rgba(51, 65, 85, 0.8);
  transform: translateY(-2px);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.gold-stat .stat-icon {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  color: #1f2937;
}

.players-stat .stat-icon {
  background: linear-gradient(135deg, #FF4444, #CC0000);
  color: white;
}

.oil-stat .stat-icon {
  background: linear-gradient(135deg, #0891b2, #0e7490);
  color: white;
}

.terrain-stat .stat-icon {
  background: linear-gradient(135deg, #059669, #047857);
  color: white;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #f1f5f9;
  margin-bottom: 2px;
}

.stat-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #cbd5e1;
  margin-bottom: 2px;
}

.stat-detail {
  font-size: 0.8rem;
  color: #94a3b8;
}

.analysis-note {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  color: #93c5fd;
  font-size: 0.9rem;
}

.analysis-note i {
  color: #3b82f6;
}

/* Ratings Section Modern */
.ratings-section-modern {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.rating-form-modern {
  background: rgba(51, 65, 85, 0.5);
  padding: 20px;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.rating-form-modern h4 {
  margin: 0 0 16px 0;
  color: #f1f5f9;
  font-size: 1.2rem;
}

.star-rating-modern {
  display: flex;
  gap: 4px;
  margin-bottom: 16px;
}

.rating-star {
  font-size: 1.5rem;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.rating-star:hover,
.rating-star.fas {
  color: #FFD700;
  transform: scale(1.1);
}

.rating-comment-input {
  width: 100%;
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 12px;
  color: #f1f5f9;
  font-size: 0.9rem;
  resize: vertical;
  margin-bottom: 16px;
  transition: border-color 0.3s ease;
}

.rating-comment-input:focus {
  outline: none;
  border-color: #d4af37;
}

.rating-comment-input::placeholder {
  color: #94a3b8;
}

.rating-submit {
  align-self: flex-start;
}

/* Existing Ratings */
.existing-ratings-modern h4 {
  margin: 0 0 16px 0;
  color: #f1f5f9;
  font-size: 1.2rem;
}

.ratings-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.rating-item-modern {
  background: rgba(51, 65, 85, 0.5);
  border-radius: 8px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.rating-item-modern:hover {
  background: rgba(51, 65, 85, 0.8);
  border-color: rgba(255, 255, 255, 0.2);
}

.rating-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.rating-user {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rating-user i {
  color: #94a3b8;
  font-size: 1.2rem;
}

.username {
  font-weight: 600;
  color: #e2e8f0;
}

.rating-stars-small {
  font-size: 0.9rem;
}

.rating-comment {
  margin: 8px 0;
  color: #cbd5e1;
  line-height: 1.5;
}

.rating-date {
  font-size: 0.8rem;
  color: #94a3b8;
}

.no-ratings-modern {
  text-align: center;
  padding: 40px 20px;
  color: #94a3b8;
}

.no-ratings-modern i {
  font-size: 2rem;
  margin-bottom: 12px;
  color: #64748b;
}

.btn-link {
  background: none;
  border: none;
  color: #3b82f6;
  cursor: pointer;
  text-decoration: underline;
  font-size: 0.9rem;
  padding: 8px 0;
}

.btn-link:hover {
  color: #2563eb;
}

/* Enhanced Interactive Overlays */
.enhanced-strategic-overlays {
  pointer-events: none;
}

.interactive-element {
  pointer-events: all;
}

.enhanced-goldmine-overlay {
  transform-origin: center;
}

.enhanced-starting-position-overlay {
  transform-origin: center;
}

/* Enhanced Tooltip */
.enhanced-tooltip {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.tooltip-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.tooltip-title {
  font-weight: 600;
  font-size: 0.95rem;
}

.goldmine-header {
  color: #FFD700;
}

.player-header {
  color: #3b82f6;
}

.tooltip-content {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.tooltip-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  color: #94a3b8;
  font-size: 0.85rem;
}

.stat-value {
  color: #f1f5f9;
  font-weight: 600;
  font-size: 0.85rem;
}

.gold-value {
  color: #FFD700;
}

.category-very-low { color: #ef4444; }
.category-low { color: #f97316; }
.category-medium { color: #eab308; }
.category-high { color: #22c55e; }
.category-very-high { color: #10b981; }

.race-human { color: #3b82f6; }
.race-orc { color: #ef4444; }
.race-unknown { color: #94a3b8; }

/* Animations */
@keyframes goldmine-pulse {
  0%, 100% {
    box-shadow: 
      0 0 10px rgba(255, 215, 0, 0.5),
      0 2px 8px rgba(0, 0, 0, 0.3),
      inset 0 1px 3px rgba(255, 255, 255, 0.3);
  }
  50% {
    box-shadow: 
      0 0 15px rgba(255, 215, 0, 0.7),
      0 2px 8px rgba(0, 0, 0, 0.3),
      inset 0 1px 3px rgba(255, 255, 255, 0.3);
  }
}

@keyframes player-glow {
  0% {
    filter: brightness(1);
  }
  100% {
    filter: brightness(1.1);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .map-content-sections {
    flex-direction: column;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .analysis-stats-grid {
    grid-template-columns: 1fr;
  }
  
  .map-title-large {
    font-size: 2rem;
  }
  
  .action-buttons-modern {
    min-width: 100%;
  }
  
  .map-info-overlay {
    flex-direction: column;
    gap: 16px;
  }
  
  .map-rating-section {
    text-align: left;
  }
}

/* User Avatar Styling for Reviews */
.user-avatar-small {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 2px solid rgba(255, 215, 0, 0.3);
  object-fit: cover;
  margin-right: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.user-avatar-small:hover {
  border-color: rgba(255, 215, 0, 0.6);
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Enhanced Reviews Section */
.rating-item-compact {
  background: rgba(30, 41, 59, 0.6);
  border: 1px solid rgba(255, 215, 0, 0.2);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.rating-item-compact:hover {
  background: rgba(30, 41, 59, 0.8);
  border-color: rgba(255, 215, 0, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.rating-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.username {
  font-weight: 600;
  color: #f1f5f9;
  font-size: 14px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.rating-stars {
  color: #ffd700;
  font-size: 14px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.rating-comment {
  margin: 12px 0 8px 0;
  padding: 12px;
  background: rgba(51, 65, 85, 0.4);
  border-radius: 8px;
  border-left: 3px solid rgba(255, 215, 0, 0.5);
}

.rating-comment p {
  margin: 0;
  color: #cbd5e1;
  font-size: 13px;
  line-height: 1.5;
  font-style: italic;
}

.rating-date {
  font-size: 11px;
  color: #94a3b8;
  text-align: right;
  margin-top: 8px;
}

/* No Reviews State */
.no-reviews {
  text-align: center;
  color: #94a3b8;
  font-style: italic;
  padding: 32px;
  background: rgba(30, 41, 59, 0.3);
  border-radius: 12px;
  border: 1px dashed rgba(255, 215, 0, 0.3);
}

/* Reviews List Header */
.reviews-list h4 {
  color: #ffd700;
  margin-bottom: 16px;
  font-family: 'Cinzel', serif;
  display: flex;
  align-items: center;
  gap: 8px;
}

.reviews-list h4 i {
  color: #ffd700;
}

/* Login Prompt for Reviews */
.login-prompt-section {
  text-align: center;
  padding: 24px;
  background: rgba(30, 41, 59, 0.4);
  border-radius: 12px;
  border: 1px solid rgba(255, 215, 0, 0.3);
  margin-bottom: 24px;
}

.login-prompt-section p {
  color: #cbd5e1;
  margin: 0;
  font-size: 14px;
}

.login-prompt-section i {
  color: #ffd700;
  margin-right: 8px;
}

/* Enhanced Strategic Overlays */
.enhanced-strategic-overlays {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

.enhanced-goldmine-overlay,
.enhanced-starting-position-overlay {
  position: absolute;
  pointer-events: all;
  z-index: 15;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Goldmine Pulse Animation */
@keyframes goldmine-pulse {
  0%, 100% {
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5), 0 2px 8px rgba(0, 0, 0, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.8), 0 2px 8px rgba(0, 0, 0, 0.3);
  }
}

/* Enhanced Tooltip Styling */
.enhanced-tooltip {
  font-family: 'Inter', sans-serif;
  font-size: 12px;
  line-height: 1.4;
}

.tooltip-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tooltip-title {
  font-weight: 600;
  color: #ffd700;
}

.tooltip-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.tooltip-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  color: #94a3b8;
  font-size: 11px;
}

.stat-value {
  color: #f1f5f9;
  font-weight: 600;
  font-size: 11px;
}

.gold-value {
  color: #ffd700;
}

/* Map Container Positioning */
.map-container-interactive {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
}

.map-detail-image {
  width: 100%;
  height: auto;
  max-height: 600px;
  object-fit: contain;
  border-radius: 12px;
} 

/* User Comments Section */
.map-comments-section-epic {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.95));
  border-radius: 16px;
  padding: 24px;
  margin: 20px 0;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.comments-header-epic {
  margin-bottom: 20px;
  border-bottom: 2px solid rgba(255, 215, 0, 0.3);
  padding-bottom: 12px;
}

.comments-header-epic h3 {
  color: #ffd700;
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.comments-header-epic i {
  color: #ffd700;
  font-size: 1.1rem;
}

.comments-list-epic {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.comment-item-epic {
  background: rgba(51, 65, 85, 0.4);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.comment-item-epic:hover {
  background: rgba(51, 65, 85, 0.6);
  border-color: rgba(255, 215, 0, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.comment-header-epic {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.comment-user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.comment-username {
  color: #ffd700;
  font-weight: 600;
  font-size: 0.95rem;
}

.comment-rating {
  display: flex;
  gap: 2px;
}

.comment-rating .star {
  font-size: 0.8rem;
}

.comment-date {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.85rem;
}

.comment-content {
  color: #f1f5f9;
  line-height: 1.6;
  font-size: 0.95rem;
  padding-left: 8px;
  border-left: 3px solid rgba(255, 215, 0, 0.3);
  background: rgba(0, 0, 0, 0.2);
  padding: 12px;
  border-radius: 8px;
}

.no-comments-epic {
  text-align: center;
  padding: 32px 16px;
  color: rgba(255, 255, 255, 0.7);
}

.no-comments-epic i {
  font-size: 2.5rem;
  margin-bottom: 16px;
  color: rgba(255, 215, 0, 0.3);
  display: block;
}

.no-comments-epic p {
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
}
