const dbBackup = require('./utils/dbBackup');

/**
 * Database backup script
 * Run this script to create a backup of your database
 */
async function runBackup() {
  try {
    console.log('🚀 Starting database backup process...');
    console.log('Database: newsite');
    console.log('MongoDB URI: mongodb://localhost:27017');
    console.log('');
    
    // Create backup
    const result = await dbBackup.createBackup();
    
    if (result.success) {
      console.log('');
      console.log('📊 Backup Summary:');
      console.log('==================');
      console.log(`✅ Backup Name: ${result.backupName}`);
      console.log(`📁 Backup Path: ${result.backupPath}`);
      console.log(`📅 Timestamp: ${result.timestamp}`);
      console.log(`💾 Size: ${dbBackup.formatSize(result.size)}`);
      console.log('');
      console.log('🎉 Database backup completed successfully!');
      
      // List all backups
      console.log('');
      console.log('📋 All Available Backups:');
      console.log('==========================');
      const allBackups = await dbBackup.listBackups();
      
      if (allBackups.length > 0) {
        allBackups.forEach((backup, index) => {
          const isLatest = index === 0 ? ' (LATEST)' : '';
          console.log(`${index + 1}. ${backup.name}${isLatest}`);
          console.log(`   📅 Created: ${backup.created.toLocaleString()}`);
          console.log(`   💾 Size: ${backup.size}`);
          console.log('');
        });
      } else {
        console.log('No backups found.');
      }
      
    } else {
      console.error('❌ Backup failed:', result.error);
      if (result.details) {
        console.error('Details:', result.details);
      }
      process.exit(1);
    }
    
  } catch (error) {
    console.error('💥 Unexpected error during backup:', error);
    process.exit(1);
  }
}

// Check if mongodump is available
console.log('🔍 Checking MongoDB tools...');
const { exec } = require('child_process');

exec('mongodump --version', (error, stdout, stderr) => {
  if (error) {
    console.error('❌ MongoDB tools not found!');
    console.error('Please install MongoDB Database Tools:');
    console.error('https://www.mongodb.com/try/download/database-tools');
    console.error('');
    console.error('Or install via chocolatey: choco install mongodb-database-tools');
    process.exit(1);
  } else {
    console.log('✅ MongoDB tools found:', stdout.trim());
    console.log('');
    runBackup();
  }
}); 