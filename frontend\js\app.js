/**
 * app.js - Main Application Initialization
 * 
 * Initializes the unified achievement system and coordinates all components.
 */

import { achievementEngine } from './core/AchievementEngine.js';
import { achievementUI } from './core/AchievementUI.js';

class Application {
  constructor() {
    this.isInitialized = false;
    this.components = {
      achievementEngine,
      achievementUI
    };
  }

  /**
   * Initialize the entire application
   */
  async init() {
    if (this.isInitialized) return;

    try {
      console.log('🚀 Initializing Application...');
      
      // Wait for DOM to be ready
      await this.waitForDOM();
      
      // Initialize core systems in order
      await this.initializeCoreComponents();
      
      // Setup global event handlers
      this.setupGlobalHandlers();
      
      this.isInitialized = true;
      console.log('✅ Application initialized successfully');
      
      // Emit global ready event
      window.dispatchEvent(new CustomEvent('app:ready'));
      
    } catch (error) {
      console.error('❌ Failed to initialize application:', error);
      throw error;
    }
  }

  /**
   * Wait for DOM to be ready
   */
  async waitForDOM() {
    return new Promise((resolve) => {
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', resolve);
      } else {
        resolve();
      }
    });
  }

  /**
   * Initialize core components
   */
  async initializeCoreComponents() {
    console.log('🔧 Initializing core components...');
    
    // Initialize achievement engine first (data layer)
    await this.components.achievementEngine.init();
    
    // Initialize achievement UI (presentation layer)
    await this.components.achievementUI.init();
    
    console.log('✅ Core components initialized');
  }

  /**
   * Setup global event handlers
   */
  setupGlobalHandlers() {
    // Handle page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        // Page became visible, refresh achievement data
        this.components.achievementEngine.refresh();
      }
    });

    // Handle online/offline status
    window.addEventListener('online', () => {
      console.log('🌐 Connection restored');
      this.components.achievementEngine.refresh();
    });

    window.addEventListener('offline', () => {
      console.log('📡 Connection lost');
    });

    // Expose global API for debugging
    if (typeof window !== 'undefined') {
      window.app = this;
      window.achievementEngine = this.components.achievementEngine;
      window.achievementUI = this.components.achievementUI;
    }
  }

  /**
   * Get application status
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      components: Object.keys(this.components).reduce((status, key) => {
        status[key] = this.components[key].isInitialized || false;
        return status;
      }, {})
    };
  }

  /**
   * Cleanup application
   */
  destroy() {
    Object.values(this.components).forEach(component => {
      if (component.destroy) {
        component.destroy();
      }
    });
    
    this.isInitialized = false;
  }
}

// Create global application instance
const app = new Application();

// Auto-initialize when script loads
app.init().catch(error => {
  console.error('💥 Application failed to start:', error);
});

// Export for module systems
export default app; 