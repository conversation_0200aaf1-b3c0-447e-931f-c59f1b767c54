<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://sandbox.web.squarecdn.com https://web.squarecdn.com https://pay.google.com https://js-sandbox.squarecdn.com https://js.squarecdn.com https://js.afterpay.com https://js-sandbox.afterpay.com; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://fonts.googleapis.com https://sandbox.web.squarecdn.com https://web.squarecdn.com; font-src 'self' https://cdnjs.cloudflare.com https://fonts.gstatic.com https://square-fonts-production-f.squarecdn.com https://d1g145x70srn7h.cloudfront.net; img-src 'self' data: https:; connect-src 'self' https://*.googleapis.com https://*.google.com https://connect.squareupsandbox.com https://connect.squareup.com https://pci-connect.squareupsandbox.com https://pci-connect.squareup.com https://pay.google.com https://*.afterpay.com https://*.clearpay.co.uk; frame-src 'self' https://www.youtube.com https://sandbox.web.squarecdn.com https://web.squarecdn.com https://pay.google.com https://*.afterpay.com https://*.clearpay.co.uk;">
  <title>WC Arena - Ladder</title>
  
  <!-- Preload critical fonts -->
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-brands-400.woff2" as="font" type="font/woff2" crossorigin>
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>
  
  <!-- CSS Dependencies -->
  <link rel="stylesheet" href="/css/warcraft-app-modern.css" />
  <link rel="stylesheet" href="/css/ladder-consolidated.css" />
  <link rel="stylesheet" href="/css/profile-specific.css" />
  <link rel="stylesheet" href="/css/rank-animation.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- Chart.js - Single version -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <div id="navbar-container"></div>

  <main>
    <!-- Game Type Selection Area (for new users) -->
    <div id="game-type-selection-area" class="hidden">
      <!-- Game type selector will be loaded here if needed -->
    </div>

    <!-- Main Ladder Content -->
    <div id="ladder-main-content">
      <!-- Header Section -->
      <div class="ladder-header">
        <!-- Game Type Tabs -->
        <div class="game-tabs-container">
          <div class="game-tabs">
            <button class="game-tab" data-game-type="war1" title="WC: Orcs & Humans">
              <div class="game-tab-icon">
                <i class="fas fa-sword"></i>
              </div>
              <div class="game-tab-info">
                <span class="game-tab-title">WC1</span>
                <span class="game-tab-subtitle">Original</span>
              </div>
            </button>
            <button class="game-tab active" data-game-type="war2" title="WC II">
              <div class="game-tab-icon">
                <i class="fas fa-shield-alt"></i>
              </div>
              <div class="game-tab-info">
                <span class="game-tab-title">WC2</span>
                <span class="game-tab-subtitle">Classic RTS</span>
              </div>
            </button>
            <button class="game-tab" data-game-type="war3" title="WC III">
              <div class="game-tab-icon">
                <i class="fas fa-crown"></i>
              </div>
              <div class="game-tab-info">
                <span class="game-tab-title">WC3</span>
                <span class="game-tab-subtitle">Heroes & Magic</span>
              </div>
            </button>
          </div>
        </div>

        <!-- Report Match Button -->
        <div class="report-match-section">
          <button id="report-match-btn" class="btn btn-primary btn-report">
            <i class="fas fa-trophy"></i> 
            <span id="report-match-text">REPORT MATCH</span>
            <i class="fas fa-trophy"></i>
          </button>
        </div>
      </div>

      <!-- Search and Filters -->
      <div class="controls-section" id="wc2-wc3-controls">
        <!-- Player Search -->
        <div class="player-search">
          <div class="search-input-group">
            <input type="text" id="player-search" placeholder="Search player name...">
            <button id="search-btn" class="btn btn-search">
              <i class="fas fa-search"></i>
              <span>Search</span>
            </button>
            <button id="clear-search-btn" class="btn btn-clear" title="Clear search">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>

        <!-- Map Search Filter -->
        <div class="map-search">
          <div class="search-input-group">
            <input type="text" id="map-search" placeholder="Search specific map...">
            <button id="map-search-btn" class="btn btn-search">
              <i class="fas fa-map"></i>
              <span>Map</span>
            </button>
            <button id="clear-map-search-btn" class="btn btn-clear" title="Clear map search">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>

        <!-- Match Type Filter -->
        <div class="match-type-filter">
          <div class="filter-buttons">
            <button class="filter-btn active" data-match-type="all">All</button>
            <button class="filter-btn" data-match-type="1v1">1v1</button>
            <button class="filter-btn" data-match-type="2v2">2v2</button>
            <button class="filter-btn" data-match-type="3v3">3v3</button>
            <button class="filter-btn" data-match-type="4v4">4v4</button>
            <button class="filter-btn" data-match-type="ffa">FFA</button>
            <button class="filter-btn" data-match-type="vsai">vs AI</button>
          </div>
        </div>
      </div>

      <!-- WC1 Simplified Controls -->
      <div class="controls-section wc1-controls" id="wc1-controls" style="display: none;">
        <div class="wc1-info-banner">
          <div class="wc1-info-content">
            <i class="fas fa-robot"></i>
            <div class="wc1-info-text">
              <h4>WC1 vs AI Arena</h4>
              <p>Battle against AI opponents on classic Warcraft: Orcs & Humans scenarios. Choose from 21 campaign maps across Forest, Swamp, and Dungeon environments.</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content Layout -->
      <div class="ladder-layout">
        <!-- Leaderboard (Main Content) -->
        <section class="leaderboard-section">
          <h2 id="leaderboard-title">Leaderboard</h2>
          <div class="leaderboard-container">
            <table class="leaderboard-table">
              <thead>
                <tr>
                  <th class="rank-header">Rank</th>
                  <th class="player-header">Player</th>
                  <th class="mmr-header">MMR</th>
                  <th class="wins-header">Wins</th>
                  <th class="losses-header">Losses</th>
                  <th class="ratio-header">Win %</th>
                </tr>
              </thead>
              <tbody id="leaderboard-body">
                <tr>
                  <td colspan="6" class="loading-cell">Loading leaderboard data...</td>
                </tr>
              </tbody>
            </table>

            <!-- Pagination -->
            <div class="pagination-controls">
              <button id="prev-page" class="btn btn-secondary">Previous</button>
              <span id="page-info">Page 1 of 1</span>
              <button id="next-page" class="btn btn-secondary">Next</button>
            </div>
          </div>
        </section>

        <!-- Ranks Sidebar -->
        <aside class="ranks-sidebar">
          <h3>Ranks</h3>
          <div class="ranks-container" id="ranks-container">
            <div class="loading">Loading ranks...</div>
          </div>
        </aside>

        <!-- Recent Matches Sidebar -->
        <aside class="recent-matches-sidebar">
          <h3>Recent Matches</h3>
          <div class="recent-matches-container" id="recent-matches-container">
            <div class="loading">Loading matches...</div>
          </div>
        </aside>
      </div>

      <!-- Statistics Section -->
      <section class="stats-section">
        <h2>Ladder Statistics</h2>
        <div class="stats-container" id="stats-container">
          <!-- Overview Stats (will be dynamically inserted) -->
          
          <!-- Race Distribution -->
          <div class="stats-card">
            <h3>Race Distribution</h3>
            <div class="stats-content">
              <div class="stats-list">
                <ul id="race-stats-list">
                  <!-- Race list will be dynamically updated based on game type -->
                  <li><span class="stats-name">Human</span><span class="stats-value">0</span></li>
                  <li><span class="stats-name">Orc</span><span class="stats-value">0</span></li>
                  <li><span class="stats-name">Random</span><span class="stats-value">0</span></li>
                </ul>
              </div>
              <div class="stats-chart">
                <canvas id="race-chart"></canvas>
              </div>
            </div>
          </div>

          <!-- Game Modes Distribution -->
          <div class="stats-card">
            <h3>Match Types</h3>
            <div class="stats-content">
              <div class="stats-list">
                <ul id="modes-stats-list">
                  <li><span class="stats-name">1v1</span><span class="stats-value">0</span></li>
                  <li><span class="stats-name">2v2</span><span class="stats-value">0</span></li>
                  <li><span class="stats-name">3v3</span><span class="stats-value">0</span></li>
                  <li><span class="stats-name">4v4</span><span class="stats-value">0</span></li>
                  <li><span class="stats-name">FFA</span><span class="stats-value">0</span></li>
                </ul>
              </div>
              <div class="stats-chart">
                <canvas id="match-type-chart"></canvas>
              </div>
            </div>
          </div>

          <!-- Rank Distribution -->
          <div class="stats-card">
            <h3>Rank Distribution</h3>
            <div class="stats-content">
              <div class="stats-chart-only">
                <canvas id="rank-chart"></canvas>
              </div>
            </div>
          </div>

          <!-- MMR Distribution -->
          <div class="stats-card">
            <h3>MMR Distribution</h3>
            <div class="stats-content">
              <div class="stats-chart-only">
                <canvas id="mmr-chart"></canvas>
              </div>
            </div>
          </div>

          <!-- Top Maps -->
          <div class="stats-card">
            <h3>Popular Maps</h3>
            <div class="stats-content">
              <div class="stats-list">
                <ul id="maps-stats-list">
                  <li><span class="stats-name">Loading...</span><span class="stats-value">0</span></li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Activity Chart -->
          <div class="stats-card">
            <h3>Recent Activity</h3>
            <div class="stats-content">
              <div class="stats-chart-only">
                <canvas id="activity-chart"></canvas>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </main>

  <!-- Report Match Modal -->
  <div id="report-match-modal" class="modal">
    <div class="modal-overlay">
      <div class="modal-content">
        <div class="modal-header">
          <h3 id="report-modal-title">Report WC II Match</h3>
          <button class="close-modal" title="Close">&times;</button>
        </div>

        <form id="report-match-form">
          <!-- Game Settings -->
          <div class="form-section">
            <h4>Game Settings</h4>
            
            <div class="form-group">
              <label for="match-type">Match Type:</label>
              <select id="match-type" name="matchType" required>
                <option value="1v1">1v1</option>
                <option value="2v2">2v2</option>
                <option value="3v3">3v3</option>
                <option value="4v4">4v4</option>
                <option value="ffa">FFA</option>
                <option value="vsai">Vs AI</option>
              </select>
            </div>

            <div class="form-group">
              <label for="map">Map:</label>
              <input type="text" id="map" name="map" required placeholder="Enter map name">
              <div id="map-suggestions" class="map-suggestions"></div>
            </div>

            <div class="form-group">
              <label for="resource-level">Resource Level:</label>
              <div class="resource-buttons">
                <input type="radio" id="resource-high" name="resourceLevel" value="high" required>
                <label for="resource-high">High</label>
                
                <input type="radio" id="resource-medium" name="resourceLevel" value="medium" required>
                <label for="resource-medium">Medium</label>
                
                <input type="radio" id="resource-low" name="resourceLevel" value="low" required>
                <label for="resource-low">Low</label>
              </div>
            </div>
          </div>

          <!-- Players Section -->
          <div class="form-section">
            <h4>Players</h4>
            <div id="players-container">
              <!-- Player inputs will be generated based on match type -->
            </div>
          </div>

          <!-- Screenshots -->
          <div class="form-group">
            <label for="screenshots">Screenshots (required):</label>
            <input type="file" id="screenshots" name="screenshots" accept=".png" multiple required>
            <small class="help-text">
              PNG format only. Must be taken within the last 2 days and unedited.
            </small>
          </div>

          <!-- Notes -->
          <div class="form-group">
            <label for="notes">Additional Notes:</label>
            <textarea id="notes" name="notes" rows="3" placeholder="Any additional information about the match"></textarea>
          </div>

          <button type="submit" class="btn btn-primary">Submit Report</button>
        </form>
      </div>
    </div>
  </div>

  <!-- WC1 Report Match Modal -->
  <div id="wc1-report-match-modal" class="modal">
    <div class="modal-overlay">
      <div class="modal-content">
        <div class="modal-header">
          <h3>Report WC1 vs AI Match</h3>
          <button class="close-modal" title="Close">&times;</button>
        </div>

        <form id="wc1-report-match-form">
          <!-- Game Settings -->
          <div class="form-section">
            <h4>vs AI Match Details</h4>
            
            <div class="form-group">
              <label for="wc1-map">Scenario Map:</label>
              <select id="wc1-map" name="map" required>
                <option value="">Select a scenario...</option>
                <optgroup label="Forest Campaign">
                  <option value="Forest 1">Forest 1</option>
                  <option value="Forest 2">Forest 2</option>
                  <option value="Forest 3">Forest 3</option>
                  <option value="Forest 4">Forest 4</option>
                  <option value="Forest 5">Forest 5</option>
                  <option value="Forest 6">Forest 6</option>
                  <option value="Forest 7">Forest 7</option>
                </optgroup>
                <optgroup label="Swamp Campaign">
                  <option value="Swamp 1">Swamp 1</option>
                  <option value="Swamp 2">Swamp 2</option>
                  <option value="Swamp 3">Swamp 3</option>
                  <option value="Swamp 4">Swamp 4</option>
                  <option value="Swamp 5">Swamp 5</option>
                  <option value="Swamp 6">Swamp 6</option>
                  <option value="Swamp 7">Swamp 7</option>
                </optgroup>
                <optgroup label="Dungeon Campaign">
                  <option value="Dungeon 1">Dungeon 1</option>
                  <option value="Dungeon 2">Dungeon 2</option>
                  <option value="Dungeon 3">Dungeon 3</option>
                  <option value="Dungeon 4">Dungeon 4</option>
                  <option value="Dungeon 5">Dungeon 5</option>
                  <option value="Dungeon 6">Dungeon 6</option>
                  <option value="Dungeon 7">Dungeon 7</option>
                </optgroup>
              </select>
            </div>

            <div class="form-group">
              <label for="wc1-race">Your Race:</label>
              <div class="race-buttons">
                <input type="radio" id="wc1-human" name="race" value="human" required>
                <label for="wc1-human">
                  <i class="fas fa-shield-alt"></i>
                  Human
                </label>
                
                <input type="radio" id="wc1-orc" name="race" value="orc" required>
                <label for="wc1-orc">
                  <i class="fas fa-hammer"></i>
                  Orc
                </label>
              </div>
            </div>

            <div class="form-group">
              <label for="wc1-result">Match Result:</label>
              <div class="result-buttons">
                <input type="radio" id="wc1-win" name="result" value="win" required>
                <label for="wc1-win" class="result-win">
                  <i class="fas fa-trophy"></i>
                  Victory
                </label>
                
                <input type="radio" id="wc1-loss" name="result" value="loss" required>
                <label for="wc1-loss" class="result-loss">
                  <i class="fas fa-skull"></i>
                  Defeat
                </label>
              </div>
            </div>
          </div>

          <!-- Match Info -->
          <div class="form-section">
            <h4>Additional Details</h4>
            
            <div class="form-group">
              <label for="wc1-duration">Match Duration (optional):</label>
              <input type="number" id="wc1-duration" name="duration" min="1" max="180" placeholder="Minutes">
            </div>

            <div class="form-group">
              <label for="wc1-notes">Notes (optional):</label>
              <textarea id="wc1-notes" name="notes" rows="3" placeholder="Strategy used, difficulty experienced, etc."></textarea>
            </div>
          </div>

          <div class="form-actions">
            <button type="button" class="btn btn-secondary" id="wc1-cancel-report">Cancel</button>
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-robot"></i>
              Submit vs AI Match
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Player Stats Modal -->
  <div id="player-stats-modal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2><i class="fas fa-chart-line"></i> <span id="stats-player-name">Player</span> Statistics</h2>
        <button class="close-modal">&times;</button>
      </div>
      <div class="modal-body">
        <!-- Stats content will be loaded here -->
        <div id="player-stats-content">
          <div class="loading">Loading player statistics...</div>
        </div>
      </div>
    </div>
  </div>

  <div id="footer-container"></div>

  <!-- JavaScript Dependencies -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
  <script src="/js/utils.js"></script>
  <script src="/js/modules/ModalManager.js"></script>
  <script src="/js/modules/ApiClient.js"></script>
  <script src="/js/main.js"></script>
  <script src="/js/mmr-calculator.js"></script>
  <script src="/js/rank-animation.js" type="module"></script>
  <script src="/js/modules/ImageCompressor.js" type="module"></script>
  <script src="/js/ultraCompressionIntegration.js" type="module"></script>
  <script src="/js/modules/LadderManager.js" type="module"></script>
  <script src="/js/modules/WC1LadderManager.js" type="module"></script>
  <script src="/js/playerDetails.js"></script>
  <!-- All JavaScript functionality moved to external modules -->
</body>
</html>
