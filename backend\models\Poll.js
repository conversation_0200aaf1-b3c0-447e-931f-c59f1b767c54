const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Poll Schema
 * 
 * Represents a poll with options and votes
 */
const PollSchema = new Schema({
  // Poll identifier
  identifier: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  
  // Poll question
  question: {
    type: String,
    required: true,
    trim: true
  },
  
  // Poll options
  options: [{
    value: {
      type: String,
      required: true
    },
    votes: {
      type: Number,
      default: 0
    }
  }],
  
  // Voters - track who has voted on this poll
  voters: [{
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    option: {
      type: String,
      required: true
    },
    votedAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Whether the poll is active
  isActive: {
    type: Boolean,
    default: true
  },
  
  // Creation date
  createdAt: {
    type: Date,
    default: Date.now
  },
  
  // Last updated date
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Static method to get a poll by identifier
PollSchema.statics.getPollByIdentifier = function(identifier) {
  return this.findOne({ identifier });
};

// Static method to vote on a poll
PollSchema.statics.vote = async function(identifier, option, userId) {
  // Find the poll
  const poll = await this.findOne({ identifier, isActive: true });
  
  if (!poll) {
    throw new Error('Poll not found or inactive');
  }

  // Check if user has already voted
  const existingVote = poll.voters.find(voter => voter.userId.toString() === userId.toString());
  if (existingVote) {
    throw new Error('User has already voted on this poll');
  }
  
  // Find the option
  const optionIndex = poll.options.findIndex(opt => opt.value === option);
  
  if (optionIndex === -1) {
    throw new Error('Invalid option');
  }
  
  // Increment vote count and add voter
  poll.options[optionIndex].votes += 1;
  poll.voters.push({
    userId,
    option,
    votedAt: new Date()
  });
  poll.updatedAt = new Date();
  
  // Save and return the updated poll
  await poll.save();
  return poll;
};

module.exports = mongoose.model('Poll', PollSchema);
