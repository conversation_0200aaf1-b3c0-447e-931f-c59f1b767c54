/*
 * Copyright (c) 2015-2016 <PERSON>h
 *
 * Permission is hereby granted, free of charge, to any person obtaining a
 * copy of this software and associated documentation files (the "Software"),
 * to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.
 */

#include "war2.h"
#include <inttypes.h>

PUDAPI Pud_Bool
war2_ppm_write(const char          *file,
               unsigned int         w,
               unsigned int         h,
               const unsigned char *data)
{
   const unsigned int size = w * h * 4u;
   FILE *f;
   unsigned int i;

   f = fopen(file, "w+");
   if (f == NULL) return PUD_FALSE;

   fprintf(f, "P3\n%u %u\n255\n", w, h);
   for (i = 0; i < size; i += 4)
     {
        fprintf(f, "%" PRIu8 " %" PRIu8 " %" PRIu8 "\n",
                data[i], data[i + 1], data[i + 2]);
     }

   fclose(f);

   return PUD_TRUE;
}
