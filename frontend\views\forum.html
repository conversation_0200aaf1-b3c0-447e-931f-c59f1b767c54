<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>WC Arena - Forum</title>
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-brands-400.woff2" as="font" type="font/woff2" crossorigin>
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>
  <link rel="stylesheet" href="/css/warcraft-app-modern.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700&display=swap" rel="stylesheet">
  
  <style>
    /* ===== WC FORUM STYLING ===== */
    
    /* Forum Container */
    .forum-main {
      max-width: 1400px;
      margin: 0 auto;
      padding: var(--space-6);
      position: relative;
      z-index: 1;
    }

    /* Forum Header */
    .forum-header {
      text-align: center;
      margin-bottom: var(--space-8);
      position: relative;
    }

    .forum-header::before {
      content: '';
      position: absolute;
      top: -20px;
      left: 50%;
      transform: translateX(-50%);
      width: 120px;
      height: 6px;
      background: linear-gradient(90deg, transparent, var(--primary-gold), transparent);
      border-radius: 3px;
    }

    .forum-title {
      font-family: var(--font-display);
      font-size: 3rem;
      font-weight: 700;
      background: linear-gradient(135deg, var(--primary-gold), var(--primary-gold-light));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin-bottom: var(--space-3);
      text-shadow: 0 0 30px rgba(212, 175, 55, 0.5);
      letter-spacing: -0.025em;
    }

    .forum-subtitle {
      color: var(--neutral-300);
      font-size: 1.125rem;
      max-width: 600px;
      margin: 0 auto;
    }

    /* Breadcrumbs */
    .forum-breadcrumbs {
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: var(--radius-lg);
      padding: var(--space-3) var(--space-4);
      margin-bottom: var(--space-6);
      display: flex;
      align-items: center;
      gap: var(--space-2);
    }

    .breadcrumb-item {
      color: var(--neutral-400);
      text-decoration: none;
      transition: var(--transition-normal);
      display: flex;
      align-items: center;
      gap: var(--space-2);
    }

    .breadcrumb-item:hover {
      color: var(--primary-gold);
    }

    .breadcrumb-separator {
      color: var(--neutral-600);
      margin: 0 var(--space-2);
    }

    /* Categories Grid */
    .categories-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: var(--space-4);
      margin-bottom: var(--space-8);
    }

    @media (max-width: 768px) {
      .categories-grid {
        grid-template-columns: 1fr;
        gap: var(--space-3);
      }
    }

    @media (max-width: 480px) {
      .categories-grid {
        grid-template-columns: 1fr;
        gap: var(--space-2);
      }
    }

    .category-card {
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: var(--radius-lg);
      padding: var(--space-4);
      transition: all var(--transition-normal);
      position: relative;
      overflow: hidden;
      cursor: pointer;
      min-height: 140px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    @media (max-width: 480px) {
      .category-card {
        padding: var(--space-3);
        min-height: 120px;
      }
    }

    .category-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, var(--primary-gold), var(--primary-gold-light));
      opacity: 0;
      transition: var(--transition-normal);
    }

    .category-card::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(212, 175, 55, 0.02), transparent 30%);
      opacity: 0;
      transition: var(--transition-normal);
      pointer-events: none;
    }

    .category-card:hover::before {
      opacity: 1;
    }

    .category-card:hover::after {
      opacity: 1;
    }

    .category-card:hover {
      transform: translateY(-2px);
      border-color: var(--primary-gold);
      box-shadow: 0 8px 32px rgba(212, 175, 55, 0.2);
    }

    @media (hover: none) {
      .category-card:hover {
        transform: none;
      }
    }

    .category-header {
      display: flex;
      align-items: flex-start;
      gap: var(--space-3);
      margin-bottom: var(--space-3);
      position: relative;
      z-index: 1;
    }

    .category-icon {
      width: 36px;
      height: 36px;
      background: linear-gradient(135deg, var(--primary-gold), var(--primary-gold-light));
      border-radius: var(--radius-md);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.125rem;
      color: var(--neutral-900);
      box-shadow: 0 3px 12px rgba(212, 175, 55, 0.3);
      flex-shrink: 0;
      transition: var(--transition-normal);
    }

    @media (max-width: 480px) {
      .category-icon {
        width: 32px;
        height: 32px;
        font-size: 1rem;
      }
    }

    .category-card:hover .category-icon {
      transform: scale(1.05);
      box-shadow: 0 4px 16px rgba(212, 175, 55, 0.4);
    }

    .category-info {
      flex: 1;
      min-width: 0;
    }

    .category-info h3 {
      font-family: var(--font-display);
      font-size: 1rem;
      font-weight: 600;
      color: var(--neutral-100);
      margin-bottom: var(--space-1);
      line-height: 1.3;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    @media (max-width: 480px) {
      .category-info h3 {
        font-size: 0.95rem;
      }
    }

    .category-description {
      color: var(--neutral-400);
      font-size: 0.8rem;
      line-height: 1.4;
      margin-bottom: var(--space-3);
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    @media (max-width: 480px) {
      .category-description {
        font-size: 0.75rem;
        -webkit-line-clamp: 1;
      }
    }

    .category-stats {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      padding-top: var(--space-2);
      border-top: 1px solid var(--glass-border);
      position: relative;
      z-index: 1;
    }

    .category-stats-left {
      display: flex;
      gap: var(--space-3);
    }

    @media (max-width: 480px) {
      .category-stats-left {
        gap: var(--space-2);
      }
    }

    .category-stat {
      display: flex;
      align-items: center;
      gap: var(--space-1);
      color: var(--neutral-400);
      font-size: 0.75rem;
      font-weight: 500;
    }

    @media (max-width: 480px) {
      .category-stat {
        font-size: 0.7rem;
      }
    }

    .category-stat i {
      color: var(--primary-gold);
      font-size: 0.75rem;
    }

    .last-post {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      text-align: right;
      flex-shrink: 0;
    }

    .last-post-author {
      color: var(--neutral-300);
      font-size: 0.75rem;
      font-weight: 500;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 80px;
    }

    @media (max-width: 480px) {
      .last-post-author {
        font-size: 0.7rem;
        max-width: 60px;
      }
    }

    .last-post-time {
      color: var(--neutral-500);
      font-size: 0.7rem;
    }

    @media (max-width: 480px) {
      .last-post-time {
        font-size: 0.65rem;
      }
    }

    /* Topics List */
    .topics-container {
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: var(--radius-xl);
      overflow: hidden;
    }

    .category-view-header {
      background: linear-gradient(135deg, var(--primary-gold), var(--primary-gold-light));
      padding: var(--space-6);
      color: var(--neutral-900);
    }

    .category-view-title {
      font-family: var(--font-display);
      font-size: 2rem;
      font-weight: 700;
      margin-bottom: var(--space-2);
    }

    .category-view-description {
      font-size: 1rem;
      opacity: 0.8;
      margin-bottom: var(--space-4);
    }

    .category-actions {
      display: flex;
      gap: var(--space-3);
      flex-wrap: wrap;
    }

    .topic-item {
      border-bottom: 1px solid var(--glass-border);
      padding: var(--space-5);
      transition: var(--transition-normal);
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: var(--space-4);
    }

    .topic-item:last-child {
      border-bottom: none;
    }

    .topic-item:hover {
      background: var(--bg-card-hover);
    }

    .topic-status {
      width: 40px;
      height: 40px;
      border-radius: var(--radius-lg);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
    }

    .topic-status.unread {
      background: linear-gradient(135deg, var(--primary-gold), var(--primary-gold-light));
      color: var(--neutral-900);
    }

    .topic-status.read {
      background: var(--neutral-700);
      color: var(--neutral-400);
    }

    .topic-status.pinned {
      background: linear-gradient(135deg, var(--alliance-blue), var(--alliance-blue-light));
      color: white;
    }

    .topic-main {
      flex: 1;
      min-width: 0;
    }

    .topic-title {
      font-size: 1.125rem;
      font-weight: 600;
      color: var(--neutral-100);
      margin-bottom: var(--space-1);
      text-decoration: none;
    }

    .topic-title:hover {
      color: var(--primary-gold);
    }

    .topic-meta {
      display: flex;
      align-items: center;
      gap: var(--space-3);
      color: var(--neutral-400);
      font-size: 0.875rem;
    }

    .topic-author {
      color: var(--neutral-300);
      font-weight: 500;
    }

    .topic-stats {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--space-2);
      min-width: 80px;
      text-align: center;
    }

    .topic-replies {
      background: var(--neutral-800);
      color: var(--primary-gold);
      padding: var(--space-1) var(--space-3);
      border-radius: var(--radius-md);
      font-weight: 600;
      font-size: 0.875rem;
    }

    .topic-views {
      color: var(--neutral-500);
      font-size: 0.75rem;
    }

    .topic-last-post {
      min-width: 150px;
      text-align: right;
    }

    /* Posts Container */
    .posts-container {
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: var(--radius-xl);
      overflow: hidden;
      margin-bottom: var(--space-6);
    }

    .topic-view-header {
      background: linear-gradient(135deg, var(--primary-gold), var(--primary-gold-light));
      padding: var(--space-6);
      color: var(--neutral-900);
    }

    .topic-view-title {
      font-family: var(--font-display);
      font-size: 1.75rem;
      font-weight: 700;
      margin-bottom: var(--space-2);
    }

    .post-item {
      border-bottom: 1px solid var(--glass-border);
      padding: var(--space-6);
      display: flex;
      gap: var(--space-4);
    }

    .post-item:last-child {
      border-bottom: none;
    }

    .post-author-section {
      min-width: 180px;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      padding: var(--space-4);
      background: var(--bg-card);
      border-radius: var(--radius-lg);
      height: fit-content;
    }

    .post-avatar {
      width: 64px;
      height: 64px;
      border-radius: 50%;
      border: 3px solid var(--primary-gold);
      margin-bottom: var(--space-3);
      object-fit: cover;
    }

    .post-author-name {
      font-weight: 600;
      color: var(--neutral-100);
      margin-bottom: var(--space-1);
    }

    .post-author-role {
      background: var(--primary-gold);
      color: var(--neutral-900);
      padding: var(--space-1) var(--space-2);
      border-radius: var(--radius-sm);
      font-size: 0.75rem;
      font-weight: 600;
      text-transform: uppercase;
      margin-bottom: var(--space-2);
    }

    .post-author-stats {
      color: var(--neutral-400);
      font-size: 0.75rem;
    }

    .post-content-section {
      flex: 1;
      min-width: 0;
    }

    .post-content {
      color: var(--neutral-200);
      line-height: 1.7;
      margin-bottom: var(--space-4);
    }

    .post-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: var(--space-3);
      border-top: 1px solid var(--glass-border);
    }

    .post-date {
      color: var(--neutral-500);
      font-size: 0.875rem;
    }

    .post-actions {
      display: flex;
      gap: var(--space-2);
    }

    .post-action-btn {
      background: none;
      border: 1px solid var(--glass-border);
      color: var(--neutral-400);
      padding: var(--space-2);
      border-radius: var(--radius-md);
      cursor: pointer;
      transition: var(--transition-normal);
    }

    .post-action-btn:hover {
      border-color: var(--primary-gold);
      color: var(--primary-gold);
    }

    /* Active states for like/dislike buttons */
    .post-action-btn.like-button.active {
      background: rgba(34, 197, 94, 0.2);
      border-color: #22c55e;
      color: #22c55e;
    }

    .post-action-btn.dislike-button.active {
      background: rgba(239, 68, 68, 0.2);
      border-color: #ef4444;
      color: #ef4444;
    }

    /* Reply Form */
    .reply-form-container {
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: var(--radius-xl);
      padding: var(--space-6);
    }

    .reply-form-title {
      font-family: var(--font-display);
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--neutral-100);
      margin-bottom: var(--space-4);
      text-align: center;
    }

    .reply-textarea {
      width: 100%;
      min-height: 120px;
      background: var(--bg-card);
      border: 1px solid var(--glass-border);
      border-radius: var(--radius-lg);
      padding: var(--space-4);
      color: var(--neutral-100);
      font-family: var(--font-primary);
      resize: vertical;
      transition: var(--transition-normal);
    }

    .reply-textarea:focus {
      outline: none;
      border-color: var(--primary-gold);
      box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
    }

    .reply-textarea::placeholder {
      color: var(--neutral-500);
    }

    /* Modals */
    .modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      backdrop-filter: blur(10px);
      display: none;
      align-items: center;
      justify-content: center;
      z-index: 10000;
    }

    .modal.active {
      display: flex;
    }

    .modal-content {
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: var(--radius-xl);
      padding: var(--space-6);
      max-width: 600px;
      width: 90%;
      position: relative;
    }

    .close-modal {
      position: absolute;
      top: var(--space-4);
      right: var(--space-4);
      background: none;
      border: none;
      color: var(--neutral-400);
      font-size: 1.5rem;
      cursor: pointer;
      transition: var(--transition-normal);
    }

    .close-modal:hover {
      color: var(--primary-gold);
    }

    .modal h2 {
      font-family: var(--font-display);
      font-size: 1.75rem;
      font-weight: 600;
      color: var(--neutral-100);
      margin-bottom: var(--space-4);
      text-align: center;
    }

    .form-group {
      margin-bottom: var(--space-4);
    }

    .form-group label {
      display: block;
      color: var(--neutral-300);
      font-weight: 500;
      margin-bottom: var(--space-2);
    }

    .form-group input,
    .form-group textarea {
      width: 100%;
      background: var(--bg-card);
      border: 1px solid var(--glass-border);
      border-radius: var(--radius-lg);
      padding: var(--space-3);
      color: var(--neutral-100);
      font-family: var(--font-primary);
      transition: var(--transition-normal);
    }

    .form-group input:focus,
    .form-group textarea:focus {
      outline: none;
      border-color: var(--primary-gold);
      box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
    }

    .form-group input::placeholder,
    .form-group textarea::placeholder {
      color: var(--neutral-500);
    }

    /* Loading States */
    .loading-spinner {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: var(--space-8);
      color: var(--neutral-400);
      font-size: 0.875rem;
    }

    .loading-spinner::before {
      content: '';
      width: 20px;
      height: 20px;
      margin-right: var(--space-2);
      border: 2px solid var(--glass-border);
      border-top: 2px solid var(--primary-gold);
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Empty State */
    .empty-state {
      text-align: center;
      padding: var(--space-8);
      color: var(--neutral-400);
    }

    .empty-state i {
      font-size: 3rem;
      margin-bottom: var(--space-4);
      color: var(--neutral-500);
    }

    .empty-state h3 {
      font-family: var(--font-display);
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--neutral-300);
      margin-bottom: var(--space-2);
    }

    .empty-state p {
      font-size: 0.875rem;
      line-height: 1.5;
      max-width: 400px;
      margin: 0 auto var(--space-4);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .forum-main {
        padding: var(--space-4);
      }

      .forum-title {
        font-size: 2rem;
      }

      .categories-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
      }

      .category-stats {
        flex-direction: column;
        gap: var(--space-2);
        align-items: stretch;
      }

      .topic-item {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-3);
      }

      .topic-stats,
      .topic-last-post {
        min-width: auto;
        text-align: left;
      }

      .post-item {
        flex-direction: column;
      }

      .post-author-section {
        min-width: auto;
        flex-direction: row;
        align-items: center;
        text-align: left;
        gap: var(--space-3);
      }

      .post-avatar {
        width: 48px;
        height: 48px;
        margin-bottom: 0;
      }

      .modal-content {
        margin: var(--space-4);
        width: calc(100% - var(--space-8));
      }
    }

    /* Hide default elements initially */
    .forum-view {
      display: none;
    }

    .forum-view.active {
      display: block;
    }
  </style>
</head>
<body>
  <!-- Navbar -->
  <div id="navbar-container"></div>

  <main class="forum-main">
    <!-- Forum Header -->
    <div class="forum-header">
      <h1 class="forum-title">WC Arena Forum</h1>
      <p class="forum-subtitle">Discuss strategies, share experiences, and connect with fellow warriors</p>
    </div>

    <!-- Breadcrumbs -->
    <nav class="forum-breadcrumbs" id="forum-breadcrumbs" style="display: none;">
      <!-- Breadcrumbs will be populated by JavaScript -->
    </nav>

    <div class="forum-container">
      <!-- Categories List View -->
      <div id="categories-view" class="forum-view active">
        <div class="loading-spinner">Loading categories...</div>
      </div>

      <!-- Category View (Topics List) -->
      <div id="category-view" class="forum-view">
        <div class="topics-container">
          <div class="category-view-header">
            <h2 id="category-name" class="category-view-title"></h2>
            <p id="category-description" class="category-view-description"></p>
            <div class="category-actions">
              <button id="new-topic-btn" class="btn btn-primary">
                <i class="fas fa-plus"></i> New Topic
              </button>
              <button id="back-to-categories" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Categories
              </button>
            </div>
          </div>
          <div id="topics-list"></div>
        </div>
      </div>

      <!-- Topic View (Posts List) -->
      <div id="topic-view" class="forum-view">
        <div class="posts-container">
          <div class="topic-view-header">
            <h2 id="topic-title-view" class="topic-view-title"></h2>
            <div class="topic-meta">
              <span id="topic-category" class="breadcrumb-link" style="cursor:pointer"></span>
              <span id="topic-date"></span>
              <span id="topic-views"></span>
              <span id="topic-replies"></span>
            </div>
            <div class="topic-actions">
              <button id="back-to-category" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Category
              </button>
              <button id="sticky-button" class="btn btn-secondary" style="display: none;">
                <i class="fas fa-thumbtack"></i> Pin Topic
              </button>
            </div>
          </div>
          <div id="posts-list"></div>
        </div>
        
        <div id="reply-form-container" class="reply-form-container">
          <h3 class="reply-form-title">Post a Reply</h3>
          <form id="reply-form">
            <div class="form-group">
              <textarea id="reply-content" class="reply-textarea" rows="5" placeholder="Share your thoughts, strategies, or experiences..."></textarea>
            </div>
            <div style="text-align: center;">
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-reply"></i> Post Reply
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </main>

  <!-- New Topic Modal -->
  <div id="new-topic-modal" class="modal">
    <div class="modal-content">
      <span class="close-modal">&times;</span>
      <h2>Create New Topic</h2>
      <form id="new-topic-form">
        <div class="form-group">
          <label for="topic-title">Title:</label>
          <input type="text" id="topic-title" placeholder="What would you like to discuss?" required>
        </div>
        <div class="form-group">
          <label for="topic-content">Content:</label>
          <textarea id="topic-content" rows="8" placeholder="Share your thoughts, ask questions, or start a discussion..." required></textarea>
        </div>
        <div style="text-align: center;">
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-plus"></i> Create Topic
          </button>
        </div>
      </form>
    </div>
  </div>

  <div id="footer-container"></div>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
  <script src="/js/utils.js"></script>
  <script src="/js/main.js"></script>
  <script src="/js/forum.js"></script>
</body>
</html>
