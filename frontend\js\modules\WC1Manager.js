/**
 * WC1Manager.js - Warcraft I Custom Scenarios Manager
 * 
 * Handles display and interaction with WC1 custom scenario images
 * Features:
 * - Category filtering (Forest, Swamp, Dungeon)
 * - Fullscreen image viewing
 * - Random scenario selection
 * - Rating and commenting system
 * - Responsive grid layout
 */

export class WC1Manager {
  constructor() {
    this.currentCategory = 'all';
    this.scenarios = [];
    this.initialized = false;
    this.currentUser = null;
  }

  /**
   * Initialize WC1 manager
   */
  async init() {
    if (this.initialized) return;
    
    console.log('🗡️ Initializing WC1 Manager...');
    
    // Get current user
    await this.loadCurrentUser();
    
    // Load scenario data from API
    await this.loadScenarios();
    
    // Setup event listeners
    this.setupEventListeners();
    
    // Render initial scenarios
    this.renderScenarios();
    
    this.initialized = true;
    console.log('✅ WC1 Manager initialized');
  }

  /**
   * Load current user data
   */
  async loadCurrentUser() {
    try {
      const response = await fetch('/api/me');
      if (response.ok) {
        this.currentUser = await response.json();
        console.log('👤 Current user loaded:', this.currentUser?.username);
      }
    } catch (error) {
      console.warn('⚠️ Could not load current user:', error);
      this.currentUser = null;
    }
  }

  /**
   * Load scenarios from API or fallback data
   */
  async loadScenarios() {
    try {
      console.log('🗡️ Attempting to load WC1 scenarios from API...');
      const response = await fetch('/api/war1maps');
      console.log('🔍 API Response status:', response.status, response.statusText);
      
      if (response.ok) {
        const responseData = await response.json();
        this.scenarios = responseData.success ? responseData.data : responseData;
        console.log(`✅ Successfully loaded ${this.scenarios.length} WC1 scenarios from API`);
        console.log('📋 First scenario:', this.scenarios[0]?._id ? 'Has valid ID' : 'Missing ID');
      } else {
        console.error(`❌ API request failed with status ${response.status}: ${response.statusText}`);
        const errorText = await response.text();
        console.error('❌ Error response:', errorText);
        await this.loadFallbackScenarios();
      }
    } catch (error) {
      console.error('❌ Network error loading scenarios:', error.message);
      console.error('❌ Full error:', error);
      await this.loadFallbackScenarios();
    }
  }

  /**
   * Load fallback scenarios (hardcoded) if API fails
   */
  async loadFallbackScenarios() {
    // Generate consistent mock IDs for fallback scenarios
    const generateMockId = (name) => {
      // Create a consistent mock ID based on the scenario name
      return 'fallback_' + name.toLowerCase().replace(' ', '_');
    };

    this.scenarios = [
      // Forest scenarios
      { _id: generateMockId('Forest 1'), name: 'Forest 1', category: 'forest', imagePath: '/uploads/war1/Forest_1.webp', averageRating: 0, ratingCount: 0 },
      { _id: generateMockId('Forest 2'), name: 'Forest 2', category: 'forest', imagePath: '/uploads/war1/Forest_2.webp', averageRating: 0, ratingCount: 0 },
      { _id: generateMockId('Forest 3'), name: 'Forest 3', category: 'forest', imagePath: '/uploads/war1/Forest_3.webp', averageRating: 0, ratingCount: 0 },
      { _id: generateMockId('Forest 4'), name: 'Forest 4', category: 'forest', imagePath: '/uploads/war1/Forest_4.webp', averageRating: 0, ratingCount: 0 },
      { _id: generateMockId('Forest 5'), name: 'Forest 5', category: 'forest', imagePath: '/uploads/war1/Forest_5.webp', averageRating: 0, ratingCount: 0 },
      { _id: generateMockId('Forest 6'), name: 'Forest 6', category: 'forest', imagePath: '/uploads/war1/Forest_6.webp', averageRating: 0, ratingCount: 0 },
      { _id: generateMockId('Forest 7'), name: 'Forest 7', category: 'forest', imagePath: '/uploads/war1/Forest_7.webp', averageRating: 0, ratingCount: 0 },
      
      // Swamp scenarios  
      { _id: generateMockId('Swamp 1'), name: 'Swamp 1', category: 'swamp', imagePath: '/uploads/war1/Swamp_1.webp', averageRating: 0, ratingCount: 0 },
      { _id: generateMockId('Swamp 2'), name: 'Swamp 2', category: 'swamp', imagePath: '/uploads/war1/Swamp_2.webp', averageRating: 0, ratingCount: 0 },
      { _id: generateMockId('Swamp 3'), name: 'Swamp 3', category: 'swamp', imagePath: '/uploads/war1/Swamp_3.webp', averageRating: 0, ratingCount: 0 },
      { _id: generateMockId('Swamp 4'), name: 'Swamp 4', category: 'swamp', imagePath: '/uploads/war1/Swamp_4.webp', averageRating: 0, ratingCount: 0 },
      { _id: generateMockId('Swamp 5'), name: 'Swamp 5', category: 'swamp', imagePath: '/uploads/war1/Swamp_5.webp', averageRating: 0, ratingCount: 0 },
      { _id: generateMockId('Swamp 6'), name: 'Swamp 6', category: 'swamp', imagePath: '/uploads/war1/Swamp_6.webp', averageRating: 0, ratingCount: 0 },
      { _id: generateMockId('Swamp 7'), name: 'Swamp 7', category: 'swamp', imagePath: '/uploads/war1/Swamp_7.webp', averageRating: 0, ratingCount: 0 },
      
      // Dungeon scenarios
      { _id: generateMockId('Dungeon 1'), name: 'Dungeon 1', category: 'dungeon', imagePath: '/uploads/war1/Dungeon_1.webp', averageRating: 0, ratingCount: 0 },
      { _id: generateMockId('Dungeon 2'), name: 'Dungeon 2', category: 'dungeon', imagePath: '/uploads/war1/Dungeon_2.webp', averageRating: 0, ratingCount: 0 },
      { _id: generateMockId('Dungeon 3'), name: 'Dungeon 3', category: 'dungeon', imagePath: '/uploads/war1/Dungeon_3.webp', averageRating: 0, ratingCount: 0 },
      { _id: generateMockId('Dungeon 4'), name: 'Dungeon 4', category: 'dungeon', imagePath: '/uploads/war1/Dungeon_4.webp', averageRating: 0, ratingCount: 0 },
      { _id: generateMockId('Dungeon 5'), name: 'Dungeon 5', category: 'dungeon', imagePath: '/uploads/war1/Dungeon_5.webp', averageRating: 0, ratingCount: 0 },
      { _id: generateMockId('Dungeon 6'), name: 'Dungeon 6', category: 'dungeon', imagePath: '/uploads/war1/Dungeon_6.webp', averageRating: 0, ratingCount: 0 },
      { _id: generateMockId('Dungeon 7'), name: 'Dungeon 7', category: 'dungeon', imagePath: '/uploads/war1/Dungeon_7.webp', averageRating: 0, ratingCount: 0 }
    ];
    
    console.log(`📋 Loaded ${this.scenarios.length} fallback WC1 scenarios with mock IDs`);
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Category tab buttons
    const categoryTabs = document.querySelectorAll('#war1-content .tab-btn');
    categoryTabs.forEach(tab => {
      tab.addEventListener('click', (e) => {
        e.preventDefault();
        const category = tab.getAttribute('data-category');
        this.switchCategory(category, tab);
      });
    });

    // Random scenario button
    const randomBtn = document.getElementById('war1-random-scenario-btn');
    if (randomBtn) {
      randomBtn.addEventListener('click', () => {
        this.showRandomScenario();
      });
    }

    console.log('🎮 WC1 event listeners setup complete');
  }

  /**
   * Switch to a different category
   */
  switchCategory(category, tabElement) {
    if (this.currentCategory === category) return;

    console.log(`🔄 Switching to WC1 category: ${category}`);
    
    // Update active tab
    const categoryTabs = document.querySelectorAll('#war1-content .tab-btn');
    categoryTabs.forEach(tab => tab.classList.remove('active'));
    if (tabElement) {
      tabElement.classList.add('active');
    }

    this.currentCategory = category;
    this.renderScenarios();
    
    console.log(`✅ Switched to ${category} category`);
  }

  /**
   * Get filtered scenarios based on current category
   */
  getFilteredScenarios() {
    if (this.currentCategory === 'all') {
      return this.scenarios;
    }
    return this.scenarios.filter(scenario => scenario.category === this.currentCategory);
  }

  /**
   * Render scenarios in the grid
   */
  renderScenarios() {
    const grid = document.getElementById('war1-scenarios-grid');
    if (!grid) return;

    const filteredScenarios = this.getFilteredScenarios();
    
    if (filteredScenarios.length === 0) {
      grid.innerHTML = this.getEmptyStateHTML();
      return;
    }

    // Create scenario cards
    const cardsHTML = filteredScenarios.map(scenario => 
      this.createScenarioCard(scenario)
    ).join('');

    grid.innerHTML = cardsHTML;

    // Setup card event listeners
    this.setupCardEventListeners();

    // FORCE UPDATE STATS COUNTER EVERY TIME WE RENDER
    console.log('🔥 FORCING stats update after rendering scenarios');
    this.updateGameStats();

    console.log(`✅ Rendered ${filteredScenarios.length} WC1 scenarios`);
  }

  /**
   * Create HTML for a scenario card
   */
  createScenarioCard(scenario) {
    const imageUrl = scenario.imagePath || scenario.image || `/uploads/war1/${scenario.name.replace(' ', '_')}.webp`;
    const rating = scenario.averageRating || 0;
    const reviewCount = scenario.ratingCount || 0;
    const scenarioId = scenario._id || scenario.id;
    
    // Generate interactive star rating for logged-in users, static for guests
    const user = this.currentUser;
    const isLoggedIn = !!user;
    let starsHTML;
    
    if (isLoggedIn && window.mapDetails && typeof window.mapDetails.generateInteractiveStarRating === 'function') {
      // Get user's current rating for this scenario
      const userRating = scenario.ratings?.find(r => 
        (r.userId === user.id) || 
        (r.userId?._id === user.id) || 
        (r.userId?.toString() === user.id?.toString())
      )?.rating || 0;
      
      // Show average rating initially, not user's personal rating
      console.log(`🌟 WC1 generating stars for ${scenario.name}: rating=${rating}, scenarioId=${scenarioId}`);
      starsHTML = window.mapDetails.generateInteractiveStarRating(rating, 'small', scenarioId);
      console.log(`🌟 WC1 generated stars HTML:`, starsHTML);
    } else {
      // Static star display for guests - show average rating
      starsHTML = `<div class="static-stars">${this.generateStarsHTML(rating)}</div>`;
    }
    
    return `
      <div class="war1-scenario-card" data-scenario-id="${scenarioId}" data-scenario-name="${scenario.name}">
        <div class="war1-scenario-image-container">
          <img src="${imageUrl}" 
               alt="${scenario.name}" 
               class="war1-scenario-image"
               loading="lazy">
          <button class="war1-fullscreen-btn" 
                  title="View ${scenario.name} Fullscreen"
                  data-image="${imageUrl}"
                  data-name="${scenario.name}">
            <i class="fas fa-expand"></i>
          </button>
        </div>
        <div class="war1-scenario-info">
          <h3 class="war1-scenario-title">${scenario.name}</h3>
          <span class="war1-scenario-category ${scenario.category}">${this.formatCategoryName(scenario.category)}</span>
          
          <!-- Rating Section -->
          <div class="war1-rating-section">
            <div class="war1-rating-display">
              <div class="war1-stars">${starsHTML}</div>
              <span class="war1-rating-value">${rating.toFixed(1)}</span>
              <span class="war1-rating-count">(${reviewCount})</span>
              ${!isLoggedIn ? '<br><small style="color: #6b7280;">Login to rate</small>' : ''}
            </div>
            <button class="war1-reviews-btn" 
                    data-scenario-id="${scenarioId}"
                    data-scenario-name="${scenario.name}">
              View Reviews
            </button>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Generate star rating HTML
   */
  generateStarsHTML(rating) {
    let starsHTML = '';
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    
    // Full stars
    for (let i = 0; i < fullStars; i++) {
      starsHTML += '<i class="fas fa-star"></i>';
    }
    
    // Half star
    if (hasHalfStar) {
      starsHTML += '<i class="fas fa-star-half-alt"></i>';
    }
    
    // Empty stars
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
    for (let i = 0; i < emptyStars; i++) {
      starsHTML += '<i class="far fa-star"></i>';
    }
    
    return starsHTML;
  }

  /**
   * Highlight stars on hover for interactive rating
   */
  highlightStarsOnHover(starsContainer, rating) {
    const stars = starsContainer.querySelectorAll('.rating-star');
    stars.forEach((star, index) => {
      const starRating = index + 1;
      if (starRating <= rating) {
        star.className = 'fas fa-star rating-star';
        star.style.color = '#ffd700';
      } else {
        star.className = 'far fa-star rating-star';
        star.style.color = '#6b7280';
      }
    });
  }

  /**
   * Reset star highlight to original state
   */
  resetStarHighlight(starsContainer) {
    const stars = starsContainer.querySelectorAll('.rating-star');
    const currentRating = parseInt(starsContainer.dataset.currentRating) || 0;
    
    stars.forEach((star, index) => {
      const starRating = index + 1;
      if (starRating <= currentRating) {
        star.className = 'fas fa-star rating-star';
        star.style.color = '#ffd700';
      } else {
        star.className = 'far fa-star rating-star';
        star.style.color = '#6b7280';
      }
    });
  }

  /**
   * Setup event listeners for scenario cards
   */
  setupCardEventListeners() {
    const scenarioCards = document.querySelectorAll('.war1-scenario-card');
    scenarioCards.forEach(card => {
      this.setupSingleCardEventListeners(card);
    });
  }

  /**
   * Setup event listeners for a single scenario card
   */
  setupSingleCardEventListeners(card) {
    // Fullscreen button
    const fullscreenBtn = card.querySelector('.war1-fullscreen-btn');
    if (fullscreenBtn) {
      fullscreenBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        const imageSrc = fullscreenBtn.getAttribute('data-image');
        const scenarioName = fullscreenBtn.getAttribute('data-name');
        this.showFullscreenImage(imageSrc, scenarioName);
      });
    }

    // Interactive star rating for logged-in users
    const interactiveStars = card.querySelector('.interactive-stars');
    if (interactiveStars && this.currentUser) {
      const newInteractiveStars = interactiveStars.cloneNode(true);
      interactiveStars.parentNode.replaceChild(newInteractiveStars, interactiveStars);

      newInteractiveStars.addEventListener('click', (e) => {
        if (e.target.classList.contains('rating-star')) {
          e.stopPropagation();
          const rating = parseInt(e.target.dataset.rating);
          const scenarioId = card.dataset.scenarioId;
          
          if (window.mapDetails && typeof window.mapDetails.handleStarClick === 'function') {
            window.mapDetails.handleStarClick(rating, scenarioId, 'wc1');
          } else {
            console.warn('MapDetails not available for rating');
          }
        }
      });
      
      newInteractiveStars.addEventListener('mouseover', (e) => {
        if (e.target.classList.contains('rating-star')) {
          const rating = parseInt(e.target.dataset.rating);
          this.highlightStarsOnHover(newInteractiveStars, rating);
        }
      });
      
      newInteractiveStars.addEventListener('mouseout', () => {
        this.resetStarHighlight(newInteractiveStars);
      });
    }

    // Review button
    const reviewBtn = card.querySelector('.war1-reviews-btn');
    if (reviewBtn) {
       const newReviewBtn = reviewBtn.cloneNode(true);
      reviewBtn.parentNode.replaceChild(newReviewBtn, reviewBtn);
      
      newReviewBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        const scenarioId = newReviewBtn.getAttribute('data-scenario-id');
        const scenarioName = newReviewBtn.getAttribute('data-scenario-name');
        this.showReviewsModal(scenarioId, scenarioName);
      });
    }
  }

  /**
   * Show rating modal for a scenario
   */
  showRatingModal(scenarioId, scenarioName) {
    if (!this.currentUser) {
      alert('Please log in to rate scenarios');
      return;
    }

    if (scenarioId && scenarioId.startsWith('fallback_')) {
      alert('Rating is not available in offline mode. Please try again when the server is accessible.');
      return;
    }

    const modalHTML = `
      <div class="modal-overlay war1-modal-overlay" id="war1-rating-modal">
        <div class="modal-content">
          <div class="modal-header">
            <h3><i class="fas fa-star"></i> Rate ${scenarioName}</h3>
            <button class="modal-close">&times;</button>
          </div>
          <div class="modal-body">
            <div class="rating-form">
              <div class="rating-stars-input">
                <span class="rating-label">Your Rating:</span>
                <div class="stars-input">
                  ${[1,2,3,4,5].map(star => 
                    `<i class="far fa-star rating-star" data-rating="${star}"></i>`
                  ).join('')}
                </div>
              </div>
              <div class="rating-comment">
                <label for="rating-comment">Comment (optional):</label>
                <textarea id="rating-comment" placeholder="Share your thoughts about this scenario..."></textarea>
              </div>
              <div class="rating-actions">
                <button class="btn-cancel">Cancel</button>
                <button class="btn-submit" disabled>Submit Rating</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);
    const modal = document.getElementById('war1-rating-modal');
    
    this.setupRatingModalEvents(modal, scenarioId);
    
    modal.style.display = 'flex';
    setTimeout(() => modal.classList.add('show'), 10);
  }

  /**
   * Setup rating modal event listeners
   */
  setupRatingModalEvents(modal, scenarioId) {
    let selectedRating = 0;
    
    const closeModal = () => {
      modal.classList.remove('show');
      setTimeout(() => modal.remove(), 300);
    };
    
    modal.querySelector('.modal-close').addEventListener('click', closeModal);
    modal.querySelector('.btn-cancel').addEventListener('click', closeModal);
    modal.addEventListener('click', (e) => {
      if (e.target === modal) closeModal();
    });
    
    const stars = modal.querySelectorAll('.rating-star');
    const submitBtn = modal.querySelector('.btn-submit');
    
    stars.forEach((star, index) => {
      star.addEventListener('click', () => {
        selectedRating = index + 1;
        
        stars.forEach((s, i) => {
          s.className = i < selectedRating ? 'fas fa-star rating-star' : 'far fa-star rating-star';
        });
        
        submitBtn.disabled = false;
      });
      
      star.addEventListener('mouseenter', () => {
        stars.forEach((s, i) => {
          if (i <= index) s.classList.add('hover');
          else s.classList.remove('hover');
        });
      });
    });
    
    modal.querySelector('.stars-input').addEventListener('mouseleave', () => {
      stars.forEach(s => s.classList.remove('hover'));
    });
    
    submitBtn.addEventListener('click', async () => {
      if (selectedRating === 0) return;
      
      const comment = modal.querySelector('#rating-comment').value;
      
      try {
        submitBtn.disabled = true;
        submitBtn.textContent = 'Submitting...';
        
        const result = await this.submitRating(scenarioId, selectedRating, comment);
        closeModal();
        
        this.updateScenarioRating(scenarioId, selectedRating, result);
        
      } catch (error) {
        alert('Failed to submit rating. Please try again.');
        submitBtn.disabled = false;
        submitBtn.textContent = 'Submit Rating';
      }
    });
  }

  /**
   * Update scenario rating in the UI immediately
   */
  updateScenarioRating(scenarioId, userRating, apiResult) {
    try {
      const scenarioCard = document.querySelector(`[data-scenario-id="${scenarioId}"]`);
      if (!scenarioCard) {
        this.loadScenarios().then(() => this.renderScenarios());
        return;
      }
      
      const scenario = this.scenarios.find(s => s._id === scenarioId);
      if (scenario) {
        const newAvgRating = apiResult.averageRating || userRating;
        const newRatingCount = apiResult.ratingCount || (scenario.ratingCount || 0) + 1;
        
        scenario.averageRating = newAvgRating;
        scenario.ratingCount = newRatingCount;
        
        if (!scenario.ratings) scenario.ratings = [];
        const existingRatingIndex = scenario.ratings.findIndex(r => r.userId === this.currentUser?.id || r.userId?._id === this.currentUser?.id);
        
        if (existingRatingIndex >= 0) {
          scenario.ratings[existingRatingIndex].rating = userRating;
        } else {
          scenario.ratings.push({ userId: this.currentUser?.id, rating: userRating });
        }
      }
      
      const starsContainer = scenarioCard.querySelector('.war1-stars');
      if (starsContainer && this.currentUser) {
        starsContainer.innerHTML = window.mapDetails.generateInteractiveStarRating(userRating, 'small', scenarioId);
      }
      
      const ratingValueEl = scenarioCard.querySelector('.war1-rating-value');
      if (ratingValueEl && scenario) {
        ratingValueEl.textContent = scenario.averageRating.toFixed(1);
      }
      
      const ratingCountEl = scenarioCard.querySelector('.war1-rating-count');
      if (ratingCountEl && scenario) {
        ratingCountEl.textContent = `(${scenario.ratingCount})`;
      }
      
      this.setupSingleCardEventListeners(scenarioCard);
      
    } catch (error) {
      this.loadScenarios().then(() => this.renderScenarios());
    }
  }

  /**
   * Submit rating to API
   */
  async submitRating(scenarioId, rating, comment) {
    if (scenarioId && scenarioId.startsWith('fallback_')) {
      throw new Error('Rating is not available in offline mode.');
    }

    const response = await fetch(`/api/war1maps/${scenarioId}/rate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ rating, comment })
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to submit rating (${response.status}): ${errorText}`);
    }
    
    return await response.json();
  }

  /**
   * Show reviews modal for a scenario
   */
  async showReviewsModal(scenarioId, scenarioName) {
    try {
      if (scenarioId && scenarioId.startsWith('fallback_')) {
        alert('Reviews are not available in offline mode.');
        return;
      }

      const response = await fetch(`/api/war1maps/${scenarioId}/ratings`);
      if (!response.ok) throw new Error('Failed to fetch reviews');
      
      const data = await response.json();
      const reviews = data.data?.ratings || data.ratings || [];
      
      const userRating = this.currentUser ? reviews.find(r => (r.userId?._id || r.userId?.id || r.userId) === this.currentUser.id) : null;

      const modalHTML = `
        <div class="modal-overlay war1-modal-overlay" id="war1-reviews-modal">
          <div class="modal-content">
            <div class="modal-header">
              <h3><i class="fas fa-comments"></i> Reviews for ${scenarioName}</h3>
              <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
              <div class="reviews-summary">
                  <div class="average-rating">
                    <span class="rating-value">${(data.data?.averageRating || 0).toFixed(1)}</span>
                    <div class="rating-stars">${this.generateStarsHTML(data.data?.averageRating || 0)}</div>
                    <span class="review-count">${(data.data?.ratingCount || 0)} review${(data.data?.ratingCount || 0) !== 1 ? 's' : ''}</span>
                  </div>
              </div>
              
              ${this.currentUser ? `
                <div class="re-rating-section">
                  <h4><i class="fas fa-star"></i> ${userRating ? 'Update Your Rating' : 'Rate This Scenario'}</h4>
                  <div class="re-rating-form">
                    <div class="stars-input">
                      ${[1,2,3,4,5].map(i => `<i class="rating-star ${userRating && i <= userRating.rating ? 'fas' : 'far'} fa-star" data-rating="${i}"></i>`).join('')}
                    </div>
                    <textarea class="rating-comment" placeholder="Add a comment...">${userRating?.comment || ''}</textarea>
                    <button class="btn-submit-re-rating" ${userRating ? '' : 'disabled'}>${userRating ? 'Update' : 'Submit'}</button>
                  </div>
                </div>
              ` : '<div class="login-prompt">Please log in to rate</div>'}

              <div class="reviews-list">
                <h4>All Reviews</h4>
                ${reviews.length > 0 ? reviews.map(review => `
                  <div class="review-item">
                    <div class="review-header">
                      <span>${review.userId?.username || 'Anonymous'}</span>
                      <div class="review-rating">${this.generateStarsHTML(review.rating)}</div>
                      <span>${new Date(review.date).toLocaleDateString()}</span>
                    </div>
                    ${review.comment ? `<p>${review.comment}</p>` : ''}
                  </div>
                `).join('') : '<p>No reviews yet.</p>'}
              </div>
            </div>
          </div>
        </div>
      `;

      document.body.insertAdjacentHTML('beforeend', modalHTML);
      const modal = document.getElementById('war1-reviews-modal');
      
      const closeModal = () => {
        modal.classList.remove('show');
        setTimeout(() => modal.remove(), 300);
      };
      
      modal.querySelector('.modal-close').addEventListener('click', closeModal);
      modal.addEventListener('click', (e) => { if (e.target === modal) closeModal(); });

      if (this.currentUser) {
        this.setupReRatingEvents(modal, scenarioId, userRating?.rating || 0);
      }
      
      modal.style.display = 'flex';
      setTimeout(() => modal.classList.add('show'), 10);
      
    } catch (error) {
      alert('Failed to load reviews.');
    }
  }

  /**
   * Setup re-rating events in the reviews modal
   */
  setupReRatingEvents(modal, scenarioId, currentRating) {
    let selectedRating = currentRating;
    
    const stars = modal.querySelectorAll('.re-rating-section .rating-star');
    const submitBtn = modal.querySelector('.btn-submit-re-rating');
    const commentTextarea = modal.querySelector('.rating-comment');
    
    stars.forEach((star, index) => {
      star.addEventListener('click', () => {
        selectedRating = index + 1;
        stars.forEach((s, i) => s.className = i < selectedRating ? 'fas fa-star rating-star' : 'far fa-star rating-star');
        submitBtn.disabled = false;
      });
    });
    
    submitBtn.addEventListener('click', async () => {
      if (selectedRating === 0) return;
      
      const comment = commentTextarea.value.trim();
      
      try {
        submitBtn.disabled = true;
        submitBtn.innerHTML = 'Submitting...';
        
        const result = await this.submitRating(scenarioId, selectedRating, comment);
        this.updateScenarioRating(scenarioId, selectedRating, result);
        
        const closeModal = () => {
          modal.classList.remove('show');
          setTimeout(() => modal.remove(), 300);
        };
        closeModal();
        
      } catch (error) {
        alert('Failed to submit rating.');
        submitBtn.disabled = false;
        submitBtn.innerHTML = currentRating > 0 ? 'Update' : 'Submit';
      }
    });
  }

  /**
   * Show fullscreen image
   */
  showFullscreenImage(imageSrc, scenarioName) {
    const existingModal = document.getElementById('war1-fullscreen-modal');
    if (existingModal) existingModal.remove();

    const modal = document.createElement('div');
    modal.id = 'war1-fullscreen-modal';
    modal.className = 'modal war1-fullscreen-modal';
    modal.innerHTML = `
      <span class="close-modal" id="close-war1-fullscreen">&times;</span>
      <div class="war1-fullscreen-container">
        <img class="war1-fullscreen-image" src="${imageSrc}" alt="${scenarioName}">
        <div class="war1-title-overlay">
          <h2>${scenarioName}</h2>
          <p>WC I Custom Scenario</p>
        </div>
      </div>
    `;
    document.body.appendChild(modal);

    setTimeout(() => modal.classList.add('show'), 10);

    const closeModal = () => {
      modal.classList.remove('show');
      setTimeout(() => modal.remove(), 300);
    };

    modal.querySelector('.close-modal').addEventListener('click', closeModal);
    modal.addEventListener('click', (e) => { if (e.target === modal) closeModal(); });
    document.addEventListener('keydown', (e) => { if (e.key === 'Escape') closeModal(); }, { once: true });
  }

  /**
   * Show random scenario
   */
  showRandomScenario() {
    if (this.scenarios.length === 0) return;
    const randomScenario = this.scenarios[Math.floor(Math.random() * this.scenarios.length)];
    const imageUrl = randomScenario.imagePath || `/uploads/war1/${randomScenario.name.replace(' ', '_')}.webp`;
    this.showFullscreenImage(imageUrl, randomScenario.name);
  }

  /**
   * Format category name for display
   */
  formatCategoryName(category) {
    if (!category) return 'Custom';
    return category.charAt(0).toUpperCase() + category.slice(1);
  }

  /**
   * Get empty state HTML
   */
  getEmptyStateHTML() {
    return `
      <div class="war1-no-scenarios">
        <h3>No Scenarios Found</h3>
        <p>Try selecting a different category.</p>
      </div>
    `;
  }

  /**
   * Get total count for a category
   */
  getCategoryCount(category) {
    if (category === 'all') return this.scenarios.length;
    return this.scenarios.filter(s => s.category === category).length;
  }

  /**
   * Update game stats for WC1
   */
  async updateGameStats() {
    if (this.scenarios.length === 0) {
      await this.loadScenarios();
    }
    
    const currentGameMapsEl = document.getElementById('current-game-maps');
    const recentUploadsEl = document.getElementById('recent-uploads');
    
    if (currentGameMapsEl) {
      currentGameMapsEl.textContent = '21';
    }
    if (recentUploadsEl) {
      recentUploadsEl.textContent = '0';
    }
    
    try {
      const response = await fetch('/api/war1maps/stats');
      if (response.ok) {
        const stats = await response.json();
        if (currentGameMapsEl && stats.totalMaps) currentGameMapsEl.textContent = stats.totalMaps;
        if (recentUploadsEl && stats.recentUploads !== undefined) recentUploadsEl.textContent = stats.recentUploads;
      }
    } catch (error) {
      //
    }
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    this.initialized = false;
    this.scenarios = [];
    this.currentCategory = 'all';
  }
}

export const wc1Manager = new WC1Manager(); 