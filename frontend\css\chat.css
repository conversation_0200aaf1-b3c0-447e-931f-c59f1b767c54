/* Chat Page Styles */

.chat-container {
  position: relative;
  display: flex;
  height: calc(100vh - 150px);
  margin: 20px;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Sidebar */
.chat-sidebar {
  width: 250px;
  background-color: rgba(30, 30, 30, 0.7);
  display: flex;
  flex-direction: column;
  border-right: 1px solid #444;
}

.chat-sidebar .chat-header {
  padding: 15px;
  border-bottom: 1px solid #444;
  background-color: rgba(40, 40, 40, 0.7);
}

.chat-sidebar h2 {
  margin: 0;
  font-size: 1.2rem;
  color: #88c0d0;
}

.user-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.user-section {
  margin-bottom: 20px;
}

.user-section h3 {
  font-size: 0.9rem;
  color: #88c0d0;
  margin: 5px 0;
  padding-bottom: 5px;
  border-bottom: 1px solid #4c566a;
}

.info-message {
  color: #4c566a;
  font-style: italic;
  font-size: 0.85rem;
  padding: 5px;
  text-align: center;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 8px 10px;
  margin-bottom: 5px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
  border-left: 3px solid transparent;
}

.user-item:hover {
  background-color: rgba(60, 60, 70, 0.7);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 10px;
  background-color: #5e81ac;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 0.9rem;
}

.user-name {
  color: #d8dee9;
  font-size: 0.95rem;
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-rank-icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

.user-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin: 0 8px;
}

.status-online {
  background-color: #a3be8c;
}

.status-away {
  background-color: #ebcb8b;
}

.status-offline {
  background-color: #4c566a;
}

/* Muted user styles */
.user-item[data-muted="true"] {
  opacity: 0.7;
  border-left: 3px solid #bf616a;
}

.user-item[data-muted="true"] .user-name {
  color: #bf616a;
}

.user-item[data-muted="true"] .user-mute-btn {
  color: #bf616a;
}

/* Mute button styles */
.user-mute-btn {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 4px;
  margin-left: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
}

.user-item:hover .user-mute-btn {
  opacity: 1;
}

.user-mute-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.user-mute-btn i {
  font-size: 0.9rem;
}

/* Muted state styles */
.user-mute-btn[data-muted="true"] {
  color: #bf616a;
}

.user-mute-btn[data-muted="true"]:hover {
  color: #bf616a;
  background-color: rgba(191, 97, 106, 0.1);
}

/* Recently active user styling */
.user-item.recently-active {
  border-left: 3px solid #5e81ac;
}

.recently-active-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #5e81ac;
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(94, 129, 172, 0.7);
  }
  70% {
    box-shadow: 0 0 0 5px rgba(94, 129, 172, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(94, 129, 172, 0);
  }
}

/* Main Chat Area */
.chat-main {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;
  position: relative;
}

.chat-main .chat-header {
  padding: 15px;
  border-bottom: 1px solid #444;
  background-color: rgba(40, 40, 40, 0.7);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 10px;
}

.chat-main h2 {
  margin: 0;
  font-size: 1.2rem;
  color: #88c0d0;
}

/* Chat Tabs */
.chat-tabs {
  display: flex;
  background-color: rgba(50, 50, 50, 0.7);
  border-bottom: 1px solid #444;
  overflow-x: auto;
  scrollbar-width: thin;
}

.chat-tab {
  padding: 10px 15px;
  cursor: pointer;
  white-space: nowrap;
  display: flex;
  align-items: center;
  border-right: 1px solid #444;
  transition: background-color 0.2s;
  position: relative;
}

.chat-tab:hover {
  background-color: rgba(60, 60, 70, 0.7);
}

.chat-tab.active {
  background-color: rgba(70, 70, 80, 0.7);
  border-bottom: 2px solid #88c0d0;
}

.chat-tab.new-message::after {
  content: '';
  position: absolute;
  top: 8px;
  right: 8px;
  width: 8px;
  height: 8px;
  background-color: #a3be8c;
  border-radius: 50%;
}

.tab-name {
  color: #d8dee9;
}

.tab-close {
  margin-left: 8px;
  color: #aaa;
  font-size: 1.2rem;
  line-height: 1;
}

.tab-close:hover {
  color: #bf616a;
}

/* Context Menu */
.context-menu {
  position: fixed;
  background-color: rgba(50, 50, 50, 0.95);
  border: 1px solid #444;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  min-width: 150px;
}

.context-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.context-menu li {
  padding: 8px 12px;
  cursor: pointer;
  color: #d8dee9;
  transition: background-color 0.2s;
}

.context-menu li:hover {
  background-color: rgba(94, 129, 172, 0.3);
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 80px;
  height: calc(100vh - 250px);
}

.welcome-message {
  text-align: center;
  padding: 15px;
  margin-bottom: 15px;
  background-color: rgba(50, 50, 50, 0.7);
  border-radius: 6px;
  color: #88c0d0;
}

.message {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
  padding: 0.5rem;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.05);
}

.message-user-info {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.message-username {
  font-weight: bold;
  color: #fff;
  margin-right: 0.5rem;
}

.mute-btn {
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  padding: 0.25rem;
  margin-left: auto;
  transition: color 0.2s;
}

.mute-btn:hover {
  color: #fff;
}

.mute-btn i {
  font-size: 1rem;
}

.message-content {
  color: #ddd;
  word-wrap: break-word;
}

/* Dark theme specific styles */
.dark-theme .message {
  background-color: rgba(255, 255, 255, 0.05);
}

.dark-theme .message:hover {
  background-color: rgba(255, 255, 255, 0.08);
}

.dark-theme .mute-btn {
  color: #666;
}

.dark-theme .mute-btn:hover {
  color: #fff;
}

/* Light theme specific styles */
.light-theme .message {
  background-color: rgba(0, 0, 0, 0.05);
}

.light-theme .message:hover {
  background-color: rgba(0, 0, 0, 0.08);
}

.light-theme .mute-btn {
  color: #999;
}

.light-theme .mute-btn:hover {
  color: #333;
}

.message-outgoing {
  align-self: flex-end;
}

.message-incoming {
  align-self: flex-start;
}

.message-meta {
  display: flex;
  margin-top: 5px;
  font-size: 0.8rem;
}

.message-outgoing .message-meta {
  justify-content: flex-end;
}

.message-sender {
  font-weight: bold;
  margin-right: 8px;
  color: #88c0d0;
}

.message-time {
  color: #aaa;
}

.system-message {
  text-align: center;
  padding: 8px;
  margin: 10px 0;
  font-size: 0.9rem;
  color: #81a1c1;
  font-style: italic;
}

.chat-input-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(30, 30, 30, 0.95);
  padding: 1rem;
  border-top: 1px solid #444;
  z-index: 100;
}

.chat-input-container form {
  display: flex;
  gap: 1rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.chat-input-container input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #444;
  border-radius: 4px;
  font-size: 1rem;
  background: rgba(50, 50, 50, 0.7);
  color: #d8dee9;
}

.chat-input-container input:focus {
  outline: none;
  border-color: #5e81ac;
}

.chat-input-container button {
  padding: 0.75rem 1.5rem;
  background: #5e81ac;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.2s;
}

.chat-input-container button:hover {
  background: #81a1c1;
}

/* Loading and error states */
.loading {
  text-align: center;
  padding: 20px;
  color: #88c0d0;
}

.error {
  text-align: center;
  padding: 20px;
  color: #bf616a;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(30, 30, 30, 0.7);
}

::-webkit-scrollbar-thumb {
  background: #4c566a;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #5e81ac;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.8);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.chat-header h2 {
  margin: 0;
  color: white;
}

#create-chat-room-btn {
  background: #3498db;
  color: white;
  border: none;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
  padding: 0;
  margin-left: 5px;
}

#create-chat-room-btn:hover {
  background: #2980b9;
}

#create-chat-room-btn:disabled {
  background: #95a5a6;
  cursor: not-allowed;
  opacity: 0.7;
}

#create-chat-room-btn:disabled:hover {
  background: #95a5a6;
}

#create-chat-room-btn i {
  font-size: 0.9rem;
  line-height: 1;
}

/* Create Chat Room Modal */
.create-chat-room-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1000;
}

.create-chat-room-modal.active {
  display: flex;
  align-items: center;
  justify-content: center;
}

.create-chat-room-content {
  background: #1a1a1a;
  padding: 2rem;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.create-chat-room-content h2 {
  color: white;
  margin-top: 0;
  margin-bottom: 1.5rem;
}

.create-chat-room-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.create-chat-room-form input {
  padding: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.05);
  color: white;
}

.create-chat-room-form input:focus {
  outline: none;
  border-color: #3498db;
}

.create-chat-room-form .form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
}

.create-chat-room-form button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.create-chat-room-form button[type="submit"] {
  background: #3498db;
  color: white;
}

.create-chat-room-form button[type="submit"]:hover {
  background: #2980b9;
}

.create-chat-room-form button[type="button"] {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.create-chat-room-form button[type="button"]:hover {
  background: rgba(255, 255, 255, 0.2);
}

.create-chat-room-form .form-group {
  margin: 1rem 0;
}

.create-chat-room-form .form-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #d8dee9;
  cursor: pointer;
}

.create-chat-room-form .form-group input[type="checkbox"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.create-chat-room-form .form-text {
  display: block;
  margin-top: 0.5rem;
  color: #88c0d0;
  font-size: 0.85rem;
  font-style: italic;
}

/* Chat Rooms Container */
.chat-rooms-container {
  background: #2c3e50;
  padding: 15px;
  margin-bottom: 15px;
  border-radius: 8px;
}

.chat-rooms-container h3 {
  color: #ecf0f1;
  margin: 0 0 10px 0;
  font-size: 1.1em;
}

.chat-room-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.chat-room-item {
  display: flex;
  align-items: center;
  padding: 8px;
  background: #34495e;
  margin-bottom: 5px;
  border-radius: 4px;
  color: #ecf0f1;
}

.chat-room-item:last-child {
  margin-bottom: 0;
}

.room-name {
  flex: 1;
  font-weight: 500;
}

.room-participants {
  margin: 0 10px;
  font-size: 0.9em;
  color: #bdc3c7;
}

.join-room-btn {
  background: #27ae60;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
  transition: background-color 0.2s;
}

.join-room-btn:hover {
  background: #219a52;
}

.join-room-btn:disabled {
  background: #95a5a6;
  cursor: not-allowed;
  opacity: 0.7;
}

.room-section {
  margin-bottom: 1rem;
}

.room-section h4 {
  color: #88c0d0;
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  padding-bottom: 0.25rem;
  border-bottom: 1px solid #4c566a;
}

.chat-room-item[data-is-private="true"] {
  background: #2c3e50;
  border-left: 3px solid #5e81ac;
}

.chat-room-item[data-is-private="true"] .room-participants {
  color: #5e81ac;
}

.chat-tab[data-is-private="true"] {
  background: rgba(94, 129, 172, 0.2);
}

.chat-tab[data-is-private="true"]:hover {
  background: rgba(94, 129, 172, 0.3);
}

.chat-tab[data-is-private="true"].active {
  background: rgba(94, 129, 172, 0.4);
}

/* Mute Button Styles */
.user-mute-btn {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 4px;
  margin-left: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
}

.user-item:hover .user-mute-btn {
  opacity: 1;
}

.user-mute-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.user-mute-btn i {
  font-size: 0.9rem;
}

/* Muted state styles */
.user-mute-btn[data-muted="true"] {
  color: #bf616a;
}

.user-mute-btn[data-muted="true"]:hover {
  color: #bf616a;
  background-color: rgba(191, 97, 106, 0.1);
}

/* Dark theme specific styles */
.dark-theme .user-mute-btn {
  color: #666;
}

.dark-theme .user-mute-btn:hover {
  color: #fff;
}

.dark-theme .user-mute-btn[data-muted="true"] {
  color: #bf616a;
}

.dark-theme .user-mute-btn[data-muted="true"]:hover {
  color: #bf616a;
  background-color: rgba(191, 97, 106, 0.1);
}

/* Light theme specific styles */
.light-theme .user-mute-btn {
  color: #999;
}

.light-theme .user-mute-btn:hover {
  color: #333;
}

.light-theme .user-mute-btn[data-muted="true"] {
  color: #bf616a;
}

.light-theme .user-mute-btn[data-muted="true"]:hover {
  color: #bf616a;
  background-color: rgba(191, 97, 106, 0.1);
}
