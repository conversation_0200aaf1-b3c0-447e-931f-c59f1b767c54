const express = require('express');
const router = express.Router();
const ForumCategory = require('../models/ForumCategory');
const ForumTopic = require('../models/ForumTopic');
const ForumPost = require('../models/ForumPost');
const FlaggedForumContent = require('../models/FlaggedForumContent');
const User = require('../models/User');

// Middleware to check if user is authenticated
function isAuthenticated(req, res, next) {
  if (req.isAuthenticated && req.isAuthenticated()) {
    return next();
  }
  res.status(401).json({ error: 'Not authenticated' });
}

// Middleware to check if user is admin
function isAdmin(req, res, next) {
  if (req.isAuthenticated && req.isAuthenticated() && req.user.role === 'admin') {
    return next();
  }
  res.status(403).json({ error: 'Not authorized' });
}

// Middleware to check if user is moderator or admin
function isModeratorOrAdmin(req, res, next) {
  if (req.isAuthenticated && req.isAuthenticated() &&
      (req.user.role === 'admin' || req.user.role === 'moderator')) {
    return next();
  }
  res.status(403).json({ error: 'Not authorized' });
}

// Middleware to check if user is the content owner or has moderator/admin privileges
function isOwnerOrModeratorOrAdmin(req, res, next) {
  if (!req.isAuthenticated || !req.isAuthenticated()) {
    return res.status(401).json({ error: 'Not authenticated' });
  }

  // If user is admin or moderator, allow access
  if (req.user.role === 'admin' || req.user.role === 'moderator') {
    return next();
  }

  // Check if user is the content owner
  const { contentType, contentId } = req.params;

  if (!contentType || !contentId) {
    return res.status(400).json({ error: 'Content type and ID are required' });
  }

  // Check ownership based on content type
  if (contentType === 'topic') {
    ForumTopic.findById(contentId)
      .then(topic => {
        if (!topic) {
          return res.status(404).json({ error: 'Topic not found' });
        }

        if (topic.author.userId.toString() === req.user._id.toString()) {
          return next();
        }

        res.status(403).json({ error: 'Not authorized' });
      })
      .catch(err => {
        console.error('Error checking topic ownership:', err);
        res.status(500).json({ error: 'Server error' });
      });
  } else if (contentType === 'post') {
    ForumPost.findById(contentId)
      .then(post => {
        if (!post) {
          return res.status(404).json({ error: 'Post not found' });
        }

        if (post.author.userId.toString() === req.user._id.toString()) {
          return next();
        }

        res.status(403).json({ error: 'Not authorized' });
      })
      .catch(err => {
        console.error('Error checking post ownership:', err);
        res.status(500).json({ error: 'Server error' });
      });
  } else {
    res.status(400).json({ error: 'Invalid content type' });
  }
}

// GET /api/forum/categories - Get all categories
router.get('/categories', async (req, res) => {
  try {
    const categories = await ForumCategory.find()
      .sort({ order: 1 })
      .lean();

    // Recalculate stats for each category
    for (const category of categories) {
      const categoryDoc = await ForumCategory.findById(category._id);
      if (categoryDoc) {
        await categoryDoc.recalculateStats();
        // Update the category object with new stats
        Object.assign(category, categoryDoc.toObject());
      }
    }

    res.json(categories);
  } catch (err) {
    console.error('Error getting forum categories:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// GET /api/forum/topics - Get all topics
router.get('/topics', async (req, res) => {
  try {
    const topics = await ForumTopic.find()
      .sort({ isPinned: -1, createdAt: -1 })
      .lean();

    res.json(topics);
  } catch (err) {
    console.error('Error getting forum topics:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// GET /api/forum/category/:id - Get a category and its topics
router.get('/category/:id', async (req, res) => {
  try {
    const categoryId = req.params.id;

    // Get category
    const category = await ForumCategory.findById(categoryId);
    if (!category) {
      return res.status(404).json({ error: 'Category not found' });
    }

    // Recalculate stats
    await category.recalculateStats();

    // Get topics in this category
    const topics = await ForumTopic.find({ category: categoryId })
      .sort({ isPinned: -1, createdAt: -1 })
      .lean();

    res.json({
      category: category.toObject(),
      topics
    });
  } catch (err) {
    console.error('Error getting category details:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// POST /api/forum/category - Create a new category (admin only)
router.post('/category', isAdmin, async (req, res) => {
  try {
    const { name, description, order } = req.body;

    if (!name || !description) {
      return res.status(400).json({ error: 'Name and description are required' });
    }

    const category = new ForumCategory({
      name,
      description,
      order: order || 0
    });

    await category.save();

    res.status(201).json(category);
  } catch (err) {
    console.error('Error creating category:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// GET /api/forum/topic/:id - Get a topic and its posts
router.get('/topic/:id', async (req, res) => {
  try {
    const topicId = req.params.id;

    // Get topic
    const topic = await ForumTopic.findById(topicId).lean();

    if (!topic) {
      return res.status(404).json({ error: 'Topic not found' });
    }

    // Get posts in this topic
    const posts = await ForumPost.find({ topic: topicId })
      .sort({ createdAt: 1 })
      .lean();

    // Increment view count
    await ForumTopic.findByIdAndUpdate(topicId, { $inc: { viewCount: 1 } });

    res.json({
      topic,
      posts
    });
  } catch (err) {
    console.error('Error getting topic details:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// POST /api/forum/topic - Create a new topic
router.post('/topic', isAuthenticated, async (req, res) => {
  try {
    const { title, content, categoryId } = req.body;

    if (!title || !content) {
      return res.status(400).json({ error: 'Title and content are required' });
    }

    // Check if category exists if provided
    if (categoryId) {
      const category = await ForumCategory.findById(categoryId);
      if (!category) {
        return res.status(404).json({ error: 'Category not found' });
      }
    }

    // Create topic
    const topic = new ForumTopic({
      title,
      category: categoryId || null,
      author: {
        userId: req.user._id,
        username: req.user.username,
        avatar: req.user.avatar,
        role: req.user.role || 'member'  // Add this line
      },
      content
    });

    await topic.save();

    // Update category stats if category is provided
    if (categoryId) {
      await ForumCategory.findByIdAndUpdate(categoryId, {
        $inc: { topicCount: 1, postCount: 1 },
        lastPost: {
          topicId: topic._id,
          userId: req.user._id,
          username: req.user.username,
          date: new Date()
        }
      });
    }

    res.status(201).json(topic);
  } catch (err) {
    console.error('Error creating topic:', err);
    res.status(500).json({ error: 'Server error' });
  }
});


// POST /api/forum/post - Create a new post (reply)
router.post('/post', isAuthenticated, async (req, res) => {
  try {
    const { topicId, content } = req.body;

    if (!topicId || !content) {
      return res.status(400).json({ error: 'Topic ID and content are required' });
    }

    // Check if topic exists and is not locked
    const topic = await ForumTopic.findById(topicId);

    if (!topic) {
      return res.status(404).json({ error: 'Topic not found' });
    }

    if (topic.isLocked) {
      return res.status(403).json({ error: 'Topic is locked' });
    }

    // Create post
    const post = new ForumPost({
      topic: topicId,
      author: {
        userId: req.user._id,
        username: req.user.username,
        avatar: req.user.avatar,
        role: req.user.role || 'member'  // Add this line
      },
      content
    });

    await post.save();

    // Update topic stats
    await ForumTopic.findByIdAndUpdate(topicId, {
      $inc: { replyCount: 1 },
      lastReply: {
        userId: req.user._id,
        username: req.user.username,
        date: new Date()
      }
    });

    // Update category stats if the topic belongs to a category
    if (topic.category) {
      await ForumCategory.findByIdAndUpdate(topic.category, {
        $inc: { postCount: 1 },
        lastPost: {
          topicId: topicId,
          userId: req.user._id,
          username: req.user.username,
          date: new Date()
        }
      });
    }

    res.status(201).json(post);
  } catch (err) {
    console.error('Error creating post:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// POST /api/forum/topic/:id/sticky - Toggle sticky status for a topic
router.post('/topic/:id/sticky', isModeratorOrAdmin, async (req, res) => {
  try {
    const topicId = req.params.id;

    // Get topic
    const topic = await ForumTopic.findById(topicId);

    if (!topic) {
      return res.status(404).json({ error: 'Topic not found' });
    }

    // Toggle sticky status
    topic.isPinned = !topic.isPinned;
    await topic.save();

    res.json({
      message: `Topic ${topic.isPinned ? 'pinned' : 'unpinned'} successfully`,
      topic
    });
  } catch (err) {
    console.error('Error toggling topic sticky status:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// POST /api/forum/topic/:id/lock - Toggle lock status for a topic
router.post('/topic/:id/lock', isModeratorOrAdmin, async (req, res) => {
  try {
    const topicId = req.params.id;

    // Get topic
    const topic = await ForumTopic.findById(topicId);

    if (!topic) {
      return res.status(404).json({ error: 'Topic not found' });
    }

    // Toggle lock status
    topic.isLocked = !topic.isLocked;
    await topic.save();

    res.json({
      message: `Topic ${topic.isLocked ? 'locked' : 'unlocked'} successfully`,
      topic
    });
  } catch (err) {
    console.error('Error toggling topic lock status:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// DELETE /api/forum/:contentType/:contentId - Delete a topic or post
router.delete('/:contentType/:contentId', isOwnerOrModeratorOrAdmin, async (req, res) => {
  try {
    const { contentType, contentId } = req.params;

    if (contentType === 'topic') {
      // Get topic
      const topic = await ForumTopic.findById(contentId);

      if (!topic) {
        return res.status(404).json({ error: 'Topic not found' });
      }

      // Store category ID before deletion
      const categoryId = topic.category;

      // Delete all posts in the topic
      await ForumPost.deleteMany({ topic: contentId });

      // Delete the topic
      await ForumTopic.findByIdAndDelete(contentId);

      // Recalculate category stats if the topic belonged to a category
      if (categoryId) {
        const category = await ForumCategory.findById(categoryId);
        if (category) {
          await category.recalculateStats();
        }
      }

      res.json({ message: 'Topic deleted successfully' });
    } else if (contentType === 'post') {
      // Get post
      const post = await ForumPost.findById(contentId);

      if (!post) {
        return res.status(404).json({ error: 'Post not found' });
      }

      // Get topic
      const topic = await ForumTopic.findById(post.topic);

      if (!topic) {
        return res.status(404).json({ error: 'Topic not found' });
      }

      // Store category ID before deletion
      const categoryId = topic.category;

      // Delete the post
      await ForumPost.findByIdAndDelete(contentId);

      // Recalculate category stats if the topic belongs to a category
      if (categoryId) {
        const category = await ForumCategory.findById(categoryId);
        if (category) {
          await category.recalculateStats();
        }
      }

      res.json({ message: 'Post deleted successfully' });
    } else {
      res.status(400).json({ error: 'Invalid content type' });
    }
  } catch (err) {
    console.error('Error deleting content:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// POST /api/forum/flag - Flag forum content as inappropriate
router.post('/flag', isAuthenticated, async (req, res) => {
  try {
    const { contentType, contentId, reason } = req.body;

    if (!contentType || !contentId || !reason) {
      return res.status(400).json({ error: 'Content type, ID, and reason are required' });
    }

    // Check if content exists
    let content;
    let topicId;
    let categoryId;
    let contentPreview;

    if (contentType === 'topic') {
      content = await ForumTopic.findById(contentId);

      if (!content) {
        return res.status(404).json({ error: 'Topic not found' });
      }

      topicId = content._id;
      categoryId = content.category;
      contentPreview = content.title;
    } else if (contentType === 'post') {
      content = await ForumPost.findById(contentId);

      if (!content) {
        return res.status(404).json({ error: 'Post not found' });
      }

      topicId = content.topic;
      categoryId = content.category;
      contentPreview = content.content.substring(0, 100) + (content.content.length > 100 ? '...' : '');

      // Get category ID if not available in post
      if (!categoryId && topicId) {
        const topic = await ForumTopic.findById(topicId);
        if (topic) {
          categoryId = topic.category;
        }
      }
    } else {
      return res.status(400).json({ error: 'Invalid content type' });
    }

    // Check if content is already flagged
    const existingFlag = await FlaggedForumContent.findOne({
      contentType,
      contentId,
      status: 'pending'
    });

    if (existingFlag) {
      return res.status(400).json({ error: 'Content is already flagged' });
    }

    // Create flagged content
    const flaggedContent = new FlaggedForumContent({
      contentType,
      contentId,
      topicId,
      categoryId,
      contentPreview,
      flaggedBy: {
        userId: req.user._id,
        username: req.user.username
      },
      reason
    });

    await flaggedContent.save();

    res.status(201).json({
      message: 'Content flagged successfully',
      flaggedContent
    });
  } catch (err) {
    console.error('Error flagging content:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// GET /api/forum/admin/flagged - Get all flagged content (admin/moderator only)
router.get('/admin/flagged', isModeratorOrAdmin, async (req, res) => {
  try {
    const flaggedContent = await FlaggedForumContent.find()
      .sort({ createdAt: -1 })
      .populate('flaggedBy.userId', 'username displayName avatar')
      .populate('reviewedBy', 'username displayName avatar')
      .populate('topicId', 'title')
      .populate('categoryId', 'name')
      .lean();

    res.json(flaggedContent);
  } catch (err) {
    console.error('Error getting flagged content:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// POST /api/forum/admin/review-flagged - Review flagged content (admin/moderator only)
router.post('/admin/review-flagged', isModeratorOrAdmin, async (req, res) => {
  try {
    const { flaggedContentId, status, reviewNotes } = req.body;

    if (!flaggedContentId || !status) {
      return res.status(400).json({ error: 'Flagged content ID and status are required' });
    }

    // Find the flagged content
    const flaggedContent = await FlaggedForumContent.findById(flaggedContentId);

    if (!flaggedContent) {
      return res.status(404).json({ error: 'Flagged content not found' });
    }

    // Update flagged content
    flaggedContent.status = status;
    flaggedContent.reviewedBy = req.user._id;
    flaggedContent.reviewedAt = new Date();
    flaggedContent.reviewNotes = reviewNotes || '';

    await flaggedContent.save();

    // If approved, delete the content
    if (status === 'approved') {
      if (flaggedContent.contentType === 'topic') {
        // Delete all posts in the topic
        await ForumPost.deleteMany({ topic: flaggedContent.contentId });

        // Get topic
        const topic = await ForumTopic.findById(flaggedContent.contentId);

        if (topic && topic.category) {
          // Update category stats
          await ForumCategory.findByIdAndUpdate(topic.category, {
            $inc: { topicCount: -1, postCount: -(topic.replyCount + 1) }
          });
        }

        // Delete the topic
        await ForumTopic.findByIdAndDelete(flaggedContent.contentId);
      } else if (flaggedContent.contentType === 'post') {
        // Get post
        const post = await ForumPost.findById(flaggedContent.contentId);

        if (post) {
          // Update topic stats
          await ForumTopic.findByIdAndUpdate(post.topic, {
            $inc: { replyCount: -1 }
          });

          // Update category stats if available
          if (post.category) {
            await ForumCategory.findByIdAndUpdate(post.category, {
              $inc: { postCount: -1 }
            });
          }

          // Delete the post
          await ForumPost.findByIdAndDelete(flaggedContent.contentId);
        }
      }
    }

    res.json({
      message: 'Flagged content reviewed successfully',
      flaggedContent
    });
  } catch (err) {
    console.error('Error reviewing flagged content:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// GET /api/forum/user/:userId/activity - Get forum activity for a user
router.get('/user/:userId/activity', async (req, res) => {
  try {
    const userId = req.params.userId;

    // Log session debug info
    console.log('Forum Activity Debug:', {
      sessionID: req.sessionID,
      isAuthenticated: req.isAuthenticated && req.isAuthenticated(),
      userParam: userId,
      user: req.user ? { id: req.user._id, username: req.user.username } : null
    });

    // Check if userId is valid
    if (!userId || userId === 'undefined' || userId === 'null') {
      console.warn('Invalid user ID provided for forum activity:', userId);
      return res.status(400).json({
        error: 'Invalid user ID',
        message: 'The user ID is undefined or invalid. Please login again.'
      });
    }

    // Validate that the userId is a valid ObjectId format
    const mongoose = require('mongoose');
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      console.warn('Invalid ObjectId format for user ID:', userId);
      return res.status(400).json({
        error: 'Invalid user ID format',
        message: 'The user ID provided is not in a valid format. Please login again.'
      });
    }

    // Get topics created by the user
    const topics = await ForumTopic.find({ 'author.userId': userId })
      .sort({ createdAt: -1 })
      .limit(10)
      .lean();

    // Get posts created by the user
    const posts = await ForumPost.find({ 'author.userId': userId })
      .sort({ createdAt: -1 })
      .limit(20)
      .populate('topic', 'title')
      .lean();

    res.json({
      topics,
      posts
    });
  } catch (err) {
    console.error('Error getting user forum activity:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// GET /api/forum/my-activity - Get forum activity for the current logged-in user
router.get('/my-activity', isAuthenticated, async (req, res) => {
  try {
    const userId = req.user._id;

    console.log('My Forum Activity Debug:', {
      sessionID: req.sessionID,
      isAuthenticated: req.isAuthenticated && req.isAuthenticated(),
      user: req.user ? { id: req.user._id, username: req.user.username } : null
    });

    // Get topics created by the user
    const topics = await ForumTopic.find({ 'author.userId': userId })
      .sort({ createdAt: -1 })
      .limit(10)
      .lean();

    // Get posts created by the user
    const posts = await ForumPost.find({ 'author.userId': userId })
      .sort({ createdAt: -1 })
      .limit(20)
      .populate('topic', 'title')
      .lean();

    // Format the response to match what the frontend expects
    res.json({
      topics,
      posts
    });
  } catch (err) {
    console.error('Error getting current user forum activity:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// POST /api/forum/post/:postId/like - Like a post
router.post('/post/:postId/like', isAuthenticated, async (req, res) => {
  try {
    const postId = req.params.postId;
    const userId = req.user._id;

    const post = await ForumPost.findById(postId);
    if (!post) {
      return res.status(404).json({ error: 'Post not found' });
    }

    // Remove from dislikes if present
    post.dislikes = post.dislikes.filter(id => id.toString() !== userId.toString());

    // Toggle like
    const likeIndex = post.likes.findIndex(id => id.toString() === userId.toString());
    if (likeIndex === -1) {
      post.likes.push(userId);
    } else {
      post.likes.splice(likeIndex, 1);
    }

    await post.save();
    res.json(post);
  } catch (err) {
    console.error('Error liking post:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// POST /api/forum/post/:postId/dislike - Dislike a post
router.post('/post/:postId/dislike', isAuthenticated, async (req, res) => {
  try {
    const postId = req.params.postId;
    const userId = req.user._id;

    const post = await ForumPost.findById(postId);
    if (!post) {
      return res.status(404).json({ error: 'Post not found' });
    }

    // Remove from likes if present
    post.likes = post.likes.filter(id => id.toString() !== userId.toString());

    // Toggle dislike
    const dislikeIndex = post.dislikes.findIndex(id => id.toString() === userId.toString());
    if (dislikeIndex === -1) {
      post.dislikes.push(userId);
    } else {
      post.dislikes.splice(dislikeIndex, 1);
    }

    await post.save();
    res.json(post);
  } catch (err) {
    console.error('Error disliking post:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// PUT /api/forum/topic/:id - Edit a topic
router.put('/topic/:id', isOwnerOrModeratorOrAdmin, async (req, res) => {
  try {
    const topicId = req.params.id;
    const { title, content } = req.body;

    if (!title && !content) {
      return res.status(400).json({ error: 'Title or content is required' });
    }

    const updateData = {};
    if (title) updateData.title = title;
    if (content) updateData.content = content;

    const topic = await ForumTopic.findByIdAndUpdate(
      topicId,
      updateData,
      { new: true }
    );

    if (!topic) {
      return res.status(404).json({ error: 'Topic not found' });
    }

    res.json(topic);
  } catch (err) {
    console.error('Error editing topic:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// PUT /api/forum/post/:id - Edit a post
router.put('/post/:id', isOwnerOrModeratorOrAdmin, async (req, res) => {
  try {
    const postId = req.params.id;
    const { content } = req.body;

    if (!content) {
      return res.status(400).json({ error: 'Content is required' });
    }

    const post = await ForumPost.findByIdAndUpdate(
      postId,
      {
        content,
        isEdited: true,
        editedAt: new Date()
      },
      { new: true }
    );

    if (!post) {
      return res.status(404).json({ error: 'Post not found' });
    }

    res.json(post);
  } catch (err) {
    console.error('Error editing post:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// POST /api/forum/topic/:topicId/sticky - Toggle sticky status (admin/moderator only)
router.post('/topic/:topicId/sticky', isModeratorOrAdmin, async (req, res) => {
  try {
    const topic = await ForumTopic.findById(req.params.topicId);
    
    if (!topic) {
      return res.status(404).json({ error: 'Topic not found' });
    }

    topic.isPinned = !topic.isPinned;
    await topic.save();

    res.json({ 
      success: true, 
      isPinned: topic.isPinned,
      message: `Topic has been ${topic.isPinned ? 'pinned' : 'unpinned'}`
    });
  } catch (error) {
    console.error('Error toggling sticky status:', error);
    res.status(500).json({ error: 'Failed to toggle sticky status' });
  }
});

// POST /api/forum/topic/:topicId/like - Like a topic
router.post('/topic/:topicId/like', isAuthenticated, async (req, res) => {
  try {
    const topicId = req.params.topicId;
    const userId = req.user._id;

    const topic = await ForumTopic.findById(topicId);
    if (!topic) {
      return res.status(404).json({ error: 'Topic not found' });
    }

    // Remove from dislikes if present
    topic.dislikes = topic.dislikes.filter(id => id.toString() !== userId.toString());

    // Toggle like
    const likeIndex = topic.likes.findIndex(id => id.toString() === userId.toString());
    if (likeIndex === -1) {
      topic.likes.push(userId);
    } else {
      topic.likes.splice(likeIndex, 1);
    }

    await topic.save();
    res.json(topic);
  } catch (err) {
    console.error('Error liking topic:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// POST /api/forum/topic/:topicId/dislike - Dislike a topic
router.post('/topic/:topicId/dislike', isAuthenticated, async (req, res) => {
  try {
    const topicId = req.params.topicId;
    const userId = req.user._id;

    const topic = await ForumTopic.findById(topicId);
    if (!topic) {
      return res.status(404).json({ error: 'Topic not found' });
    }

    // Remove from likes if present
    topic.likes = topic.likes.filter(id => id.toString() !== userId.toString());

    // Toggle dislike
    const dislikeIndex = topic.dislikes.findIndex(id => id.toString() === userId.toString());
    if (dislikeIndex === -1) {
      topic.dislikes.push(userId);
    } else {
      topic.dislikes.splice(dislikeIndex, 1);
    }

    await topic.save();
    res.json(topic);
  } catch (err) {
    console.error('Error disliking topic:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

module.exports = router;
