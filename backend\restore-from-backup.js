const { exec } = require('child_process');
const path = require('path');
const mongoose = require('mongoose');
require('dotenv').config();

/**
 * Database restoration script
 * This script will:
 * 1. Import campaigns and forum categories from the June 2nd backup
 * 2. Delete current players, matches, and maps from the database
 * 3. Keep the site structure intact
 */

const BACKUP_PATH = path.join(__dirname, '../dbbackups/backup_newsite_2025-06-02_20-26-13-314Z/newsite');
const DB_NAME = 'newsite';
const MONGO_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017';

async function connectToDatabase() {
    try {
        await mongoose.connect(MONGO_URI, {
            useNewUrlParser: true,
            useUnifiedTopology: true,
        });
        console.log('✅ Connected to MongoDB');
    } catch (error) {
        console.error('❌ Failed to connect to MongoDB:', error);
        process.exit(1);
    }
}

async function deleteCollections(collections) {
    console.log('\n🗑️ Cleaning up current database...');
    
    for (const collectionName of collections) {
        try {
            const collection = mongoose.connection.db.collection(collectionName);
            const result = await collection.deleteMany({});
            console.log(`✅ Deleted ${result.deletedCount} documents from '${collectionName}' collection`);
        } catch (error) {
            console.error(`❌ Error deleting from '${collectionName}':`, error.message);
        }
    }
}

async function restoreCollection(collectionName) {
    return new Promise((resolve, reject) => {
        const backupFile = path.join(BACKUP_PATH, `${collectionName}.bson`);
        const command = `mongorestore --host="localhost:27017" --db="${DB_NAME}" --collection="${collectionName}" --drop "${backupFile}"`;
        
        console.log(`🔄 Restoring ${collectionName} from backup...`);
        console.log(`Command: ${command}`);
        
        exec(command, (error, stdout, stderr) => {
            if (error) {
                console.error(`❌ Failed to restore ${collectionName}:`, error.message);
                reject(error);
                return;
            }
            
            console.log(`✅ Successfully restored ${collectionName}`);
            if (stdout) console.log(`Output: ${stdout.trim()}`);
            resolve();
        });
    });
}

async function main() {
    console.log('🚀 Starting database restoration process...');
    console.log('📁 Backup Path:', BACKUP_PATH);
    console.log('🗄️ Database:', DB_NAME);
    console.log('🔗 MongoDB URI:', MONGO_URI);
    
    // Connect to database
    await connectToDatabase();
    
    try {
        // Step 1: Delete current data for a clean slate
        console.log('\n📋 Step 1: Cleaning current database...');
        const collectionsToClean = ['players', 'matches', 'maps'];
        await deleteCollections(collectionsToClean);
        
        // Step 2: Import campaigns and forum categories from backup
        console.log('\n📋 Step 2: Restoring data from backup...');
        const collectionsToRestore = ['campaigns', 'forumcategories'];
        
        for (const collection of collectionsToRestore) {
            await restoreCollection(collection);
        }
        
        console.log('\n🎉 Database restoration completed successfully!');
        console.log('\n📊 Summary:');
        console.log('==================');
        console.log('✅ Cleaned collections: players, matches, maps');
        console.log('✅ Restored collections: campaigns, forumcategories');
        console.log('✅ Site structure preserved');
        
        // Verify the restoration
        console.log('\n🔍 Verifying restoration...');
        const db = mongoose.connection.db;
        
        const campaignsCount = await db.collection('campaigns').countDocuments();
        const forumCategoriesCount = await db.collection('forumcategories').countDocuments();
        const playersCount = await db.collection('players').countDocuments();
        const matchesCount = await db.collection('matches').countDocuments();
        const mapsCount = await db.collection('maps').countDocuments();
        
        console.log(`📈 Campaigns: ${campaignsCount} documents`);
        console.log(`📈 Forum Categories: ${forumCategoriesCount} documents`);
        console.log(`📈 Players: ${playersCount} documents (should be 0)`);
        console.log(`📈 Matches: ${matchesCount} documents (should be 0)`);
        console.log(`📈 Maps: ${mapsCount} documents (should be 0)`);
        
    } catch (error) {
        console.error('❌ Restoration failed:', error);
        process.exit(1);
    } finally {
        await mongoose.connection.close();
        console.log('👋 Database connection closed');
    }
}

// Check if mongorestore is available
console.log('🔍 Checking MongoDB tools...');
exec('mongorestore --version', (error, stdout, stderr) => {
    if (error) {
        console.error('❌ MongoDB tools not found!');
        console.error('Please install MongoDB Database Tools:');
        console.error('https://www.mongodb.com/try/download/database-tools');
        console.error('');
        console.error('Or install via chocolatey: choco install mongodb-database-tools');
        process.exit(1);
    } else {
        console.log('✅ MongoDB tools found:', stdout.trim());
        console.log('');
        main().catch(console.error);
    }
}); 