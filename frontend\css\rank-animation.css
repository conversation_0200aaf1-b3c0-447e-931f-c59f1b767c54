/* Rank Animation Styles */
#rank-animation-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  background-color: rgba(0, 0, 0, 0.85);
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.5s ease;
}

#rank-animation-container.visible {
  opacity: 1;
  pointer-events: auto;
}

.rank-animation {
  text-align: center;
  color: white;
  max-width: 80%;
  animation: scaleIn 0.5s ease forwards;
}

.rank-animation h2 {
  font-size: 3rem;
  margin-bottom: 1rem;
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.7);
  animation: pulse 2s infinite alternate;
}

.rank-animation-images {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 2rem 0;
}

.rank-image {
  width: 150px;
  height: 150px;
  object-fit: contain;
  transition: transform 0.8s ease, opacity 0.8s ease;
}

.rank-image.old {
  opacity: 0.5;
  transform: scale(0.8) translateX(-50px);
}

.rank-image.new {
  opacity: 1;
  transform: scale(1.2) translateX(50px);
  animation: glow 2s infinite alternate;
}

.rank-arrow {
  font-size: 3rem;
  margin: 0 2rem;
  color: gold;
  animation: arrowPulse 1s infinite alternate;
}

.rank-up .rank-arrow {
  color: #4CAF50;
}

.rank-down .rank-arrow {
  color: #F44336;
}

.rank-animation-message {
  font-size: 1.5rem;
  margin: 1rem 0;
  animation: fadeIn 1s ease;
}

.rank-animation-button {
  background: linear-gradient(135deg, #88c0d0, #81a1c1);
  color: white;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.2rem;
  border-radius: 8px;
  cursor: pointer;
  margin-top: 2rem;
  transition: all 0.3s ease;
  animation: fadeIn 1.5s ease;
}

.rank-animation-button:hover {
  background: linear-gradient(135deg, #81a1c1, #88c0d0);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Achievement animation styles */
.achievement-unlock {
  position: absolute;
  top: 20px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.95);
  color: white;
  padding: 1rem;
  border-radius: 8px;
  max-width: 300px;
  box-shadow: 0 0 15px rgba(255, 215, 0, 0.7);
  border: 2px solid gold;
  animation: achievementSlideIn 0.5s ease, achievementGlow 2s infinite alternate;
  z-index: 9999;
}

/* Black overlay behind achievements */
.achievement-container {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 9998;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.achievement-container.visible {
  opacity: 1;
}

.achievement-unlock h3 {
  color: gold;
  margin-bottom: 0.5rem;
}

.achievement-unlock h4 {
  color: #88c0d0;
  margin-bottom: 0.5rem;
}

.achievement-unlock p {
  margin-bottom: 0.5rem;
}

.achievement-unlock .achievement-rewards {
  color: gold;
  font-weight: bold;
}

/* Animation keyframes */
@keyframes scaleIn {
  from { transform: scale(0.8); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes pulse {
  from { text-shadow: 0 0 10px rgba(255, 255, 255, 0.5); }
  to { text-shadow: 0 0 20px rgba(255, 255, 255, 0.9), 0 0 30px rgba(136, 192, 208, 0.8); }
}

@keyframes glow {
  from { filter: drop-shadow(0 0 5px rgba(136, 192, 208, 0.5)); }
  to { filter: drop-shadow(0 0 15px rgba(136, 192, 208, 0.9)); }
}

@keyframes arrowPulse {
  from { transform: scale(1); }
  to { transform: scale(1.2); }
}

@keyframes achievementSlideIn {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes achievementGlow {
  from { box-shadow: 0 0 5px rgba(255, 215, 0, 0.5); }
  to { box-shadow: 0 0 15px rgba(255, 215, 0, 0.8); }
}
