/* ===== ENHANCED PLAYER CARD STYLES ===== */
/* Clean, focused CSS for the new row-based player card layout */

/* ===== CLICKABLE PLAYER CARDS ===== */
.player-card-clickable {
  background: linear-gradient(135deg, rgba(30, 30, 30, 0.95), rgba(40, 40, 40, 0.95));
  border: 2px solid rgba(79, 70, 229, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  flex: 1;
}

.player-card-clickable::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #4f46e5, #7c3aed);
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.player-card-clickable:hover {
  border-color: rgba(79, 70, 229, 0.8);
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(79, 70, 229, 0.25);
}

.player-card-clickable:hover::before {
  opacity: 1;
}

.player-basic-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.player-name-rank {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
  min-width: 120px;
}

.player-rank-image {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  object-fit: cover;
}

.player-basic-info .player-name {
  font-size: 1.25rem;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.player-basic-info .player-rank {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(79, 70, 229, 0.2);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  border: 1px solid rgba(79, 70, 229, 0.3);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  align-self: flex-start;
}

.player-mmr-section {
  display: flex;
  align-items: baseline;
  gap: 0.5rem;
  background: rgba(79, 70, 229, 0.1);
  padding: 0.75rem 1rem;
  border-radius: 8px;
  border: 1px solid rgba(79, 70, 229, 0.2);
}

.player-mmr-section .mmr-value {
  font-size: 1.5rem;
  font-weight: 800;
  color: #4f46e5;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.player-mmr-section .mmr-label {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
}

.player-stats-row {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  padding-top: 0.75rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  flex: 1;
}

.stat-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #ffffff;
}

.stat-label {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.player-action-hint {
  font-size: 0.8rem;
  color: rgba(79, 70, 229, 0.8);
  text-align: center;
  padding: 0.5rem;
  background: rgba(79, 70, 229, 0.1);
  border-radius: 6px;
  border: 1px dashed rgba(79, 70, 229, 0.3);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  opacity: 0.7;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.player-card-clickable:hover .player-action-hint {
  opacity: 1;
  color: #4f46e5;
  border-color: rgba(79, 70, 229, 0.6);
  background: rgba(79, 70, 229, 0.15);
}

/* ===== NO PLAYERS MESSAGE ===== */
.no-activity-message {
  text-align: center;
  padding: 3rem 2rem;
  color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.02);
  border: 1px dashed rgba(255, 255, 255, 0.1);
  border-radius: 12px;
}

.no-activity-message i {
  font-size: 3rem;
  color: rgba(79, 70, 229, 0.5);
  margin-bottom: 1rem;
}

.no-activity-message h3 {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.5rem;
  font-size: 1.25rem;
}

.no-activity-message p {
  margin-bottom: 1.5rem;
  font-style: italic;
}

.activity-actions .btn {
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.activity-actions .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(79, 70, 229, 0.3);
}

/* ===== PLAYER CARD CONTAINER ===== */
.player-card-container {
  position: relative;
  display: flex;
  background: linear-gradient(135deg, rgba(30, 30, 30, 0.95), rgba(40, 40, 40, 0.95));
  border: 2px solid rgba(79, 70, 229, 0.3);
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  gap: 0.75rem;
  flex: 1;
  max-height: 120px;
}

.player-card-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #4f46e5, #7c3aed);
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.player-card-container:hover {
  border-color: rgba(79, 70, 229, 0.8);
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(79, 70, 229, 0.25);
}

.player-card-container:hover::before {
  opacity: 1;
}

/* ===== CLICKABLE PLAYER CARDS ===== */
.player-card-clickable {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
}

.player-card-clickable .player-basic-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.player-card-clickable .rank-icon {
  width: 36px;
  height: 36px;
  border-radius: 6px;
  object-fit: cover;
  border: 2px solid rgba(79, 70, 229, 0.3);
}

.player-card-clickable .player-name {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
  flex: 1;
}

.player-card-clickable .player-rank {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(79, 70, 229, 0.2);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.player-card-clickable .player-mmr-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.75rem;
  background: rgba(79, 70, 229, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(79, 70, 229, 0.2);
}

.player-card-clickable .mmr-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: #4f46e5;
  line-height: 1;
}

.player-card-clickable .mmr-label {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.player-card-clickable .player-stats-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.player-card-clickable .stat-item {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.05);
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.player-card-clickable .action-hint {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: rgba(79, 70, 229, 0.8);
  background: rgba(79, 70, 229, 0.1);
  padding: 0.75rem;
  border-radius: 6px;
  border: 1px solid rgba(79, 70, 229, 0.2);
  transition: all 0.3s ease;
}

.player-card-container:hover .action-hint {
  background: rgba(79, 70, 229, 0.2);
  color: rgba(79, 70, 229, 1);
}

/* ===== UNLINK BUTTON ===== */
.player-unlink-action {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  opacity: 0;
  transform: translateX(10px);
  transition: all 0.3s ease;
}

.player-card-container:hover .player-unlink-action {
  opacity: 1;
  transform: translateX(0);
}

.btn-unlink {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border: none;
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  min-height: 40px;
  font-size: 1rem;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.btn-unlink:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

.btn-unlink:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(239, 68, 68, 0.3);
}

.btn-unlink:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-unlink i {
  font-size: 1rem;
}

/* ===== NO PLAYERS MESSAGE ===== */
.no-players-message {
  text-align: center;
  padding: 3rem 2rem;
  color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.02);
  border: 1px dashed rgba(255, 255, 255, 0.1);
  border-radius: 12px;
}

.no-players-message i {
  font-size: 3rem;
  color: rgba(79, 70, 229, 0.5);
  margin-bottom: 1rem;
}

.no-players-message h3 {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.5rem;
  font-size: 1.25rem;
}

.no-players-message p {
  margin-bottom: 1.5rem;
  font-style: italic;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .player-card-container {
    padding: 1rem;
    gap: 0.75rem;
  }
  
  .player-card-clickable .player-basic-info {
    gap: 0.75rem;
  }
  
  .player-card-clickable .rank-icon {
    width: 40px;
    height: 40px;
  }
  
  .player-card-clickable .player-name {
    font-size: 1.125rem;
  }
  
  .player-card-clickable .mmr-value {
    font-size: 1.25rem;
  }
  
  .player-card-clickable .player-stats-row {
    gap: 0.5rem;
  }
  
  .player-card-clickable .stat-item {
    font-size: 0.8rem;
    padding: 0.375rem 0.5rem;
  }
  
  .btn-unlink {
    min-width: 36px;
    min-height: 36px;
    padding: 0.5rem;
  }
  
  .player-unlink-action {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Race Performance Cards */
.race-stat-card {
  background: linear-gradient(135deg, rgba(30, 30, 30, 0.95), rgba(40, 40, 40, 0.95));
  border: 1px solid rgba(79, 70, 229, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.race-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #4f46e5, #7c3aed);
}

.race-stat-card:hover {
  border-color: rgba(79, 70, 229, 0.6);
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(79, 70, 229, 0.2);
}

.race-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.race-icon {
  font-size: 1.5rem;
  color: #4f46e5;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(79, 70, 229, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(79, 70, 229, 0.3);
}

.race-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.race-stats {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.race-stats .stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
}

.race-stats .stat-label {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.race-stats .stat-value {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
}

.race-stats .stat-value.good {
  color: #22c55e;
}

.race-stats .stat-value.average {
  color: #f59e0b;
}

.race-stats .stat-value.poor {
  color: #ef4444;
}

/* Map Performance Cards */
.map-stat-card {
  background: linear-gradient(135deg, rgba(30, 30, 30, 0.95), rgba(40, 40, 40, 0.95));
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.map-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #22c55e, #16a34a);
}

.map-stat-card:hover {
  border-color: rgba(34, 197, 94, 0.6);
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(34, 197, 94, 0.2);
}

.map-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.map-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffffff;
  text-transform: capitalize;
}

.map-stats {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.map-stats .stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
}

.map-stats .stat-label {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.map-stats .stat-value {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
}

.map-stats .stat-value.good {
  color: #22c55e;
}

.map-stats .stat-value.average {
  color: #f59e0b;
}

.map-stats .stat-value.poor {
  color: #ef4444;
}

/* Performance Grid Layout */
.performance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  padding: 0;
}

/* Loading & No Data States */
.performance-grid .loading,
.performance-grid .no-data {
  grid-column: 1 / -1;
  text-align: center;
  padding: 3rem 2rem;
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
}

.performance-grid .no-data {
  background: rgba(255, 255, 255, 0.02);
  border: 1px dashed rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .performance-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .race-stat-card,
  .map-stat-card {
    padding: 1rem;
  }
  
  .race-header,
  .map-header {
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
  }
  
  .race-stats,
  .map-stats {
    gap: 0.5rem;
  }
} 
