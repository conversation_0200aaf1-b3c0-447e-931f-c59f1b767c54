@echo off
echo.
echo =======================================================
echo  WC2 Strategic Analysis Server - Enhanced
echo =======================================================
echo.

echo 🔄 Cleaning up any running servers...
echo Killing any running Node.js processes...
taskkill /f /im node.exe >nul 2>&1
if errorlevel 1 (
    echo ✅ No existing Node.js processes found
) else (
    echo ✅ Killed existing Node.js processes
)

echo.
echo 🧹 Clearing npm cache...
call npm cache clean --force >nul 2>&1

echo.
echo 📂 Navigating to backend directory...
cd /d "%~dp0backend"

if not exist "package.json" (
    echo ❌ ERROR: package.json not found in backend directory
    echo Please make sure you're running this from the project root
    pause
    exit /b 1
)

echo.
echo 📦 Installing/updating dependencies...
call npm install

echo.
echo 🚀 Starting the enhanced server...
echo.
echo 🌐 Server will be available at:
echo    - Enhanced Map Viewer: http://localhost:3000/enhanced-map-viewer.html
echo    - Main Site: http://localhost:3000
echo    - Maps Page: http://localhost:3000/views/maps.html
echo.
echo 📝 Features Available:
echo    - ✅ Enhanced strategic thumbnails with goldmine/player overlays
echo    - ✅ Fixed terrain rendering with correct grass/tree ratios
echo    - ✅ All goldmines with correct gold amounts (30k-65k)
echo    - ✅ Goldmine hover tooltips with categories
echo    - ✅ Proximity analysis to starting positions
echo    - ✅ Interactive legends and fullscreen modal view
echo.
echo 💡 Press Ctrl+C to stop the server
echo.

call npm start

echo.
echo 🛑 Server stopped.
pause 