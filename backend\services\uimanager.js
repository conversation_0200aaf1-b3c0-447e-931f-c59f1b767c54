// ui-manager.js
const UIManager = {
  initDOMRefs() {
    this.loginPage = document.getElementById('login-page');
    this.appPage = document.getElementById('app-page');
    this.avatar = document.getElementById('user-avatar');
    this.username = document.getElementById('user-username');
    this.adminLink = document.getElementById('admin-link');
  },

  showLogin() {
    this.loginPage.style.display = 'flex';
    this.appPage.style.display = 'none';
  },

  showApp(user) {
    this.loginPage.style.display = 'none';
    this.appPage.style.display = 'flex';
    this.updateUser(user);
  },

  updateUser(user) {
    this.avatar.textContent = user.avatar || user.username.charAt(0) || 'U';
    this.username.textContent = user.username || 'User';
    this.adminLink.style.display = user.isAdmin ? 'block' : 'none';
  }
};

export default UIManager;
