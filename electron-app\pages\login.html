<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WC Arena - Login</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            position: relative;
        }

        /* Animated background */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"><animate attributeName="opacity" values="0;1;0" dur="3s" repeatCount="indefinite"/></circle><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"><animate attributeName="opacity" values="0;1;0" dur="4s" repeatCount="indefinite"/></circle><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"><animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite"/></circle></svg>') repeat;
            animation: float 20s linear infinite;
        }

        @keyframes float {
            0% { transform: translateY(0); }
            100% { transform: translateY(-100px); }
        }

        .login-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 400px;
            width: 100%;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            font-weight: bold;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        h1 {
            color: #ffffff;
            margin-bottom: 10px;
            font-size: 28px;
            font-weight: 300;
        }

        .subtitle {
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 30px;
            font-size: 14px;
        }

        .oauth-buttons {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 30px;
        }

        .oauth-btn {
            padding: 15px 20px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            position: relative;
            overflow: hidden;
        }

        .oauth-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .oauth-btn:active {
            transform: translateY(0);
        }

        .oauth-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .oauth-btn.google {
            background: linear-gradient(45deg, #4285f4, #34a853);
            color: white;
        }

        .oauth-btn.discord {
            background: linear-gradient(45deg, #5865f2, #7289da);
            color: white;
        }

        .oauth-btn.twitch {
            background: linear-gradient(45deg, #9146ff, #6441a5);
            color: white;
        }

        .oauth-btn .icon {
            font-size: 20px;
        }

        .loading {
            display: none;
            margin: 20px auto;
        }

        .loading.active {
            display: block;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid #fff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            background: rgba(255, 59, 48, 0.2);
            border: 1px solid rgba(255, 59, 48, 0.5);
            color: #ff3b30;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            display: none;
            font-size: 14px;
        }

        .error-message.active {
            display: block;
        }

        .settings-link {
            color: rgba(255, 255, 255, 0.6);
            text-decoration: none;
            font-size: 12px;
            transition: color 0.3s ease;
        }

        .settings-link:hover {
            color: rgba(255, 255, 255, 0.9);
        }

        .server-status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 8px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .server-status.online {
            background: rgba(52, 199, 89, 0.2);
            color: #34c759;
            border: 1px solid rgba(52, 199, 89, 0.3);
        }

        .server-status.offline {
            background: rgba(255, 59, 48, 0.2);
            color: #ff3b30;
            border: 1px solid rgba(255, 59, 48, 0.3);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: currentColor;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">⚔️</div>
        <h1>WC Arena</h1>
        <p class="subtitle">Choose your login method</p>
        
        <div class="error-message" id="errorMessage"></div>
        
        <div class="oauth-buttons" id="oauthButtons">
            <button class="oauth-btn google" data-provider="google">
                <span class="icon">🔍</span>
                Continue with Google
            </button>
            <button class="oauth-btn discord" data-provider="discord">
                <span class="icon">💬</span>
                Continue with Discord
            </button>
            <button class="oauth-btn twitch" data-provider="twitch">
                <span class="icon">📺</span>
                Continue with Twitch
            </button>
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p style="color: rgba(255, 255, 255, 0.8); margin-top: 10px;">Authenticating...</p>
        </div>

        <div class="server-status" id="serverStatus">
            <div class="status-dot"></div>
            <span>Checking server connection...</span>
        </div>

        <a href="#" class="settings-link" onclick="openSettings()">Settings</a>
    </div>

    <script>
        class ElectronLogin {
            constructor() {
                this.availableProviders = [];
                this.isLoading = false;
                this.serverUrl = 'http://localhost:3000';
                
                this.init();
            }

            async init() {
                try {
                    // Get configuration from main process
                    const config = await window.electronAPI.config.get();
                    this.serverUrl = config.serverUrl;
                    
                    // Check server status and available OAuth providers
                    await this.checkServerStatus();
                    
                    // Set up event listeners
                    this.setupEventListeners();
                    
                    console.log('Electron login initialized');
                } catch (error) {
                    console.error('Failed to initialize login:', error);
                    this.showError('Failed to initialize application. Please check your connection.');
                }
            }

            setupEventListeners() {
                // OAuth button clicks
                document.querySelectorAll('.oauth-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const provider = e.currentTarget.dataset.provider;
                        this.handleOAuthLogin(provider);
                    });
                });

                // Window focus event
                window.electronAPI.onWindowFocus = () => {
                    // Refresh server status when window gains focus
                    this.checkServerStatus();
                };
            }

            async checkServerStatus() {
                try {
                    const response = await fetch(`${this.serverUrl}/auth/config`);
                    
                    if (response.ok) {
                        const data = await response.json();
                        this.availableProviders = Object.keys(data.availableStrategies)
                            .filter(key => data.availableStrategies[key]);
                        
                        this.updateServerStatus(true);
                        this.updateOAuthButtons();
                    } else {
                        throw new Error('Server responded with error');
                    }
                } catch (error) {
                    console.error('Server check failed:', error);
                    this.updateServerStatus(false);
                    this.disableOAuthButtons();
                }
            }

            updateServerStatus(isOnline) {
                const statusElement = document.getElementById('serverStatus');
                
                if (isOnline) {
                    statusElement.className = 'server-status online';
                    statusElement.innerHTML = '<div class="status-dot"></div><span>Server online</span>';
                } else {
                    statusElement.className = 'server-status offline';
                    statusElement.innerHTML = '<div class="status-dot"></div><span>Server offline</span>';
                }
            }

            updateOAuthButtons() {
                document.querySelectorAll('.oauth-btn').forEach(btn => {
                    const provider = btn.dataset.provider;
                    btn.disabled = !this.availableProviders.includes(provider);
                    
                    if (!this.availableProviders.includes(provider)) {
                        btn.style.opacity = '0.4';
                        btn.title = `${provider.charAt(0).toUpperCase() + provider.slice(1)} OAuth is not configured`;
                    }
                });
            }

            disableOAuthButtons() {
                document.querySelectorAll('.oauth-btn').forEach(btn => {
                    btn.disabled = true;
                    btn.style.opacity = '0.4';
                    btn.title = 'Server is offline';
                });
            }

            async handleOAuthLogin(provider) {
                if (this.isLoading) return;
                
                try {
                    this.setLoading(true);
                    this.hideError();
                    
                    console.log(`Starting OAuth login with ${provider}`);
                    
                    const result = await window.electronAPI.auth.login(provider);
                    
                    if (result.success) {
                        console.log('OAuth login successful:', result.user);
                        // The main process will handle the redirect to the main app
                    } else {
                        throw new Error(result.error || 'Authentication failed');
                    }
                } catch (error) {
                    console.error('OAuth login failed:', error);
                    this.showError(error.message || 'Authentication failed. Please try again.');
                } finally {
                    this.setLoading(false);
                }
            }

            setLoading(loading) {
                this.isLoading = loading;
                
                const loadingElement = document.getElementById('loading');
                const buttonsElement = document.getElementById('oauthButtons');
                
                if (loading) {
                    loadingElement.classList.add('active');
                    buttonsElement.style.display = 'none';
                } else {
                    loadingElement.classList.remove('active');
                    buttonsElement.style.display = 'flex';
                }
            }

            showError(message) {
                const errorElement = document.getElementById('errorMessage');
                errorElement.textContent = message;
                errorElement.classList.add('active');
            }

            hideError() {
                const errorElement = document.getElementById('errorMessage');
                errorElement.classList.remove('active');
            }
        }

        // Global functions
        async function openSettings() {
            const result = await window.electronAPI.dialog.showMessage({
                type: 'info',
                title: 'Settings',
                message: 'Settings functionality coming soon!',
                detail: 'This will allow you to configure server URL and other preferences.',
                buttons: ['OK']
            });
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new ElectronLogin();
        });
    </script>
</body>
</html> 