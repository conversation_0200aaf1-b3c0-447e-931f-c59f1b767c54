/**
 * Admin Panel JavaScript
 * Basic admin functionality and utilities
 */

console.log('📋 Admin.js loaded');

// Global admin utilities
window.adminUtils = {
  
  /**
   * Show loading state
   */
  showLoading(message = 'Loading...') {
    const overlay = document.getElementById('loading-overlay');
    const messageEl = document.getElementById('loading-message');
    
    if (overlay) {
      overlay.style.display = 'flex';
    }
    
    if (messageEl) {
      messageEl.textContent = message;
    }
  },
  
  /**
   * Hide loading state
   */
  hideLoading() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
      overlay.style.display = 'none';
    }
  },
  
  /**
   * Show notification
   */
  showNotification(message, type = 'info') {
    const container = document.getElementById('notification-container');
    if (!container) return;
    
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
      <div class="notification-content">
        <span>${message}</span>
        <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
          <i class="fas fa-times"></i>
        </button>
      </div>
    `;
    
    container.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 5000);
  },
  
  /**
   * Confirm dialog
   */
  async confirm(message, title = 'Confirm') {
    return new Promise((resolve) => {
      const modal = document.createElement('div');
      modal.className = 'modal-overlay';
      modal.innerHTML = `
        <div class="modal-dialog">
          <div class="modal-header">
            <h3>${title}</h3>
          </div>
          <div class="modal-body">
            <p>${message}</p>
          </div>
          <div class="modal-footer">
            <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove(); window.tempResolve(false);">Cancel</button>
            <button class="btn btn-danger" onclick="this.closest('.modal-overlay').remove(); window.tempResolve(true);">Confirm</button>
          </div>
        </div>
      `;
      
      window.tempResolve = resolve;
      document.body.appendChild(modal);
    });
  }
};

console.log('✅ Admin utilities loaded');
