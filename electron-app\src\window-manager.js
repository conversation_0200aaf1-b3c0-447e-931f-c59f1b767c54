const { BrowserWindow, shell, app } = require('electron');
const path = require('path');

class WindowManager {
  constructor(store) {
    this.store = store;
    this.mainWindow = null;
  }

  createMainWindow() {
    // Get saved window bounds or use defaults
    const bounds = this.store.get('windowBounds');
    
    this.mainWindow = new BrowserWindow({
      width: bounds.width,
      height: bounds.height,
      minWidth: 1000,
      minHeight: 700,
      icon: this.getIconPath(),
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        preload: path.join(__dirname, '..', 'preload.js'),
        webSecurity: true
      },
      show: false,
      titleBarStyle: 'default',
      autoHideMenuBar: true,
      title: 'Warcraft Arena'
    });

    // Save window bounds when they change
    this.mainWindow.on('resize', () => {
      const bounds = this.mainWindow.getBounds();
      this.store.set('windowBounds', bounds);
    });

    this.mainWindow.on('moved', () => {
      const bounds = this.mainWindow.getBounds();
      this.store.set('windowBounds', bounds);
    });

    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow.show();
      
      // Focus the window
      this.mainWindow.focus();
    });

    // Handle external links
    this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
      shell.openExternal(url);
      return { action: 'deny' };
    });

    // Prevent navigation to external sites
    this.mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
      const allowedOrigins = [
        this.store.get('serverUrl'),
        'file://',
        'warcraftarena://'
      ];
      
      const isAllowed = allowedOrigins.some(origin => navigationUrl.startsWith(origin));
      
      if (!isAllowed) {
        event.preventDefault();
        shell.openExternal(navigationUrl);
      }
    });

    // Clean up when window is closed
    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    // Handle window focus for better UX
    this.mainWindow.on('focus', () => {
      this.mainWindow.webContents.executeJavaScript(`
        if (window.electronAPI && window.electronAPI.onWindowFocus) {
          window.electronAPI.onWindowFocus();
        }
      `).catch(err => {
        // Ignore errors if the method doesn't exist
      });
    });

    return this.mainWindow;
  }

  loadLoginPage() {
    if (!this.mainWindow) return;
    
    // Load the custom Electron login page
    const loginPagePath = path.join(__dirname, '..', 'pages', 'login.html');
    this.mainWindow.loadFile(loginPagePath);
  }

  loadMainApp() {
    if (!this.mainWindow) return;
    
    const serverUrl = this.store.get('serverUrl');
    this.mainWindow.loadURL(serverUrl);
  }

  loadErrorPage(error) {
    if (!this.mainWindow) return;
    
    const errorPagePath = path.join(__dirname, '..', 'pages', 'error.html');
    this.mainWindow.loadFile(errorPagePath);
    
    // Pass error info to the error page
    this.mainWindow.webContents.once('dom-ready', () => {
      this.mainWindow.webContents.executeJavaScript(`
        if (window.setErrorInfo) {
          window.setErrorInfo(${JSON.stringify(error)});
        }
      `);
    });
  }

  getIconPath() {
    const iconDir = path.join(__dirname, '..', 'assets');
    
    switch (process.platform) {
      case 'win32':
        return path.join(iconDir, 'icon.ico');
      case 'darwin':
        return path.join(iconDir, 'icon.icns');
      default:
        return path.join(iconDir, 'icon.png');
    }
  }

  // Utility methods
  show() {
    if (this.mainWindow) {
      if (this.mainWindow.isMinimized()) {
        this.mainWindow.restore();
      }
      this.mainWindow.show();
      this.mainWindow.focus();
    }
  }

  hide() {
    if (this.mainWindow) {
      this.mainWindow.hide();
    }
  }

  close() {
    if (this.mainWindow) {
      this.mainWindow.close();
    }
  }

  isMinimized() {
    return this.mainWindow ? this.mainWindow.isMinimized() : false;
  }

  isVisible() {
    return this.mainWindow ? this.mainWindow.isVisible() : false;
  }

  setTitle(title) {
    if (this.mainWindow) {
      this.mainWindow.setTitle(title);
    }
  }

  // Send data to the renderer process
  sendToRenderer(channel, data) {
    if (this.mainWindow && this.mainWindow.webContents) {
      this.mainWindow.webContents.send(channel, data);
    }
  }

  // Execute JavaScript in the renderer process
  executeJavaScript(code) {
    if (this.mainWindow && this.mainWindow.webContents) {
      return this.mainWindow.webContents.executeJavaScript(code);
    }
    return Promise.resolve();
  }
}

module.exports = { WindowManager }; 