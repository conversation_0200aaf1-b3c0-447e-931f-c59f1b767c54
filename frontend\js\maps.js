/**
 * Maps.js - Modular Maps System
 * 
 * This is the new, clean replacement for the original 72KB maps.js monster.
 * Instead of one massive file, we now have a focused orchestrator that coordinates
 * specialized modules for maximum maintainability and performance.
 * 
 * Architecture:
 * - MapsCore: Core initialization, authentication, state management
 * - MapsGrid: Grid display, pagination, search, statistics
 * - MapDetails: Modal details, strategic analysis, ratings, terrain charts
 * - MapManagement: Upload, edit, import operations
 * 
 * Original: 72.7KB, 2,059 lines of tangled code
 * New: 4 focused modules + clean orchestrator = Better organization & maintainability
 */

import { mapsCore } from './modules/MapsCore.js';
import { mapsGrid } from './modules/MapsGrid.js';
import { mapDetails } from './modules/MapDetails.js';
import { mapManagement } from './modules/MapManagement.js';
import { wc1Manager } from './modules/WC1Manager.js';
// WC3Manager removed - using War3Net system instead

/**
 * Maps System Orchestrator
 * Coordinates all maps modules and provides unified initialization
 */
class MapsSystem {
  constructor() {
    this.initialized = false;
    this.modules = {
      core: mapsCore,
      grid: mapsGrid,
      details: mapDetails,
      management: mapManagement,
      wc1: wc1Manager,
      // wc3: removed - using War3Net system instead
    };
  }

  /**
   * Initialize the complete maps system
   */
  async init() {
    if (this.initialized) {
      console.log('⚠️ Maps system already initialized');
      return;
    }

    console.log('🚀 Initializing Maps System...');
    console.log('📦 Loading modules: Core → Grid → Details → Management');

    try {
      // Initialize modules in dependency order
      await this.modules.core.init();
      this.modules.grid.init();
      this.modules.details.init();
      this.modules.management.init();
      
      // Initialize WC1 manager
      await this.modules.wc1.init();
      
      // WC3 manager removed - using War3Net system instead

      // Initialize game tabs functionality
      this.modules.core.initializeGameTabs();

      // Load initial data
      await this.loadInitialData();

      this.initialized = true;
      
      console.log('✅ Maps System fully initialized');
      console.log('🎯 All modules loaded and coordinated');
      
      // Setup global cleanup
      this.setupGlobalCleanup();

    } catch (error) {
      console.error('❌ Failed to initialize Maps System:', error);
      this.showError(`Failed to initialize maps: ${error.message}`);
    }
  }

  /**
   * Load initial data
   */
  async loadInitialData() {
    try {
      console.log('📊 Loading initial maps data...');
      
      // Load maps for current tab
      await this.modules.grid.loadMaps();
      
      console.log('✅ Initial data loaded');
    } catch (error) {
      console.error('❌ Failed to load initial data:', error);
      // Don't throw - allow system to continue with empty state
    }
  }

  /**
   * Setup global cleanup handlers
   */
  setupGlobalCleanup() {
    // Cleanup on page unload
    window.addEventListener('beforeunload', () => {
      this.cleanup();
    });

    // Global error handling
    window.addEventListener('error', (event) => {
      console.error('🚨 Global error in Maps System:', event.error);
    });

    // Setup global functions for backward compatibility
    window.mapsSystem = this;
  }

  /**
   * Get system status
   */
  getStatus() {
    return {
      initialized: this.initialized,
      modules: {
        core: this.modules.core.getState?.() || { initialized: true },
        grid: { initialized: true },
        details: { initialized: true },
        management: { initialized: true }
      },
      performance: {
        moduleCount: Object.keys(this.modules).length,
        memoryUsage: this.getMemoryUsage()
      }
    };
  }

  /**
   * Get approximate memory usage
   */
  getMemoryUsage() {
    if (window.performance && window.performance.memory) {
      return {
        used: Math.round(window.performance.memory.usedJSHeapSize / 1024 / 1024) + 'MB',
        total: Math.round(window.performance.memory.totalJSHeapSize / 1024 / 1024) + 'MB'
      };
    }
    return { used: 'Unknown', total: 'Unknown' };
  }

  /**
   * Refresh all data
   */
  async refresh() {
    console.log('🔄 Refreshing Maps System...');
    
    try {
      // Re-check authentication
      await this.modules.core.checkAuthStatus();
      
      // Reload maps
      await this.modules.grid.loadMaps();
      
      console.log('✅ Maps System refreshed');
    } catch (error) {
      console.error('❌ Failed to refresh Maps System:', error);
      this.showError(`Failed to refresh: ${error.message}`);
    }
  }

  /**
   * Show error message
   */
  showError(message) {
    if (typeof window.NotificationUtils !== 'undefined') {
      window.NotificationUtils.error(message, 5000);
    } else {
      alert(`Error: ${message}`);
    }
  }

  /**
   * Show success message
   */
  showSuccess(message) {
    if (typeof window.NotificationUtils !== 'undefined') {
      window.NotificationUtils.success(message, 3000);
    } else {
      console.log(`Success: ${message}`);
    }
  }

  /**
   * Cleanup all modules
   */
  cleanup() {
    if (!this.initialized) return;

    console.log('🧹 Cleaning up Maps System...');
    
    try {
      // Cleanup modules in reverse order
      Object.values(this.modules).reverse().forEach(module => {
        if (module.cleanup && typeof module.cleanup === 'function') {
          module.cleanup();
        }
      });

      this.initialized = false;
      console.log('✅ Maps System cleanup complete');
      
    } catch (error) {
      console.error('❌ Error during Maps System cleanup:', error);
    }
  }
}

// Create singleton instance
const mapsSystem = new MapsSystem();

// Initialize when DOM is ready - SINGLE INITIALIZATION POINT
function initializeMapsSystem() {
  // Prevent double initialization
  if (window.mapsSystemInitialized) {
    console.log('⚠️ Maps system already initialized, skipping...');
    return;
  }
  
  window.mapsSystemInitialized = true;
  mapsSystem.init();
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeMapsSystem);
} else {
  // DOM already loaded
  initializeMapsSystem();
}

// Global function for game tab changes
function loadMapsData(gameId) {
  console.log(`🎮 Loading maps data for game: ${gameId}`);
  
  if (mapsSystem.initialized && mapsSystem.modules.core && mapsSystem.modules.grid) {
    // Update the current game in the core module
    mapsSystem.modules.core.currentGame = gameId;
    
    // Update localStorage to persist the selection
    localStorage.setItem('selectedGame', gameId);
    
    // For War2, ensure we trigger the reload properly
    if (gameId === 'war2') {
      // Force reload for War2 to ensure maps show
      mapsSystem.modules.grid.loadMaps();
    } else if (gameId === 'war3') {
      // Trigger War3 maps reload
      mapsSystem.modules.grid.loadMaps();
    }
    // War1 is handled by WC1Manager, no action needed
    
  } else {
    console.warn('⚠️ Maps system not fully initialized yet');
    // If system not ready, try again after a short delay
    setTimeout(() => {
      if (mapsSystem.initialized) {
        loadMapsData(gameId);
      }
    }, 500);
  }
}

// Export for external access
window.mapsSystem = mapsSystem;
window.initializeMapsSystem = initializeMapsSystem;
window.loadMapsData = loadMapsData;

console.log('🗺️ Maps System loaded - Ready for initialization');
console.log('📈 Performance: Modular architecture with focused responsibilities');
console.log('🔄 Maintainability: 4 clean modules vs 1 monster file');

export default mapsSystem; 