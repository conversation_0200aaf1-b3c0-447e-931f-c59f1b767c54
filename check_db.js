const mongoose = require('mongoose');
const War3Map = require('./backend/models/War3Map');
const User = require('./backend/models/User');
const UserMembership = require('./backend/models/UserMembership');

async function checkDB() {
  try {
    await mongoose.connect('mongodb://localhost:27017/game-files-db');
    console.log('Connected to MongoDB');
    
    const count = await War3Map.countDocuments();
    console.log(`Total War3 maps in database: ${count}`);
    
    if (count > 0) {
      const sample = await War3Map.find().limit(5);
      console.log('\nFirst 5 maps:');
      sample.forEach(map => {
        console.log(`- ${map.name} (thumbnail: ${map.thumbnailPath || 'none'})`);
      });
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

async function checkTurtlemanMembership() {
  try {
    // Connect to MongoDB
    await mongoose.connect('mongodb://127.0.0.1:27017/newsite', {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });

    console.log('🔍 Connected to MongoDB, checking turtleman membership...');

    // Find the user
    const user = await User.findOne({ username: 'turtleman' });
    if (!user) {
      console.log('❌ User "turtleman" not found');
      return;
    }

    console.log('👤 Found user:', {
      id: user._id,
      username: user.username,
      role: user.role
    });

    // Find their membership
    const membership = await UserMembership.findOne({ user: user._id });
    if (!membership) {
      console.log('❌ No membership found for turtleman');
      return;
    }

    console.log('🛡️ Membership details:', {
      tier: membership.tier,
      tierName: membership.tierName,
      isActive: membership.isActive,
      subscriptionStatus: membership.subscriptionStatus,
      subscriptionType: membership.subscriptionType,
      paymentProvider: membership.paymentProvider,
      unlockedImages: membership.unlockedImages,
      subscriptionStartDate: membership.subscriptionStartDate,
      lastPaymentDate: membership.lastPaymentDate,
      nextBillingDate: membership.nextBillingDate
    });

    // Check if it's properly active
    const isSubscriptionActive = membership.isSubscriptionActive();
    console.log('📊 Subscription status check:', {
      isSubscriptionActive,
      accessibleImages: membership.getAccessibleImages()
    });

    if (membership.tier === 4 && membership.tierName === 'Dragon Lord') {
      console.log('✅ Dragon Lord membership is properly configured!');
    } else {
      console.log('⚠️ Membership configuration issue detected');
    }

  } catch (error) {
    console.error('❌ Error checking membership:', error);
  } finally {
    await mongoose.disconnect();
    console.log('👋 Disconnected from MongoDB');
  }
}

checkDB();
checkTurtlemanMembership(); 