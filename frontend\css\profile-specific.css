/* ===== PROFILE SPECIFIC STYLES ===== */
/* Clean, organized CSS for the My Profile page */
/* Based on myprofile.html structure - No duplication, no dead code */

/* ===== 1. PROFILE HERO SECTION ===== */
.profile-hero {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 50%, rgba(51, 65, 85, 0.95) 100%);
  padding: 1rem 0.75rem;
  border-radius: 0 0 1rem 1rem;
  margin-bottom: 1rem;
  position: relative;
  overflow: visible;
}

.profile-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(212, 175, 55, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.profile-hero-content {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  position: relative;
  z-index: 1;
}

.profile-avatar-section {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  flex: 1;
}

.profile-avatar-container {
  position: relative;
  flex-shrink: 0;
}

.profile-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 3px solid var(--primary-gold);
  background: linear-gradient(135deg, #1e293b, #334155);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.profile-status {
  position: absolute;
  bottom: 4px;
  right: 4px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid white;
  background: #22c55e;
}

.profile-status.offline {
  background: #64748b;
}

.profile-level-badge {
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--primary-gold);
  color: #0f172a;
  padding: 0.2rem 0.5rem;
  border-radius: 0.75rem;
  font-size: 0.65rem;
  font-weight: 600;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.profile-user-info {
  flex: 1;
  min-width: 0;
}

.profile-username-row {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
  justify-content: flex-start;
}

/* More specific selector to override navbar CSS without */
.profile-hero .profile-username,
#profile-username.profile-username {
  font-family: var(--font-display);
  font-size: 1.25rem;
  font-weight: 800;
  background: linear-gradient(135deg, 
    #ffd700 0%, 
    #ffed4e 25%, 
    #f59e0b 50%, 
    #d97706 75%, 
    #b45309 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 
    0 0 8px rgba(255, 215, 0, 0.8),
    0 0 16px rgba(255, 215, 0, 0.6),
    0 0 24px rgba(255, 215, 0, 0.4),
    1px 1px 4px rgba(0, 0, 0, 0.8),
    2px 2px 8px rgba(0, 0, 0, 0.6);
  filter: drop-shadow(0 0 12px rgba(255, 215, 0, 0.5));
  margin: 0;
  flex: none;
  min-width: auto;
  max-width: 100%;
  word-break: break-word;
  line-height: 1.1;
  white-space: normal;
  overflow: visible;
  text-overflow: unset;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  animation: usernameGlow 3s ease-in-out infinite alternate,
             gradientShift 6s ease-in-out infinite;
  letter-spacing: 1px;
  text-transform: uppercase;
  cursor: pointer;
  position: relative;
  z-index: 2;
}

/* Hover effect for the username */
.profile-hero .profile-username:hover,
#profile-username.profile-username:hover {
  transform: scale(1.05) translateY(-2px);
  filter: drop-shadow(0 0 25px rgba(255, 215, 0, 0.8));
  text-shadow: 
    0 0 15px rgba(255, 215, 0, 1),
    0 0 30px rgba(255, 215, 0, 0.8),
    0 0 45px rgba(255, 215, 0, 0.6),
    2px 2px 8px rgba(0, 0, 0, 0.9),
    4px 4px 20px rgba(0, 0, 0, 0.7);
}

/* Keyframe animations for the username */
@keyframes usernameGlow {
  0% {
    filter: drop-shadow(0 0 15px rgba(255, 215, 0, 0.5));
  }
  100% {
    filter: drop-shadow(0 0 25px rgba(255, 215, 0, 0.8));
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.profile-action-buttons {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-shrink: 0;
}

.change-username-btn,
.delete-account-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
}

.change-username-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: var(--primary-gold);
  color: var(--primary-gold);
  transform: translateY(-1px);
}

.change-username-btn.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.change-username-btn.disabled:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  color: white;
  transform: none;
}

.delete-account-btn {
  background: rgba(220, 53, 69, 0.1);
  border-color: rgba(220, 53, 69, 0.3);
  color: #dc3545;
}

.delete-account-btn:hover {
  background: rgba(220, 53, 69, 0.2);
  border-color: #dc3545;
  color: #ff6b7a;
  transform: translateY(-1px);
}

.notifications-container {
  position: relative;
}

.notification-bell {
  background: none;
  border: none;
  color: white;
  font-size: 1.25rem;
  padding: 0.5rem;
  border-radius: 0.5rem;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
}

.notification-bell:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--primary-gold);
}

.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 0.625rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.notifications-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.75rem;
  min-width: 300px;
  max-height: 400px;
  overflow-y: auto;
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease;
  z-index: 1000;
}

.notifications-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.notifications-header {
  padding: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notifications-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
}

.mark-all-read {
  background: none;
  border: none;
  color: var(--primary-gold);
  font-size: 0.75rem;
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.mark-all-read:hover {
  background: rgba(212, 175, 55, 0.1);
}

.notifications-list {
  max-height: 300px;
  overflow-y: auto;
}

.notifications-empty {
  padding: 2rem;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
}

.notifications-empty i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  opacity: 0.5;
}

.username-change-container {
  margin-bottom: 1rem;
}

.username-change-info {
  background: rgba(13, 110, 253, 0.1);
  border: 1px solid rgba(13, 110, 253, 0.2);
  border-radius: 0.5rem;
  padding: 0.75rem;
  margin-bottom: 1rem;
  color: #87ceeb;
  font-size: 0.875rem;
}

.username-change-info p {
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
}

.username-change-info ul {
  margin: 0;
  padding-left: 1.5rem;
}

.username-change-info li {
  margin-bottom: 0.25rem;
}

.username-change-form {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  flex-wrap: wrap;
}

.username-change-form input {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem;
  border-radius: 0.375rem;
  flex: 1;
  min-width: 200px;
}

.username-change-form input:focus {
  outline: none;
  border-color: var(--primary-gold);
  box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
}

.btn-save-username,
.btn-cancel-username {
  background: var(--primary-gold);
  border: none;
  color: #0f172a;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-cancel-username {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-save-username:hover {
  background: var(--primary-gold-light);
  transform: translateY(-1px);
}

.btn-cancel-username:hover {
  background: rgba(255, 255, 255, 0.2);
}

.username-feedback {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  padding: 0.5rem;
  border-radius: 0.375rem;
}

.username-feedback.success {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.username-feedback.error {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.profile-email {
  margin-bottom: 1.5rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

.email-privacy {
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.8rem;
  margin-left: 0.5rem;
}

.profile-stats-condensed {
  display: flex;
  gap: 0.25rem;
  margin-top: 0.5rem;
  flex-wrap: wrap;
}

.stat-condensed {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.375rem;
  padding: 0.375rem 0.25rem;
  text-align: center;
  transition: all 0.2s ease;
  width: 80px;
  min-width: 80px;
  max-width: 80px;
  flex-shrink: 0;
}

.stat-condensed:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(212, 175, 55, 0.3);
  transform: translateY(-2px);
}

.stat-condensed-icon {
  color: var(--primary-gold);
  font-size: 0.875rem;
  margin-bottom: 0.125rem;
}

.stat-condensed-value {
  font-size: 0.75rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.1rem;
}

.stat-condensed-label {
  font-size: 0.6rem;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.25px;
}

.profile-points {
  display: flex;
  gap: 0.75rem;
  flex-shrink: 0;
  margin-left: 0.5rem;
}

.points-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
  padding: 0.75rem 0.5rem;
  text-align: center;
  min-width: 80px;
  transition: all 0.2s ease;
}

.points-card:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(212, 175, 55, 0.3);
  transform: translateY(-4px);
}

.points-card i {
  color: var(--primary-gold);
  font-size: 1.125rem;
  margin-bottom: 0.25rem;
}

.points-card span:first-of-type {
  display: block;
  font-size: 1rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.15rem;
}

.points-card span:last-of-type {
  font-size: 0.65rem;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.25px;
}

/* ===== 2. MAIN PROFILE CONTENT ===== */
.profile-main {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem 2rem;
  margin-top: -1rem;
}

.layout-controls {
  display: flex;
  gap: 0.5rem;
  margin: 1rem 0 0.75rem 0;
  justify-content: center;
  flex-wrap: wrap;
}

.layout-control-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.layout-control-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: var(--primary-gold);
  color: var(--primary-gold);
  transform: translateY(-1px);
}

.layout-control-btn i {
  font-size: 0.875rem;
}

.profile-grid-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  align-items: start;
}

/* ===== 3. PROFILE SECTIONS ===== */
.profile-section {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  height: 450px; /* Fixed uniform height for all normal sections */
  display: flex;
  flex-direction: column;
}

.profile-section:hover {
  border-color: rgba(212, 175, 55, 0.3);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.profile-section.expanded {
  grid-column: 1 / -1;
  height: 600px; /* Increased height for expanded sections */
}

.profile-section.minimized {
  height: 75px !important; /* Override normal height when minimized */
}

.profile-section.minimized .section-content {
  display: none;
}

.grid-position-indicator {
  position: absolute;
  top: 0.5rem;
  left: 0.5rem;
  background: rgba(212, 175, 55, 0.2);
  color: var(--primary-gold);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.625rem;
  font-weight: 600;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.profile-section:hover .grid-position-indicator {
  opacity: 1;
}

.section-header {
  background: rgba(255, 255, 255, 0.05);
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  flex-shrink: 0; /* Prevent header from shrinking */
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
}

.section-title i {
  color: var(--primary-gold);
  font-size: 1.25rem;
}

.section-controls {
  display: flex;
  gap: 0.5rem;
}

.section-control-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.section-control-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: var(--primary-gold);
  color: var(--primary-gold);
}

.section-content {
  padding: 1.5rem;
  flex: 1; /* Take up remaining space */
  overflow-y: auto; /* Add vertical scrollbar when needed */
  overflow-x: hidden; /* Prevent horizontal scrolling */
  max-height: calc(450px - 80px); /* Total section height minus header */
}

/* ===== SECTION CONTENT STYLES ===== */

/* Ultra-Compact Achievement Cards for Profile Page */
.section-content .achievements-grid {
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr)); /* Half the previous size */
  gap: 0.4rem; /* Much smaller gap */
}

.section-content .achievement-card {
  padding: 0.5rem; /* Half the padding */
  border-radius: 4px; /* Smaller border radius */
}

.section-content .achievement-icon {
  width: 24px; /* Much smaller icon */
  height: 24px;
  font-size: 0.8rem;
  margin-bottom: 0.4rem;
}

.section-content .achievement-name {
  font-size: 0.7rem; /* Much smaller text */
  margin-bottom: 0.2rem;
  line-height: 1.2;
}

.section-content .achievement-description {
  font-size: 0.6rem; /* Much smaller description */
  line-height: 1.2;
  margin-bottom: 0.3rem;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* Limit to 2 lines */
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.section-content .achievement-rewards {
  font-size: 0.6rem; /* Much smaller rewards display */
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
}

.section-content .achievement-rewards .exp-reward,
.section-content .achievement-rewards .gold-reward,
.section-content .achievement-rewards .honor-reward {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.55rem;
  font-weight: 500;
}

.section-content .achievement-rewards .exp-reward {
  color: #ffd700;
}

.section-content .achievement-rewards .gold-reward {
  color: #f39c12;
}

.section-content .achievement-rewards .honor-reward {
  color: #e74c3c;
}

.section-content .achievement-status {
  margin-top: 0.4rem;
  padding-top: 0.4rem;
}

.section-content .completed-date,
.section-content .locked-text {
  font-size: 0.55rem;
}

/* Ultra-Compact Campaign Progress Items for Profile Page */
.section-content .campaign-progress-item {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 3px;
  padding: 0.4rem; /* Half the padding */
  margin-bottom: 0.25rem; /* Half the margin */
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.2s ease;
}

.section-content .campaign-progress-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(212, 175, 55, 0.2);
}

.section-content .campaign-info {
  flex: 1;
}

.section-content .campaign-name {
  font-size: 0.7rem; /* Much smaller campaign name */
  font-weight: 600;
  color: white;
  margin-bottom: 0.1rem;
  line-height: 1.2;
}

.section-content .campaign-details {
  display: flex;
  gap: 0.25rem;
  align-items: center;
}

.section-content .campaign-details .badge-normal,
.section-content .campaign-details .badge-hard,
.section-content .campaign-details .badge-expert {
  font-size: 0.55rem; /* Much smaller badge text */
  padding: 0.1rem 0.25rem;
  border-radius: 2px;
  font-weight: 500;
}

.section-content .campaign-details .badge-normal {
  background: rgba(34, 197, 94, 0.2);
  color: #4ade80;
}

.section-content .campaign-details .badge-hard {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
}

.section-content .campaign-details .badge-expert {
  background: rgba(239, 68, 68, 0.2);
  color: #f87171;
}

.section-content .completion-date {
  font-size: 0.55rem; /* Much smaller date */
  color: rgba(255, 255, 255, 0.6);
}

.section-content .campaign-stats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.1rem;
}

.section-content .completion-time,
.section-content .campaign-rewards {
  display: flex;
  align-items: center;
  gap: 0.2rem;
  font-size: 0.55rem; /* Much smaller stats */
  color: rgba(255, 255, 255, 0.7);
}

.section-content .campaign-rewards {
  flex-direction: column;
  gap: 0.1rem;
}

.section-content .exp-earned,
.section-content .gold-earned {
  display: flex;
  align-items: center;
  gap: 0.2rem;
  font-size: 0.5rem;
}

.section-content .exp-earned {
  color: #ffd700;
}

.section-content .gold-earned {
  color: #f39c12;
}

.section-content .completion-time i,
.section-content .exp-earned i,
.section-content .gold-earned i {
  font-size: 0.5rem;
}

/* Ultra-Compact Recent Campaign Items */
.section-content .recent-item {
  padding: 0.4rem; /* Half the padding */
  margin-bottom: 0.25rem;
  border-radius: 3px;
}

.section-content .item-title {
  font-size: 0.7rem; /* Much smaller title */
  margin-bottom: 0.1rem;
  line-height: 1.2;
}

.section-content .item-subtitle {
  font-size: 0.55rem; /* Much smaller subtitle */
}

.section-content .completion-time {
  font-size: 0.55rem; /* Much smaller completion time */
}

/* Ultra-Compact Campaign Progress Overview */
.section-content .campaign-progress-content {
  padding: 0; /* Remove extra padding */
}

.section-content .progress-summary {
  margin-bottom: 0.5rem; /* Half the margin */
}

.section-content .progress-summary h3 {
  font-size: 0.8rem; /* Much smaller heading */
  margin-bottom: 0.4rem;
  color: var(--primary-gold);
}

.section-content .summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(50px, 1fr)); /* Much smaller minimum width */
  gap: 0.25rem; /* Much smaller gap */
  margin-bottom: 0.5rem;
}

.section-content .summary-stats .stat-card {
  padding: 0.25rem; /* Half the padding */
  text-align: center;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.section-content .summary-stats .stat-value {
  font-size: 0.7rem; /* Much smaller stat value */
  font-weight: 700;
  color: var(--primary-gold);
  display: block;
  margin-bottom: 0.1rem;
}

.section-content .summary-stats .stat-label {
  font-size: 0.5rem; /* Much smaller stat label */
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.section-content .campaign-progress-list h4 {
  font-size: 0.75rem; /* Much smaller section heading */
  margin-bottom: 0.4rem;
  color: white;
}

.section-content .progress-items {
  max-height: 150px; /* Smaller height to save more space */
  overflow-y: auto; /* Add scrolling if needed */
}

.section-content .speedrun-records {
  margin-top: 0.5rem;
}

.section-content .speedrun-records h4 {
  font-size: 0.75rem; /* Much smaller heading */
  margin-bottom: 0.4rem;
  color: white;
}

.section-content .speedrun-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.25rem; /* Half the padding */
  margin-bottom: 0.1rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 2px;
  font-size: 0.6rem; /* Much smaller text */
}

.section-content .speedrun-campaign {
  flex: 1;
  color: white;
}

.section-content .speedrun-time {
  color: var(--primary-gold);
  font-weight: 600;
  margin: 0 0.25rem;
}

.section-content .speedrun-rank {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.55rem;
}

/* Custom Scrollbar Styling for Sections */
.section-content::-webkit-scrollbar,
.activity-content::-webkit-scrollbar {
  width: 8px;
}

.section-content::-webkit-scrollbar-track,
.activity-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.section-content::-webkit-scrollbar-thumb,
.activity-content::-webkit-scrollbar-thumb {
  background: rgba(212, 175, 55, 0.4);
  border-radius: 4px;
  transition: background 0.2s ease;
}

.section-content::-webkit-scrollbar-thumb:hover,
.activity-content::-webkit-scrollbar-thumb:hover {
  background: rgba(212, 175, 55, 0.6);
}

/* Firefox Scrollbar Styling */
.section-content,
.activity-content {
  scrollbar-width: thin;
  scrollbar-color: rgba(212, 175, 55, 0.4) rgba(255, 255, 255, 0.05);
}

/* About Me Section */
.bio-content {
  width: 100%;
}

.bio-display {
  margin-bottom: 1.5rem;
  transition: opacity 0.3s ease-in-out;
}

.bio-display.editing {
  opacity: 0;
}

.bio-text-container {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 0.5rem;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.bio-text-container p {
  flex: 1;
  margin: 0;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  transition: all 0.3s ease;
}

/* Hide any stray inline-edit-btn elements from ProfileFormHandlers */
/* These are auto-generated buttons that conflict with our dedicated edit buttons */
.inline-edit-btn,
.bio-text-container .inline-edit-btn,
#bio-text + .inline-edit-btn {
  display: none;
  visibility: hidden;
}

/* Ensure only our main edit buttons are visible */
.edit-btn {
  background: rgba(212, 175, 55, 0.1);
  border: 1px solid rgba(212, 175, 55, 0.3);
  color: var(--primary-gold);
  padding: 0.5rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.edit-btn:hover {
  background: rgba(212, 175, 55, 0.2);
  border-color: var(--primary-gold);
  transform: translateY(-1px);
}

.bio-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.bio-stat {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 0.5rem;
  padding: 1rem;
  transition: all 0.2s ease;
}

.bio-stat:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(212, 175, 55, 0.2);
}

.bio-stat-label {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.bio-stat-value {
  color: white;
  font-weight: 500;
  font-size: 0.9rem;
}

/* Enhanced Inline Edit Form with Smooth Animations */
.inline-edit-form {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-top: 1rem;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  max-height: 0;
  overflow: hidden;
}

.inline-edit-form:not(.d-none) {
  opacity: 1;
  transform: translateY(0);
  max-height: 1000px; /* Increased to accommodate all fields */
  animation: slideInDown 0.3s ease-out;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
    max-height: 0;
  }
  to {
    opacity: 1;
    transform: translateY(0);
    max-height: 1000px;
  }
}

/* Smooth edit button state transitions */
.edit-btn.cancel-mode {
  background: rgba(239, 68, 68, 0.2);
  border-color: #ef4444;
  color: #ef4444;
}

.edit-btn.cancel-mode:hover {
  background: rgba(239, 68, 68, 0.3);
  transform: translateY(-1px) scale(1.05);
}

/* Forms */
.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.5rem;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.375rem;
  padding: 0.75rem;
  color: white;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary-gold);
  box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
  background: rgba(255, 255, 255, 0.08);
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  align-items: center;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Override mobile stacking for profile page form actions */
@media (max-width: 768px) {
  .profile-section .platform-card .form-actions {
    flex-direction: row;
    flex-wrap: wrap;
  }
}

.epic-btn {
  background: var(--primary-gold);
  border: none;
  color: #0f172a;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.epic-btn:hover {
  background: var(--primary-gold-light);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
}

.d-none {
  display: none;
}

/* Content Creator Section */
.platform-cards {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.platform-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.platform-card:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(212, 175, 55, 0.2);
}

.platform-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.platform-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
}

.platform-icon.youtube {
  background: linear-gradient(135deg, #ff0000, #cc0000);
}

.platform-icon.twitch {
  background: linear-gradient(135deg, #9146ff, #6441a5);
}

.platform-info {
  flex: 1;
}

.platform-info h4 {
  margin: 0 0 0.25rem 0;
  color: white;
  font-size: 1rem;
  font-weight: 600;
}

.platform-info p {
  margin: 0;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
}

.platform-description {
  padding: 1rem 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 1rem;
}

.platform-description p {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 1rem;
}

.content-types {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.content-type-badge {
  background: rgba(212, 175, 55, 0.2);
  color: var(--primary-gold);
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid rgba(212, 175, 55, 0.3);
}

/* Player Names Section */
.section-action-area {
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}



/* Stats Grids */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
  padding: 1rem;
  text-align: center;
  transition: all 0.2s ease;
}

.stat-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(212, 175, 55, 0.3);
  transform: translateY(-2px);
}

.stat-icon {
  color: var(--primary-gold);
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Experience Bar */
.experience-section {
  margin-top: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
  padding: 1rem;
}

.experience-section h4 {
  margin: 0 0 1rem 0;
  color: var(--primary-gold);
  font-size: 1rem;
}

.experience-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.experience-header h4 {
  margin: 0;
  color: var(--primary-gold);
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.experience-text {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.experience-bar {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
  height: 1rem;
  overflow: hidden;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.experience-fill {
  background: linear-gradient(90deg, #ffd700, #ffed4a);
  height: 100%;
  border-radius: 1rem;
  transition: width 0.5s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.experience-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Modals */
.modal {
  display: none;
  position: fixed;
  z-index: 10000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease;
}

.modal.show {
  display: block;
}

/* Delete Account Modal Specific Styles */
.delete-account-modal {
  z-index: 99999;
  background-color: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(6px);
}

.delete-account-modal-content {
  position: relative;
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  margin: 2% auto;
  padding: 0;
  border: 2px solid #dc3545;
  border-radius: 1rem;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px rgba(220, 53, 69, 0.3);
  animation: slideInUp 0.3s ease;
}

.delete-account-modal-header {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
  padding: 1.5rem 2rem;
  border-radius: 1rem 1rem 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 1;
}

.delete-account-modal-close {
  background: none;
  border: none;
  font-size: 2rem;
  font-weight: bold;
  color: white;
  cursor: pointer;
  padding: 0;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.delete-account-modal-close:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

.delete-account-modal .modal-body {
  padding: 2rem;
  color: #e2e8f0;
}

.delete-account-modal .warning-message {
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.3);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.delete-account-modal .warning-message h3 {
  color: #dc3545;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.delete-account-modal .deletion-details ul {
  color: #e2e8f0;
  line-height: 1.6;
  margin: 0;
  padding-left: 1.5rem;
}

.delete-account-modal .deletion-details h4 {
  color: #ffd700;
  margin-bottom: 1rem;
}

.delete-account-modal .confirmation-input label {
  display: block;
  color: #e2e8f0;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.delete-account-modal .confirmation-input input {
  width: 100%;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: white;
  font-family: monospace;
}

.delete-account-modal .confirmation-input input:focus {
  outline: none;
  border-color: #dc3545;
  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2);
}

.delete-account-modal .form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.modal-content {
  position: relative;
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  margin: 2% auto;
  padding: 0;
  border: 2px solid var(--primary-gold);
  border-radius: 1rem;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
  animation: slideInUp 0.3s ease;
}

/* Player Stats Modal Specific Styling */
#player-stats-modal .modal-content {
  max-width: 900px;
  width: 95%;
}

.modal-header {
  background: linear-gradient(135deg, var(--primary-gold) 0%, #d4ac0d 100%);
  color: #0f172a;
  padding: 1.5rem 2rem;
  border-radius: 1rem 1rem 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 1;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.modal-header h2 i {
  font-size: 1.25rem;
}

.close {
  background: none;
  border: none;
  font-size: 2rem;
  font-weight: bold;
  color: #0f172a;
  cursor: pointer;
  padding: 0;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close:hover {
  background: rgba(15, 23, 42, 0.1);
  transform: scale(1.1);
}

.modal-body {
  padding: 2rem;
  color: #e2e8f0;
}

/* Modal Tabs */
.modal .activity-tabs {
  margin-bottom: 2rem;
  border-bottom: 2px solid rgba(212, 175, 55, 0.2);
}

.modal .activity-tab {
  background: none;
  border: none;
  color: #94a3b8;
  padding: 1rem 1.5rem;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  position: relative;
}

.modal .activity-tab:hover {
  color: var(--primary-gold);
  background: rgba(212, 175, 55, 0.1);
}

.modal .activity-tab.active {
  color: var(--primary-gold);
  border-bottom-color: var(--primary-gold);
  background: rgba(212, 175, 55, 0.1);
}

/* Overview Layout */
.overview-layout {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.overview-chart-inline {
  background: rgba(15, 23, 42, 0.5);
  border-radius: 0.75rem;
  padding: 1.5rem;
  border: 1px solid rgba(212, 175, 55, 0.2);
}

.overview-chart-inline canvas {
  max-width: 100%;
  height: auto;
}

/* Match History */
.matches-header {
  display: grid;
  grid-template-columns: 1fr 2fr 2fr 1fr 1fr 1fr 1fr 1.5fr;
  gap: 1rem;
  padding: 1rem;
  background: rgba(15, 23, 42, 0.5);
  border-radius: 0.5rem;
  font-weight: 600;
  color: var(--primary-gold);
  margin-bottom: 1rem;
}

.header-cell {
  text-align: center;
}

.matches-container {
  min-height: 200px;
}

/* Performance Sections */
.performance-sections {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.performance-section {
  background: rgba(15, 23, 42, 0.3);
  border-radius: 0.75rem;
  padding: 1.5rem;
  border: 1px solid rgba(212, 175, 55, 0.2);
}

.performance-section .section-header {
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(212, 175, 55, 0.2);
}

.performance-section .section-header h4 {
  margin: 0;
  color: var(--primary-gold);
  font-size: 1.25rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.performance-section .section-header h4 i {
  font-size: 1.1rem;
}

/* Error Message */
.error-message {
  text-align: center;
  color: #ef4444;
  padding: 2rem;
  font-size: 1.1rem;
}

/* Pagination Controls */
.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(212, 175, 55, 0.2);
}

.pagination-btn {
  background: rgba(212, 175, 55, 0.1);
  border: 1px solid var(--primary-gold);
  color: var(--primary-gold);
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.pagination-btn:hover:not(:disabled) {
  background: var(--primary-gold);
  color: #0f172a;
  transform: translateY(-1px);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  color: #94a3b8;
  font-size: 0.9rem;
}

/* Clan Management Specific Styles */
#clan-container {
  min-height: 200px;
  transition: all 0.3s ease;
}

.clan-management-content {
  text-align: center;
  padding: 2rem;
}

.clan-management-content h3 {
  color: #ffffff;
  margin-bottom: 1rem;
}

.clan-management-content p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 2rem;
}

.clan-action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.clan-action-btn {
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
  cursor: pointer;
}

.clan-action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(79, 70, 229, 0.3);
}

.clan-action-btn.secondary {
  background: linear-gradient(135deg, #6b7280, #4b5563);
}

.clan-action-btn.secondary:hover {
  box-shadow: 0 8px 20px rgba(107, 114, 128, 0.3);
}

/* Clan Info Display */
.clan-info-content {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
}

.clan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.clan-header h3 {
  color: #ffffff;
  margin: 0;
  font-size: 1.25rem;
}

.clan-role {
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.clan-details {
  margin-bottom: 1.5rem;
}

.clan-stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
}

.clan-stat i {
  color: #4f46e5;
  width: 1rem;
}

.clan-description {
  color: rgba(255, 255, 255, 0.7);
  font-style: italic;
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
  border-left: 3px solid #4f46e5;
}

/* Clan Cards in Modals */
.modal-clan-card {
  background: linear-gradient(135deg, rgba(30, 30, 30, 0.95), rgba(40, 40, 40, 0.95));
  border: 1px solid rgba(79, 70, 229, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.modal-clan-card:hover {
  border-color: rgba(79, 70, 229, 0.6);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(79, 70, 229, 0.2);
}

.modal-clan-card .clan-info {
  flex: 1;
  margin-bottom: 1rem;
}

.modal-clan-card .clan-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-clan-card .clan-header h4 {
  color: #ffffff;
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.clan-game {
  background: rgba(79, 70, 229, 0.2);
  color: #4f46e5;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.clan-description {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.clan-stats {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  margin-bottom: 1rem;
}

.clan-stats .stat {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.6);
}

.clan-stats .stat i {
  color: #4f46e5;
}

.clan-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-start;
}

.clan-actions .epic-btn,
.clan-actions .clan-action-btn {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
}

/* Search Filters */
.clan-search-filters {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.clan-search-filters .form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: 1rem;
  align-items: end;
}

.clan-search-filters input,
.clan-search-filters select {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #ffffff;
  border-radius: 6px;
  padding: 0.75rem;
}

.clan-search-filters input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* No Results Message */
.no-results {
  text-align: center;
  padding: 3rem 2rem;
  color: rgba(255, 255, 255, 0.6);
}

.no-results i {
  font-size: 3rem;
  color: rgba(79, 70, 229, 0.5);
  margin-bottom: 1rem;
}

.no-results p {
  font-style: italic;
}

/* Loading State */
#clans-list .loading {
  text-align: center;
  padding: 2rem;
  color: rgba(255, 255, 255, 0.6);
}

#clans-list .loading::after {
  content: '';
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid rgba(79, 70, 229, 0.3);
  border-top: 2px solid #4f46e5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 0.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .clan-action-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .clan-search-filters .form-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
  
  .clan-stats {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .clan-actions {
    flex-direction: column;
  }
}

/* ===== 4. RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .profile-grid-container {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  .profile-hero-content {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .profile-avatar-section {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .profile-username-row {
    justify-content: center;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .profile-action-buttons {
    margin-left: 0;
    justify-content: center;
  }
  
  .profile-stats-condensed {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }
  
  .profile-grid-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .layout-controls {
    justify-content: center;
    gap: 0.5rem;
  }
  
  .layout-control-btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }
  
  .layout-control-btn span {
    display: none;
  }
  
  /* Scale down the username for tablets */
  .profile-hero .profile-username,
  #profile-username.profile-username {
    font-size: 3rem; /* 75% of desktop size */
    letter-spacing: 1.5px;
  }
}

@media (max-width: 480px) {
  .profile-hero {
    padding: 1rem 0.5rem;
    margin-bottom: 1rem;
  }
  
  .profile-main {
    padding: 0 0.5rem 1rem;
  }
  
  /* Scale down further for mobile phones */
  .profile-hero .profile-username,
  #profile-username.profile-username {
    font-size: 2rem; /* Still double the original mobile size */
    letter-spacing: 1px;
    /* Slightly reduce effects on mobile for performance */
    animation: usernameGlow 4s ease-in-out infinite alternate;
  }
  
  .change-username-btn span,
  .logout-btn-inline span {
    display: none;
  }
  
  .section-header {
    padding: 0.75rem 1rem;
  }
  
  .section-content {
    padding: 1rem;
  }
}

/* ===== 5. ANIMATION UTILITIES ===== */
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.draggable-section {
  cursor: move;
}

.draggable-section:hover {
  cursor: grab;
}

.draggable-section:active {
  cursor: grabbing;
}

/* ===== 6. LOADING STATES ===== */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
}

.loading::before {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-top: 2px solid var(--primary-gold);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.tab-badge {
  display: inline-block;
  background: #dc3545;
  color: white;
  font-size: 0.75rem;
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 5px;
  min-width: 18px;
  text-align: center;
  line-height: 1.2;
  position: relative;
  top: -1px;
}

.activity-tab.active .tab-badge {
  background: #1a1a1a;
  color: #d4af37;
}

/* Match History Styles */
.match-row {
  display: grid;
  grid-template-columns: 60px 1fr 1fr 60px 60px 80px 60px 80px;
  gap: 10px;
  padding: 8px 10px;
  border-bottom: 1px solid #3a3a3a;
  align-items: center;
  font-size: 0.9rem;
}

.match-row.win {
  background: linear-gradient(90deg, rgba(46, 204, 113, 0.1) 0%, transparent 100%);
  border-left: 3px solid #2ecc71;
}

.match-row.loss {
  background: linear-gradient(90deg, rgba(231, 76, 60, 0.1) 0%, transparent 100%);
  border-left: 3px solid #e74c3c;
}

.match-row .match-result {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
}

.match-row.win .match-result {
  color: #2ecc71;
}

.match-row.loss .match-result {
  color: #e74c3c;
}

.match-row .match-race {
  display: flex;
  align-items: center;
  gap: 6px;
}

.match-row .match-race img {
  width: 20px;
  height: 20px;
  border-radius: 3px;
}

.match-row .mmr-positive {
  color: #2ecc71;
  font-weight: bold;
}

.match-row .mmr-negative {
  color: #e74c3c;
  font-weight: bold;
}

.match-opponent, .match-map {
  font-weight: 500;
}

.match-mmr {
  font-weight: bold;
}

.match-row.win .match-mmr {
  color: #2ecc71;
}

.match-row.loss .match-mmr {
  color: #e74c3c;
}

/* Performance Items */
.performance-item {
  background: #2a2a2a;
  border: 1px solid #3a3a3a;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.performance-item:hover {
  background: #333;
  border-color: #d4af37;
  transform: translateY(-2px);
}

.perf-name {
  font-weight: bold;
  color: #d4af37;
  margin-bottom: 4px;
}

.perf-stats {
  display: flex;
  gap: 15px;
  font-size: 0.9rem;
}

.perf-games {
  color: #bbb;
}

.perf-winrate {
  color: #2ecc71;
  font-weight: bold;
}

/* No data and error states */
.no-data {
  text-align: center;
  padding: 40px 20px;
  color: #888;
  font-style: italic;
}

.no-data i {
  font-size: 2rem;
  margin-bottom: 10px;
  display: block;
}

.error-message {
  background: #e74c3c;
  color: white;
  padding: 15px;
  border-radius: 8px;
  text-align: center;
  margin: 20px 0;
}

/* New player modal styles */
.match-row.loss {
  background: linear-gradient(90deg, rgba(231, 76, 60, 0.1) 0%, transparent 100%);
  border-left: 3px solid #e74c3c;
}

.match-row .match-result {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
}

.match-row.win .match-result {
  color: #2ecc71;
}

.match-row .match-race {
  display: flex;
  align-items: center;
  gap: 6px;
}

.match-row .match-race img {
  width: 20px;
  height: 20px;
  border-radius: 3px;
}

.match-row .mmr-positive {
  color: #2ecc71;
  font-weight: bold;
}

.match-row .mmr-negative {
  color: #e74c3c;
  font-weight: bold;
}

.matches-header {
  display: grid;
  grid-template-columns: 60px 1fr 1fr 60px 60px 80px 60px 80px;
  gap: 10px;
  padding: 10px;
  background: #2c3e50;
  border-radius: 6px 6px 0 0;
  font-weight: bold;
  color: #d4af37;
  font-size: 0.9rem;
}

.matches-list {
  max-height: 300px;
  overflow-y: auto;
}

.matches-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #2c3e50;
  border-radius: 0 0 6px 6px;
  margin-top: 10px;
}

.pagination-btn {
  background: #d4af37;
  color: #1a1a1a;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease;
}

.pagination-btn:hover:not(:disabled) {
  background: #ffd700;
  transform: translateY(-1px);
}

.pagination-btn:disabled {
  background: #555;
  color: #888;
  cursor: not-allowed;
}

.pagination-info {
  color: #d4af37;
  font-weight: bold;
}

/* Performance Tab Styles */
.performance-content {
  padding: 20px;
}

.charts-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.chart-section {
  background: #2c3e50;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #34495e;
}

.chart-section h4 {
  color: #d4af37;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-container {
  position: relative;
  height: 250px;
  width: 100%;
}

.maps-list {
  max-height: 250px;
  overflow-y: auto;
}

.map-performance-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #34495e;
  transition: background 0.2s ease;
}

.map-performance-item:hover {
  background: #34495e;
}

.map-name {
  font-weight: bold;
  color: #ecf0f1;
}

.map-stats {
  display: flex;
  gap: 10px;
  align-items: center;
}

.map-games {
  color: #bdc3c7;
  font-size: 0.9rem;
}

.map-winrate {
  font-weight: bold;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85rem;
}

.map-winrate.high {
  background: #27ae60;
  color: white;
}

.map-winrate.medium {
  background: #f39c12;
  color: white;
}

.map-winrate.low {
  background: #e74c3c;
  color: white;
}

/* Overview Tab Styles */
.overview-content {
  padding: 20px;
}

.player-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 25px;
  border: 2px solid #d4af37;
}

.player-info h2 {
  color: #d4af37;
  margin: 0 0 10px 0;
  font-size: 1.8rem;
  font-weight: bold;
}

.player-rank {
  display: flex;
  align-items: center;
  gap: 10px;
}

.rank-icon {
  width: 32px;
  height: 32px;
  border-radius: 4px;
}

.rank-name {
  color: #ecf0f1;
  font-weight: bold;
  font-size: 1.1rem;
}

.player-mmr {
  text-align: center;
}

.mmr-value {
  font-size: 2.5rem;
  font-weight: bold;
  color: #d4af37;
  line-height: 1;
}

.mmr-label {
  color: #bdc3c7;
  font-size: 0.9rem;
  margin-top: 5px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin-bottom: 25px;
}

.stat-card {
  background: #2c3e50;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #34495e;
  display: flex;
  align-items: center;
  gap: 15px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.2);
}

.stat-icon {
  font-size: 1.5rem;
  color: #d4af37;
  width: 30px;
  text-align: center;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #ecf0f1;
  line-height: 1;
}

.stat-label {
  color: #bdc3c7;
  font-size: 0.85rem;
  margin-top: 5px;
}

.match-type-breakdown {
  background: #2c3e50;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #34495e;
}

.match-type-breakdown h3 {
  color: #d4af37;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.match-types-grid {
  display: grid;
  gap: 15px;
}

.match-type-card {
  background: #34495e;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #4a5f7a;
}

.match-type-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.match-type-name {
  font-weight: bold;
  color: #d4af37;
  font-size: 1.1rem;
}

.match-type-count {
  color: #bdc3c7;
  font-size: 0.9rem;
}

.match-type-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.win-loss-bar {
  height: 8px;
  background: #e74c3c;
  border-radius: 4px;
  overflow: hidden;
}

.wins-bar {
  height: 100%;
  background: #27ae60;
  transition: width 0.3s ease;
}

.win-loss-text {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.wins {
  color: #27ae60;
  font-weight: bold;
}

.losses {
  color: #e74c3c;
  font-weight: bold;
}

.winrate {
  color: #d4af37;
  font-weight: bold;
}

/* ===== BARRACKS GAME TYPE FILTER ===== */
.barracks-filter-bar {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.game-type-tabs {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.game-type-tab {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.7);
  font-family: 'Cinzel', serif;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  overflow: hidden;
  min-width: 100px;
  justify-content: center;
}

.game-type-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.1), transparent);
  transition: left 0.6s;
}

.game-type-tab:hover {
  background: rgba(255, 215, 0, 0.08);
  border-color: rgba(255, 215, 0, 0.2);
  color: #ffd700;
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 3px 10px rgba(255, 215, 0, 0.2);
}

.game-type-tab:hover::before {
  left: 100%;
}

.game-type-tab.active {
  background: linear-gradient(45deg, rgba(255, 215, 0, 0.15), rgba(255, 237, 78, 0.15));
  border-color: #ffd700;
  color: #ffd700;
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 3px 10px rgba(255, 215, 0, 0.3);
}

.game-type-tab i {
  font-size: 1rem;
  transition: transform 0.3s ease;
}

.game-type-tab:hover i {
  transform: scale(1.1);
}

.game-type-tab span {
  font-size: 0.9rem;
  letter-spacing: 0.5px;
}

/* Game-specific styling */
.game-type-tab[data-game-type="war1"] {
  border-color: rgba(139, 69, 19, 0.3);
}

.game-type-tab[data-game-type="war1"]:hover,
.game-type-tab[data-game-type="war1"].active {
  border-color: #8b4513;
  color: #daa520;
  background: linear-gradient(45deg, rgba(139, 69, 19, 0.2), rgba(218, 165, 32, 0.15));
}

.game-type-tab[data-game-type="war2"] {
  border-color: rgba(65, 105, 225, 0.3);
}

.game-type-tab[data-game-type="war2"]:hover,
.game-type-tab[data-game-type="war2"].active {
  border-color: #4169e1;
  color: #87ceeb;
  background: linear-gradient(45deg, rgba(65, 105, 225, 0.2), rgba(135, 206, 235, 0.15));
}

.game-type-tab[data-game-type="war3"] {
  border-color: rgba(138, 43, 226, 0.3);
}

.game-type-tab[data-game-type="war3"]:hover,
.game-type-tab[data-game-type="war3"].active {
  border-color: #8a2be2;
  color: #da70d6;
  background: linear-gradient(45deg, rgba(138, 43, 226, 0.2), rgba(218, 112, 214, 0.15));
}

/* Player cards styling for each game type */
.player-card[data-game-type="war1"] {
  border-left: 3px solid #8b4513;
}

.player-card[data-game-type="war2"] {
  border-left: 3px solid #4169e1;
}

.player-card[data-game-type="war3"] {
  border-left: 3px solid #8a2be2;
}

/* Enhanced player card layout for Barracks */
.player-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 0.75rem;
  margin-bottom: 0.75rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.player-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 215, 0, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.player-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.player-name {
  font-size: 1rem;
  font-weight: 600;
  color: #ffd700;
  font-family: 'Cinzel', serif;
}

.player-game-type {
  background: rgba(255, 215, 0, 0.2);
  color: #ffd700;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.player-rank-section {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.player-rank-image {
  width: 36px;
  height: 36px;
  border-radius: 6px;
  border: 2px solid rgba(255, 215, 0, 0.3);
}

.rank-info {
  flex: 1;
}

.rank-name {
  font-weight: 600;
  color: #ecf0f1;
  margin-bottom: 0.2rem;
  font-size: 0.9rem;
}

.player-mmr {
  color: #bdc3c7;
  font-size: 0.8rem;
}

.player-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.4rem;
  margin-bottom: 0.5rem;
}

.stat-item {
  text-align: center;
  padding: 0.4rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 6px;
}

.stat-value {
  display: block;
  font-size: 0.9rem;
  font-weight: 600;
  color: #ffd700;
  margin-bottom: 0.2rem;
}

.stat-label {
  font-size: 0.7rem;
  color: #bdc3c7;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.player-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-view-stats {
  flex: 1;
  padding: 0.4rem 0.8rem;
  background: linear-gradient(45deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.1));
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: 6px;
  color: #ffd700;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.4rem;
}

.btn-view-stats:hover {
  background: linear-gradient(45deg, rgba(255, 215, 0, 0.3), rgba(255, 215, 0, 0.2));
  border-color: rgba(255, 215, 0, 0.5);
  transform: translateY(-1px);
}

/* ===== BARRACKS WELCOME MESSAGE ===== */
.no-players-welcome {
  text-align: center;
  padding: 3rem 2rem;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 100%);
  border: 1px solid rgba(255, 215, 0, 0.2);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.no-players-welcome::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 215, 0, 0.05) 50%, transparent 70%);
  pointer-events: none;
}

.welcome-icon {
  position: relative;
  z-index: 1;
  margin-bottom: 1.5rem;
}

.welcome-icon i {
  font-size: 4rem;
  color: #ffd700;
  filter: drop-shadow(0 4px 8px rgba(255, 215, 0, 0.3));
  animation: pulse 2s ease-in-out infinite alternate;
}

.no-players-welcome h3 {
  color: #ffd700;
  font-family: 'Cinzel', serif;
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  background: linear-gradient(135deg, #ffd700 0%, #ffed4a 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.no-players-welcome > p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  line-height: 1.6;
  margin: 0 0 2rem 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.welcome-benefits {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin: 2rem 0;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 215, 0, 0.1);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.benefit-item:hover {
  background: rgba(255, 215, 0, 0.1);
  border-color: rgba(255, 215, 0, 0.3);
  transform: translateX(5px);
}

.benefit-item i {
  font-size: 1.5rem;
  color: #ffd700;
  flex-shrink: 0;
}

.benefit-item span {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  font-weight: 500;
}

.welcome-actions {
  margin-top: 2rem;
}

.welcome-actions .epic-btn {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  background: linear-gradient(135deg, #ffd700, #ffed4a);
  color: #1a1a2e;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.welcome-actions .epic-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
  background: linear-gradient(135deg, #ffed4a, #ffd700);
}

@keyframes pulse {
  0% { transform: scale(1); }
  100% { transform: scale(1.05); }
}

/* ===== FILTER ACTIONS ===== */
.filter-actions {
  margin-top: 1.5rem;
}

.filter-actions .epic-btn.secondary {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.1));
  border: 1px solid rgba(255, 215, 0, 0.3);
  color: #ffd700;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
}

.filter-actions .epic-btn.secondary:hover {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.3), rgba(255, 215, 0, 0.2));
  border-color: rgba(255, 215, 0, 0.5);
  color: #fff;
}

/* ===== EXISTING NO PLAYERS MESSAGE ===== */
.no-players-message {
  text-align: center;
  padding: 2rem;
  color: #bdc3c7;
}

.no-players-message i {
  font-size: 3rem;
  color: rgba(255, 215, 0, 0.3);
  margin-bottom: 1rem;
}

.no-players-message p {
  margin: 0.5rem 0;
}

.no-players-message p:first-of-type {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ecf0f1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .charts-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .matches-header,
  .match-row {
    grid-template-columns: 1fr 1fr 1fr;
    font-size: 0.8rem;
  }
  
  .matches-header .header-cell:nth-child(n+4),
  .match-row > div:nth-child(n+4) {
    display: none;
  }
  
  .player-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .game-type-tabs {
    gap: 0.25rem;
  }
  
  .game-type-tab {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    min-width: 80px;
  }
  
  .game-type-tab span {
    font-size: 0.75rem;
  }

  /* Barracks welcome message responsive */
  .no-players-welcome {
    padding: 2rem 1rem;
  }
  
  .no-players-welcome h3 {
    font-size: 1.5rem;
  }
  
  .no-players-welcome > p {
    font-size: 1rem;
  }
  
  .welcome-icon i {
    font-size: 3rem;
  }
  
  .benefit-item {
    padding: 0.75rem;
    gap: 0.75rem;
  }
  
  .benefit-item i {
    font-size: 1.25rem;
  }
  
  .benefit-item span {
    font-size: 0.9rem;
  }
  
  .welcome-actions .epic-btn {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }
}

/* ===== ENHANCED CLAN MANAGEMENT STYLING ===== */

/* No Clan Container */
.no-clan-container {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 100%);
  border: 1px solid rgba(255, 215, 0, 0.2);
  border-radius: 16px;
  padding: 2.5rem;
  text-align: center;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.no-clan-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 215, 0, 0.05) 50%, transparent 70%);
  pointer-events: none;
}

.no-clan-header {
  position: relative;
  z-index: 1;
  margin-bottom: 2.5rem;
}

.no-clan-header h2 {
  color: #ffd700;
  font-family: 'Cinzel', serif;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  background: linear-gradient(135deg, #ffd700 0%, #ffed4a 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.no-clan-header p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

/* Clan Actions Grid */
.clan-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-bottom: 2.5rem;
  position: relative;
  z-index: 1;
}

.action-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
  border: 1px solid rgba(255, 215, 0, 0.2);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(5px);
}

.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.1), transparent);
  transition: left 0.6s ease;
}

.action-card:hover::before {
  left: 100%;
}

.action-card:hover {
  transform: translateY(-8px) scale(1.02);
  border-color: rgba(255, 215, 0, 0.4);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 215, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.action-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #ffd700 0%, #ffed4a 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  box-shadow: 
    0 8px 16px rgba(255, 215, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.action-card:hover .action-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 
    0 12px 24px rgba(255, 215, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.action-icon i {
  font-size: 2rem;
  color: #0f172a;
  transition: all 0.3s ease;
}

.action-card:hover .action-icon i {
  transform: scale(1.1);
}

.action-card h3 {
  color: #ffffff;
  font-family: 'Cinzel', serif;
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.action-card p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 1.5rem;
}

.action-card .btn {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  border: none;
  color: white;
  padding: 0.875rem 2rem;
  border-radius: 10px;
  font-weight: 600;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
  position: relative;
  overflow: hidden;
  min-width: 140px;
}

.action-card .btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.action-card .btn:hover::before {
  left: 100%;
}

.action-card .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(79, 70, 229, 0.4);
}

.action-card .btn-secondary {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
}

.action-card .btn-secondary:hover {
  box-shadow: 0 8px 20px rgba(107, 114, 128, 0.4);
}

/* Clan Tab Content */
.clan-tab-content {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 215, 0, 0.1);
  border-radius: 12px;
  padding: 2rem;
  position: relative;
  z-index: 1;
}

/* Search Section */
.clan-search-section {
  margin-bottom: 2rem;
}

.search-bar {
  display: flex;
  gap: 1rem;
  max-width: 500px;
  margin: 0 auto;
}

.search-bar input {
  flex: 1;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 215, 0, 0.2);
  border-radius: 10px;
  padding: 1rem 1.5rem;
  color: #ffffff;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.search-bar input:focus {
  outline: none;
  border-color: rgba(255, 215, 0, 0.4);
  box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
  background: rgba(255, 255, 255, 0.08);
}

.search-bar input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.search-bar .btn {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4a 100%);
  border: none;
  color: #0f172a;
  padding: 1rem 1.5rem;
  border-radius: 10px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.search-bar .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 215, 0, 0.4);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 2rem;
  color: rgba(255, 255, 255, 0.6);
}

.empty-state-icon {
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 215, 0, 0.05) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  border: 2px dashed rgba(255, 215, 0, 0.3);
}

.empty-state-icon i {
  font-size: 2.5rem;
  color: rgba(255, 215, 0, 0.6);
}

.empty-state-title {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  font-family: 'Cinzel', serif;
}

.empty-state-message {
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 2rem;
  font-style: italic;
}

.empty-state-action {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4a 100%);
  border: none;
  color: #0f172a;
  padding: 1rem 2rem;
  border-radius: 10px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.empty-state-action:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 215, 0, 0.4);
}

/* Responsive Design for Clan Management */
@media (max-width: 768px) {
  .no-clan-container {
    padding: 1.5rem;
  }
  
  .no-clan-header h2 {
    font-size: 1.75rem;
  }
  
  .clan-actions-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
  }
  
  .action-card {
    padding: 1.5rem;
  }
  
  .action-icon {
    width: 70px;
    height: 70px;
    margin-bottom: 1rem;
  }
  
  .action-icon i {
    font-size: 1.75rem;
  }
  
  .action-card h3 {
    font-size: 1.25rem;
  }
  
  .search-bar {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .clan-tab-content {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .no-clan-header h2 {
    font-size: 1.5rem;
  }
  
  .no-clan-header p {
    font-size: 1rem;
  }
  
  .action-card h3 {
    font-size: 1.1rem;
  }
  
  .action-card p {
    font-size: 0.9rem;
  }
} 

/* Activity Tabs */
.activity-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 0.5rem;
}

.activity-tab {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem 0.5rem 0 0;
  padding: 0.75rem 1.25rem;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 500;
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.activity-tab::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: transparent;
  transition: background 0.3s ease;
}

.activity-tab:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 215, 0, 0.3);
  color: rgba(255, 255, 255, 0.9);
}

.activity-tab.active {
  background: rgba(255, 215, 0, 0.1);
  border-color: rgba(255, 215, 0, 0.3);
  color: #ffd700;
  border-bottom-color: transparent;
}

.activity-tab.active::after {
  background: #ffd700;
}

/* Add collapse button to tabs */
.activity-tab-collapse {
  margin-left: auto;
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 0.2rem;
  border-radius: 0.25rem;
  transition: all 0.3s ease;
  opacity: 0.7;
}

.activity-tab-collapse:hover {
  background: rgba(255, 255, 255, 0.1);
  opacity: 1;
}

.activity-tab.collapsed {
  opacity: 0.5;
}

.activity-tab.collapsed .activity-tab-collapse i {
  transform: rotate(180deg);
}

/* Activity Content */
.activity-content {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 0.75rem;
  padding: 1rem;
  transition: all 0.3s ease;
}

.activity-content.collapsed {
  display: none;
}

/* War Table specific styles */
#section-campaign .stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 0.75rem;
  margin-bottom: 1rem;
}

#section-campaign .stat-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  padding: 0.75rem;
  text-align: center;
  transition: all 0.2s ease;
}

#section-campaign .stat-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 215, 0, 0.3);
  transform: translateY(-2px);
}

#section-campaign .stat-icon {
  color: var(--primary-gold);
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
}

#section-campaign .stat-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.25rem;
}

#section-campaign .stat-label {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Experience section in War Table */
#section-campaign .experience-section {
  margin-top: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  padding: 0.75rem;
}

#section-campaign .experience-section h4 {
  margin: 0 0 0.75rem 0;
  color: var(--primary-gold);
  font-size: 0.9rem;
}

/* Achievement cards in War Table */
#section-campaign .achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 0.75rem;
}

#section-campaign .achievement-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  padding: 0.75rem;
  transition: all 0.3s ease;
}

#section-campaign .achievement-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 215, 0, 0.3);
  transform: translateY(-2px);
}

#section-campaign .achievement-card.completed {
  border-color: rgba(34, 197, 94, 0.3);
  background: rgba(34, 197, 94, 0.05);
}

#section-campaign .achievement-icon {
  color: var(--primary-gold);
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

#section-campaign .achievement-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: white;
  margin-bottom: 0.5rem;
}

#section-campaign .achievement-description {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

/* ===============================
   AVATAR SELECTION SYSTEM
   =============================== */

/* Change Avatar Button */
.change-avatar-btn {
  position: absolute;
  bottom: -8px;
  right: -8px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: #1a1a1a;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
  z-index: 10;
}

.change-avatar-btn:hover {
  background: linear-gradient(135deg, #ffed4e, #ffd700);
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(255, 215, 0, 0.6);
}

.change-avatar-btn i {
  font-size: 14px;
}

/* Avatar Selection Modal */
.avatar-options-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 1rem 0;
}

.avatar-option {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 2px solid transparent;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.avatar-option:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 215, 0, 0.5);
  transform: translateY(-2px);
}

.avatar-option.selected {
  background: rgba(255, 215, 0, 0.1);
  border-color: #ffd700;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.avatar-option.unavailable {
  opacity: 0.5;
  cursor: not-allowed;
}

.avatar-option.unavailable:hover {
  transform: none;
  border-color: transparent;
  background: rgba(255, 255, 255, 0.05);
}

.avatar-preview {
  position: relative;
  width: 80px;
  height: 80px;
  flex-shrink: 0;
}

.avatar-preview img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.avatar-selection-indicator {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 24px;
  height: 24px;
  background: #4CAF50;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: scale(0);
  transition: all 0.3s ease;
  border: 2px solid white;
}

.avatar-option.selected .avatar-selection-indicator {
  opacity: 1;
  transform: scale(1);
}

.avatar-selection-indicator i {
  color: white;
  font-size: 10px;
}

.avatar-unavailable-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: #ccc;
  font-weight: 500;
}

.avatar-info h3,
.avatar-info h4 {
  margin: 0 0 0.25rem 0;
  color: #ffd700;
  font-size: 1.1rem;
  font-weight: 600;
}

.avatar-info p {
  margin: 0;
  color: #ccc;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Custom Avatars Section */
.custom-avatars-section {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 1.5rem;
}

.custom-section-title {
  margin: 0 0 1rem 0;
  color: #ffd700;
  font-size: 1.2rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.custom-section-title i {
  color: #FFD700;
  animation: twinkle 2s infinite;
}

@keyframes twinkle {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.custom-avatars-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.custom-option {
  flex-direction: column;
  text-align: center;
  padding: 1.5rem 1rem;
}

.custom-option .avatar-preview {
  width: 100px;
  height: 100px;
  margin: 0 auto 1rem auto;
}

.custom-option .avatar-info h4 {
  font-size: 1rem;
  margin-bottom: 0.5rem;
}

.custom-option .avatar-info p {
  font-size: 0.85rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .avatar-option {
    flex-direction: column;
    text-align: center;
    padding: 1.5rem 1rem;
  }
  
  .custom-avatars-grid {
    grid-template-columns: 1fr;
  }
  
  .change-avatar-btn {
    width: 28px;
    height: 28px;
    bottom: -6px;
    right: -6px;
  }
  
  .change-avatar-btn i {
    font-size: 12px;
  }
}

/* Modal footer for avatar selection */
.avatar-selection-modal .modal-footer {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 1rem;
  margin-top: 1rem;
}

.avatar-selection-modal .form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

/* Success feedback */
.avatar-update-success {
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  color: #4CAF50;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.avatar-update-success i {
  color: #4CAF50;
}

/* Hero Tier Section Styles */
.hero-tier-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.hero-status-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 215, 0, 0.05));
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.hero-status-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 193, 7, 0.2);
}

.hero-status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  border-radius: 50%;
  color: #1a1a1a;
  font-size: 1.8rem;
  flex-shrink: 0;
}

.hero-status-info {
  flex: 1;
}

.hero-status-info h3 {
  margin: 0 0 0.5rem 0;
  color: #ffd700;
  font-size: 1.4rem;
  font-weight: 700;
}

.hero-status-info p {
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

.hero-status-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 193, 7, 0.5);
  border-radius: 50%;
  color: #ffd700;
  font-size: 1.2rem;
  font-weight: bold;
}

/* Non-hero status styling */
.hero-status-card.no-hero {
  background: linear-gradient(135deg, rgba(108, 117, 125, 0.1), rgba(108, 117, 125, 0.05));
  border-color: rgba(108, 117, 125, 0.3);
}

.hero-status-card.no-hero .hero-status-icon {
  background: linear-gradient(135deg, #6c757d, #adb5bd);
  color: #fff;
}

.hero-status-card.no-hero .hero-status-info h3 {
  color: #adb5bd;
}

.hero-status-card.no-hero .hero-status-badge {
  border-color: rgba(108, 117, 125, 0.5);
  color: #adb5bd;
}

/* Unlocked Images Section */
.unlocked-images-section {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.unlocked-images-section h4 {
  margin: 0 0 1rem 0;
  color: #e2e8f0;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.unlocked-images-section h4 i {
  color: #ffd700;
}

.unlocked-images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 1rem;
  max-width: 100%;
}

.unlocked-image-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.unlocked-image-item:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 193, 7, 0.3);
}

.unlocked-image-item img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 193, 7, 0.5);
}

.unlocked-image-item span {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
}

/* Locked image item */
.unlocked-image-item.locked {
  opacity: 0.5;
  border-color: rgba(108, 117, 125, 0.3);
}

.unlocked-image-item.locked img {
  border-color: rgba(108, 117, 125, 0.5);
  filter: grayscale(100%);
}

.unlocked-image-item.locked::after {
  content: '\f023';
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #6c757d;
  font-size: 1.2rem;
}

.unlocked-image-item.locked {
  position: relative;
}

/* Hero Action Section */
.hero-action-section {
  text-align: center;
  padding: 1.5rem;
  background: rgba(255, 193, 7, 0.05);
  border: 1px solid rgba(255, 193, 7, 0.2);
  border-radius: 8px;
}

.hero-action-btn {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  min-width: 200px;
}

.hero-action-btn i {
  margin-right: 0.5rem;
}

.hero-action-subtitle {
  margin: 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Change Plan button styling for existing heroes */
.hero-action-btn.change-plan {
  background: linear-gradient(135deg, #28a745, #34ce57);
  border-color: #28a745;
}

.hero-action-btn.change-plan:hover {
  background: linear-gradient(135deg, #218838, #28a745);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

/* Cancel subscription button */
.hero-action-btn.cancel-subscription {
  background: linear-gradient(135deg, #dc3545, #e74c3c);
  border-color: #dc3545;
  margin-top: 0.5rem;
}

.hero-action-btn.cancel-subscription:hover {
  background: linear-gradient(135deg, #c82333, #dc3545);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

/* Avatar Lock Overlay */
.avatar-lock-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  gap: 0.5rem;
  font-size: 0.8rem;
  color: #ffd700;
  transition: all 0.3s ease;
}

.avatar-lock-overlay i {
  font-size: 1.5rem;
  color: #dc3545;
}

.avatar-option.locked {
  opacity: 0.6;
  cursor: not-allowed;
}

.avatar-option.locked:hover {
  transform: none;
  box-shadow: none;
}

.avatar-option.locked .avatar-preview {
  position: relative;
}

.avatar-option.locked .avatar-lock-overlay {
  display: flex !important;
}

/* Responsive design */
@media (max-width: 768px) {
  .hero-status-card {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
  
  .unlocked-images-grid {
    grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
    gap: 0.75rem;
  }
  
  .unlocked-image-item img {
    width: 40px;
    height: 40px;
  }
  
  .hero-action-btn {
    min-width: auto;
    width: 100%;
  }
}
