"""
Populate MongoDB war3maps collection using the comprehensive War3 parser
Includes all strategic data: goldmines, creeps, structures, starting positions, drop tables, shop inventories
"""

from war3_map_info_parser import War3MapInfoParser
from pymongo import MongoClient
from datetime import datetime
from pathlib import Path
import os

def populate_mongodb():
    """Populate MongoDB with complete War3 map data including all strategic information"""
    
    # Initialize parser
    parser = War3MapInfoParser()
    
    # Connect to MongoDB
    try:
        client = MongoClient('mongodb://localhost:27017/')
        db = client['newsite']
        collection = db['war3maps']
        print("✅ Connected to MongoDB")
    except Exception as e:
        print(f"❌ Failed to connect to MongoDB: {e}")
        return
    
    # Clear existing collection
    print("🗑️ Clearing existing collection...")
    collection.delete_many({})
    
    # Get all maps
    maps_dir = Path('uploads/war3')
    map_files = list(maps_dir.glob('*.w3x'))
    print(f"📊 Found {len(map_files)} map files")
    
    successful_parses = 0
    failed_parses = 0
    
    for i, map_file in enumerate(map_files, 1):
        print(f"\n🎯 [{i}/{len(map_files)}] Processing {map_file.name}...")
        
        try:
            # Parse the map
            map_data = parser.parse_map(str(map_file))
            
            if not map_data:
                print(f"    ❌ Failed to parse {map_file.name}")
                failed_parses += 1
                continue
            
            # Combine starting positions from all sources
            all_starting_positions = []
            
            # Add JASS starting positions
            jass_starting = map_data.get('starting_positions', [])
            if jass_starting:
                all_starting_positions.extend(jass_starting)
                print(f"    📍 Found {len(jass_starting)} JASS starting positions")
            
            # Add DOO starting positions
            doo_starting = map_data.get('doo_starting_positions', [])
            if doo_starting:
                all_starting_positions.extend(doo_starting)
                print(f"    📍 Found {len(doo_starting)} DOO starting positions")
            
            # Prepare comprehensive strategic data
            strategic_data = {
                # JASS data (from script parsing)
                'jassGoldmines': map_data.get('jass_goldmines', []),
                'jassNeutralStructures': map_data.get('jass_neutral_structures', []),
                'jassCreepUnits': map_data.get('jass_creep_units', []),
                'jassStartingPositions': jass_starting,
                
                # DOO data (from binary file parsing)
                'dooGoldmines': map_data.get('doo_goldmines', []),
                'dooNeutralStructures': map_data.get('doo_neutral_structures', []),
                'dooCreepUnits': map_data.get('doo_creep_units', []),
                'dooStartingPositions': doo_starting,
                
                # Combined starting positions
                'starting_positions': all_starting_positions,
                
                # Advanced features
                'dropTables': map_data.get('drop_tables', []),
                'inferredDropTables': map_data.get('inferred_drop_tables', []),
                'shopInventories': map_data.get('shop_inventories', []),
                
                # Metadata
                'hasJassData': bool(map_data.get('jass_goldmines') or map_data.get('jass_creep_units')),
                'hasDooData': bool(map_data.get('doo_goldmines') or map_data.get('doo_creep_units')),
                'parsingMethod': 'JASS+DOO' if (map_data.get('jass_goldmines') and map_data.get('doo_goldmines')) else 'DOO' if map_data.get('doo_goldmines') else 'JASS'
            }
            
            # Calculate totals for quick access
            total_goldmines = len(set(
                [(g.get('x', 0), g.get('y', 0)) for g in strategic_data['jassGoldmines']] +
                [(g.get('x', 0), g.get('y', 0)) for g in strategic_data['dooGoldmines']]
            ))
            
            total_structures = len(set(
                [(s.get('x', 0), s.get('y', 0)) for s in strategic_data['jassNeutralStructures']] +
                [(s.get('x', 0), s.get('y', 0)) for s in strategic_data['dooNeutralStructures']]
            ))
            
            total_creeps = len(set(
                [(c.get('x', 0), c.get('y', 0)) for c in strategic_data['jassCreepUnits']] +
                [(c.get('x', 0), c.get('y', 0)) for c in strategic_data['dooCreepUnits']]
            ))
            
            # Fix player count - use starting positions count if available, otherwise use W3I data
            player_count = len(all_starting_positions) if all_starting_positions else map_data.get('players', 0)
            
            # Prepare MongoDB document
            doc = {
                'filename': map_file.name,
                'name': map_data.get('name', map_file.stem),
                'author': map_data.get('author', 'Unknown'),
                'description': map_data.get('description', ''),
                'players': player_count,
                'mapSize': f"{map_data.get('width', 0)}x{map_data.get('height', 0)}",
                'tileset': map_data.get('tileset', 'Unknown'),
                
                # Strategic counts
                'goldmines': total_goldmines,
                'neutralStructures': total_structures,
                'creepUnits': total_creeps,
                'startingLocations': len(all_starting_positions),
                'dropTables': len(strategic_data['dropTables']),
                'inferredDropTables': len(strategic_data['inferredDropTables']),
                'shopInventories': len(strategic_data['shopInventories']),
                
                # File paths
                'thumbnailPath': f"uploads/war3images/{map_file.stem}.png",
                'hasThumbnail': Path(f"uploads/war3images/{map_file.stem}.png").exists(),
                'overlayPath': f"uploads/war3images/strategic_overlays_with_thumbnails/{map_file.stem.replace(' ', '_')}_strategic_overlay.png",
                
                # Quality metrics
                'accuracyScore': 1.0,  # Assume perfect accuracy with comprehensive parser
                'parsingMethod': strategic_data['parsingMethod'],
                'dataCompleteness': {
                    'hasJass': strategic_data['hasJassData'],
                    'hasDoo': strategic_data['hasDooData'],
                    'hasDropTables': len(strategic_data['dropTables']) > 0,
                    'hasInferredDropTables': len(strategic_data['inferredDropTables']) > 0,
                    'hasShopInventories': len(strategic_data['shopInventories']) > 0,
                    'hasStartingPositions': len(all_starting_positions) > 0
                },
                
                # Complete strategic data
                'strategicData': strategic_data,
                
                # Timestamps
                'createdAt': datetime.now(),
                'updatedAt': datetime.now()
            }
            
            # Insert into MongoDB
            collection.insert_one(doc)
            successful_parses += 1
            
            print(f"    ✅ Inserted: {doc['name']}")
            print(f"       👥 {doc['players']} players, 💰 {doc['goldmines']} goldmines, 🏛️ {doc['neutralStructures']} structures")
            print(f"       👹 {doc['creepUnits']} creeps, 📍 {doc['startingLocations']} starting positions")
            print(f"       💎 {doc['dropTables']} JASS drops, 🔮 {doc['inferredDropTables']} inferred drops, 🛒 {doc['shopInventories']} shop inventories")
            print(f"       📊 Method: {doc['parsingMethod']}")
            
        except Exception as e:
            print(f"    ❌ Error processing {map_file.name}: {e}")
            failed_parses += 1
    
    print(f"\n🎉 Population complete!")
    print(f"✅ Successfully parsed: {successful_parses}/{len(map_files)} maps")
    print(f"❌ Failed to parse: {failed_parses}/{len(map_files)} maps")
    
    # Show sample records
    sample_count = collection.count_documents({})
    print(f"📊 Total documents in collection: {sample_count}")
    
    # Show statistics
    jass_count = collection.count_documents({'dataCompleteness.hasJass': True})
    doo_count = collection.count_documents({'dataCompleteness.hasDoo': True})
    both_count = collection.count_documents({'$and': [{'dataCompleteness.hasJass': True}, {'dataCompleteness.hasDoo': True}]})
    drop_count = collection.count_documents({'dataCompleteness.hasDropTables': True})
    inferred_drop_count = collection.count_documents({'dataCompleteness.hasInferredDropTables': True})
    shop_count = collection.count_documents({'dataCompleteness.hasShopInventories': True})
    starting_count = collection.count_documents({'dataCompleteness.hasStartingPositions': True})
    
    print(f"\n📈 Data Coverage Statistics:")
    print(f"   🎮 JASS data: {jass_count}/{sample_count} maps ({jass_count/sample_count*100:.1f}%)")
    print(f"   🗂️ DOO data: {doo_count}/{sample_count} maps ({doo_count/sample_count*100:.1f}%)")
    print(f"   🔄 Both JASS+DOO: {both_count}/{sample_count} maps ({both_count/sample_count*100:.1f}%)")
    print(f"   💎 JASS drop tables: {drop_count}/{sample_count} maps ({drop_count/sample_count*100:.1f}%)")
    print(f"   🔮 Inferred drop tables: {inferred_drop_count}/{sample_count} maps ({inferred_drop_count/sample_count*100:.1f}%)")
    print(f"   🛒 Shop inventories: {shop_count}/{sample_count} maps ({shop_count/sample_count*100:.1f}%)")
    print(f"   📍 Starting positions: {starting_count}/{sample_count} maps ({starting_count/sample_count*100:.1f}%)")
    
    # Show a sample record
    sample = collection.find_one({'players': {'$gt': 0}})
    if sample:
        print(f"\n📋 Sample record:")
        print(f"  Name: {sample.get('name')}")
        print(f"  Author: {sample.get('author')}")
        print(f"  Players: {sample.get('players')}")
        print(f"  Size: {sample.get('mapSize')}")
        print(f"  Tileset: {sample.get('tileset')}")
        print(f"  Strategic Elements: {sample.get('goldmines')}💰 {sample.get('neutralStructures')}🏛️ {sample.get('creepUnits')}👹")
        print(f"  Advanced Features: {sample.get('dropTables')}💎 {sample.get('inferredDropTables')}🔮 {sample.get('shopInventories')}🛒")
        print(f"  Parsing Method: {sample.get('parsingMethod')}")
    
    client.close()

if __name__ == '__main__':
    populate_mongodb() 