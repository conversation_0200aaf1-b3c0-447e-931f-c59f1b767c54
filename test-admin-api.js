const fetch = require('node-fetch');

async function testAdminAPI() {
  try {
    console.log('Testing admin dashboard-stats API...');
    
    const response = await fetch('http://localhost:3000/api/admin/dashboard-stats', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': 'connect.sid=your-session-id' // You'd need a real session
      }
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', response.headers.raw());
    
    const text = await response.text();
    console.log('Response body:', text);
    
    if (response.ok) {
      try {
        const json = JSON.parse(text);
        console.log('Parsed JSON:', json);
      } catch (e) {
        console.log('Failed to parse as JSON');
      }
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

testAdminAPI();
