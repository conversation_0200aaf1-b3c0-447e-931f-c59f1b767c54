const express = require('express');
const router = express.Router();
const User = require('../models/User');
const AchievementService = require('../services/achievementService');
const { ensureAuthenticated } = require('../middleware/auth');

/**
 * Get current user profile
 * GET /api/users/me
 */
router.get('/me', ensureAuthenticated, async (req, res) => {
  try {
    const user = await User.findById(req.user._id).select('-password');

    // Award test achievement for visiting profile
    try {
      await AchievementService.awardAchievement(user._id, 'test_visit_profile');
    } catch (error) {
      console.log('Could not award test achievement (may already be awarded):', error.message);
    }

    // Ensure role is included for permission checks
    if (!user.role) {
      user.role = 'user'; // Default role if not set
    }

    res.json(user);
  } catch (error) {
    console.error('Error getting user profile:', error);
    res.status(500).json({ error: 'Failed to get user profile' });
  }
});

/**
 * Update user profile
 * PUT /api/users/me
 */
router.put('/me', ensureAuthenticated, async (req, res) => {
  try {
    let { 
      username, 
      email, 
      avatar, 
      bio,
      age,
      gender,
      country,
      favorite_game,
      favorite_race,
      favorite_strategy,
      first_played
    } = req.body;
    
    const user = await User.findById(req.user._id);

    // Update basic fields
    if (username) {
      username = username.toUpperCase(); // Convert to uppercase
      user.username = username;
    }
    if (email) user.email = email;
    if (avatar) user.avatar = avatar;
    if (bio !== undefined) user.bio = bio;

    // Initialize profile object if it doesn't exist
    if (!user.profile) {
      user.profile = {};
    }
    if (!user.profile.warcraftPreferences) {
      user.profile.warcraftPreferences = {};
    }

    // Update profile fields
    if (age !== undefined) user.profile.age = age;
    if (gender !== undefined) user.profile.gender = gender;
    if (country !== undefined) user.profile.country = country;
    
    // Update Warcraft preferences
    if (favorite_game !== undefined) user.profile.warcraftPreferences.favoriteGame = favorite_game;
    if (favorite_race !== undefined) user.profile.warcraftPreferences.favoriteRace = favorite_race;
    if (favorite_strategy !== undefined) user.profile.warcraftPreferences.favoriteStrategy = favorite_strategy;
    if (first_played !== undefined) {
      user.profile.warcraftPreferences.firstPlayed = first_played ? new Date(first_played) : null;
    }

    await user.save();
    res.json(user);
  } catch (error) {
    console.error('Error updating user profile:', error);
    res.status(500).json({ error: 'Failed to update user profile' });
  }
});

/**
 * Get user by ID
 * GET /api/users/:id
 */
router.get('/:id', ensureAuthenticated, async (req, res) => {
  try {
    const user = await User.findById(req.params.id).select('-password');
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }
    res.json(user);
  } catch (error) {
    console.error('Error getting user:', error);
    res.status(500).json({ error: 'Failed to get user' });
  }
});

/**
 * Delete user account and all related data
 * DELETE /api/users/delete-account
 */
router.delete('/delete-account', ensureAuthenticated, async (req, res) => {
  try {
    const userId = req.user._id;
    const UserDeletionService = require('../services/userDeletionService');
    
    console.log(`🗑️ Starting deletion process for user: ${userId}`);
    
    // Use the deletion service to handle all cleanup
    const result = await UserDeletionService.deleteUserAndCleanup(userId);
    
    console.log(`✅ User deletion completed successfully: ${userId}`);
    
    // Log out the user by destroying the session
    req.logout((err) => {
      if (err) {
        console.error('Error during logout:', err);
      }
    });
    
    res.json({ 
      success: true, 
      message: 'Account deleted successfully',
      cleanup: result
    });
    
  } catch (error) {
    console.error('Error deleting user account:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to delete account: ' + error.message 
    });
  }
});

/**
 * Save user profile layout
 * POST /api/users/profile/layout
 */
router.post('/profile/layout', ensureAuthenticated, async (req, res) => {
  try {
    const userId = req.user._id;
    const layoutData = req.body;
    
    console.log(`💾 Saving profile layout for user: ${userId}`);
    console.log(`📊 Layout data received:`, JSON.stringify(layoutData, null, 2));
    
    // Validate layout data
    if (!layoutData || !layoutData.sections || !Array.isArray(layoutData.sections)) {
      console.log('❌ Invalid layout data received');
      return res.status(400).json({ error: 'Invalid layout data' });
    }
    
    // Find and update user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    // Initialize profile object if it doesn't exist
    if (!user.profile) {
      user.profile = {};
    }
    
    // Save layout data
    user.profile.layout = {
      sections: layoutData.sections,
      timestamp: new Date(),
      version: '1.0'
    };
    
    console.log(`💾 About to save user with layout:`, {
      userId,
      hasProfile: !!user.profile,
      hasLayout: !!user.profile.layout,
      layoutSectionCount: user.profile.layout ? user.profile.layout.sections.length : 0
    });
    
    const saveResult = await user.save();
    
    console.log(`💾 Save result:`, {
      acknowledged: !!saveResult,
      modifiedCount: saveResult.modifiedCount || 'N/A',
      upsertedCount: saveResult.upsertedCount || 'N/A'
    });
    
    // Verify the save worked by re-fetching the user
    const verifyUser = await User.findById(userId);
    console.log(`🔍 Verification check - user profile after save:`, {
      hasProfile: !!verifyUser.profile,
      hasLayout: !!(verifyUser.profile && verifyUser.profile.layout),
      profileKeys: verifyUser.profile ? Object.keys(verifyUser.profile) : 'no profile'
    });
    
    console.log(`✅ Profile layout saved successfully for user: ${userId}`);
    res.json({ success: true, message: 'Layout saved successfully' });
    
  } catch (error) {
    console.error('Error saving profile layout:', error);
    res.status(500).json({ error: 'Failed to save layout' });
  }
});

/**
 * Get user profile layout
 * GET /api/users/profile/layout
 */
router.get('/profile/layout', ensureAuthenticated, async (req, res) => {
  try {
    const userId = req.user._id;
    
    console.log(`📖 Loading profile layout for user: ${userId}`);
    
    const user = await User.findById(userId);
    if (!user) {
      console.log(`❌ User not found: ${userId}`);
      return res.status(404).json({ error: 'User not found' });
    }
    
    console.log(`🔍 User profile structure:`, {
      hasProfile: !!user.profile,
      hasLayout: !!(user.profile && user.profile.layout),
      profileKeys: user.profile ? Object.keys(user.profile) : 'no profile'
    });
    
    // Show the entire user profile for debugging
    console.log(`🔍 Full user profile:`, user.profile);
    
    // Return layout data if it exists
    if (user.profile && user.profile.layout) {
      console.log(`✅ Profile layout found for user: ${userId}`);
      console.log(`📊 Layout data:`, JSON.stringify(user.profile.layout, null, 2));
      res.json(user.profile.layout);
    } else {
      console.log(`ℹ️ No profile layout found for user: ${userId}`);
      res.json(null); // No layout saved yet
    }
    
  } catch (error) {
    console.error('Error loading profile layout:', error);
    res.status(500).json({ error: 'Failed to load layout' });
  }
});

module.exports = router;