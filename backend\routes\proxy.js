const express = require('express');
const router = express.Router();
const axios = require('axios');

/**
 * Proxy route for external images (YouTube, Twitch, etc.)
 * This helps bypass CORS restrictions when loading profile images
 */
router.get('/image', async (req, res) => {
  try {
    const { url } = req.query;
    
    if (!url) {
      return res.status(400).json({ error: 'URL parameter is required' });
    }

    // Validate that the URL is from trusted sources
    const allowedDomains = [
      'yt3.ggpht.com',
      'yt3.googleusercontent.com',
      'static-cdn.jtvnw.net',
      'i.ytimg.com',
      'ytimg.googleusercontent.com',
      'lh3.googleusercontent.com',  // Google profile images
      'lh4.googleusercontent.com',  // Google profile images
      'lh5.googleusercontent.com',  // Google profile images
      'lh6.googleusercontent.com'   // Google profile images
    ];

    const urlObj = new URL(url);
    if (!allowedDomains.includes(urlObj.hostname)) {
      return res.status(403).json({ error: 'Domain not allowed' });
    }

    // Fetch the image
    const response = await axios.get(url, {
      responseType: 'stream',
      timeout: 10000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });

    // Set appropriate headers
    res.set({
      'Content-Type': response.headers['content-type'] || 'image/jpeg',
      'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
      'Access-Control-Allow-Origin': '*'
    });

    // Pipe the image stream to the response
    response.data.pipe(res);

  } catch (error) {
    console.error('Error proxying image:', error.message);
    
    // Return a default image on error
    const defaultImagePath = 'frontend/assets/img/ranks/emblem.png';
    res.sendFile(defaultImagePath, { root: '.' }, (err) => {
      if (err) {
        res.status(500).json({ error: 'Error loading image' });
      }
    });
  }
});

module.exports = router; 