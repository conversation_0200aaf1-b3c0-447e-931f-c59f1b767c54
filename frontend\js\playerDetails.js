/**
 * PlayerDetails.js - Player Statistics Modal for Profile Page
 * 
 * This file handles the player statistics modal specifically for the profile page.
 * Updated to use the new ModalManager system.
 */

// Ensure this only runs on profile pages to avoid conflicts with ladder page
if (window.location.pathname.includes('/views/myprofile.html') || 
    window.location.pathname.includes('/profile') ||
    window.location.pathname.includes('/ladder') ||
    document.getElementById('player-stats-modal')) {
  
  console.log('🎯 PlayerDetails.js loaded for profile/ladder page');

/**
 * Player Details Module
 * Handles displaying detailed player statistics in a modal using the new ModalManager
 */

// Global utility functions using the new modal system
window.showLoadingModal = function(message) {
  if (window.modalManager) {
    const modalId = window.modalManager.createModal({
      id: 'loading-modal',
      title: 'Loading',
      content: `<div class="loading-container"><div class="loading-spinner"></div><p>${message}</p></div>`,
      size: 'sm',
      showCloseButton: false,
      backdrop: false,
      keyboard: false
    });
    window.modalManager.show('loading-modal');
  }
};

window.hideLoadingModal = function() {
  if (window.modalManager) {
    window.modalManager.hide('loading-modal');
  }
};

window.showErrorModal = function(message) {
  if (window.modalManager) {
    window.modalManager.alert(message, 'Error');
  }
};

window.viewScreenshots = function(screenshots) {
  // Create modal container if it doesn't exist
  let modal = document.getElementById('screenshot-modal');

  if (!modal) {
      modal = document.createElement('div');
      modal.id = 'screenshot-modal';
      modal.className = 'modal';
      document.body.appendChild(modal);
  }

  // Create modal content
  let modalContent = `
      <div class="modal-content">
          <span class="close-modal">&times;</span>
          <div class="screenshot-container">
  `;

  // Add screenshots
  if (screenshots && screenshots.length > 0) {
      screenshots.forEach((screenshot, index) => {
          modalContent += `
              <div class="screenshot-item" id="screenshot-${index}">
                  <img src="${screenshot.url}" alt="Match Screenshot">
                  <div class="screenshot-actions">
                      <button class="btn btn-danger flag-screenshot" data-screenshot-url="${screenshot.url}" data-screenshot-id="${screenshot._id || ''}">
                          <i class="fas fa-flag"></i> Flag Inappropriate Content
                      </button>
                  </div>
              </div>
          `;
      });
  } else {
      modalContent += '<div class="no-data">No screenshots available for this match.</div>';
  }


  modalContent += `
          </div>
      </div>
  `;

  // Set modal content
  modal.innerHTML = modalContent;

  // Show modal
  ModalManager.show('screenshot-modal');

  // Add event listener to close button
  const closeBtn = modal.querySelector('.close-modal');
  closeBtn.addEventListener('click', () => {
      ModalManager.hide('screenshot-modal');
      modal.remove(); // Remove modal from DOM when closed
  });

  // Add flag button event listeners
  const flagButtons = modal.querySelectorAll('.flag-screenshot');
  flagButtons.forEach(button => {
      button.addEventListener('click', () => {
          const screenshotUrl = button.dataset.screenshotUrl;
          const screenshotId = button.dataset.screenshotId;
          flagScreenshot(screenshotUrl, screenshotId);
      });
  });

  // Close modal when clicking outside of it
  window.addEventListener('click', (event) => {
      if (event.target === modal) {
          ModalManager.hide('screenshot-modal');
          modal.remove(); // Remove modal from DOM when closed
      }
  });
};

window.disputeMatch = async function(matchId) {
  try {
      // Remove any existing dispute modal first
      const existingDisputeModal = document.getElementById('dispute-modal');
      if (existingDisputeModal) {
          existingDisputeModal.remove();
      }

      // Create modal for dispute reason
      const modal = document.createElement('div');
      modal.className = 'modal';
      modal.id = 'dispute-modal';
      ModalManager.show('dispute-modal');

      modal.innerHTML = `
          <div class="modal-content">
              <span class="close-modal">&times;</span>
              <h2>Dispute Match</h2>
              <p>Please provide a reason for disputing this match:</p>
              <form id="dispute-form" enctype="multipart/form-data">
                  <div class="form-group">
                      <label for="player-name">Your Player Name:</label>
                      <input type="text" id="player-name" name="playerName" required placeholder="Enter your player name">
                  </div>
                  <div class="form-group">
                      <label for="dispute-reason">Reason:</label>
                      <textarea id="dispute-reason" name="reason" rows="4" required placeholder="Explain why this match result is incorrect..."></textarea>
                  </div>
                  <div class="form-group">
                      <label for="dispute-evidence">Evidence Screenshots (optional):</label>
                      <input type="file" id="dispute-evidence" name="evidence" accept="image/*" multiple>
                      <small>Upload screenshots as evidence to support your claim</small>
                  </div>
                  <input type="hidden" name="matchId" value="${matchId}">
                  <button type="submit" class="btn btn-primary">Submit Dispute</button>
              </form>
          </div>
      `;

      document.body.appendChild(modal);

      // Close button functionality
      const closeBtn = modal.querySelector('.close-modal');
      closeBtn.addEventListener('click', () => {
          document.body.removeChild(modal);
          modal.remove(); // Ensure it's fully removed
      });

      // Handle form submission
      const disputeForm = document.getElementById('dispute-form');
      disputeForm.addEventListener('submit', async (event) => {
          event.preventDefault();

          const formData = new FormData(disputeForm);

          if (!formData.get('playerName') || !formData.get('reason')) {
              alert('Please provide your player name and a reason for the dispute.');
              return;
          }

          try {
              // Show loading indicator
              const submitBtn = disputeForm.querySelector('button[type="submit"]');
              submitBtn.disabled = true;
              submitBtn.textContent = 'Submitting...';

              // Submit dispute to API
              const response = await fetch('/api/ladder/dispute-match', {
                  method: 'POST',
                  body: formData,
                  credentials: 'include'
              });

              const result = await response.json();

              if (!response.ok) {
                  throw new Error(result.error || 'Failed to submit dispute');
              }

              // Show success message
              alert('Dispute submitted successfully. An admin will review your request.');

              // Close modal
              document.body.removeChild(modal);
              modal.remove(); // Ensure it's fully removed

          } catch (error) {
              console.error('Error submitting dispute:', error);
              alert(`Error submitting dispute: ${error.message}`);

              // Re-enable submit button
              const submitBtn = disputeForm.querySelector('button[type="submit"]');
              submitBtn.disabled = false;
              submitBtn.textContent = 'Submit Dispute';
          }
      });

  } catch (error) {
      console.error('Error creating dispute modal:', error);
      alert('Error creating dispute form. Please try again later.');
  }
};

window.flagScreenshot = async function(screenshotUrl, screenshotId) {
  try {
      if (!confirm('Are you sure you want to flag this screenshot as inappropriate content? This action cannot be undone.')) {
          return;
      }

      const response = await fetch('/api/ladder/flag-screenshot', {
          method: 'POST',
          headers: {
              'Content-Type': 'application/json'
          },
          body: JSON.stringify({
              screenshotUrl,
              screenshotId
          }),
          credentials: 'include'
      });

      if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to flag screenshot');
      }

      alert('Screenshot has been flagged for review.');
  } catch (error) {
      console.error('Error flagging screenshot:', error);
      alert(`Error flagging screenshot: ${error.message}`);
  }
};

/**
 * Show player details in a modal using the new ModalManager
 */
window.openPlayerDetailsModal = async function(playerName) {
  if (!window.modalManager) {
    console.error('❌ ModalManager not available');
    return;
  }

  try {
    console.log(`🎭 Opening player details modal for ${playerName}`);
    
    // Show loading modal
    window.showLoadingModal('Loading player details...');
    
    // Load player details using the correct API endpoint that exists
    const response = await fetch(`/api/ladder/player/${encodeURIComponent(playerName)}`);
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to load player');
    }
    
    const data = await response.json();
    console.log('🎯 Player modal API response:', data);
    
    const player = data.player || {};
    const stats = player.stats || {};
    
    console.log('🎯 Extracted player:', player);
    console.log('🎯 Extracted stats:', stats);
    
    const wins = stats.wins || 0;
    const losses = stats.losses || 0;
    const totalGames = stats.totalMatches || wins + losses;
    const winRate = totalGames > 0 ? Math.round((wins / totalGames) * 100) : 0;

    // Create modal content with tabs
    const modalContent = createPlayerModalContent(player);
    
    // Hide loading modal
    window.hideLoadingModal();
    
    // Create and show player details modal
    window.modalManager.createModal({
      id: 'player-stats-modal',
      title: `${player.name || player.username || 'Player'} Statistics`,
      content: modalContent,
      size: 'xl',
      showCloseButton: true
    });

    window.modalManager.show('player-stats-modal');
    
    // Setup modal tabs and load initial data (pass player name instead of ID)
    setupPlayerModalTabs(playerName);
    
    console.log('✅ Player details modal opened successfully');
    
  } catch (error) {
    console.error('❌ Failed to show player details modal:', error);
    window.hideLoadingModal();
    window.showErrorModal(`Failed to load player details: ${error.message}`);
  }
};

/**
 * Create the player modal content structure
 */
function createPlayerModalContent(playerData) {
  return `
    <div class="player-modal-container">
      <!-- Player Header -->
      <div class="player-modal-header">
        <div class="player-info">
          <h3 class="player-name">${playerData.name || playerData.username}</h3>
          <div class="player-rank">
            <img src="${playerData.rank?.image || '/assets/img/ranks/emblem.png'}" alt="Rank" class="rank-icon">
            <span class="rank-name">${playerData.rank?.name || 'Unranked'}</span>
          </div>
          <div class="player-mmr">
            <span class="mmr-value">${playerData.mmr || 0}</span>
            <span class="mmr-label">MMR</span>
          </div>
        </div>
      </div>

      <!-- Modal Tabs -->
      <div class="modal-tabs">
        <button class="modal-tab active" data-tab="overview">Overview</button>
        <button class="modal-tab" data-tab="matches">Recent Matches</button>
        <button class="modal-tab" data-tab="performance">Performance</button>
      </div>

      <!-- Tab Content -->
      <div class="modal-tab-content active" id="overview-content">
        <div class="loading">Loading overview...</div>
      </div>

      <div class="modal-tab-content" id="matches-content">
        <div class="loading">Loading matches...</div>
      </div>

      <div class="modal-tab-content" id="performance-content">
        <div class="loading">Loading performance data...</div>
      </div>
    </div>
  `;
}

/**
 * Setup modal tabs and event handlers
 */
function setupPlayerModalTabs(playerName) {
  const modal = document.getElementById('player-stats-modal');
  if (!modal) return;

  // Tab switching
  const tabs = modal.querySelectorAll('.modal-tab');
  const contents = modal.querySelectorAll('.modal-tab-content');

  tabs.forEach(tab => {
    tab.addEventListener('click', async () => {
      // Remove active class from all tabs and contents
      tabs.forEach(t => t.classList.remove('active'));
      contents.forEach(c => c.classList.remove('active'));

      // Add active class to clicked tab
      tab.classList.add('active');
      
      // Show corresponding content
      const tabName = tab.dataset.tab;
      const targetContent = modal.querySelector(`#${tabName}-content`);
      if (targetContent) {
        targetContent.classList.add('active');
        
        // Load tab data if not already loaded
        if (!targetContent.dataset.loaded) {
          await loadTabData(tabName, playerName, targetContent);
          targetContent.dataset.loaded = 'true';
        }
      }
    });
  });

  // Load initial overview tab
  const overviewTab = modal.querySelector('.modal-tab[data-tab="overview"]');
  if (overviewTab) {
    overviewTab.click();
  }
}

/**
 * Load data for a specific tab
 */
async function loadTabData(tabName, playerName, contentElement) {
  try {
    contentElement.innerHTML = '<div class="loading">Loading...</div>';
    
    switch (tabName) {
      case 'overview':
        await loadOverviewData(playerName, contentElement);
        break;
      case 'matches':
        await loadMatchesData(playerName, contentElement);
        break;
      case 'performance':
        await loadPerformanceData(playerName, contentElement);
        break;
    }
  } catch (error) {
    console.error(`Error loading ${tabName} data:`, error);
    contentElement.innerHTML = `<div class="error-message">Failed to load ${tabName} data</div>`;
  }
}

/**
 * Load overview tab data
 */
async function loadOverviewData(playerName, contentElement) {
  try {
    // Use the correct API endpoint that exists
    const response = await fetch(`/api/ladder/player/${encodeURIComponent(playerName)}`);

    if (!response.ok) {
      throw new Error('Failed to load player data');
    }

    const data = await response.json();
    console.log('🎯 Player modal API response:', data);
    
    const player = data.player || {};
    const stats = player.stats || {};
    
    console.log('🎯 Extracted player:', player);
    console.log('🎯 Extracted stats:', stats);
    
    const wins = stats.wins || 0;
    const losses = stats.losses || 0;
    const totalGames = stats.totalMatches || wins + losses;
    const winRate = totalGames > 0 ? Math.round((wins / totalGames) * 100) : 0;

    contentElement.innerHTML = `
      <div class="overview-content">
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon"><i class="fas fa-trophy"></i></div>
            <div class="stat-content">
              <div class="stat-value">${wins}</div>
              <div class="stat-label">Total Wins</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon"><i class="fas fa-times"></i></div>
            <div class="stat-content">
              <div class="stat-value">${losses}</div>
              <div class="stat-label">Total Losses</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon"><i class="fas fa-percentage"></i></div>
            <div class="stat-content">
              <div class="stat-value">${winRate}%</div>
              <div class="stat-label">Win Rate</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon"><i class="fas fa-gamepad"></i></div>
            <div class="stat-content">
              <div class="stat-value">${totalGames}</div>
              <div class="stat-label">Total Games</div>
            </div>
          </div>
        </div>
        <div class="player-info-section">
          <h4>Player Information</h4>
          <div class="player-details-grid">
            <div class="detail-item">
              <span class="detail-label">MMR:</span>
              <span class="detail-value">${player.mmr || 1500}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Rank:</span>
              <span class="detail-value">${player.rank?.name || 'Unranked'}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Race:</span>
              <span class="detail-value">${player.race || 'Unknown'}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Game Type:</span>
              <span class="detail-value">${player.gameType || 'war2'}</span>
            </div>
          </div>
        </div>
      </div>
    `;
  } catch (error) {
    console.error('Error loading overview data:', error);
    contentElement.innerHTML = '<div class="error-message">Failed to load overview data</div>';
  }
}

/**
 * Load matches tab data
 */
async function loadMatchesData(playerName, contentElement) {
  try {
    // Fetch player matches from the API
    const response = await fetch(`/api/ladder/player/${encodeURIComponent(playerName)}`);
    
    if (!response.ok) {
      throw new Error('Failed to load matches data');
    }

    const data = await response.json();
    const matches = data.matches || [];
    
    console.log('🎯 Player matches:', matches);
    console.log('🎯 Total matches found:', matches.length);

    if (matches.length === 0) {
      contentElement.innerHTML = `
        <div class="matches-content">
          <div class="no-data">
            <i class="fas fa-history"></i>
            <h3>No Recent Matches</h3>
            <p>No match history found for ${playerName}.</p>
          </div>
        </div>
      `;
      return;
    }

    // Create matches list HTML
    const matchesHtml = matches.slice(0, 10).map(match => {
      const matchDate = new Date(match.date || match.createdAt);
      const formattedDate = matchDate.toLocaleDateString();
      const timeAgo = getTimeAgo(matchDate);
      
      // Find this player in the match - try multiple ways to match
      let playerInMatch = match.players?.find(p => 
        p.playerId?.name === playerName || 
        p.name === playerName ||
        p.playerName === playerName
      );
      
      // If not found, try comparing IDs if available
      if (!playerInMatch && match.players) {
        playerInMatch = match.players.find(p => {
          const playerId = p.playerId?._id || p.playerId;
          const playerNameFromId = p.playerId?.name;
          return playerNameFromId === playerName || playerId === playerName;
        });
      }
      
      console.log(`🎮 Match players for ${match.map?.name}:`, match.players);
      console.log(`🎮 Looking for player: "${playerName}"`);
      console.log(`🎮 Found playerInMatch:`, playerInMatch);
      
      // Try multiple fields for outcome
      let outcome = 'unknown';
      if (playerInMatch) {
        // First try explicit outcome fields
        outcome = playerInMatch.outcome || 
                 playerInMatch.result || 
                 (playerInMatch.isWinner === true ? 'win' : 
                  playerInMatch.isWinner === false ? 'loss' : null);
        
        // If no explicit outcome, determine from MMR change
        if (!outcome || outcome === 'unknown') {
          const mmrChange = playerInMatch.mmrChange;
          if (mmrChange > 0) {
            outcome = 'win';
          } else if (mmrChange < 0) {
            outcome = 'loss';
          } else if (mmrChange === 0) {
            outcome = 'draw';
          }
        }
      }
      
      console.log(`🎮 Final outcome: ${outcome}`);
      console.log(`🎮 Match: ${match.map?.name} - ${outcome.toUpperCase()} - ${timeAgo}`);
      
      const outcomeClass = outcome === 'win' ? 'match-win' : outcome === 'loss' ? 'match-loss' : 'match-draw';
      const outcomeIcon = outcome === 'win' ? 'trophy' : outcome === 'loss' ? 'times' : 'equals';
      
      // Get opponent(s)
      const opponents = match.players?.filter(p => 
        (p.playerId?.name || p.name) !== playerName
      ).map(p => p.playerId?.name || p.name || 'Unknown').join(', ') || 'Unknown';

      return `
        <div class="match-item ${outcomeClass}">
          <div class="match-header">
            <div class="match-outcome">
              <i class="fas fa-${outcomeIcon}"></i>
              <span class="outcome-text">${outcome.toUpperCase()}</span>
            </div>
            <div class="match-type">${match.matchType || '1v1'}</div>
            <div class="match-date" title="${formattedDate}">${timeAgo}</div>
          </div>
          <div class="match-details">
            <div class="match-map">
              <i class="fas fa-map"></i>
              <span>${match.map?.name || match.mapName || 'Unknown Map'}</span>
            </div>
            <div class="match-opponents">
              <i class="fas fa-users"></i>
              <span>vs ${opponents}</span>
            </div>
            ${playerInMatch?.mmrChange ? `
              <div class="mmr-change ${playerInMatch.mmrChange > 0 ? 'positive' : 'negative'}">
                <i class="fas fa-chart-line"></i>
                <span>${playerInMatch.mmrChange > 0 ? '+' : ''}${playerInMatch.mmrChange} MMR</span>
              </div>
            ` : ''}
          </div>
        </div>
      `;
    }).join('');

    contentElement.innerHTML = `
      <div class="matches-content">
        <div class="matches-header">
          <h3><i class="fas fa-history"></i> Recent Matches</h3>
          <p>Last ${Math.min(matches.length, 10)} matches for ${playerName}</p>
        </div>
        <div class="matches-list">
          ${matchesHtml}
        </div>
      </div>
    `;

  } catch (error) {
    console.error('Error loading matches data:', error);
    contentElement.innerHTML = `
      <div class="matches-content">
        <div class="error-message">
          <i class="fas fa-exclamation-triangle"></i>
          <h3>Failed to Load Matches</h3>
          <p>Unable to load match history for ${playerName}.</p>
        </div>
      </div>
    `;
  }
}

// Helper function to format time ago
function getTimeAgo(date) {
  const now = new Date();
  const diffMs = now - date;
  const diffMins = Math.floor(diffMs / 60000);
  const diffHours = Math.floor(diffMins / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (diffMins < 1) return 'Just now';
  if (diffMins < 60) return `${diffMins}m ago`;
  if (diffHours < 24) return `${diffHours}h ago`;
  if (diffDays < 7) return `${diffDays}d ago`;
  
  return date.toLocaleDateString();
}

/**
 * Load performance tab data
 */
async function loadPerformanceData(playerName, contentElement) {
  try {
    // Fetch player data for performance analytics
    const response = await fetch(`/api/ladder/player/${encodeURIComponent(playerName)}`);
    
    if (!response.ok) {
      throw new Error('Failed to load performance data');
    }

    const data = await response.json();
    const player = data.player || {};
    const stats = player.stats || {};
    
    console.log('🎯 Player performance data:', { player, stats });

    // Debug: Let's see what the raw stats structure looks like
    console.log('📊 Raw stats object:', JSON.stringify(stats, null, 2));

    // Process match type performance
    const matchTypes = stats.matchTypes || {};
    const matchTypeHtml = Object.entries(matchTypes)
      .filter(([_, typeStats]) => typeStats.matches > 0)
      .map(([type, typeStats]) => {
        const winRate = typeStats.matches > 0 ? Math.round((typeStats.wins / typeStats.matches) * 100) : 0;
        const winRateClass = winRate >= 70 ? 'excellent' : winRate >= 60 ? 'good' : winRate >= 50 ? 'average' : 'poor';
        
        return `
          <div class="performance-item">
            <div class="performance-header">
              <h4>${type.toUpperCase()}</h4>
              <span class="win-rate ${winRateClass}">${winRate}%</span>
            </div>
            <div class="performance-stats">
              <div class="stat-row">
                <span class="stat-label">Matches:</span>
                <span class="stat-value">${typeStats.matches}</span>
              </div>
              <div class="stat-row">
                <span class="stat-label">Record:</span>
                <span class="stat-value">${typeStats.wins}W / ${typeStats.losses}L</span>
              </div>
              ${typeStats.mmr ? `
                <div class="stat-row">
                  <span class="stat-label">MMR:</span>
                  <span class="stat-value">${typeStats.mmr}</span>
                </div>
              ` : ''}
            </div>
            <div class="performance-bar">
              <div class="bar-fill" style="width: ${winRate}%"></div>
            </div>
          </div>
        `;
      }).join('');

    // Process race performance
    const races = stats.races || {};
    const raceWins = stats.raceWins || {};
    const raceHtml = Object.entries(races)
      .filter(([_, count]) => count > 0)
      .map(([race, count]) => {
        const wins = raceWins[race] || 0;
        const winRate = count > 0 ? Math.round((wins / count) * 100) : 0;
        const winRateClass = winRate >= 70 ? 'excellent' : winRate >= 60 ? 'good' : winRate >= 50 ? 'average' : 'poor';
        const raceIcon = race === 'human' ? 'crown' : race === 'orc' ? 'fist-raised' : 'random';
        
        return `
          <div class="race-performance-item">
            <div class="race-header">
              <i class="fas fa-${raceIcon}"></i>
              <span class="race-name">${race.charAt(0).toUpperCase() + race.slice(1)}</span>
            </div>
            <div class="race-stats">
              <div class="race-record">${wins}W / ${count - wins}L</div>
              <div class="race-winrate ${winRateClass}">${winRate}%</div>
            </div>
          </div>
        `;
      }).join('');

    // Process top maps performance - HYBRID APPROACH
    // Combine backend aggregated stats with recent match calculations
    const matches = data.matches || [];
    const backendMaps = stats.maps || {};
    const backendMapWins = stats.mapWins || {};
    
    // Start with backend aggregated data (covers historical matches)
    const mapStats = {};
    Object.entries(backendMaps).forEach(([mapName, totalCount]) => {
      if (totalCount > 0) {
        mapStats[mapName] = {
          total: totalCount,
          wins: backendMapWins[mapName] || 0,
          losses: totalCount - (backendMapWins[mapName] || 0),
          draws: 0,
          source: 'backend'
        };
      }
    });
    
    // Supplement with recent match data (covers any gaps + recent matches)
    matches.forEach(match => {
      const mapName = match.map?.name || match.mapName || 'Unknown Map';
      
      // Find this player in the match
      let playerInMatch = match.players?.find(p => 
        p.playerId?.name === playerName || 
        p.name === playerName ||
        p.playerName === playerName
      );
      
      if (!playerInMatch && match.players) {
        playerInMatch = match.players.find(p => {
          const playerNameFromId = p.playerId?.name;
          return playerNameFromId === playerName;
        });
      }
      
      if (playerInMatch) {
        // If this map wasn't in backend data, add it (likely recent)
        if (!mapStats[mapName]) {
          mapStats[mapName] = { total: 0, wins: 0, losses: 0, draws: 0, source: 'recent' };
        }
        
        // For maps only in recent data, count them
        // For maps in backend data, this is just verification/recent updates
        if (mapStats[mapName].source === 'recent') {
          mapStats[mapName].total++;
          
          // Determine outcome from MMR change
          const mmrChange = playerInMatch.mmrChange;
          if (mmrChange > 0) {
            mapStats[mapName].wins++;
          } else if (mmrChange < 0) {
            mapStats[mapName].losses++;
          } else if (mmrChange === 0) {
            mapStats[mapName].draws++;
          }
        }
      }
    });
    
    console.log('🗺️ Hybrid map stats for', playerName, ':', mapStats);
    console.log('📊 Backend aggregated maps:', Object.keys(backendMaps).length);
    console.log('🔄 Recent-only maps:', Object.values(mapStats).filter(m => m.source === 'recent').length);

    // Calculate overall performance metrics
    const totalMatches = stats.totalMatches || 0;
    const totalWins = stats.wins || 0;
    const overallWinRate = totalMatches > 0 ? Math.round((totalWins / totalMatches) * 100) : 0;
    const highestMMR = stats.highestMmr || player.mmr || 1500;
    const currentMMR = player.mmr || 1500;
    const mmrGain = currentMMR - 1500;

    contentElement.innerHTML = `
      <div class="performance-content">
        <!-- Performance Summary -->
        <div class="performance-summary">
          <h3><i class="fas fa-chart-line"></i> Performance Summary</h3>
          <div class="summary-stats">
            <div class="summary-stat">
              <div class="stat-icon"><i class="fas fa-percentage"></i></div>
              <div class="stat-info">
                <div class="stat-value">${overallWinRate}%</div>
                <div class="stat-label">Overall Win Rate</div>
              </div>
            </div>
            <div class="summary-stat">
              <div class="stat-icon"><i class="fas fa-trophy"></i></div>
              <div class="stat-info">
                <div class="stat-value">${highestMMR}</div>
                <div class="stat-label">Peak MMR</div>
              </div>
            </div>
            <div class="summary-stat">
              <div class="stat-icon"><i class="fas fa-arrow-up"></i></div>
              <div class="stat-info">
                <div class="stat-value ${mmrGain >= 0 ? 'positive' : 'negative'}">${mmrGain >= 0 ? '+' : ''}${mmrGain}</div>
                <div class="stat-label">MMR Gain</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Match Type Performance -->
        ${matchTypeHtml ? `
          <div class="performance-section">
            <h4><i class="fas fa-gamepad"></i> Match Type Performance</h4>
            <div class="match-type-performance">
              ${matchTypeHtml}
            </div>
          </div>
        ` : ''}

        <!-- Race Performance -->
        ${raceHtml ? `
          <div class="performance-section">
            <h4><i class="fas fa-users"></i> Race Performance</h4>
            <div class="race-performance">
              ${raceHtml}
            </div>
          </div>
        ` : ''}

        <!-- Top Maps -->
        ${(() => {
          // First get the top 5 most played maps (minimum 3 games)
          const topPlayedMaps = Object.entries(mapStats)
            .filter(([_, mapData]) => mapData.total >= 3)
            .sort(([, a], [, b]) => b.total - a.total)
            .slice(0, 5);
          
          // Then sort these 5 maps by win rate (best performance first)
          const sortedByWinRate = topPlayedMaps
            .sort(([, a], [, b]) => {
              const aWinRate = a.total > 0 ? (a.wins / a.total) : 0;
              const bWinRate = b.total > 0 ? (b.wins / b.total) : 0;
              return bWinRate - aWinRate;
            });
          
          return sortedByWinRate.length > 0 ? `
            <div class="performance-section">
              <h4><i class="fas fa-map"></i> Best Map Performance</h4>
              <div class="map-performance">
                ${sortedByWinRate.map(([mapName, mapData]) => {
                  const winRate = mapData.total > 0 ? Math.round((mapData.wins / mapData.total) * 100) : 0;
                  const winRateClass = winRate >= 70 ? 'excellent' : winRate >= 60 ? 'good' : winRate >= 50 ? 'average' : 'poor';
                  const sourceIcon = mapData.source === 'backend' ? '📊' : '🔄';
                  
                  console.log(`🗺️ ${mapName}: ${mapData.total} total games, ${mapData.wins} wins, ${mapData.losses} losses, ${winRate}% win rate (${mapData.source})`);
                  
                  return `
                    <div class="map-performance-item">
                      <div class="map-header">
                        <span class="map-name">${sourceIcon} ${mapName}</span>
                        <span class="map-winrate ${winRateClass}">${winRate}%</span>
                      </div>
                      <div class="map-stats">
                        <span class="map-record">${mapData.wins}W / ${mapData.losses}L (${mapData.total} games)</span>
                      </div>
                    </div>
                  `;
                }).join('')}
              </div>
              <div class="map-stats-note">
                <small><i class="fas fa-info-circle"></i> 📊 = Historical data, 🔄 = Recent matches. Top 5 most played maps sorted by win rate (min. 3 games).</small>
              </div>
            </div>
          ` : '';
        })()}

        ${!matchTypeHtml && !raceHtml && Object.keys(mapStats).length === 0 ? `
          <div class="no-data">
            <i class="fas fa-chart-line"></i>
            <h3>No Performance Data</h3>
            <p>Not enough match data available for detailed performance analysis.</p>
            <p><small>Play more matches to see detailed statistics here!</small></p>
          </div>
        ` : ''}
      </div>
    `;

  } catch (error) {
    console.error('Error loading performance data:', error);
    contentElement.innerHTML = `
      <div class="performance-content">
        <div class="error-message">
          <i class="fas fa-exclamation-triangle"></i>
          <h3>Failed to Load Performance Data</h3>
          <p>Unable to load performance analytics for ${playerName}.</p>
        </div>
      </div>
    `;
  }
}

/**
 * Close player details modal
 */
window.closePlayerDetailsModal = function() {
  if (window.modalManager) {
    window.modalManager.hide('player-stats-modal');
  }
};

// Legacy function support for existing code
window.showPlayerStatsModal = window.openPlayerDetailsModal;
window.showPlayerDetails = window.openPlayerDetailsModal;

// Also ensure the global function is available immediately
if (!window.showPlayerDetails) {
  window.showPlayerDetails = function(playerName) {
    if (window.modalManager) {
      console.log('✅ ModalManager available, opening player modal for:', playerName);
      window.openPlayerDetailsModal(playerName);
    } else {
      console.error('❌ ModalManager not loaded yet');
      // Fallback - try a simple alert to test
      alert('ModalManager not available. Player Name: ' + playerName);
    }
  };
}



// Close the conditional block for profile page only
} else {
  console.log('🚫 PlayerDetails.js skipped - not on profile page');
}

// Add CSS styles for the player modal
const modalStyles = `
<style>
/* Player Modal Styles */
.player-modal-container {
  max-width: 100%;
  padding: 0;
}

.player-modal-header {
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.1), rgba(184, 134, 11, 0.1));
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin: -1rem -1rem 0 -1rem;
}

.player-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.player-name {
  font-size: 1.5rem;
  margin: 0;
  color: #daa520;
}

.player-rank {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.rank-icon {
  width: 32px;
  height: 32px;
}

.player-mmr {
  margin-left: auto;
  text-align: center;
}

.mmr-value {
  display: block;
  font-size: 1.8rem;
  font-weight: bold;
  color: #daa520;
}

.mmr-label {
  font-size: 0.8rem;
  color: #ccc;
  text-transform: uppercase;
}

/* Modal Tabs */
.modal-tabs {
  display: flex;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin: 1rem -1rem 0 -1rem;
}

.modal-tab {
  flex: 1;
  padding: 1rem;
  background: none;
  border: none;
  color: #ccc;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.modal-tab:hover {
  background: rgba(255, 255, 255, 0.05);
  color: #daa520;
}

.modal-tab.active {
  background: rgba(218, 165, 32, 0.1);
  color: #daa520;
  border-bottom: 2px solid #daa520;
}

.modal-tab-content {
  display: none;
  padding: 1.5rem 0;
}

.modal-tab-content.active {
  display: block;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.stat-value {
  font-size: 1.8rem;
  font-weight: bold;
  color: #daa520;
}

.stat-label {
  font-size: 0.9rem;
  color: #ccc;
  text-transform: uppercase;
}

/* Matches Styles */
.matches-header {
  margin-bottom: 1rem;
}

.matches-header h3 {
  color: #daa520;
  margin: 0 0 0.5rem 0;
}

.matches-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.match-item {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1rem;
  border-left: 4px solid;
}

.match-item.match-win {
  border-left-color: #4CAF50;
}

.match-item.match-loss {
  border-left-color: #f44336;
}

.match-item.match-draw {
  border-left-color: #ff9800;
}

.match-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.match-outcome {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: bold;
}

.match-win .match-outcome {
  color: #4CAF50;
}

.match-loss .match-outcome {
  color: #f44336;
}

.match-draw .match-outcome {
  color: #ff9800;
}

.match-details {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  color: #ccc;
}

.match-details > div {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.mmr-change.positive {
  color: #4CAF50;
}

.mmr-change.negative {
  color: #f44336;
}

/* Performance Styles */
.performance-summary {
  margin-bottom: 2rem;
}

.performance-summary h3 {
  color: #daa520;
  margin: 0 0 1rem 0;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.summary-stat {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  font-size: 1.5rem;
  color: #daa520;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #fff;
}

.stat-value.positive {
  color: #4CAF50;
}

.stat-value.negative {
  color: #f44336;
}

.performance-section {
  margin-bottom: 2rem;
}

.performance-section h4 {
  color: #daa520;
  margin: 0 0 1rem 0;
}

.match-type-performance,
.race-performance,
.map-performance {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.performance-item,
.race-performance-item,
.map-performance-item {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1rem;
}

.performance-header,
.race-header,
.map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.win-rate,
.race-winrate,
.map-winrate {
  font-weight: bold;
}

.win-rate.excellent,
.race-winrate.excellent,
.map-winrate.excellent {
  color: #4CAF50;
}

.win-rate.good,
.race-winrate.good,
.map-winrate.good {
  color: #8BC34A;
}

.win-rate.average,
.race-winrate.average,
.map-winrate.average {
  color: #FF9800;
}

.win-rate.poor,
.race-winrate.poor,
.map-winrate.poor {
  color: #f44336;
}

.performance-bar {
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  margin-top: 0.5rem;
}

.bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #daa520, #f4d03f);
  transition: width 0.3s ease;
}

.no-data,
.error-message {
  text-align: center;
  padding: 2rem;
  color: #ccc;
}

.no-data i,
.error-message i {
  font-size: 3rem;
  color: #555;
  margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .player-info {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .player-mmr {
    margin-left: 0;
  }

  .modal-tabs {
    flex-direction: column;
  }

  .match-header {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }

  .match-details {
    flex-direction: column;
    gap: 0.5rem;
  }

  .summary-stats {
    grid-template-columns: 1fr;
  }
}
</style>
`;

// Add styles to the document head
if (!document.getElementById('player-modal-styles')) {
  const styleElement = document.createElement('div');
  styleElement.id = 'player-modal-styles';
  styleElement.innerHTML = modalStyles;
  document.head.appendChild(styleElement);
}