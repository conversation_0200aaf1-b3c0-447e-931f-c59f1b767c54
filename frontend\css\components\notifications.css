/* =============================================================================
   NOTIFICATIONS COMPONENT STYLES
   ============================================================================= */

.notification-bell-container {
  position: relative;
  display: inline-flex;
}

.notification-bell,
.notification-bell-avatar {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 50%;
  color: var(--neutral-300);
  cursor: pointer;
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px);
}

.notification-bell:hover,
.notification-bell-avatar:hover {
  background: var(--glass-border);
  border-color: var(--warcraft-gold);
  color: var(--warcraft-gold);
  box-shadow: var(--shadow-glow);
}

.notification-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--horde-red);
  color: white;
  border-radius: 50%;
  min-width: 20px;
  height: 20px;
  font-size: var(--text-xs);
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--bg-primary);
  animation: none;
}

.notification-badge:empty {
  display: none;
}

.notifications-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  background: linear-gradient(135deg, rgba(20, 20, 20, 0.95), rgba(40, 40, 40, 0.9));
  border: 2px solid var(--warcraft-gold);
  border-radius: var(--radius-lg);
  min-width: 320px;
  max-width: 400px;
  max-height: 450px;
  overflow-y: auto;
  z-index: var(--z-dropdown);
  box-shadow: var(--glass-shadow), var(--shadow-glow);
  backdrop-filter: blur(15px);
  display: none;
  animation: slideDownFade 0.3s ease-out;
  transform-origin: top right;
}

.notifications-dropdown.show {
  display: block;
}

@keyframes slideDownFade {
  0% {
    opacity: 0;
    transform: translateY(-20px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4);
  border-bottom: 1px solid rgba(212, 175, 55, 0.3);
}

.notifications-header h4 {
  color: var(--warcraft-gold);
  margin: 0;
  font-family: var(--font-display);
  font-size: var(--text-lg);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.mark-all-read {
  background: none;
  border: 1px solid rgba(212, 175, 55, 0.5);
  color: var(--warcraft-gold);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  cursor: pointer;
  font-size: var(--text-xs);
  font-weight: 500;
  transition: all var(--transition-fast);
}

.mark-all-read:hover {
  background: rgba(212, 175, 55, 0.2);
  border-color: var(--warcraft-gold);
}

.notifications-list {
  max-height: 350px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all var(--transition-fast);
  cursor: pointer;
}

.notification-item:hover {
  background: rgba(212, 175, 55, 0.1);
}

.notification-item.unread {
  background: rgba(37, 99, 235, 0.1);
  border-left: 3px solid var(--alliance-blue);
}

.notification-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-sm);
  flex-shrink: 0;
}

.notification-icon.success {
  background: rgba(16, 185, 129, 0.2);
  color: var(--success);
}

.notification-icon.error {
  background: rgba(239, 68, 68, 0.2);
  color: var(--error);
}

.notification-icon.info {
  background: rgba(37, 99, 235, 0.2);
  color: var(--info);
}

.notification-icon.warning {
  background: rgba(245, 158, 11, 0.2);
  color: var(--warning);
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  color: var(--neutral-100);
  font-weight: 600;
  font-size: var(--text-sm);
  margin: 0 0 var(--space-1) 0;
  line-height: 1.4;
}

.notification-message {
  color: var(--neutral-400);
  font-size: var(--text-xs);
  margin: 0 0 var(--space-1) 0;
  line-height: 1.4;
  word-wrap: break-word;
}

.notification-time {
  color: var(--neutral-500);
  font-size: var(--text-xs);
  margin: 0;
}

.notification-actions {
  display: flex;
  gap: var(--space-2);
  margin-top: var(--space-2);
  flex-wrap: wrap;
}

.notification-action-btn {
  padding: var(--space-1) var(--space-3);
  border: none;
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.notification-action-btn.primary {
  background: var(--alliance-blue);
  color: white;
}

.notification-action-btn.primary:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.notification-action-btn.success {
  background: var(--success);
  color: white;
}

.notification-action-btn.success:hover {
  background: #059669;
  transform: translateY(-1px);
}

.notification-action-btn.danger {
  background: var(--error);
  color: white;
}

.notification-action-btn.danger:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

.notification-action-btn.warning {
  background: var(--warning);
  color: white;
}

.notification-action-btn.warning:hover {
  background: #d97706;
  transform: translateY(-1px);
}

.notifications-empty {
  text-align: center;
  padding: var(--space-8) var(--space-4);
  color: var(--neutral-400);
}

.notifications-empty i {
  font-size: var(--text-4xl);
  margin-bottom: var(--space-4);
  opacity: 0.5;
}

.notifications-empty p {
  margin: 0;
  font-size: var(--text-sm);
}

/* Notification Toasts */
.notification-toast {
  position: fixed;
  top: var(--space-4);
  right: var(--space-4);
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  max-width: 400px;
  z-index: var(--z-toast);
  box-shadow: var(--glass-shadow);
  animation: slideInRight 0.3s ease-out;
}

.notification-toast.success {
  border-color: var(--success);
  background: rgba(16, 185, 129, 0.1);
}

.notification-toast.error {
  border-color: var(--error);
  background: rgba(239, 68, 68, 0.1);
}

.notification-toast.info {
  border-color: var(--info);
  background: rgba(37, 99, 235, 0.1);
}

.notification-toast.warning {
  border-color: var(--warning);
  background: rgba(245, 158, 11, 0.1);
}

@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(100%);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.toast-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2);
}

.toast-title {
  color: var(--neutral-100);
  font-weight: 600;
  font-size: var(--text-sm);
  margin: 0;
}

.toast-close {
  background: none;
  border: none;
  color: var(--neutral-400);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.toast-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--neutral-100);
}

.toast-body {
  color: var(--neutral-300);
  font-size: var(--text-sm);
  line-height: 1.4;
}

/* Mobile Responsive */
@media (max-width: 480px) {
  .notifications-dropdown {
    right: var(--space-2);
    left: var(--space-2);
    min-width: auto;
    max-width: none;
  }
  
  .notification-toast {
    right: var(--space-2);
    left: var(--space-2);
    max-width: none;
  }
} 
