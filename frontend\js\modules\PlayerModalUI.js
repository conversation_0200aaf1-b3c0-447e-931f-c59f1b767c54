/**
 * PlayerModalUI.js - Main Modal Structure and UI Components
 * 
 * Extracted from the 72KB playerDetails.js monster.
 * Handles the main player details modal creation, tab system, and UI management.
 * 
 * Responsibilities:
 * - Main modal structure and layout
 * - Tab system and navigation
 * - UI component rendering and updates
 * - Integration with other player modal modules
 * - Modal lifecycle management
 */

import { playerModalCore } from './PlayerModalCore.js';
import { playerStatsCharts } from './PlayerStatsCharts.js';
import { playerMatchHistory } from './PlayerMatchHistory.js';

export class PlayerModalUI {
  constructor() {
    this.currentPlayer = null;
    this.activeTab = 'overview';
    this.tabData = new Map();
    this.modalElement = null;
  }

  /**
   * Initialize the player modal UI system
   */
  init() {
    console.log('🎨 Initializing Player Modal UI...');
    this.setupGlobalFunctions();
    this.setupModalStyles();
    console.log('✅ Player Modal UI initialized');
  }

  /**
   * Setup global functions for backward compatibility
   */
  setupGlobalFunctions() {
    window.playerModalUI = this;
    window.showPlayerDetailsModal = (playerId) => this.showPlayerDetailsModal(playerId);
    window.switchPlayerTab = (tabName) => this.switchTab(tabName);
    
    console.log('📋 Global player modal UI functions registered');
  }

  /**
   * Show player details modal for a specific player
   */
  async showPlayerDetailsModal(playerId) {
    if (!playerId) {
      console.error('❌ No player ID provided');
      return;
    }

    try {
      console.log(`🎭 Opening player details modal for ${playerId}`);
      
      // Show loading modal
      playerModalCore.showLoadingModal('Loading player details...');
      
      // Load player data
      const playerData = await this.loadPlayerData(playerId);
      if (!playerData) {
        throw new Error('Failed to load player data');
      }

      this.currentPlayer = playerData;
      
      // Create and show modal
      this.createPlayerModal(playerData);
      
      // Hide loading modal
      playerModalCore.hideLoadingModal();
      
      // Initialize charts and other components
      this.initializeModalComponents(playerData);
      
      console.log('✅ Player details modal opened successfully');

    } catch (error) {
      console.error('❌ Failed to show player details modal:', error);
      playerModalCore.hideLoadingModal();
      playerModalCore.showErrorModal(`Failed to load player details: ${error.message}`);
    }
  }

  /**
   * Load player data from API
   */
  async loadPlayerData(playerId) {
    try {
      const response = await fetch(`/api/ladder/players/${playerId}/details`, {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error || 'Failed to load player data');
      }

    } catch (error) {
      console.error('❌ Failed to load player data:', error);
      throw error;
    }
  }

  /**
   * Create the main player modal structure
   */
  createPlayerModal(playerData) {
    // Remove any existing modal
    const existingModal = document.getElementById('player-details-modal');
    if (existingModal) {
      existingModal.remove();
    }

    const modal = document.createElement('div');
    modal.id = 'player-details-modal';
    modal.className = 'modal-overlay active player-details-modal';
    
    modal.innerHTML = `
      <div class="modal-content player-modal-content">
        ${this.generateModalHeader(playerData)}
        ${this.generateTabNavigation()}
        ${this.generateTabContent(playerData)}
      </div>
    `;

    document.body.appendChild(modal);
    this.modalElement = modal;
    
    // Setup event listeners
    this.setupModalEventListeners(modal);
    
    console.log('🏗️ Player modal structure created');
  }

  /**
   * Generate modal header with player info
   */
  generateModalHeader(playerData) {
    const player = playerData.player;
    const stats = playerData.stats;
    
    return `
      <div class="player-modal-header">
        <button class="close-modal" onclick="closePlayerDetailsModal()">&times;</button>
        
        <div class="player-header-content">
          <div class="player-basic-info">
            <div class="player-avatar">
              <img src="${player.avatar || '/assets/img/default-avatar.svg'}" 
                   alt="${player.name}" 
                   loading="lazy">
            </div>
            
            <div class="player-details">
              <h2 class="player-name">${player.name}</h2>
              <div class="player-meta">
                <span class="player-rank">${this.formatRank(stats.rank)}</span>
                <span class="player-rating">${stats.rating || 0} ELO</span>
                <span class="player-status ${player.isOnline ? 'online' : 'offline'}">
                  ${player.isOnline ? 'Online' : 'Offline'}
                </span>
              </div>
            </div>
          </div>
          
          <div class="player-quick-stats">
            <div class="quick-stat">
              <span class="stat-value">${stats.totalGames || 0}</span>
              <span class="stat-label">Games</span>
            </div>
            <div class="quick-stat">
              <span class="stat-value">${stats.wins || 0}</span>
              <span class="stat-label">Wins</span>
            </div>
            <div class="quick-stat">
              <span class="stat-value">${this.calculateWinRate(stats)}%</span>
              <span class="stat-label">Win Rate</span>
            </div>
            <div class="quick-stat">
              <span class="stat-value">${stats.streak || 0}</span>
              <span class="stat-label">${(stats.streak || 0) >= 0 ? 'Win Streak' : 'Loss Streak'}</span>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Generate tab navigation
   */
  generateTabNavigation() {
    const tabs = [
      { id: 'overview', label: 'Overview', icon: 'fas fa-chart-bar' },
      { id: 'matches', label: 'Match History', icon: 'fas fa-history' },
      { id: 'statistics', label: 'Statistics', icon: 'fas fa-chart-pie' },
      { id: 'achievements', label: 'Achievements', icon: 'fas fa-trophy' }
    ];

    return `
      <div class="player-modal-tabs">
        ${tabs.map(tab => `
          <button class="tab-button ${tab.id === this.activeTab ? 'active' : ''}" 
                  data-tab="${tab.id}"
                  onclick="switchPlayerTab('${tab.id}')">
            <i class="${tab.icon}"></i>
            <span>${tab.label}</span>
          </button>
        `).join('')}
      </div>
    `;
  }

  /**
   * Generate tab content area
   */
  generateTabContent(playerData) {
    return `
      <div class="player-modal-body">
        <div class="tab-content-container">
          ${this.generateOverviewTab(playerData)}
          ${this.generateMatchesTab(playerData)}
          ${this.generateStatisticsTab(playerData)}
          ${this.generateAchievementsTab(playerData)}
        </div>
      </div>
    `;
  }

  /**
   * Generate overview tab content
   */
  generateOverviewTab(playerData) {
    const { player, stats, recentMatches } = playerData;
    
    return `
      <div class="tab-content overview-content ${this.activeTab === 'overview' ? 'active' : ''}">
        <div class="overview-grid">
          
          <!-- Player Information -->
          <div class="overview-section player-info-section">
            <h3><i class="fas fa-user"></i> Player Information</h3>
            <div class="info-grid">
              <div class="info-item">
                <span class="info-label">Joined:</span>
                <span class="info-value">${new Date(player.joinDate || player.createdAt).toLocaleDateString()}</span>
              </div>
              <div class="info-item">
                <span class="info-label">Last Seen:</span>
                <span class="info-value">${this.formatLastSeen(player.lastSeen)}</span>
              </div>
              <div class="info-item">
                <span class="info-label">Total Playtime:</span>
                <span class="info-value">${this.formatPlaytime(stats.totalPlaytime)}</span>
              </div>
              <div class="info-item">
                <span class="info-label">Preferred Race:</span>
                <span class="info-value race-${stats.preferredRace || 'random'}">
                  ${this.capitalizeRace(stats.preferredRace)}
                </span>
              </div>
            </div>
          </div>

          <!-- Recent Performance -->
          <div class="overview-section performance-section">
            <h3><i class="fas fa-chart-line"></i> Recent Performance</h3>
            <div class="performance-stats">
              <div class="performance-item">
                <span class="perf-label">Last 10 Games:</span>
                <div class="match-results">
                  ${this.generateRecentResults(recentMatches)}
                </div>
              </div>
              <div class="performance-item">
                <span class="perf-label">This Month:</span>
                <span class="perf-value">${stats.monthlyWins || 0}W - ${stats.monthlyLosses || 0}L</span>
              </div>
              <div class="performance-item">
                <span class="perf-label">Rating Change:</span>
                <span class="perf-value rating-change ${(stats.ratingChange || 0) >= 0 ? 'positive' : 'negative'}">
                  ${(stats.ratingChange || 0) >= 0 ? '+' : ''}${stats.ratingChange || 0}
                </span>
              </div>
            </div>
          </div>

          <!-- Quick Charts -->
          <div class="overview-section charts-section">
            <h3><i class="fas fa-chart-pie"></i> Race Distribution</h3>
            <div class="mini-chart-container">
              <canvas id="overviewRaceChart" width="200" height="200"></canvas>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="overview-section actions-section">
            <h3><i class="fas fa-tools"></i> Quick Actions</h3>
            <div class="action-buttons">
              <button class="btn btn-primary challenge-btn" onclick="challengePlayer('${player.id}')">
                <i class="fas fa-sword-cross"></i> Challenge Player
              </button>
              <button class="btn btn-secondary message-btn" onclick="messagePlayer('${player.id}')">
                <i class="fas fa-envelope"></i> Send Message
              </button>
              <button class="btn btn-info view-profile-btn" onclick="viewFullProfile('${player.id}')">
                <i class="fas fa-user-circle"></i> View Full Profile
              </button>
            </div>
          </div>

        </div>
      </div>
    `;
  }

  /**
   * Generate matches tab content
   */
  generateMatchesTab(playerData) {
    return `
      <div class="tab-content matches-content ${this.activeTab === 'matches' ? 'active' : ''}">
        <div class="matches-header">
          <h3><i class="fas fa-history"></i> Match History</h3>
          <div class="matches-filters">
            <select class="filter-select" id="match-result-filter">
              <option value="">All Results</option>
              <option value="win">Wins Only</option>
              <option value="loss">Losses Only</option>
              <option value="draw">Draws Only</option>
            </select>
            <select class="filter-select" id="match-race-filter">
              <option value="">All Races</option>
              <option value="human">Human</option>
              <option value="orc">Orc</option>
              <option value="random">Random</option>
            </select>
            <input type="text" class="filter-input" id="match-map-filter" placeholder="Filter by map...">
          </div>
        </div>
        <div id="match-history-container" class="match-history-container">
          <!-- Match history will be loaded here -->
        </div>
      </div>
    `;
  }

  /**
   * Generate statistics tab content
   */
  generateStatisticsTab(playerData) {
    return `
      <div class="tab-content statistics-content ${this.activeTab === 'statistics' ? 'active' : ''}">
        <div class="statistics-header">
          <h3><i class="fas fa-chart-pie"></i> Detailed Statistics</h3>
        </div>
        
        <div class="statistics-grid">
          <div class="stat-section">
            <h4>Race Performance</h4>
            <div class="chart-container">
              <canvas id="raceDistributionChart"></canvas>
            </div>
          </div>
          
          <div class="stat-section">
            <h4>Win Rate by Race</h4>
            <div class="chart-container">
              <canvas id="raceWinRateChart"></canvas>
            </div>
          </div>
          
          <div class="stat-section">
            <h4>Map Performance</h4>
            <div class="chart-container">
              <canvas id="mapDistributionChart"></canvas>
            </div>
          </div>
          
          <div class="stat-section">
            <h4>Resource Preferences</h4>
            <div class="chart-container">
              <canvas id="resourceDistributionChart"></canvas>
            </div>
          </div>
        </div>
        
        <div class="stats-tables">
          <div class="stats-column">
            <h4>Race Statistics</h4>
            <div class="stats-table" id="race-stats-table">
              <!-- Race statistics table will be generated here -->
            </div>
          </div>
          
          <div class="stats-column">
            <h4>Map Performance</h4>
            <div class="stats-table" id="map-stats-table">
              <!-- Map statistics table will be generated here -->
            </div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Generate achievements tab content
   */
  generateAchievementsTab(playerData) {
    const achievements = playerData.achievements || [];
    
    return `
      <div class="tab-content achievements-content ${this.activeTab === 'achievements' ? 'active' : ''}">
        <div class="achievements-header">
          <h3><i class="fas fa-trophy"></i> Achievements</h3>
          <div class="achievement-summary">
            <span class="achievement-count">${achievements.length} Achievements Unlocked</span>
          </div>
        </div>
        
        <div class="achievements-grid">
          ${achievements.length > 0 
            ? achievements.map(achievement => this.generateAchievementCard(achievement)).join('')
            : this.getNoAchievementsHTML()
          }
        </div>
      </div>
    `;
  }

  /**
   * Generate achievement card
   */
  generateAchievementCard(achievement) {
    return `
      <div class="achievement-card ${achievement.unlocked ? 'unlocked' : 'locked'}">
        <div class="achievement-icon">
          <i class="${achievement.icon || 'fas fa-trophy'}"></i>
        </div>
        <div class="achievement-info">
          <h4 class="achievement-title">${achievement.title}</h4>
          <p class="achievement-description">${achievement.description}</p>
          ${achievement.unlocked 
            ? `<span class="achievement-date">Unlocked: ${new Date(achievement.unlockedAt).toLocaleDateString()}</span>`
            : `<span class="achievement-progress">${achievement.progress || 0}% Complete</span>`
          }
        </div>
      </div>
    `;
  }

  /**
   * Get no achievements HTML
   */
  getNoAchievementsHTML() {
    return `
      <div class="no-achievements">
        <i class="fas fa-trophy"></i>
        <h4>No Achievements Yet</h4>
        <p>This player hasn't unlocked any achievements yet. Keep playing to earn your first achievement!</p>
      </div>
    `;
  }

  /**
   * Switch to a different tab
   */
  switchTab(tabName) {
    if (this.activeTab === tabName) {
      return;
    }

    console.log(`🔄 Switching to tab: ${tabName}`);
    this.activeTab = tabName;

    // Update tab buttons
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
      if (button.dataset.tab === tabName) {
        button.classList.add('active');
      } else {
        button.classList.remove('active');
      }
    });

    // Update tab content
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
      content.classList.remove('active');
    });

    const activeContent = document.querySelector(`.${tabName}-content`);
    if (activeContent) {
      activeContent.classList.add('active');
      
      // Load tab-specific data if needed
      this.loadTabData(tabName);
    }

    console.log(`✅ Switched to tab: ${tabName}`);
  }

  /**
   * Load data for specific tab
   */
  async loadTabData(tabName) {
    if (!this.currentPlayer) return;

    switch (tabName) {
      case 'matches':
        await this.loadMatchHistoryData();
        break;
      case 'statistics':
        this.initializeStatisticsCharts();
        break;
    }
  }

  /**
   * Load match history data for matches tab
   */
  async loadMatchHistoryData() {
    const data = await playerMatchHistory.loadMatchHistory(this.currentPlayer.player.id);
    if (data && data.matches) {
      playerMatchHistory.renderMatchHistory(data.matches);
    }
  }

  /**
   * Initialize statistics charts
   */
  initializeStatisticsCharts() {
    if (this.currentPlayer && this.currentPlayer.stats) {
      playerStatsCharts.initializeAllCharts(this.currentPlayer.stats);
    }
  }

  /**
   * Setup modal event listeners
   */
  setupModalEventListeners(modal) {
    // Close modal when clicking outside
    modal.addEventListener('click', (event) => {
      if (event.target === modal) {
        this.closeModal();
      }
    });

    // Setup keyboard shortcuts
    document.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));

    console.log('✅ Modal event listeners setup');
  }

  /**
   * Handle keyboard shortcuts
   */
  handleKeyboardShortcuts(event) {
    if (!this.modalElement) return;

    switch (event.key) {
      case 'Escape':
        this.closeModal();
        break;
      case '1':
      case '2':
      case '3':
      case '4':
        if (event.ctrlKey || event.metaKey) {
          event.preventDefault();
          const tabs = ['overview', 'matches', 'statistics', 'achievements'];
          const tabIndex = parseInt(event.key) - 1;
          if (tabs[tabIndex]) {
            this.switchTab(tabs[tabIndex]);
          }
        }
        break;
    }
  }

  /**
   * Initialize modal components after creation
   */
  initializeModalComponents(playerData) {
    // Initialize charts for overview tab
    setTimeout(() => {
      this.createOverviewChart(playerData.stats);
    }, 100);

    // Store player ID for match history loading
    playerMatchHistory.currentPlayerId = playerData.player.id;
  }

  /**
   * Create overview mini chart
   */
  createOverviewChart(stats) {
    if (!stats.races) return;

    const canvas = document.getElementById('overviewRaceChart');
    if (!canvas) return;

    // Use the stats charts module to create a smaller version
    playerStatsCharts.createRaceDistributionChart(stats);
  }

  /**
   * Close the modal
   */
  closeModal() {
    playerModalCore.closePlayerDetailsModal();
    this.currentPlayer = null;
    this.modalElement = null;
    
    // Remove keyboard event listener
    document.removeEventListener('keydown', this.handleKeyboardShortcuts);
  }

  /**
   * Utility methods
   */
  formatRank(rank) {
    if (!rank) return 'Unranked';
    return `#${rank}`;
  }

  calculateWinRate(stats) {
    if (!stats.totalGames || stats.totalGames === 0) return 0;
    return Math.round((stats.wins / stats.totalGames) * 100);
  }

  formatLastSeen(lastSeen) {
    if (!lastSeen) return 'Unknown';
    const date = new Date(lastSeen);
    const now = new Date();
    const diffMs = now - date;
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    return date.toLocaleDateString();
  }

  formatPlaytime(playtime) {
    if (!playtime) return '0 hours';
    const hours = Math.floor(playtime / 3600);
    if (hours < 1) return '< 1 hour';
    return `${hours} hours`;
  }

  capitalizeRace(race) {
    if (!race) return 'Unknown';
    return race.charAt(0).toUpperCase() + race.slice(1);
  }

  generateRecentResults(recentMatches) {
    if (!recentMatches || recentMatches.length === 0) {
      return '<span class="no-recent">No recent matches</span>';
    }

    return recentMatches.slice(0, 10).map(match => {
      const result = match.result || 'unknown';
      return `<span class="result-dot result-${result}" title="${result}"></span>`;
    }).join('');
  }

  /**
   * Setup modal styles
   */
  setupModalStyles() {
    if (document.getElementById('player-modal-ui-styles')) return;

    const styles = document.createElement('style');
    styles.id = 'player-modal-ui-styles';
    styles.textContent = `
      .player-details-modal .modal-content {
        max-width: 1200px;
        width: 95vw;
        max-height: 90vh;
        padding: 0;
        overflow: hidden;
      }

      .player-modal-header {
        background: linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(51, 65, 85, 0.2));
        border-bottom: 2px solid rgba(212, 175, 55, 0.3);
        padding: 20px 30px;
        position: relative;
      }

      .player-header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 30px;
      }

      .player-basic-info {
        display: flex;
        align-items: center;
        gap: 20px;
      }

      .player-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        overflow: hidden;
        border: 3px solid #D4AF37;
      }

      .player-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .player-name {
        font-size: 28px;
        font-weight: 700;
        color: #D4AF37;
        margin: 0 0 10px 0;
      }

      .player-meta {
        display: flex;
        gap: 15px;
        align-items: center;
      }

      .player-rank,
      .player-rating {
        background: rgba(212, 175, 55, 0.2);
        color: #D4AF37;
        padding: 4px 10px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 600;
      }

      .player-status {
        padding: 4px 10px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
      }

      .player-status.online {
        background: rgba(16, 185, 129, 0.2);
        color: #10b981;
      }

      .player-status.offline {
        background: rgba(107, 114, 128, 0.2);
        color: #6b7280;
      }

      .player-quick-stats {
        display: flex;
        gap: 25px;
      }

      .quick-stat {
        text-align: center;
      }

      .stat-value {
        display: block;
        font-size: 24px;
        font-weight: 700;
        color: #f1f5f9;
        line-height: 1;
      }

      .stat-label {
        display: block;
        font-size: 12px;
        color: #94a3b8;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-top: 4px;
      }

      .player-modal-tabs {
        display: flex;
        background: rgba(51, 65, 85, 0.3);
        border-bottom: 1px solid rgba(148, 163, 184, 0.2);
      }

      .tab-button {
        flex: 1;
        padding: 15px 20px;
        background: none;
        border: none;
        color: #94a3b8;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        font-size: 14px;
        font-weight: 500;
        border-bottom: 3px solid transparent;
      }

      .tab-button:hover {
        color: #e2e8f0;
        background: rgba(212, 175, 55, 0.1);
      }

      .tab-button.active {
        color: #D4AF37;
        background: rgba(212, 175, 55, 0.1);
        border-bottom-color: #D4AF37;
      }

      .player-modal-body {
        height: 60vh;
        overflow-y: auto;
        padding: 0;
      }

      .tab-content {
        display: none;
        padding: 30px;
        height: 100%;
      }

      .tab-content.active {
        display: block;
      }

      .overview-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        height: 100%;
      }

      .overview-section {
        background: rgba(51, 65, 85, 0.3);
        border-radius: 8px;
        padding: 20px;
        border: 1px solid rgba(148, 163, 184, 0.2);
      }

      .overview-section h3 {
        color: #D4AF37;
        margin: 0 0 20px 0;
        font-size: 16px;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .info-grid {
        display: grid;
        gap: 15px;
      }

      .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid rgba(148, 163, 184, 0.1);
      }

      .info-label {
        color: #94a3b8;
        font-size: 14px;
      }

      .info-value {
        color: #f1f5f9;
        font-weight: 500;
      }

      .performance-stats {
        display: flex;
        flex-direction: column;
        gap: 15px;
      }

      .performance-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .perf-label {
        color: #94a3b8;
        font-size: 14px;
      }

      .perf-value {
        color: #f1f5f9;
        font-weight: 500;
      }

      .rating-change.positive {
        color: #10b981;
      }

      .rating-change.negative {
        color: #ef4444;
      }

      .match-results {
        display: flex;
        gap: 4px;
      }

      .result-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
      }

      .result-dot.result-win {
        background: #10b981;
      }

      .result-dot.result-loss {
        background: #ef4444;
      }

      .result-dot.result-draw {
        background: #f97316;
      }

      .mini-chart-container {
        height: 200px;
        position: relative;
      }

      .action-buttons {
        display: flex;
        flex-direction: column;
        gap: 10px;
      }

      .btn {
        padding: 10px 15px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
        justify-content: center;
      }

      .btn-primary {
        background: #D4AF37;
        color: #0f172a;
      }

      .btn-primary:hover {
        background: #E8C547;
      }

      .btn-secondary {
        background: #475569;
        color: #f1f5f9;
      }

      .btn-secondary:hover {
        background: #64748b;
      }

      .btn-info {
        background: #0ea5e9;
        color: white;
      }

      .btn-info:hover {
        background: #0284c7;
      }

      .matches-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 2px solid rgba(212, 175, 55, 0.3);
      }

      .matches-filters {
        display: flex;
        gap: 10px;
      }

      .filter-select,
      .filter-input {
        padding: 8px 12px;
        border: 1px solid rgba(148, 163, 184, 0.3);
        border-radius: 4px;
        background: rgba(51, 65, 85, 0.8);
        color: #f1f5f9;
        font-size: 14px;
      }

      .statistics-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin-bottom: 30px;
      }

      .stat-section {
        background: rgba(51, 65, 85, 0.3);
        border-radius: 8px;
        padding: 20px;
        border: 1px solid rgba(148, 163, 184, 0.2);
      }

      .stat-section h4 {
        color: #D4AF37;
        margin: 0 0 15px 0;
        font-size: 16px;
      }

      .achievements-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
      }

      .achievement-card {
        background: rgba(51, 65, 85, 0.3);
        border-radius: 8px;
        padding: 20px;
        border: 1px solid rgba(148, 163, 184, 0.2);
        display: flex;
        gap: 15px;
        transition: all 0.3s ease;
      }

      .achievement-card.unlocked {
        border-color: #D4AF37;
        background: rgba(212, 175, 55, 0.1);
      }

      .achievement-card.locked {
        opacity: 0.6;
      }

      .achievement-icon {
        font-size: 24px;
        color: #D4AF37;
        width: 40px;
        text-align: center;
      }

      .achievement-title {
        color: #f1f5f9;
        margin: 0 0 8px 0;
        font-size: 16px;
      }

      .achievement-description {
        color: #94a3b8;
        margin: 0 0 8px 0;
        font-size: 14px;
        line-height: 1.4;
      }

      .achievement-date,
      .achievement-progress {
        font-size: 12px;
        color: #D4AF37;
        font-weight: 500;
      }

      .no-achievements {
        text-align: center;
        padding: 60px 20px;
        color: #94a3b8;
        grid-column: 1 / -1;
      }

      .no-achievements i {
        font-size: 48px;
        margin-bottom: 20px;
        opacity: 0.5;
      }

      .no-recent {
        color: #94a3b8;
        font-style: italic;
        font-size: 12px;
      }
    `;

    document.head.appendChild(styles);
  }

  /**
   * Cleanup modal UI
   */
  cleanup() {
    console.log('🧹 Cleaning up Player Modal UI...');
    
    this.currentPlayer = null;
    this.activeTab = 'overview';
    this.tabData.clear();
    this.modalElement = null;

    console.log('✅ Player Modal UI cleanup complete');
  }
}

// Create and export singleton instance
export const playerModalUI = new PlayerModalUI(); 