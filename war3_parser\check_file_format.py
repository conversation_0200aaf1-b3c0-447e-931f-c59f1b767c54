#!/usr/bin/env python3
"""
Check War3 map file format
"""

import os
from pathlib import Path

def check_file_format(file_path):
    """Check the format of a War3 map file"""
    print(f"🔍 Checking file: {file_path}")
    
    with open(file_path, 'rb') as f:
        # Read first 16 bytes to check header
        header = f.read(16)
        
        print(f"📋 File size: {os.path.getsize(file_path)} bytes")
        print(f"🔤 Header (hex): {header.hex()}")
        print(f"🔤 Header (ascii): {header}")
        
        # Check for common archive formats
        if header.startswith(b'PK'):
            print("✅ This is a ZIP archive")
        elif header.startswith(b'MPQ\x1a'):
            print("✅ This is an MPQ archive")
        elif header.startswith(b'MPQ\x1b'):
            print("✅ This is an MPQ archive (newer format)")
        elif b'MPQ' in header:
            print("✅ This appears to be an MPQ archive")
        else:
            print("❓ Unknown format")
            
        # Try to find MPQ signature anywhere in first 512 bytes
        f.seek(0)
        first_chunk = f.read(512)
        if b'MPQ' in first_chunk:
            mpq_pos = first_chunk.find(b'MPQ')
            print(f"🎯 Found MPQ signature at offset: {mpq_pos}")

def main():
    # Check a few different map files
    maps_dir = Path("uploads/war3")
    map_files = list(maps_dir.glob("*.w3x"))[:5]  # Check first 5 files
    
    for map_file in map_files:
        check_file_format(map_file)
        print("-" * 50)

if __name__ == "__main__":
    main() 