/*
 * Copyright (c) 2014-2016 <PERSON>h
 *
 * Permission is hereby granted, free of charge, to any person obtaining a
 * copy of this software and associated documentation files (the "Software"),
 * to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.
 */

#include "pud_private.h"
#include "pud.h"

static const uint8_t _forest_colors[][3] =
{
   [0x0010] = { 0x04, 0x38, 0x75 },
   [0x0011] = { 0x04, 0x38, 0x75 },
   [0x0012] = { 0x04, 0x38, 0x75 },
   [0x0013] = { 0x04, 0x38, 0x75 },
   [0x0020] = { 0x04, 0x34, 0x71 },
   [0x0021] = { 0x04, 0x34, 0x71 },
   [0x0022] = { 0x04, 0x34, 0x71 },
   [0x0023] = { 0x04, 0x34, 0x71 },
   [0x0030] = { 0x6d, 0x41, 0x00 },
   [0x0031] = { 0x75, 0x45, 0x04 },
   [0x0032] = { 0x6d, 0x41, 0x00 },
   [0x0034] = { 0x51, 0x30, 0x00 },
   [0x0035] = { 0x6d, 0x41, 0x00 },
   [0x0036] = { 0x6d, 0x41, 0x00 },
   [0x0037] = { 0x6d, 0x41, 0x00 },
   [0x0038] = { 0x6d, 0x41, 0x00 },
   [0x0039] = { 0x61, 0x38, 0x00 },
   [0x003a] = { 0x6d, 0x41, 0x00 },
   [0x003b] = { 0x6d, 0x41, 0x00 },
   [0x0040] = { 0x61, 0x38, 0x00 },
   [0x0041] = { 0x61, 0x38, 0x00 },
   [0x0042] = { 0x6d, 0x41, 0x00 },
   [0x0044] = { 0x61, 0x38, 0x00 },
   [0x0045] = { 0x61, 0x38, 0x00 },
   [0x0046] = { 0x61, 0x38, 0x00 },
   [0x0047] = { 0x61, 0x38, 0x00 },
   [0x0048] = { 0x61, 0x38, 0x00 },
   [0x0049] = { 0x51, 0x30, 0x00 },
   [0x004a] = { 0x61, 0x38, 0x00 },
   [0x004b] = { 0x61, 0x38, 0x00 },
   [0x0050] = { 0x28, 0x55, 0x0c },
   [0x0051] = { 0x28, 0x55, 0x0c },
   [0x0052] = { 0x28, 0x55, 0x0c },
   [0x0054] = { 0x24, 0x49, 0x04 },
   [0x0055] = { 0x28, 0x55, 0x0c },
   [0x0056] = { 0x24, 0x49, 0x04 },
   [0x0057] = { 0x2c, 0x5d, 0x10 },
   [0x0058] = { 0x24, 0x49, 0x04 },
   [0x0059] = { 0x41, 0x2c, 0x00 },
   [0x005a] = { 0x24, 0x49, 0x04 },
   [0x005b] = { 0x28, 0x55, 0x0c },
   [0x005c] = { 0x24, 0x49, 0x04 },
   [0x005d] = { 0x28, 0x55, 0x0c },
   [0x005e] = { 0x24, 0x49, 0x04 },
   [0x005f] = { 0x28, 0x55, 0x0c },
   [0x0060] = { 0x24, 0x49, 0x04 },
   [0x0061] = { 0x24, 0x49, 0x04 },
   [0x0062] = { 0x24, 0x49, 0x04 },
   [0x0064] = { 0x24, 0x49, 0x04 },
   [0x0065] = { 0x49, 0x49, 0x49 },
   [0x0066] = { 0x24, 0x49, 0x04 },
   [0x0067] = { 0x24, 0x49, 0x04 },
   [0x0068] = { 0x24, 0x49, 0x04 },
   [0x0069] = { 0x24, 0x49, 0x04 },
   [0x006a] = { 0x24, 0x49, 0x04 },
   [0x006b] = { 0x49, 0x49, 0x49 },
   [0x006c] = { 0x24, 0x49, 0x04 },
   [0x006d] = { 0x49, 0x49, 0x49 },
   [0x006e] = { 0x24, 0x49, 0x04 },
   [0x006f] = { 0x49, 0x49, 0x49 },
   [0x0070] = { 0x00, 0x4d, 0x00 },
   [0x0071] = { 0x00, 0x4d, 0x00 },
   [0x0072] = { 0x00, 0x4d, 0x00 },
   [0x0080] = { 0x18, 0x18, 0x18 },
   [0x0081] = { 0x49, 0x49, 0x49 },
   [0x0082] = { 0x3c, 0x3c, 0x3c },
   [0x0083] = { 0x3c, 0x3c, 0x3c },
   [0x0090] = { 0x51, 0x51, 0x51 },
   [0x0092] = { 0x49, 0x49, 0x49 },
   [0x0094] = { 0x75, 0x75, 0x75 },
   [0x00a0] = { 0x69, 0x69, 0x69 },
   [0x00a2] = { 0x28, 0x55, 0x0c },
   [0x00a4] = { 0x75, 0x75, 0x75 },
   [0x00b0] = { 0x96, 0x96, 0x96 },
   [0x00b2] = { 0x96, 0x96, 0x96 },
   [0x00b4] = { 0x8a, 0x8a, 0x8a },
   [0x00c0] = { 0x3c, 0x3c, 0x3c },
   [0x00c2] = { 0x8a, 0x61, 0x18 },
   [0x00c4] = { 0x8a, 0x8a, 0x8a },
   [0x0100] = { 0x04, 0x34, 0x71 },
   [0x0101] = { 0x04, 0x34, 0x71 },
   [0x0110] = { 0x04, 0x38, 0x75 },
   [0x0111] = { 0x04, 0x34, 0x71 },
   [0x0120] = { 0x04, 0x34, 0x71 },
   [0x0121] = { 0x04, 0x34, 0x71 },
   [0x0122] = { 0x04, 0x34, 0x71 },
   [0x0130] = { 0x04, 0x38, 0x75 },
   [0x0131] = { 0x04, 0x38, 0x75 },
   [0x0140] = { 0x04, 0x34, 0x71 },
   [0x0141] = { 0x04, 0x34, 0x71 },
   [0x0142] = { 0x04, 0x34, 0x71 },
   [0x0150] = { 0x04, 0x38, 0x75 },
   [0x0151] = { 0x04, 0x38, 0x75 },
   [0x0160] = { 0x04, 0x34, 0x71 },
   [0x0161] = { 0x04, 0x34, 0x71 },
   [0x0170] = { 0x04, 0x38, 0x75 },
   [0x0171] = { 0x04, 0x38, 0x75 },
   [0x0180] = { 0x04, 0x34, 0x71 },
   [0x0181] = { 0x04, 0x34, 0x71 },
   [0x0190] = { 0x04, 0x38, 0x75 },
   [0x0191] = { 0x04, 0x34, 0x71 },
   [0x0192] = { 0x04, 0x38, 0x75 },
   [0x01a0] = { 0x04, 0x34, 0x71 },
   [0x01a1] = { 0x04, 0x34, 0x71 },
   [0x01b0] = { 0x04, 0x38, 0x75 },
   [0x01b1] = { 0x04, 0x38, 0x75 },
   [0x01b2] = { 0x04, 0x38, 0x75 },
   [0x01c0] = { 0x04, 0x34, 0x71 },
   [0x01c1] = { 0x04, 0x34, 0x71 },
   [0x01d0] = { 0x04, 0x38, 0x75 },
   [0x01d1] = { 0x04, 0x38, 0x75 },
   [0x0200] = { 0x61, 0x38, 0x00 },
   [0x0201] = { 0x00, 0x20, 0x55 },
   [0x0210] = { 0x75, 0x45, 0x04 },
   [0x0211] = { 0x6d, 0x41, 0x00 },
   [0x0220] = { 0x04, 0x34, 0x71 },
   [0x0221] = { 0x00, 0x30, 0x69 },
   [0x0222] = { 0x00, 0x28, 0x5d },
   [0x0230] = { 0x75, 0x45, 0x04 },
   [0x0231] = { 0x6d, 0x41, 0x00 },
   [0x0240] = { 0x00, 0x20, 0x55 },
   [0x0241] = { 0x00, 0x30, 0x69 },
   [0x0242] = { 0x00, 0x20, 0x55 },
   [0x0250] = { 0x61, 0x38, 0x00 },
   [0x0251] = { 0x61, 0x38, 0x00 },
   [0x0260] = { 0x04, 0x34, 0x71 },
   [0x0261] = { 0x04, 0x38, 0x75 },
   [0x0270] = { 0x6d, 0x41, 0x00 },
   [0x0271] = { 0x6d, 0x41, 0x00 },
   [0x0280] = { 0x00, 0x30, 0x69 },
   [0x0281] = { 0x00, 0x30, 0x69 },
   [0x0290] = { 0x75, 0x45, 0x04 },
   [0x0291] = { 0x75, 0x45, 0x04 },
   [0x0292] = { 0x75, 0x45, 0x04 },
   [0x02a0] = { 0x59, 0x34, 0x10 },
   [0x02a1] = { 0x61, 0x38, 0x00 },
   [0x02b0] = { 0x6d, 0x41, 0x00 },
   [0x02b1] = { 0x6d, 0x41, 0x00 },
   [0x02b2] = { 0x75, 0x45, 0x04 },
   [0x02c0] = { 0x00, 0x30, 0x69 },
   [0x02c1] = { 0x00, 0x30, 0x69 },
   [0x02d0] = { 0x61, 0x38, 0x00 },
   [0x02d1] = { 0x6d, 0x41, 0x00 },
   [0x0300] = { 0x61, 0x38, 0x00 },
   [0x0301] = { 0x61, 0x38, 0x00 },
   [0x0310] = { 0x75, 0x45, 0x04 },
   [0x0311] = { 0x75, 0x45, 0x04 },
   [0x0320] = { 0x6d, 0x41, 0x00 },
   [0x0321] = { 0x61, 0x38, 0x00 },
   [0x0322] = { 0x61, 0x38, 0x00 },
   [0x0330] = { 0x75, 0x45, 0x04 },
   [0x0331] = { 0x75, 0x45, 0x04 },
   [0x0340] = { 0x61, 0x38, 0x00 },
   [0x0341] = { 0x61, 0x38, 0x00 },
   [0x0342] = { 0x61, 0x38, 0x00 },
   [0x0350] = { 0x61, 0x38, 0x00 },
   [0x0351] = { 0x75, 0x45, 0x04 },
   [0x0360] = { 0x61, 0x38, 0x00 },
   [0x0361] = { 0x61, 0x38, 0x00 },
   [0x0370] = { 0x6d, 0x41, 0x00 },
   [0x0371] = { 0x6d, 0x41, 0x00 },
   [0x0380] = { 0x61, 0x38, 0x00 },
   [0x0381] = { 0x61, 0x38, 0x00 },
   [0x0390] = { 0x75, 0x45, 0x04 },
   [0x0391] = { 0x61, 0x38, 0x00 },
   [0x0392] = { 0x75, 0x45, 0x04 },
   [0x03a0] = { 0x6d, 0x41, 0x00 },
   [0x03a1] = { 0x6d, 0x41, 0x00 },
   [0x03b0] = { 0x75, 0x45, 0x04 },
   [0x03b1] = { 0x61, 0x38, 0x00 },
   [0x03b2] = { 0x6d, 0x41, 0x00 },
   [0x03c0] = { 0x6d, 0x41, 0x00 },
   [0x03c1] = { 0x6d, 0x41, 0x00 },
   [0x03d0] = { 0x61, 0x38, 0x00 },
   [0x03d1] = { 0x61, 0x38, 0x00 },
   [0x0400] = { 0x51, 0x51, 0x51 },
   [0x0401] = { 0x7d, 0x7d, 0x7d },
   [0x0410] = { 0x3c, 0x3c, 0x3c },
   [0x0411] = { 0x75, 0x75, 0x75 },
   [0x0420] = { 0x7d, 0x7d, 0x7d },
   [0x0421] = { 0x69, 0x69, 0x69 },
   [0x0430] = { 0x61, 0x38, 0x00 },
   [0x0431] = { 0x69, 0x69, 0x69 },
   [0x0440] = { 0x30, 0x30, 0x30 },
   [0x0441] = { 0x3c, 0x3c, 0x3c },
   [0x0450] = { 0x3c, 0x3c, 0x3c },
   [0x0451] = { 0x3c, 0x3c, 0x3c },
   [0x0460] = { 0x75, 0x75, 0x75 },
   [0x0470] = { 0x6d, 0x41, 0x00 },
   [0x0471] = { 0x6d, 0x41, 0x00 },
   [0x0480] = { 0x69, 0x69, 0x69 },
   [0x0481] = { 0x51, 0x51, 0x51 },
   [0x0490] = { 0x49, 0x49, 0x49 },
   [0x0491] = { 0x30, 0x30, 0x30 },
   [0x04a0] = { 0x3c, 0x3c, 0x3c },
   [0x04b0] = { 0x6d, 0x41, 0x00 },
   [0x04b1] = { 0x6d, 0x41, 0x00 },
   [0x04c0] = { 0x3c, 0x3c, 0x3c },
   [0x04d0] = { 0x3c, 0x3c, 0x3c },
   [0x0500] = { 0x75, 0x45, 0x04 },
   [0x0501] = { 0x2c, 0x5d, 0x10 },
   [0x0510] = { 0x28, 0x55, 0x0c },
   [0x0511] = { 0x24, 0x49, 0x04 },
   [0x0520] = { 0x6d, 0x41, 0x00 },
   [0x0521] = { 0x75, 0x45, 0x04 },
   [0x0522] = { 0x6d, 0x41, 0x00 },
   [0x0530] = { 0x4d, 0x6d, 0x1c },
   [0x0531] = { 0x2c, 0x5d, 0x10 },
   [0x0540] = { 0x75, 0x45, 0x04 },
   [0x0541] = { 0x61, 0x38, 0x00 },
   [0x0542] = { 0x3c, 0x65, 0x14 },
   [0x0550] = { 0x28, 0x55, 0x0c },
   [0x0551] = { 0x2c, 0x5d, 0x10 },
   [0x0560] = { 0x6d, 0x41, 0x00 },
   [0x0561] = { 0x6d, 0x41, 0x00 },
   [0x0570] = { 0x2c, 0x5d, 0x10 },
   [0x0571] = { 0x28, 0x55, 0x0c },
   [0x0580] = { 0x6d, 0x41, 0x00 },
   [0x0581] = { 0x75, 0x45, 0x04 },
   [0x0590] = { 0x28, 0x55, 0x0c },
   [0x0591] = { 0x2c, 0x5d, 0x10 },
   [0x0592] = { 0x2c, 0x5d, 0x10 },
   [0x05a0] = { 0x6d, 0x41, 0x00 },
   [0x05a1] = { 0x6d, 0x41, 0x00 },
   [0x05b0] = { 0x51, 0x30, 0x00 },
   [0x05b1] = { 0x2c, 0x5d, 0x10 },
   [0x05b2] = { 0x28, 0x55, 0x0c },
   [0x05c0] = { 0x75, 0x45, 0x04 },
   [0x05c1] = { 0x6d, 0x41, 0x00 },
   [0x05d0] = { 0x51, 0x30, 0x00 },
   [0x05d1] = { 0x28, 0x55, 0x0c },
   [0x0600] = { 0x28, 0x55, 0x0c },
   [0x0601] = { 0x28, 0x55, 0x0c },
   [0x0610] = { 0x2c, 0x5d, 0x10 },
   [0x0611] = { 0x28, 0x55, 0x0c },
   [0x0620] = { 0x28, 0x55, 0x0c },
   [0x0621] = { 0x28, 0x55, 0x0c },
   [0x0622] = { 0x28, 0x55, 0x0c },
   [0x0630] = { 0x2c, 0x5d, 0x10 },
   [0x0631] = { 0x2c, 0x5d, 0x10 },
   [0x0640] = { 0x28, 0x55, 0x0c },
   [0x0641] = { 0x28, 0x55, 0x0c },
   [0x0642] = { 0x28, 0x55, 0x0c },
   [0x0650] = { 0x2c, 0x5d, 0x10 },
   [0x0651] = { 0x2c, 0x5d, 0x10 },
   [0x0660] = { 0x28, 0x55, 0x0c },
   [0x0661] = { 0x28, 0x55, 0x0c },
   [0x0670] = { 0x2c, 0x5d, 0x10 },
   [0x0671] = { 0x2c, 0x5d, 0x10 },
   [0x0680] = { 0x28, 0x55, 0x0c },
   [0x0681] = { 0x28, 0x55, 0x0c },
   [0x0690] = { 0x2c, 0x5d, 0x10 },
   [0x0691] = { 0x2c, 0x5d, 0x10 },
   [0x0692] = { 0x2c, 0x5d, 0x10 },
   [0x06a0] = { 0x28, 0x55, 0x0c },
   [0x06a1] = { 0x28, 0x55, 0x0c },
   [0x06b0] = { 0x28, 0x55, 0x0c },
   [0x06b1] = { 0x28, 0x55, 0x0c },
   [0x06b2] = { 0x2c, 0x5d, 0x10 },
   [0x06c0] = { 0x28, 0x55, 0x0c },
   [0x06c1] = { 0x28, 0x55, 0x0c },
   [0x06d0] = { 0x2c, 0x5d, 0x10 },
   [0x06d1] = { 0x2c, 0x5d, 0x10 },
   [0x0700] = { 0x00, 0x2c, 0x00 },
   [0x0701] = { 0x00, 0x2c, 0x00 },
   [0x0710] = { 0x14, 0x34, 0x00 },
   [0x0711] = { 0x00, 0x2c, 0x00 },
   [0x0720] = { 0x00, 0x2c, 0x00 },
   [0x0721] = { 0x00, 0x4d, 0x00 },
   [0x0730] = { 0x28, 0x55, 0x0c },
   [0x0731] = { 0x28, 0x55, 0x0c },
   [0x0740] = { 0x00, 0x4d, 0x00 },
   [0x0741] = { 0x00, 0x4d, 0x00 },
   [0x0750] = { 0x00, 0x2c, 0x00 },
   [0x0751] = { 0x00, 0x2c, 0x00 },
   [0x0760] = { 0x00, 0x4d, 0x00 },
   [0x0761] = { 0x00, 0x4d, 0x00 },
   [0x0770] = { 0x24, 0x49, 0x04 },
   [0x0771] = { 0x28, 0x55, 0x0c },
   [0x0780] = { 0x00, 0x2c, 0x00 },
   [0x0781] = { 0x00, 0x2c, 0x00 },
   [0x0790] = { 0x14, 0x34, 0x00 },
   [0x0791] = { 0x14, 0x34, 0x00 },
   [0x07a0] = { 0x00, 0x4d, 0x00 },
   [0x07a1] = { 0x00, 0x4d, 0x00 },
   [0x07b0] = { 0x28, 0x55, 0x0c },
   [0x07b1] = { 0x28, 0x55, 0x0c },
   [0x07c0] = { 0x00, 0x4d, 0x00 },
   [0x07c1] = { 0x00, 0x4d, 0x00 },
   [0x07d0] = { 0x41, 0x2c, 0x00 },
   [0x07d1] = { 0x41, 0x2c, 0x00 },
   [0x0800] = { 0x51, 0x51, 0x51 },
   [0x0802] = { 0x75, 0x75, 0x75 },
   [0x0804] = { 0x75, 0x75, 0x75 },
   [0x0810] = { 0x51, 0x51, 0x51 },
   [0x0812] = { 0x75, 0x75, 0x75 },
   [0x0814] = { 0x75, 0x75, 0x75 },
   [0x0820] = { 0x51, 0x51, 0x51 },
   [0x0822] = { 0x75, 0x75, 0x75 },
   [0x0824] = { 0x75, 0x75, 0x75 },
   [0x0830] = { 0x8a, 0x8a, 0x8a },
   [0x0832] = { 0x8a, 0x8a, 0x8a },
   [0x0834] = { 0x75, 0x75, 0x75 },
   [0x0840] = { 0x8a, 0x8a, 0x8a },
   [0x0841] = { 0x8a, 0x8a, 0x8a },
   [0x0843] = { 0x24, 0x24, 0x24 },
   [0x0844] = { 0x30, 0x30, 0x30 },
   [0x0846] = { 0x75, 0x75, 0x75 },
   [0x0847] = { 0x75, 0x75, 0x75 },
   [0x0850] = { 0x8a, 0x8a, 0x8a },
   [0x0852] = { 0x8a, 0x8a, 0x8a },
   [0x0854] = { 0x75, 0x75, 0x75 },
   [0x0860] = { 0x8a, 0x8a, 0x8a },
   [0x0862] = { 0x3c, 0x3c, 0x3c },
   [0x0864] = { 0x75, 0x75, 0x75 },
   [0x0870] = { 0x8a, 0x8a, 0x8a },
   [0x0872] = { 0x8a, 0x8a, 0x8a },
   [0x0874] = { 0x75, 0x75, 0x75 },
   [0x0880] = { 0x8a, 0x8a, 0x8a },
   [0x0882] = { 0x8a, 0x8a, 0x8a },
   [0x0884] = { 0x75, 0x75, 0x75 },
   [0x0890] = { 0x8a, 0x8a, 0x8a },
   [0x0891] = { 0x8a, 0x8a, 0x8a },
   [0x0893] = { 0x75, 0x75, 0x75 },
   [0x0894] = { 0x8a, 0x8a, 0x8a },
   [0x0896] = { 0x5d, 0x5d, 0x5d },
   [0x0897] = { 0x75, 0x75, 0x75 },
   [0x08a0] = { 0x8a, 0x8a, 0x8a },
   [0x08a2] = { 0x51, 0x51, 0x51 },
   [0x08a4] = { 0x5d, 0x5d, 0x5d },
   [0x08b0] = { 0x96, 0x96, 0x96 },
   [0x08b2] = { 0x96, 0x96, 0x96 },
   [0x08b4] = { 0x75, 0x75, 0x75 },
   [0x08c0] = { 0x96, 0x96, 0x96 },
   [0x08c2] = { 0x96, 0x96, 0x96 },
   [0x08c4] = { 0x75, 0x75, 0x75 },
   [0x08d0] = { 0x96, 0x96, 0x96 },
   [0x08d2] = { 0x96, 0x96, 0x96 },
   [0x08d4] = { 0x75, 0x75, 0x75 },
   [0x0900] = { 0x69, 0x69, 0x69 },
   [0x0902] = { 0x28, 0x55, 0x0c },
   [0x0904] = { 0x75, 0x75, 0x75 },
   [0x0910] = { 0x69, 0x69, 0x69 },
   [0x0912] = { 0x28, 0x55, 0x0c },
   [0x0914] = { 0x75, 0x75, 0x75 },
   [0x0920] = { 0x69, 0x69, 0x69 },
   [0x0922] = { 0x28, 0x55, 0x0c },
   [0x0924] = { 0x75, 0x75, 0x75 },
   [0x0930] = { 0x69, 0x69, 0x69 },
   [0x0932] = { 0xa2, 0x86, 0x4d },
   [0x0934] = { 0x75, 0x75, 0x75 },
   [0x0940] = { 0x69, 0x69, 0x69 },
   [0x0941] = { 0x69, 0x69, 0x69 },
   [0x0943] = { 0xa2, 0x86, 0x4d },
   [0x0944] = { 0x49, 0x49, 0x49 },
   [0x0946] = { 0x75, 0x75, 0x75 },
   [0x0947] = { 0x75, 0x75, 0x75 },
   [0x0950] = { 0x69, 0x69, 0x69 },
   [0x0952] = { 0xa2, 0x86, 0x4d },
   [0x0954] = { 0x75, 0x75, 0x75 },
   [0x0960] = { 0x69, 0x69, 0x69 },
   [0x0962] = { 0x49, 0x49, 0x49 },
   [0x0964] = { 0x75, 0x75, 0x75 },
   [0x0970] = { 0x5d, 0x5d, 0x5d },
   [0x0972] = { 0x3c, 0x3c, 0x3c },
   [0x0974] = { 0x75, 0x75, 0x75 },
   [0x0980] = { 0x5d, 0x5d, 0x5d },
   [0x0982] = { 0x3c, 0x3c, 0x3c },
   [0x0984] = { 0x75, 0x75, 0x75 },
   [0x0990] = { 0x5d, 0x5d, 0x5d },
   [0x0991] = { 0x5d, 0x5d, 0x5d },
   [0x0993] = { 0x3c, 0x3c, 0x3c },
   [0x0994] = { 0x41, 0x2c, 0x00 },
   [0x0996] = { 0x5d, 0x5d, 0x5d },
   [0x0997] = { 0x75, 0x75, 0x75 },
   [0x09a0] = { 0x5d, 0x5d, 0x5d },
   [0x09a2] = { 0x3c, 0x3c, 0x3c },
   [0x09a4] = { 0x5d, 0x5d, 0x5d },
   [0x09b0] = { 0x3c, 0x3c, 0x3c },
   [0x09b2] = { 0x8a, 0x61, 0x18 },
   [0x09b4] = { 0x75, 0x75, 0x75 },
   [0x09c0] = { 0x3c, 0x3c, 0x3c },
   [0x09c2] = { 0x8a, 0x61, 0x18 },
   [0x09c4] = { 0x75, 0x75, 0x75 },
   [0x09d0] = { 0x3c, 0x3c, 0x3c },
   [0x09d2] = { 0x8a, 0x61, 0x18 },
   [0x09d4] = { 0x75, 0x75, 0x75 },
};

static const uint8_t _winter_colors[][3] =
{
   [0x0010] = { 0x04, 0x38, 0x75 },
   [0x0011] = { 0x04, 0x38, 0x75 },
   [0x0012] = { 0x04, 0x38, 0x75 },
   [0x0013] = { 0x04, 0x38, 0x75 },
   [0x0015] = { 0x61, 0x9a, 0xcb },
   [0x0016] = { 0x04, 0x38, 0x75 },
   [0x0017] = { 0x04, 0x38, 0x75 },
   [0x0020] = { 0x04, 0x34, 0x71 },
   [0x0021] = { 0x04, 0x34, 0x71 },
   [0x0022] = { 0x04, 0x34, 0x71 },
   [0x0023] = { 0x04, 0x34, 0x71 },
   [0x0025] = { 0x61, 0x9a, 0xcb },
   [0x0026] = { 0x04, 0x34, 0x71 },
   [0x0027] = { 0x04, 0x34, 0x71 },
   [0x0030] = { 0x18, 0x55, 0x8a },
   [0x0031] = { 0x18, 0x55, 0x8a },
   [0x0032] = { 0x18, 0x55, 0x8a },
   [0x0034] = { 0x18, 0x55, 0x8a },
   [0x0035] = { 0x69, 0x6d, 0x86 },
   [0x0036] = { 0x18, 0x55, 0x8a },
   [0x0037] = { 0x18, 0x55, 0x8a },
   [0x0038] = { 0x18, 0x55, 0x8a },
   [0x0039] = { 0x18, 0x55, 0x8a },
   [0x003a] = { 0x18, 0x55, 0x8a },
   [0x003b] = { 0x18, 0x55, 0x8a },
   [0x0040] = { 0x14, 0x4d, 0x8e },
   [0x0041] = { 0x14, 0x4d, 0x8e },
   [0x0042] = { 0x14, 0x4d, 0x8e },
   [0x0044] = { 0x14, 0x4d, 0x8e },
   [0x0045] = { 0x69, 0x6d, 0x86 },
   [0x0046] = { 0x14, 0x4d, 0x8e },
   [0x0047] = { 0x14, 0x4d, 0x8e },
   [0x0048] = { 0x14, 0x4d, 0x8e },
   [0x0049] = { 0x14, 0x4d, 0x8e },
   [0x004a] = { 0x14, 0x4d, 0x8e },
   [0x004b] = { 0x14, 0x4d, 0x8e },
   [0x0050] = { 0x8e, 0x8e, 0x9e },
   [0x0051] = { 0x96, 0x96, 0xa2 },
   [0x0052] = { 0x8e, 0x8e, 0x9e },
   [0x0054] = { 0x8e, 0x8e, 0x9e },
   [0x0055] = { 0x8e, 0x8e, 0x9e },
   [0x0056] = { 0x8e, 0x8e, 0x9e },
   [0x0057] = { 0x96, 0x96, 0xa2 },
   [0x0058] = { 0x96, 0x96, 0xa2 },
   [0x0059] = { 0x8e, 0x8e, 0x9e },
   [0x005a] = { 0x8e, 0x8e, 0x9e },
   [0x005b] = { 0x8e, 0x8e, 0x9e },
   [0x005c] = { 0x8e, 0x8e, 0x9e },
   [0x005d] = { 0x8e, 0x8e, 0x9e },
   [0x005e] = { 0x8e, 0x8e, 0x9e },
   [0x005f] = { 0x96, 0x96, 0xa2 },
   [0x0060] = { 0x86, 0x86, 0x9a },
   [0x0061] = { 0x8e, 0x8e, 0x9e },
   [0x0062] = { 0x86, 0x86, 0x9a },
   [0x0064] = { 0x86, 0x86, 0x9a },
   [0x0065] = { 0x86, 0x86, 0x9a },
   [0x0066] = { 0x86, 0x86, 0x9a },
   [0x0067] = { 0x8e, 0x8e, 0x9e },
   [0x0068] = { 0x8e, 0x8e, 0x9e },
   [0x0069] = { 0x86, 0x86, 0x9a },
   [0x006a] = { 0x86, 0x86, 0x9a },
   [0x006b] = { 0x86, 0x86, 0x9a },
   [0x006c] = { 0x86, 0x86, 0x9a },
   [0x006d] = { 0x86, 0x86, 0x9a },
   [0x006e] = { 0x8e, 0x8e, 0x9e },
   [0x006f] = { 0x8e, 0x8e, 0x9e },
   [0x0070] = { 0x20, 0x59, 0x65 },
   [0x0071] = { 0x20, 0x59, 0x65 },
   [0x0072] = { 0x20, 0x59, 0x65 },
   [0x0080] = { 0x4d, 0x55, 0x71 },
   [0x0081] = { 0xa2, 0xa2, 0xa6 },
   [0x0082] = { 0x49, 0x28, 0x20 },
   [0x0083] = { 0x49, 0x28, 0x20 },
   [0x0090] = { 0x4d, 0x55, 0x71 },
   [0x0092] = { 0x41, 0x49, 0x69 },
   [0x0094] = { 0x71, 0x75, 0x8e },
   [0x00a0] = { 0x69, 0x6d, 0x86 },
   [0x00a2] = { 0x8e, 0x8e, 0x9e },
   [0x00a4] = { 0x71, 0x75, 0x8e },
   [0x00b0] = { 0x96, 0x96, 0xa2 },
   [0x00b2] = { 0x96, 0x96, 0xa2 },
   [0x00b4] = { 0x59, 0x61, 0x7d },
   [0x00c0] = { 0x14, 0x49, 0x49 },
   [0x00c2] = { 0x8a, 0x61, 0x4d },
   [0x00c4] = { 0x59, 0x61, 0x7d },
   [0x0100] = { 0x04, 0x34, 0x71 },
   [0x0101] = { 0x04, 0x34, 0x71 },
   [0x0110] = { 0x04, 0x38, 0x75 },
   [0x0111] = { 0x04, 0x34, 0x71 },
   [0x0120] = { 0x04, 0x34, 0x71 },
   [0x0121] = { 0x04, 0x34, 0x71 },
   [0x0122] = { 0x04, 0x34, 0x71 },
   [0x0130] = { 0x04, 0x38, 0x75 },
   [0x0131] = { 0x04, 0x38, 0x75 },
   [0x0140] = { 0x04, 0x34, 0x71 },
   [0x0141] = { 0x04, 0x34, 0x71 },
   [0x0142] = { 0x04, 0x34, 0x71 },
   [0x0150] = { 0x04, 0x38, 0x75 },
   [0x0151] = { 0x04, 0x38, 0x75 },
   [0x0160] = { 0x04, 0x34, 0x71 },
   [0x0161] = { 0x04, 0x34, 0x71 },
   [0x0170] = { 0x04, 0x38, 0x75 },
   [0x0171] = { 0x04, 0x38, 0x75 },
   [0x0180] = { 0x04, 0x34, 0x71 },
   [0x0181] = { 0x04, 0x34, 0x71 },
   [0x0190] = { 0x04, 0x38, 0x75 },
   [0x0191] = { 0x04, 0x34, 0x71 },
   [0x0192] = { 0x04, 0x38, 0x75 },
   [0x01a0] = { 0x04, 0x34, 0x71 },
   [0x01a1] = { 0x04, 0x34, 0x71 },
   [0x01b0] = { 0x04, 0x38, 0x75 },
   [0x01b1] = { 0x04, 0x38, 0x75 },
   [0x01b2] = { 0x04, 0x38, 0x75 },
   [0x01c0] = { 0x04, 0x34, 0x71 },
   [0x01c1] = { 0x04, 0x34, 0x71 },
   [0x01d0] = { 0x04, 0x38, 0x75 },
   [0x01d1] = { 0x04, 0x38, 0x75 },
   [0x0200] = { 0x04, 0x34, 0x71 },
   [0x0201] = { 0x55, 0x7d, 0xb2 },
   [0x0210] = { 0x18, 0x55, 0x8a },
   [0x0211] = { 0x38, 0x65, 0x9a },
   [0x0220] = { 0x04, 0x38, 0x75 },
   [0x0221] = { 0x04, 0x38, 0x75 },
   [0x0222] = { 0x04, 0x38, 0x75 },
   [0x0230] = { 0x18, 0x55, 0x8a },
   [0x0231] = { 0x18, 0x55, 0x8a },
   [0x0240] = { 0x04, 0x38, 0x75 },
   [0x0241] = { 0x18, 0x55, 0x8a },
   [0x0242] = { 0x04, 0x38, 0x75 },
   [0x0250] = { 0x2c, 0x5d, 0x96 },
   [0x0251] = { 0x2c, 0x5d, 0x96 },
   [0x0260] = { 0x04, 0x38, 0x75 },
   [0x0261] = { 0x04, 0x38, 0x75 },
   [0x0270] = { 0x18, 0x55, 0x8a },
   [0x0271] = { 0x18, 0x55, 0x8a },
   [0x0280] = { 0x04, 0x38, 0x75 },
   [0x0281] = { 0x04, 0x38, 0x75 },
   [0x0290] = { 0x14, 0x4d, 0x8e },
   [0x0291] = { 0x18, 0x55, 0x8a },
   [0x0292] = { 0x18, 0x55, 0x8a },
   [0x02a0] = { 0x04, 0x38, 0x75 },
   [0x02a1] = { 0x04, 0x38, 0x75 },
   [0x02b0] = { 0x14, 0x4d, 0x8e },
   [0x02b1] = { 0x18, 0x55, 0x8a },
   [0x02b2] = { 0x2c, 0x5d, 0x96 },
   [0x02c0] = { 0x04, 0x38, 0x75 },
   [0x02c1] = { 0x04, 0x38, 0x75 },
   [0x02d0] = { 0x18, 0x55, 0x8a },
   [0x02d1] = { 0x2c, 0x5d, 0x96 },
   [0x0300] = { 0x14, 0x4d, 0x8e },
   [0x0301] = { 0x14, 0x4d, 0x8e },
   [0x0310] = { 0x14, 0x4d, 0x8e },
   [0x0311] = { 0x18, 0x55, 0x8a },
   [0x0320] = { 0x14, 0x4d, 0x8e },
   [0x0321] = { 0x14, 0x4d, 0x8e },
   [0x0322] = { 0x14, 0x4d, 0x8e },
   [0x0330] = { 0x18, 0x55, 0x8a },
   [0x0331] = { 0x18, 0x55, 0x8a },
   [0x0340] = { 0x14, 0x4d, 0x8e },
   [0x0341] = { 0x14, 0x4d, 0x8e },
   [0x0342] = { 0x14, 0x4d, 0x8e },
   [0x0350] = { 0x18, 0x55, 0x8a },
   [0x0351] = { 0x18, 0x55, 0x8a },
   [0x0360] = { 0x14, 0x4d, 0x8e },
   [0x0361] = { 0x14, 0x4d, 0x8e },
   [0x0370] = { 0x18, 0x55, 0x8a },
   [0x0371] = { 0x18, 0x55, 0x8a },
   [0x0380] = { 0x14, 0x4d, 0x8e },
   [0x0381] = { 0x14, 0x4d, 0x8e },
   [0x0390] = { 0x18, 0x55, 0x8a },
   [0x0391] = { 0x14, 0x4d, 0x8e },
   [0x0392] = { 0x18, 0x55, 0x8a },
   [0x03a0] = { 0x14, 0x4d, 0x8e },
   [0x03a1] = { 0x14, 0x4d, 0x8e },
   [0x03b0] = { 0x18, 0x55, 0x8a },
   [0x03b1] = { 0x18, 0x55, 0x8a },
   [0x03b2] = { 0x18, 0x55, 0x8a },
   [0x03c0] = { 0x18, 0x55, 0x8a },
   [0x03c1] = { 0x18, 0x55, 0x8a },
   [0x03d0] = { 0x14, 0x4d, 0x8e },
   [0x03d1] = { 0x14, 0x4d, 0x8e },
   [0x0400] = { 0xa2, 0xa2, 0xa6 },
   [0x0401] = { 0x8e, 0x8e, 0x9e },
   [0x0410] = { 0x49, 0x28, 0x20 },
   [0x0411] = { 0x7d, 0x55, 0x49 },
   [0x0420] = { 0x86, 0x86, 0x9a },
   [0x0421] = { 0x75, 0x49, 0x3c },
   [0x0430] = { 0x59, 0x61, 0x7d },
   [0x0431] = { 0x61, 0x65, 0x82 },
   [0x0440] = { 0x3c, 0x24, 0x20 },
   [0x0441] = { 0x49, 0x28, 0x20 },
   [0x0450] = { 0x69, 0x6d, 0x86 },
   [0x0451] = { 0x3c, 0x24, 0x20 },
   [0x0460] = { 0x7d, 0x55, 0x49 },
   [0x0470] = { 0x18, 0x55, 0x8a },
   [0x0471] = { 0x08, 0x45, 0x79 },
   [0x0480] = { 0x75, 0x49, 0x3c },
   [0x0481] = { 0xa2, 0xa2, 0xa6 },
   [0x0490] = { 0x71, 0x75, 0x8e },
   [0x0491] = { 0x18, 0x55, 0x8a },
   [0x04a0] = { 0x49, 0x28, 0x20 },
   [0x04b0] = { 0x38, 0x65, 0x9a },
   [0x04b1] = { 0x00, 0x30, 0x69 },
   [0x04c0] = { 0x49, 0x28, 0x20 },
   [0x04d0] = { 0x41, 0x49, 0x69 },
   [0x0500] = { 0x69, 0x6d, 0x86 },
   [0x0501] = { 0x69, 0x6d, 0x86 },
   [0x0510] = { 0x8e, 0x8e, 0x9e },
   [0x0511] = { 0x8e, 0x8e, 0x9e },
   [0x0520] = { 0x18, 0x55, 0x8a },
   [0x0521] = { 0x18, 0x55, 0x8a },
   [0x0522] = { 0x18, 0x55, 0x8a },
   [0x0530] = { 0x96, 0x96, 0xa2 },
   [0x0531] = { 0x96, 0x96, 0xa2 },
   [0x0540] = { 0x18, 0x55, 0x8a },
   [0x0541] = { 0x18, 0x55, 0x8a },
   [0x0542] = { 0x18, 0x55, 0x8a },
   [0x0550] = { 0x8e, 0x8e, 0x9e },
   [0x0551] = { 0x96, 0x96, 0xa2 },
   [0x0560] = { 0x18, 0x55, 0x8a },
   [0x0561] = { 0x18, 0x55, 0x8a },
   [0x0570] = { 0x8e, 0x8e, 0x9e },
   [0x0571] = { 0x8e, 0x8e, 0x9e },
   [0x0580] = { 0x18, 0x55, 0x8a },
   [0x0581] = { 0x69, 0x6d, 0x86 },
   [0x0590] = { 0x96, 0x96, 0xa2 },
   [0x0591] = { 0x96, 0x96, 0xa2 },
   [0x0592] = { 0x96, 0x96, 0xa2 },
   [0x05a0] = { 0x18, 0x55, 0x8a },
   [0x05a1] = { 0x18, 0x55, 0x8a },
   [0x05b0] = { 0x8e, 0x8e, 0x9e },
   [0x05b1] = { 0x8e, 0x8e, 0x9e },
   [0x05b2] = { 0x8e, 0x8e, 0x9e },
   [0x05c0] = { 0x18, 0x55, 0x8a },
   [0x05c1] = { 0x18, 0x55, 0x8a },
   [0x05d0] = { 0x8e, 0x8e, 0x9e },
   [0x05d1] = { 0x8e, 0x8e, 0x9e },
   [0x0600] = { 0x86, 0x86, 0x9a },
   [0x0601] = { 0x8e, 0x8e, 0x9e },
   [0x0610] = { 0x8e, 0x8e, 0x9e },
   [0x0611] = { 0x86, 0x86, 0x9a },
   [0x0620] = { 0x8e, 0x8e, 0x9e },
   [0x0621] = { 0x86, 0x86, 0x9a },
   [0x0622] = { 0x86, 0x86, 0x9a },
   [0x0630] = { 0x96, 0x96, 0xa2 },
   [0x0631] = { 0x96, 0x96, 0xa2 },
   [0x0640] = { 0x86, 0x86, 0x9a },
   [0x0641] = { 0x86, 0x86, 0x9a },
   [0x0642] = { 0x86, 0x86, 0x9a },
   [0x0650] = { 0x96, 0x96, 0xa2 },
   [0x0651] = { 0x96, 0x96, 0xa2 },
   [0x0660] = { 0x8e, 0x8e, 0x9e },
   [0x0661] = { 0x8e, 0x8e, 0x9e },
   [0x0670] = { 0x8e, 0x8e, 0x9e },
   [0x0671] = { 0x96, 0x96, 0xa2 },
   [0x0680] = { 0x86, 0x86, 0x9a },
   [0x0681] = { 0x8e, 0x8e, 0x9e },
   [0x0690] = { 0x86, 0x86, 0x9a },
   [0x0691] = { 0x8e, 0x8e, 0x9e },
   [0x0692] = { 0x8e, 0x8e, 0x9e },
   [0x06a0] = { 0x8e, 0x8e, 0x9e },
   [0x06a1] = { 0x8e, 0x8e, 0x9e },
   [0x06b0] = { 0x8e, 0x8e, 0x9e },
   [0x06b1] = { 0x8e, 0x8e, 0x9e },
   [0x06b2] = { 0x8e, 0x8e, 0x9e },
   [0x06c0] = { 0x96, 0x96, 0xa2 },
   [0x06c1] = { 0x96, 0x96, 0xa2 },
   [0x06d0] = { 0x8e, 0x8e, 0x9e },
   [0x06d1] = { 0x8e, 0x8e, 0x9e },
   [0x0700] = { 0x04, 0x28, 0x08 },
   [0x0701] = { 0x04, 0x28, 0x08 },
   [0x0710] = { 0x0c, 0x30, 0x0c },
   [0x0711] = { 0x04, 0x28, 0x08 },
   [0x0720] = { 0x04, 0x28, 0x08 },
   [0x0721] = { 0x20, 0x59, 0x65 },
   [0x0730] = { 0x8e, 0x8e, 0x9e },
   [0x0731] = { 0x8e, 0x8e, 0x9e },
   [0x0740] = { 0x20, 0x59, 0x65 },
   [0x0741] = { 0x20, 0x59, 0x65 },
   [0x0750] = { 0x3c, 0x24, 0x20 },
   [0x0751] = { 0x3c, 0x24, 0x20 },
   [0x0760] = { 0x20, 0x59, 0x65 },
   [0x0761] = { 0x20, 0x59, 0x65 },
   [0x0770] = { 0x8e, 0x8e, 0x9e },
   [0x0771] = { 0x8e, 0x8e, 0x9e },
   [0x0780] = { 0x20, 0x59, 0x65 },
   [0x0781] = { 0x20, 0x59, 0x65 },
   [0x0790] = { 0x0c, 0x30, 0x0c },
   [0x0791] = { 0x0c, 0x30, 0x0c },
   [0x07a0] = { 0x20, 0x59, 0x65 },
   [0x07a1] = { 0x20, 0x59, 0x65 },
   [0x07b0] = { 0x8e, 0x8e, 0x9e },
   [0x07b1] = { 0x8e, 0x8e, 0x9e },
   [0x07c0] = { 0x20, 0x59, 0x65 },
   [0x07c1] = { 0x20, 0x59, 0x65 },
   [0x07d0] = { 0x3c, 0x24, 0x20 },
   [0x07d1] = { 0x3c, 0x24, 0x20 },
   [0x0800] = { 0x4d, 0x55, 0x71 },
   [0x0802] = { 0x71, 0x75, 0x8e },
   [0x0804] = { 0x71, 0x75, 0x8e },
   [0x0810] = { 0x4d, 0x55, 0x71 },
   [0x0812] = { 0x71, 0x75, 0x8e },
   [0x0814] = { 0x71, 0x75, 0x8e },
   [0x0820] = { 0x4d, 0x55, 0x71 },
   [0x0822] = { 0x71, 0x75, 0x8e },
   [0x0824] = { 0x71, 0x75, 0x8e },
   [0x0830] = { 0x86, 0x86, 0x9a },
   [0x0832] = { 0x86, 0x86, 0x9a },
   [0x0834] = { 0x59, 0x61, 0x7d },
   [0x0840] = { 0x86, 0x86, 0x9a },
   [0x0841] = { 0x86, 0x86, 0x9a },
   [0x0843] = { 0x00, 0x28, 0x5d },
   [0x0844] = { 0x00, 0x28, 0x5d },
   [0x0846] = { 0x59, 0x61, 0x7d },
   [0x0847] = { 0x59, 0x61, 0x7d },
   [0x0850] = { 0x86, 0x86, 0x9a },
   [0x0852] = { 0x86, 0x86, 0x9a },
   [0x0854] = { 0x59, 0x61, 0x7d },
   [0x0860] = { 0x86, 0x86, 0x9a },
   [0x0862] = { 0x30, 0x49, 0x59 },
   [0x0864] = { 0x59, 0x61, 0x7d },
   [0x0870] = { 0x86, 0x86, 0x9a },
   [0x0872] = { 0x86, 0x86, 0x9a },
   [0x0874] = { 0x59, 0x61, 0x7d },
   [0x0880] = { 0x86, 0x86, 0x9a },
   [0x0882] = { 0x86, 0x86, 0x9a },
   [0x0884] = { 0x59, 0x61, 0x7d },
   [0x0890] = { 0x86, 0x86, 0x9a },
   [0x0891] = { 0x86, 0x86, 0x9a },
   [0x0893] = { 0x71, 0x75, 0x8e },
   [0x0894] = { 0x86, 0x86, 0x9a },
   [0x0896] = { 0x59, 0x61, 0x7d },
   [0x0897] = { 0x41, 0x49, 0x69 },
   [0x08a0] = { 0x86, 0x86, 0x9a },
   [0x08a2] = { 0x4d, 0x55, 0x71 },
   [0x08a4] = { 0x59, 0x61, 0x7d },
   [0x08b0] = { 0x96, 0x96, 0xa2 },
   [0x08b2] = { 0x96, 0x96, 0xa2 },
   [0x08b4] = { 0x59, 0x61, 0x7d },
   [0x08c0] = { 0x96, 0x96, 0xa2 },
   [0x08c2] = { 0x96, 0x96, 0xa2 },
   [0x08c4] = { 0x59, 0x61, 0x7d },
   [0x08d0] = { 0x96, 0x96, 0xa2 },
   [0x08d2] = { 0x96, 0x96, 0xa2 },
   [0x08d4] = { 0x41, 0x49, 0x69 },
   [0x0900] = { 0x69, 0x6d, 0x86 },
   [0x0902] = { 0x69, 0x6d, 0x86 },
   [0x0904] = { 0x71, 0x75, 0x8e },
   [0x0910] = { 0x69, 0x6d, 0x86 },
   [0x0912] = { 0x8e, 0x8e, 0x9e },
   [0x0914] = { 0x71, 0x75, 0x8e },
   [0x0920] = { 0x69, 0x6d, 0x86 },
   [0x0922] = { 0x8e, 0x8e, 0x9e },
   [0x0924] = { 0x71, 0x75, 0x8e },
   [0x0930] = { 0x69, 0x6d, 0x86 },
   [0x0932] = { 0xaa, 0x86, 0x4d },
   [0x0934] = { 0x59, 0x61, 0x7d },
   [0x0940] = { 0x69, 0x6d, 0x86 },
   [0x0941] = { 0x69, 0x6d, 0x86 },
   [0x0943] = { 0xaa, 0x86, 0x4d },
   [0x0944] = { 0x41, 0x49, 0x69 },
   [0x0946] = { 0x59, 0x61, 0x7d },
   [0x0947] = { 0x59, 0x61, 0x7d },
   [0x0950] = { 0x69, 0x6d, 0x86 },
   [0x0952] = { 0xaa, 0x86, 0x4d },
   [0x0954] = { 0x59, 0x61, 0x7d },
   [0x0960] = { 0x69, 0x6d, 0x86 },
   [0x0962] = { 0x41, 0x49, 0x69 },
   [0x0964] = { 0x59, 0x61, 0x7d },
   [0x0970] = { 0x59, 0x61, 0x7d },
   [0x0972] = { 0x14, 0x49, 0x49 },
   [0x0974] = { 0x59, 0x61, 0x7d },
   [0x0980] = { 0x59, 0x61, 0x7d },
   [0x0982] = { 0x14, 0x49, 0x49 },
   [0x0984] = { 0x59, 0x61, 0x7d },
   [0x0990] = { 0x59, 0x61, 0x7d },
   [0x0991] = { 0x59, 0x61, 0x7d },
   [0x0993] = { 0x3c, 0x24, 0x20 },
   [0x0994] = { 0x14, 0x49, 0x49 },
   [0x0996] = { 0x59, 0x61, 0x7d },
   [0x0997] = { 0x41, 0x49, 0x69 },
   [0x09a0] = { 0x59, 0x61, 0x7d },
   [0x09a2] = { 0x14, 0x49, 0x49 },
   [0x09a4] = { 0x59, 0x61, 0x7d },
   [0x09b0] = { 0x14, 0x49, 0x49 },
   [0x09b2] = { 0x8a, 0x61, 0x4d },
   [0x09b4] = { 0x59, 0x61, 0x7d },
   [0x09c0] = { 0x14, 0x49, 0x49 },
   [0x09c2] = { 0x8a, 0x61, 0x4d },
   [0x09c4] = { 0x59, 0x61, 0x7d },
   [0x09d0] = { 0x14, 0x49, 0x49 },
   [0x09d2] = { 0x8a, 0x61, 0x4d },
   [0x09d4] = { 0x41, 0x49, 0x69 },
};

static const uint8_t _wasteland_colors[][3] =
{
   [0x0010] = { 0x0c, 0x20, 0x2c },
   [0x0011] = { 0x0c, 0x20, 0x2c },
   [0x0012] = { 0x0c, 0x20, 0x2c },
   [0x0013] = { 0x0c, 0x20, 0x2c },
   [0x0015] = { 0x0c, 0x20, 0x2c },
   [0x0016] = { 0x0c, 0x20, 0x2c },
   [0x0017] = { 0x0c, 0x20, 0x2c },
   [0x0020] = { 0x10, 0x20, 0x2c },
   [0x0021] = { 0x10, 0x20, 0x2c },
   [0x0022] = { 0x10, 0x20, 0x2c },
   [0x0023] = { 0x10, 0x20, 0x2c },
   [0x0025] = { 0x10, 0x20, 0x2c },
   [0x0026] = { 0x10, 0x20, 0x2c },
   [0x0027] = { 0x10, 0x20, 0x2c },
   [0x0030] = { 0x4d, 0x28, 0x0c },
   [0x0031] = { 0x45, 0x24, 0x08 },
   [0x0032] = { 0x4d, 0x28, 0x0c },
   [0x0034] = { 0x4d, 0x28, 0x0c },
   [0x0035] = { 0x45, 0x24, 0x08 },
   [0x0036] = { 0x4d, 0x28, 0x0c },
   [0x0037] = { 0x4d, 0x28, 0x0c },
   [0x0038] = { 0x45, 0x24, 0x08 },
   [0x0039] = { 0x4d, 0x28, 0x0c },
   [0x003a] = { 0x45, 0x24, 0x08 },
   [0x003b] = { 0x4d, 0x28, 0x0c },
   [0x0040] = { 0x45, 0x24, 0x08 },
   [0x0041] = { 0x41, 0x20, 0x08 },
   [0x0042] = { 0x45, 0x24, 0x08 },
   [0x0044] = { 0x3c, 0x1c, 0x08 },
   [0x0045] = { 0x41, 0x20, 0x08 },
   [0x0046] = { 0x45, 0x24, 0x08 },
   [0x0047] = { 0x3c, 0x1c, 0x08 },
   [0x0048] = { 0x3c, 0x1c, 0x08 },
   [0x0049] = { 0x41, 0x20, 0x08 },
   [0x004a] = { 0x41, 0x20, 0x08 },
   [0x004b] = { 0x45, 0x24, 0x08 },
   [0x0050] = { 0x79, 0x38, 0x04 },
   [0x0051] = { 0x82, 0x41, 0x04 },
   [0x0052] = { 0x82, 0x41, 0x04 },
   [0x0054] = { 0x79, 0x38, 0x04 },
   [0x0055] = { 0x82, 0x41, 0x04 },
   [0x0056] = { 0x82, 0x41, 0x04 },
   [0x0057] = { 0xa6, 0x59, 0x14 },
   [0x0058] = { 0x79, 0x38, 0x04 },
   [0x0059] = { 0x82, 0x41, 0x04 },
   [0x005a] = { 0x82, 0x41, 0x04 },
   [0x005b] = { 0x8e, 0x49, 0x04 },
   [0x005c] = { 0x82, 0x41, 0x04 },
   [0x005d] = { 0x82, 0x41, 0x04 },
   [0x005e] = { 0x82, 0x41, 0x04 },
   [0x005f] = { 0x82, 0x41, 0x04 },
   [0x0060] = { 0x71, 0x30, 0x04 },
   [0x0061] = { 0x79, 0x38, 0x04 },
   [0x0062] = { 0x79, 0x38, 0x04 },
   [0x0064] = { 0x71, 0x30, 0x04 },
   [0x0065] = { 0x79, 0x38, 0x04 },
   [0x0066] = { 0x79, 0x38, 0x04 },
   [0x0067] = { 0x71, 0x30, 0x04 },
   [0x0068] = { 0x51, 0x1c, 0x08 },
   [0x0069] = { 0x79, 0x38, 0x04 },
   [0x006a] = { 0x79, 0x38, 0x04 },
   [0x006b] = { 0x82, 0x41, 0x04 },
   [0x006c] = { 0x79, 0x38, 0x04 },
   [0x006d] = { 0x79, 0x38, 0x04 },
   [0x006e] = { 0x51, 0x1c, 0x08 },
   [0x006f] = { 0x71, 0x30, 0x04 },
   [0x0070] = { 0x1c, 0x24, 0x00 },
   [0x0071] = { 0x1c, 0x24, 0x00 },
   [0x0072] = { 0x04, 0x10, 0x00 },
   [0x0080] = { 0x18, 0x10, 0x10 },
   [0x0081] = { 0x49, 0x3c, 0x38 },
   [0x0082] = { 0x41, 0x34, 0x30 },
   [0x0083] = { 0x41, 0x34, 0x30 },
   [0x0090] = { 0x38, 0x28, 0x28 },
   [0x0092] = { 0x38, 0x28, 0x28 },
   [0x0094] = { 0x5d, 0x51, 0x4d },
   [0x00a0] = { 0x55, 0x45, 0x45 },
   [0x00a2] = { 0x79, 0x38, 0x04 },
   [0x00a4] = { 0x5d, 0x51, 0x4d },
   [0x00b0] = { 0x8e, 0x86, 0x82 },
   [0x00b2] = { 0x79, 0x6d, 0x69 },
   [0x00b4] = { 0x71, 0x65, 0x61 },
   [0x00c0] = { 0x24, 0x18, 0x18 },
   [0x00c2] = { 0x82, 0x41, 0x04 },
   [0x00c4] = { 0x71, 0x65, 0x61 },
   [0x0100] = { 0x10, 0x20, 0x2c },
   [0x0101] = { 0x10, 0x20, 0x2c },
   [0x0110] = { 0x0c, 0x20, 0x2c },
   [0x0111] = { 0x0c, 0x20, 0x2c },
   [0x0120] = { 0x10, 0x20, 0x2c },
   [0x0121] = { 0x10, 0x20, 0x2c },
   [0x0122] = { 0x10, 0x20, 0x2c },
   [0x0130] = { 0x0c, 0x20, 0x2c },
   [0x0131] = { 0x0c, 0x20, 0x2c },
   [0x0140] = { 0x10, 0x20, 0x2c },
   [0x0141] = { 0x0c, 0x20, 0x2c },
   [0x0142] = { 0x10, 0x20, 0x2c },
   [0x0150] = { 0x0c, 0x20, 0x2c },
   [0x0151] = { 0x0c, 0x20, 0x2c },
   [0x0160] = { 0x10, 0x20, 0x2c },
   [0x0161] = { 0x10, 0x20, 0x2c },
   [0x0170] = { 0x0c, 0x20, 0x2c },
   [0x0171] = { 0x0c, 0x20, 0x2c },
   [0x0180] = { 0x10, 0x20, 0x2c },
   [0x0181] = { 0x10, 0x20, 0x2c },
   [0x0190] = { 0x0c, 0x20, 0x2c },
   [0x0191] = { 0x0c, 0x20, 0x2c },
   [0x0192] = { 0x0c, 0x20, 0x2c },
   [0x01a0] = { 0x10, 0x20, 0x2c },
   [0x01a1] = { 0x10, 0x20, 0x2c },
   [0x01b0] = { 0x0c, 0x20, 0x2c },
   [0x01b1] = { 0x0c, 0x20, 0x2c },
   [0x01b2] = { 0x0c, 0x20, 0x2c },
   [0x01c0] = { 0x10, 0x20, 0x2c },
   [0x01c1] = { 0x0c, 0x20, 0x2c },
   [0x01d0] = { 0x0c, 0x20, 0x2c },
   [0x01d1] = { 0x0c, 0x20, 0x2c },
   [0x0200] = { 0x2c, 0x10, 0x04 },
   [0x0201] = { 0x0c, 0x28, 0x34 },
   [0x0210] = { 0x45, 0x24, 0x08 },
   [0x0211] = { 0x45, 0x24, 0x08 },
   [0x0220] = { 0x0c, 0x20, 0x2c },
   [0x0221] = { 0x0c, 0x20, 0x2c },
   [0x0222] = { 0x0c, 0x20, 0x2c },
   [0x0230] = { 0x45, 0x24, 0x08 },
   [0x0231] = { 0x45, 0x24, 0x08 },
   [0x0240] = { 0x0c, 0x20, 0x2c },
   [0x0241] = { 0x0c, 0x20, 0x2c },
   [0x0242] = { 0x0c, 0x20, 0x2c },
   [0x0250] = { 0x45, 0x24, 0x08 },
   [0x0251] = { 0x45, 0x24, 0x08 },
   [0x0260] = { 0x0c, 0x20, 0x2c },
   [0x0261] = { 0x0c, 0x20, 0x2c },
   [0x0270] = { 0x4d, 0x28, 0x0c },
   [0x0271] = { 0x4d, 0x28, 0x0c },
   [0x0280] = { 0x0c, 0x20, 0x2c },
   [0x0281] = { 0x0c, 0x20, 0x2c },
   [0x0290] = { 0x41, 0x20, 0x08 },
   [0x0291] = { 0x4d, 0x28, 0x0c },
   [0x0292] = { 0x41, 0x20, 0x08 },
   [0x02a0] = { 0x0c, 0x20, 0x2c },
   [0x02a1] = { 0x0c, 0x20, 0x2c },
   [0x02b0] = { 0x45, 0x24, 0x08 },
   [0x02b1] = { 0x45, 0x24, 0x08 },
   [0x02b2] = { 0x45, 0x24, 0x08 },
   [0x02c0] = { 0x0c, 0x20, 0x2c },
   [0x02c1] = { 0x0c, 0x20, 0x2c },
   [0x02d0] = { 0x45, 0x24, 0x08 },
   [0x02d1] = { 0x4d, 0x28, 0x0c },
   [0x0300] = { 0x4d, 0x28, 0x0c },
   [0x0301] = { 0x4d, 0x28, 0x0c },
   [0x0310] = { 0x4d, 0x28, 0x0c },
   [0x0311] = { 0x4d, 0x28, 0x0c },
   [0x0320] = { 0x45, 0x24, 0x08 },
   [0x0321] = { 0x41, 0x20, 0x08 },
   [0x0322] = { 0x45, 0x24, 0x08 },
   [0x0330] = { 0x4d, 0x28, 0x0c },
   [0x0331] = { 0x4d, 0x28, 0x0c },
   [0x0340] = { 0x45, 0x24, 0x08 },
   [0x0341] = { 0x41, 0x20, 0x08 },
   [0x0342] = { 0x45, 0x24, 0x08 },
   [0x0350] = { 0x4d, 0x28, 0x0c },
   [0x0351] = { 0x4d, 0x28, 0x0c },
   [0x0360] = { 0x45, 0x24, 0x08 },
   [0x0361] = { 0x45, 0x24, 0x08 },
   [0x0370] = { 0x4d, 0x28, 0x0c },
   [0x0371] = { 0x4d, 0x28, 0x0c },
   [0x0380] = { 0x45, 0x24, 0x08 },
   [0x0381] = { 0x45, 0x24, 0x08 },
   [0x0390] = { 0x4d, 0x28, 0x0c },
   [0x0391] = { 0x45, 0x24, 0x08 },
   [0x0392] = { 0x4d, 0x28, 0x0c },
   [0x03a0] = { 0x45, 0x24, 0x08 },
   [0x03a1] = { 0x45, 0x24, 0x08 },
   [0x03b0] = { 0x4d, 0x28, 0x0c },
   [0x03b1] = { 0x45, 0x24, 0x08 },
   [0x03b2] = { 0x4d, 0x28, 0x0c },
   [0x03c0] = { 0x45, 0x24, 0x08 },
   [0x03c1] = { 0x45, 0x24, 0x08 },
   [0x03d0] = { 0x4d, 0x28, 0x0c },
   [0x03d1] = { 0x4d, 0x28, 0x0c },
   [0x0400] = { 0x49, 0x3c, 0x38 },
   [0x0401] = { 0x71, 0x65, 0x61 },
   [0x0410] = { 0x38, 0x28, 0x28 },
   [0x0411] = { 0x71, 0x65, 0x61 },
   [0x0420] = { 0x71, 0x65, 0x61 },
   [0x0421] = { 0x5d, 0x51, 0x4d },
   [0x0430] = { 0x4d, 0x28, 0x0c },
   [0x0431] = { 0x38, 0x28, 0x28 },
   [0x0440] = { 0x2c, 0x20, 0x20 },
   [0x0441] = { 0x38, 0x28, 0x28 },
   [0x0450] = { 0x38, 0x28, 0x28 },
   [0x0451] = { 0x38, 0x28, 0x28 },
   [0x0460] = { 0x71, 0x65, 0x61 },
   [0x0470] = { 0x4d, 0x28, 0x0c },
   [0x0471] = { 0x45, 0x24, 0x08 },
   [0x0480] = { 0x5d, 0x51, 0x4d },
   [0x0481] = { 0x49, 0x3c, 0x38 },
   [0x0490] = { 0x41, 0x34, 0x30 },
   [0x0491] = { 0x2c, 0x20, 0x20 },
   [0x04a0] = { 0x38, 0x28, 0x28 },
   [0x04b0] = { 0x4d, 0x28, 0x0c },
   [0x04b1] = { 0x45, 0x24, 0x08 },
   [0x04c0] = { 0x38, 0x28, 0x28 },
   [0x04d0] = { 0x38, 0x28, 0x28 },
   [0x0500] = { 0x51, 0x1c, 0x08 },
   [0x0501] = { 0x4d, 0x28, 0x0c },
   [0x0510] = { 0x79, 0x38, 0x04 },
   [0x0511] = { 0x82, 0x41, 0x04 },
   [0x0520] = { 0x4d, 0x28, 0x0c },
   [0x0521] = { 0x45, 0x24, 0x08 },
   [0x0522] = { 0x4d, 0x28, 0x0c },
   [0x0530] = { 0x82, 0x41, 0x04 },
   [0x0531] = { 0x79, 0x38, 0x04 },
   [0x0540] = { 0x4d, 0x28, 0x0c },
   [0x0541] = { 0x45, 0x18, 0x08 },
   [0x0542] = { 0x45, 0x24, 0x08 },
   [0x0550] = { 0x82, 0x41, 0x04 },
   [0x0551] = { 0x79, 0x38, 0x04 },
   [0x0560] = { 0x4d, 0x28, 0x0c },
   [0x0561] = { 0x4d, 0x28, 0x0c },
   [0x0570] = { 0x79, 0x38, 0x04 },
   [0x0571] = { 0x82, 0x41, 0x04 },
   [0x0580] = { 0x4d, 0x28, 0x0c },
   [0x0581] = { 0x51, 0x1c, 0x08 },
   [0x0590] = { 0x82, 0x41, 0x04 },
   [0x0591] = { 0x79, 0x38, 0x04 },
   [0x0592] = { 0x82, 0x41, 0x04 },
   [0x05a0] = { 0x4d, 0x28, 0x0c },
   [0x05a1] = { 0x4d, 0x28, 0x0c },
   [0x05b0] = { 0x8e, 0x49, 0x04 },
   [0x05b1] = { 0x79, 0x38, 0x04 },
   [0x05b2] = { 0x79, 0x38, 0x04 },
   [0x05c0] = { 0x45, 0x24, 0x08 },
   [0x05c1] = { 0x41, 0x20, 0x08 },
   [0x05d0] = { 0x82, 0x41, 0x04 },
   [0x05d1] = { 0x82, 0x41, 0x04 },
   [0x0600] = { 0x79, 0x38, 0x04 },
   [0x0601] = { 0x71, 0x30, 0x04 },
   [0x0610] = { 0x82, 0x41, 0x04 },
   [0x0611] = { 0x79, 0x38, 0x04 },
   [0x0620] = { 0x71, 0x30, 0x04 },
   [0x0621] = { 0x79, 0x38, 0x04 },
   [0x0622] = { 0x79, 0x38, 0x04 },
   [0x0630] = { 0x79, 0x38, 0x04 },
   [0x0631] = { 0x82, 0x41, 0x04 },
   [0x0640] = { 0x79, 0x38, 0x04 },
   [0x0641] = { 0x71, 0x30, 0x04 },
   [0x0642] = { 0x79, 0x38, 0x04 },
   [0x0650] = { 0x65, 0x28, 0x04 },
   [0x0651] = { 0x82, 0x41, 0x04 },
   [0x0660] = { 0x79, 0x38, 0x04 },
   [0x0661] = { 0x79, 0x38, 0x04 },
   [0x0670] = { 0x79, 0x38, 0x04 },
   [0x0671] = { 0x79, 0x38, 0x04 },
   [0x0680] = { 0x79, 0x38, 0x04 },
   [0x0681] = { 0x71, 0x30, 0x04 },
   [0x0690] = { 0x79, 0x38, 0x04 },
   [0x0691] = { 0x82, 0x41, 0x04 },
   [0x0692] = { 0x82, 0x41, 0x04 },
   [0x06a0] = { 0x79, 0x38, 0x04 },
   [0x06a1] = { 0x79, 0x38, 0x04 },
   [0x06b0] = { 0x79, 0x38, 0x04 },
   [0x06b1] = { 0x82, 0x41, 0x04 },
   [0x06b2] = { 0x82, 0x41, 0x04 },
   [0x06c0] = { 0x79, 0x38, 0x04 },
   [0x06c1] = { 0x79, 0x38, 0x04 },
   [0x06d0] = { 0x65, 0x28, 0x04 },
   [0x06d1] = { 0x65, 0x28, 0x04 },
   [0x0700] = { 0x1c, 0x24, 0x00 },
   [0x0701] = { 0x1c, 0x24, 0x00 },
   [0x0710] = { 0x51, 0x1c, 0x08 },
   [0x0711] = { 0x38, 0x14, 0x08 },
   [0x0720] = { 0x08, 0x18, 0x00 },
   [0x0721] = { 0x1c, 0x24, 0x00 },
   [0x0730] = { 0x79, 0x38, 0x04 },
   [0x0731] = { 0x79, 0x38, 0x04 },
   [0x0740] = { 0x1c, 0x24, 0x00 },
   [0x0741] = { 0x1c, 0x24, 0x00 },
   [0x0750] = { 0x38, 0x14, 0x08 },
   [0x0751] = { 0x38, 0x14, 0x08 },
   [0x0760] = { 0x1c, 0x24, 0x00 },
   [0x0761] = { 0x1c, 0x24, 0x00 },
   [0x0770] = { 0x79, 0x38, 0x04 },
   [0x0771] = { 0x82, 0x41, 0x04 },
   [0x0780] = { 0x1c, 0x24, 0x00 },
   [0x0781] = { 0x1c, 0x24, 0x00 },
   [0x0790] = { 0x45, 0x18, 0x08 },
   [0x0791] = { 0x38, 0x14, 0x08 },
   [0x07a0] = { 0x04, 0x10, 0x00 },
   [0x07a1] = { 0x1c, 0x24, 0x00 },
   [0x07b0] = { 0x79, 0x38, 0x04 },
   [0x07b1] = { 0x82, 0x41, 0x04 },
   [0x07c0] = { 0x1c, 0x24, 0x00 },
   [0x07c1] = { 0x04, 0x10, 0x00 },
   [0x07d0] = { 0x34, 0x30, 0x00 },
   [0x07d1] = { 0x34, 0x30, 0x00 },
   [0x0800] = { 0x41, 0x34, 0x30 },
   [0x0802] = { 0x5d, 0x51, 0x4d },
   [0x0804] = { 0x5d, 0x51, 0x4d },
   [0x0810] = { 0x41, 0x34, 0x30 },
   [0x0812] = { 0x5d, 0x51, 0x4d },
   [0x0814] = { 0x5d, 0x51, 0x4d },
   [0x0820] = { 0x41, 0x34, 0x30 },
   [0x0822] = { 0x5d, 0x51, 0x4d },
   [0x0824] = { 0x5d, 0x51, 0x4d },
   [0x0830] = { 0x71, 0x65, 0x61 },
   [0x0832] = { 0x71, 0x65, 0x61 },
   [0x0834] = { 0x5d, 0x51, 0x4d },
   [0x0840] = { 0x71, 0x65, 0x61 },
   [0x0841] = { 0x71, 0x65, 0x61 },
   [0x0843] = { 0x18, 0x10, 0x10 },
   [0x0844] = { 0x24, 0x18, 0x18 },
   [0x0846] = { 0x5d, 0x51, 0x4d },
   [0x0847] = { 0x5d, 0x51, 0x4d },
   [0x0850] = { 0x71, 0x65, 0x61 },
   [0x0852] = { 0x71, 0x65, 0x61 },
   [0x0854] = { 0x5d, 0x51, 0x4d },
   [0x0860] = { 0x71, 0x65, 0x61 },
   [0x0862] = { 0x2c, 0x20, 0x20 },
   [0x0864] = { 0x5d, 0x51, 0x4d },
   [0x0870] = { 0x71, 0x65, 0x61 },
   [0x0872] = { 0x71, 0x65, 0x61 },
   [0x0874] = { 0x5d, 0x51, 0x4d },
   [0x0880] = { 0x71, 0x65, 0x61 },
   [0x0882] = { 0x71, 0x65, 0x61 },
   [0x0884] = { 0x5d, 0x51, 0x4d },
   [0x0890] = { 0x71, 0x65, 0x61 },
   [0x0891] = { 0x71, 0x65, 0x61 },
   [0x0893] = { 0x5d, 0x51, 0x4d },
   [0x0894] = { 0x71, 0x65, 0x61 },
   [0x0896] = { 0x49, 0x3c, 0x38 },
   [0x0897] = { 0x5d, 0x51, 0x4d },
   [0x08a0] = { 0x71, 0x65, 0x61 },
   [0x08a2] = { 0x41, 0x34, 0x30 },
   [0x08a4] = { 0x49, 0x3c, 0x38 },
   [0x08b0] = { 0x79, 0x6d, 0x69 },
   [0x08b2] = { 0x79, 0x6d, 0x69 },
   [0x08b4] = { 0x5d, 0x51, 0x4d },
   [0x08c0] = { 0x79, 0x6d, 0x69 },
   [0x08c2] = { 0x79, 0x6d, 0x69 },
   [0x08c4] = { 0x5d, 0x51, 0x4d },
   [0x08d0] = { 0x79, 0x6d, 0x69 },
   [0x08d2] = { 0x79, 0x6d, 0x69 },
   [0x08d4] = { 0x5d, 0x51, 0x4d },
   [0x0900] = { 0x55, 0x45, 0x45 },
   [0x0902] = { 0x79, 0x38, 0x04 },
   [0x0904] = { 0x5d, 0x51, 0x4d },
   [0x0910] = { 0x55, 0x45, 0x45 },
   [0x0912] = { 0x79, 0x38, 0x04 },
   [0x0914] = { 0x5d, 0x51, 0x4d },
   [0x0920] = { 0x55, 0x45, 0x45 },
   [0x0922] = { 0x79, 0x38, 0x04 },
   [0x0924] = { 0x5d, 0x51, 0x4d },
   [0x0930] = { 0x55, 0x45, 0x45 },
   [0x0932] = { 0xa6, 0x59, 0x14 },
   [0x0934] = { 0x5d, 0x51, 0x4d },
   [0x0940] = { 0x55, 0x45, 0x45 },
   [0x0941] = { 0x55, 0x45, 0x45 },
   [0x0943] = { 0xa6, 0x59, 0x14 },
   [0x0944] = { 0x38, 0x28, 0x28 },
   [0x0946] = { 0x5d, 0x51, 0x4d },
   [0x0947] = { 0x5d, 0x51, 0x4d },
   [0x0950] = { 0x55, 0x45, 0x45 },
   [0x0952] = { 0xa6, 0x59, 0x14 },
   [0x0954] = { 0x5d, 0x51, 0x4d },
   [0x0960] = { 0x55, 0x45, 0x45 },
   [0x0962] = { 0x38, 0x28, 0x28 },
   [0x0964] = { 0x5d, 0x51, 0x4d },
   [0x0970] = { 0x49, 0x3c, 0x38 },
   [0x0972] = { 0x2c, 0x20, 0x20 },
   [0x0974] = { 0x5d, 0x51, 0x4d },
   [0x0980] = { 0x49, 0x3c, 0x38 },
   [0x0982] = { 0x2c, 0x20, 0x20 },
   [0x0984] = { 0x5d, 0x51, 0x4d },
   [0x0990] = { 0x49, 0x3c, 0x38 },
   [0x0991] = { 0x49, 0x3c, 0x38 },
   [0x0993] = { 0x2c, 0x20, 0x20 },
   [0x0994] = { 0x59, 0x34, 0x18 },
   [0x0996] = { 0x49, 0x3c, 0x38 },
   [0x0997] = { 0x5d, 0x51, 0x4d },
   [0x09a0] = { 0x49, 0x3c, 0x38 },
   [0x09a2] = { 0x2c, 0x20, 0x20 },
   [0x09a4] = { 0x49, 0x3c, 0x38 },
   [0x09b0] = { 0x2c, 0x20, 0x20 },
   [0x09b2] = { 0x82, 0x41, 0x04 },
   [0x09b4] = { 0x5d, 0x51, 0x4d },
   [0x09c0] = { 0x2c, 0x20, 0x20 },
   [0x09c2] = { 0x82, 0x41, 0x04 },
   [0x09c4] = { 0x5d, 0x51, 0x4d },
   [0x09d0] = { 0x2c, 0x20, 0x20 },
   [0x09d2] = { 0x82, 0x41, 0x04 },
   [0x09d4] = { 0x5d, 0x51, 0x4d },
};

static const uint8_t _swamp_colors[][3] =
{
   [0x0010] = { 0x18, 0x20, 0x08 },
   [0x0011] = { 0x18, 0x20, 0x08 },
   [0x0012] = { 0x18, 0x20, 0x08 },
   [0x0013] = { 0x18, 0x20, 0x08 },
   [0x0020] = { 0x14, 0x1c, 0x08 },
   [0x0021] = { 0x14, 0x1c, 0x08 },
   [0x0022] = { 0x14, 0x1c, 0x08 },
   [0x0023] = { 0x14, 0x1c, 0x08 },
   [0x0030] = { 0x61, 0x34, 0x04 },
   [0x0031] = { 0x61, 0x34, 0x04 },
   [0x0032] = { 0x59, 0x2c, 0x00 },
   [0x0034] = { 0x69, 0x3c, 0x08 },
   [0x0035] = { 0x49, 0x24, 0x00 },
   [0x0036] = { 0x61, 0x34, 0x04 },
   [0x0037] = { 0x61, 0x34, 0x04 },
   [0x0038] = { 0x61, 0x34, 0x04 },
   [0x0039] = { 0x75, 0x4d, 0x10 },
   [0x0040] = { 0x59, 0x2c, 0x00 },
   [0x0041] = { 0x59, 0x2c, 0x00 },
   [0x0042] = { 0x55, 0x28, 0x00 },
   [0x0044] = { 0x55, 0x28, 0x00 },
   [0x0045] = { 0x59, 0x2c, 0x00 },
   [0x0046] = { 0x59, 0x2c, 0x00 },
   [0x0047] = { 0x59, 0x2c, 0x00 },
   [0x0048] = { 0x6d, 0x45, 0x0c },
   [0x0049] = { 0x24, 0x34, 0x08 },
   [0x0050] = { 0x45, 0x2c, 0x1c },
   [0x0051] = { 0x51, 0x38, 0x28 },
   [0x0052] = { 0x51, 0x38, 0x28 },
   [0x0054] = { 0x45, 0x2c, 0x1c },
   [0x0055] = { 0x45, 0x2c, 0x1c },
   [0x0056] = { 0x51, 0x38, 0x28 },
   [0x0057] = { 0x51, 0x38, 0x28 },
   [0x0058] = { 0x30, 0x1c, 0x14 },
   [0x0059] = { 0x51, 0x38, 0x28 },
   [0x005a] = { 0x51, 0x38, 0x28 },
   [0x005b] = { 0x18, 0x20, 0x08 },
   [0x005c] = { 0x45, 0x2c, 0x1c },
   [0x005d] = { 0x24, 0x34, 0x08 },
   [0x005e] = { 0x28, 0x14, 0x0c },
   [0x005f] = { 0x45, 0x2c, 0x1c },
   [0x0060] = { 0x38, 0x24, 0x18 },
   [0x0061] = { 0x45, 0x2c, 0x1c },
   [0x0062] = { 0x45, 0x2c, 0x1c },
   [0x0064] = { 0x38, 0x24, 0x18 },
   [0x0065] = { 0x38, 0x24, 0x18 },
   [0x0066] = { 0x38, 0x28, 0x28 },
   [0x0067] = { 0x45, 0x2c, 0x1c },
   [0x0068] = { 0x45, 0x2c, 0x1c },
   [0x0069] = { 0x38, 0x24, 0x18 },
   [0x006a] = { 0x45, 0x2c, 0x1c },
   [0x006b] = { 0x45, 0x2c, 0x1c },
   [0x006c] = { 0x45, 0x2c, 0x1c },
   [0x006d] = { 0x18, 0x20, 0x08 },
   [0x006e] = { 0x38, 0x24, 0x18 },
   [0x006f] = { 0x24, 0x34, 0x08 },
   [0x0070] = { 0x41, 0x28, 0x0c },
   [0x0071] = { 0x7d, 0x59, 0x41 },
   [0x0072] = { 0x41, 0x28, 0x0c },
   [0x0080] = { 0x41, 0x34, 0x30 },
   [0x0081] = { 0x49, 0x3c, 0x38 },
   [0x0082] = { 0x18, 0x10, 0x10 },
   [0x0083] = { 0x38, 0x28, 0x28 },
   [0x0090] = { 0x38, 0x28, 0x28 },
   [0x0092] = { 0x41, 0x34, 0x30 },
   [0x0094] = { 0x71, 0x65, 0x61 },
   [0x00a0] = { 0x5d, 0x51, 0x4d },
   [0x00a2] = { 0x45, 0x2c, 0x1c },
   [0x00a4] = { 0x71, 0x65, 0x61 },
   [0x00b0] = { 0x86, 0x79, 0x75 },
   [0x00b2] = { 0x86, 0x79, 0x75 },
   [0x00b4] = { 0x79, 0x6d, 0x69 },
   [0x00c0] = { 0x38, 0x28, 0x28 },
   [0x00c2] = { 0x7d, 0x55, 0x18 },
   [0x00c4] = { 0x79, 0x6d, 0x69 },
   [0x0100] = { 0x14, 0x1c, 0x08 },
   [0x0101] = { 0x14, 0x1c, 0x08 },
   [0x0110] = { 0x18, 0x20, 0x08 },
   [0x0111] = { 0x18, 0x20, 0x08 },
   [0x0120] = { 0x14, 0x1c, 0x08 },
   [0x0121] = { 0x14, 0x1c, 0x08 },
   [0x0122] = { 0x14, 0x1c, 0x08 },
   [0x0130] = { 0x18, 0x20, 0x08 },
   [0x0131] = { 0x18, 0x20, 0x08 },
   [0x0140] = { 0x14, 0x1c, 0x08 },
   [0x0141] = { 0x18, 0x20, 0x08 },
   [0x0142] = { 0x14, 0x1c, 0x08 },
   [0x0150] = { 0x18, 0x20, 0x08 },
   [0x0151] = { 0x18, 0x20, 0x08 },
   [0x0160] = { 0x14, 0x1c, 0x08 },
   [0x0161] = { 0x14, 0x1c, 0x08 },
   [0x0170] = { 0x18, 0x20, 0x08 },
   [0x0171] = { 0x18, 0x20, 0x08 },
   [0x0180] = { 0x14, 0x1c, 0x08 },
   [0x0181] = { 0x14, 0x1c, 0x08 },
   [0x0190] = { 0x18, 0x20, 0x08 },
   [0x0191] = { 0x18, 0x20, 0x08 },
   [0x0192] = { 0x18, 0x20, 0x08 },
   [0x01a0] = { 0x14, 0x1c, 0x08 },
   [0x01a1] = { 0x14, 0x1c, 0x08 },
   [0x01b0] = { 0x18, 0x20, 0x08 },
   [0x01b1] = { 0x18, 0x20, 0x08 },
   [0x01b2] = { 0x18, 0x20, 0x08 },
   [0x01c0] = { 0x14, 0x1c, 0x08 },
   [0x01c1] = { 0x18, 0x20, 0x08 },
   [0x01d0] = { 0x18, 0x20, 0x08 },
   [0x01d1] = { 0x18, 0x20, 0x08 },
   [0x0200] = { 0x18, 0x20, 0x08 },
   [0x0201] = { 0x18, 0x20, 0x08 },
   [0x0210] = { 0x59, 0x2c, 0x00 },
   [0x0211] = { 0x59, 0x2c, 0x00 },
   [0x0220] = { 0x18, 0x20, 0x08 },
   [0x0221] = { 0x18, 0x20, 0x08 },
   [0x0222] = { 0x18, 0x20, 0x08 },
   [0x0230] = { 0x61, 0x34, 0x04 },
   [0x0231] = { 0x61, 0x34, 0x04 },
   [0x0240] = { 0x18, 0x20, 0x08 },
   [0x0241] = { 0x18, 0x20, 0x08 },
   [0x0242] = { 0x18, 0x20, 0x08 },
   [0x0250] = { 0x55, 0x28, 0x00 },
   [0x0251] = { 0x55, 0x28, 0x00 },
   [0x0260] = { 0x18, 0x20, 0x08 },
   [0x0261] = { 0x18, 0x20, 0x08 },
   [0x0270] = { 0x61, 0x34, 0x04 },
   [0x0271] = { 0x61, 0x34, 0x04 },
   [0x0280] = { 0x28, 0x30, 0x0c },
   [0x0281] = { 0x28, 0x30, 0x0c },
   [0x0290] = { 0x59, 0x2c, 0x00 },
   [0x0291] = { 0x59, 0x2c, 0x00 },
   [0x0292] = { 0x59, 0x2c, 0x00 },
   [0x02a0] = { 0x18, 0x20, 0x08 },
   [0x02a1] = { 0x18, 0x20, 0x08 },
   [0x02b0] = { 0x3c, 0x1c, 0x00 },
   [0x02b1] = { 0x61, 0x34, 0x04 },
   [0x02b2] = { 0x59, 0x2c, 0x00 },
   [0x02c0] = { 0x18, 0x20, 0x08 },
   [0x02c1] = { 0x28, 0x30, 0x0c },
   [0x02d0] = { 0x55, 0x28, 0x00 },
   [0x02d1] = { 0x61, 0x34, 0x04 },
   [0x0300] = { 0x59, 0x2c, 0x00 },
   [0x0301] = { 0x59, 0x2c, 0x00 },
   [0x0310] = { 0x59, 0x2c, 0x00 },
   [0x0311] = { 0x61, 0x34, 0x04 },
   [0x0320] = { 0x59, 0x2c, 0x00 },
   [0x0321] = { 0x59, 0x2c, 0x00 },
   [0x0322] = { 0x55, 0x28, 0x00 },
   [0x0330] = { 0x61, 0x34, 0x04 },
   [0x0331] = { 0x61, 0x34, 0x04 },
   [0x0340] = { 0x59, 0x2c, 0x00 },
   [0x0341] = { 0x59, 0x2c, 0x00 },
   [0x0342] = { 0x55, 0x28, 0x00 },
   [0x0350] = { 0x61, 0x34, 0x04 },
   [0x0351] = { 0x61, 0x34, 0x04 },
   [0x0360] = { 0x59, 0x2c, 0x00 },
   [0x0361] = { 0x59, 0x2c, 0x00 },
   [0x0370] = { 0x61, 0x34, 0x04 },
   [0x0371] = { 0x61, 0x34, 0x04 },
   [0x0380] = { 0x59, 0x2c, 0x00 },
   [0x0381] = { 0x59, 0x2c, 0x00 },
   [0x0390] = { 0x61, 0x34, 0x04 },
   [0x0391] = { 0x59, 0x2c, 0x00 },
   [0x0392] = { 0x59, 0x2c, 0x00 },
   [0x03a0] = { 0x59, 0x2c, 0x00 },
   [0x03a1] = { 0x59, 0x2c, 0x00 },
   [0x03b0] = { 0x61, 0x34, 0x04 },
   [0x03b1] = { 0x61, 0x34, 0x04 },
   [0x03b2] = { 0x59, 0x2c, 0x00 },
   [0x03c0] = { 0x61, 0x34, 0x04 },
   [0x03c1] = { 0x61, 0x34, 0x04 },
   [0x03d0] = { 0x59, 0x2c, 0x00 },
   [0x03d1] = { 0x59, 0x2c, 0x00 },
   [0x0400] = { 0x38, 0x28, 0x28 },
   [0x0401] = { 0x24, 0x18, 0x18 },
   [0x0410] = { 0x61, 0x34, 0x04 },
   [0x0411] = { 0x49, 0x3c, 0x38 },
   [0x0420] = { 0x38, 0x28, 0x28 },
   [0x0421] = { 0x41, 0x34, 0x30 },
   [0x0430] = { 0x61, 0x34, 0x04 },
   [0x0431] = { 0x38, 0x28, 0x28 },
   [0x0440] = { 0x18, 0x10, 0x10 },
   [0x0441] = { 0x55, 0x45, 0x45 },
   [0x0450] = { 0x41, 0x34, 0x30 },
   [0x0451] = { 0x61, 0x34, 0x04 },
   [0x0460] = { 0x41, 0x34, 0x30 },
   [0x0470] = { 0x61, 0x34, 0x04 },
   [0x0471] = { 0x6d, 0x45, 0x0c },
   [0x0480] = { 0x41, 0x34, 0x30 },
   [0x0481] = { 0x38, 0x28, 0x28 },
   [0x0490] = { 0x61, 0x34, 0x04 },
   [0x0491] = { 0x2c, 0x20, 0x20 },
   [0x04a0] = { 0x41, 0x34, 0x30 },
   [0x04b0] = { 0x61, 0x34, 0x04 },
   [0x04b1] = { 0x55, 0x28, 0x00 },
   [0x04c0] = { 0x41, 0x34, 0x30 },
   [0x04d0] = { 0x41, 0x34, 0x30 },
   [0x0500] = { 0x3c, 0x1c, 0x00 },
   [0x0501] = { 0x6d, 0x45, 0x0c },
   [0x0510] = { 0x45, 0x2c, 0x1c },
   [0x0511] = { 0x30, 0x1c, 0x14 },
   [0x0520] = { 0x45, 0x2c, 0x1c },
   [0x0521] = { 0x59, 0x2c, 0x00 },
   [0x0522] = { 0x61, 0x34, 0x04 },
   [0x0530] = { 0x51, 0x38, 0x28 },
   [0x0531] = { 0x38, 0x24, 0x18 },
   [0x0540] = { 0x6d, 0x45, 0x0c },
   [0x0541] = { 0x6d, 0x45, 0x0c },
   [0x0542] = { 0x6d, 0x45, 0x0c },
   [0x0550] = { 0x49, 0x24, 0x00 },
   [0x0551] = { 0x45, 0x2c, 0x1c },
   [0x0560] = { 0x6d, 0x45, 0x0c },
   [0x0561] = { 0x6d, 0x45, 0x0c },
   [0x0570] = { 0x51, 0x38, 0x28 },
   [0x0571] = { 0x45, 0x2c, 0x1c },
   [0x0580] = { 0x59, 0x2c, 0x00 },
   [0x0581] = { 0x6d, 0x45, 0x0c },
   [0x0590] = { 0x45, 0x2c, 0x1c },
   [0x0591] = { 0x45, 0x2c, 0x1c },
   [0x0592] = { 0x51, 0x38, 0x28 },
   [0x05a0] = { 0x59, 0x2c, 0x00 },
   [0x05a1] = { 0x61, 0x34, 0x04 },
   [0x05b0] = { 0x45, 0x2c, 0x1c },
   [0x05b1] = { 0x51, 0x38, 0x28 },
   [0x05b2] = { 0x51, 0x38, 0x28 },
   [0x05c0] = { 0x61, 0x34, 0x04 },
   [0x05c1] = { 0x61, 0x34, 0x04 },
   [0x05d0] = { 0x3c, 0x1c, 0x00 },
   [0x05d1] = { 0x7d, 0x55, 0x18 },
   [0x0600] = { 0x45, 0x2c, 0x1c },
   [0x0601] = { 0x38, 0x24, 0x18 },
   [0x0610] = { 0x51, 0x38, 0x28 },
   [0x0611] = { 0x45, 0x2c, 0x1c },
   [0x0620] = { 0x38, 0x24, 0x18 },
   [0x0621] = { 0x45, 0x2c, 0x1c },
   [0x0622] = { 0x45, 0x2c, 0x1c },
   [0x0630] = { 0x45, 0x2c, 0x1c },
   [0x0631] = { 0x51, 0x38, 0x28 },
   [0x0640] = { 0x45, 0x2c, 0x1c },
   [0x0641] = { 0x38, 0x24, 0x18 },
   [0x0642] = { 0x45, 0x2c, 0x1c },
   [0x0650] = { 0x30, 0x1c, 0x14 },
   [0x0651] = { 0x51, 0x38, 0x28 },
   [0x0660] = { 0x45, 0x2c, 0x1c },
   [0x0661] = { 0x45, 0x2c, 0x1c },
   [0x0670] = { 0x45, 0x2c, 0x1c },
   [0x0671] = { 0x45, 0x2c, 0x1c },
   [0x0680] = { 0x45, 0x2c, 0x1c },
   [0x0681] = { 0x38, 0x24, 0x18 },
   [0x0690] = { 0x45, 0x2c, 0x1c },
   [0x0691] = { 0x51, 0x38, 0x28 },
   [0x0692] = { 0x51, 0x38, 0x28 },
   [0x06a0] = { 0x45, 0x2c, 0x1c },
   [0x06a1] = { 0x45, 0x2c, 0x1c },
   [0x06b0] = { 0x45, 0x2c, 0x1c },
   [0x06b1] = { 0x51, 0x38, 0x28 },
   [0x06b2] = { 0x51, 0x38, 0x28 },
   [0x06c0] = { 0x45, 0x2c, 0x1c },
   [0x06c1] = { 0x45, 0x2c, 0x1c },
   [0x06d0] = { 0x30, 0x1c, 0x14 },
   [0x06d1] = { 0x30, 0x1c, 0x14 },
   [0x0700] = { 0x5d, 0x3c, 0x20 },
   [0x0701] = { 0x5d, 0x3c, 0x20 },
   [0x0710] = { 0x41, 0x28, 0x0c },
   [0x0711] = { 0x41, 0x28, 0x0c },
   [0x0720] = { 0x7d, 0x59, 0x41 },
   [0x0721] = { 0x41, 0x28, 0x0c },
   [0x0730] = { 0x51, 0x38, 0x28 },
   [0x0731] = { 0x45, 0x2c, 0x1c },
   [0x0740] = { 0x7d, 0x59, 0x41 },
   [0x0741] = { 0x41, 0x28, 0x0c },
   [0x0750] = { 0x45, 0x2c, 0x1c },
   [0x0751] = { 0x45, 0x2c, 0x1c },
   [0x0760] = { 0x41, 0x28, 0x0c },
   [0x0761] = { 0x41, 0x28, 0x0c },
   [0x0770] = { 0x45, 0x2c, 0x1c },
   [0x0771] = { 0x45, 0x2c, 0x1c },
   [0x0780] = { 0x7d, 0x59, 0x41 },
   [0x0781] = { 0x7d, 0x59, 0x41 },
   [0x0790] = { 0x45, 0x2c, 0x1c },
   [0x0791] = { 0x51, 0x38, 0x28 },
   [0x07a0] = { 0x41, 0x28, 0x0c },
   [0x07a1] = { 0x41, 0x28, 0x0c },
   [0x07b0] = { 0x45, 0x2c, 0x1c },
   [0x07b1] = { 0x51, 0x38, 0x28 },
   [0x07c0] = { 0x7d, 0x59, 0x41 },
   [0x07c1] = { 0x7d, 0x59, 0x41 },
   [0x07d0] = { 0x45, 0x2c, 0x1c },
   [0x07d1] = { 0x45, 0x2c, 0x1c },
   [0x0800] = { 0x49, 0x3c, 0x38 },
   [0x0802] = { 0x71, 0x65, 0x61 },
   [0x0804] = { 0x71, 0x65, 0x61 },
   [0x0810] = { 0x49, 0x3c, 0x38 },
   [0x0812] = { 0x71, 0x65, 0x61 },
   [0x0814] = { 0x71, 0x65, 0x61 },
   [0x0820] = { 0x49, 0x3c, 0x38 },
   [0x0822] = { 0x71, 0x65, 0x61 },
   [0x0824] = { 0x71, 0x65, 0x61 },
   [0x0830] = { 0x79, 0x6d, 0x69 },
   [0x0832] = { 0x79, 0x6d, 0x69 },
   [0x0834] = { 0x71, 0x65, 0x61 },
   [0x0840] = { 0x79, 0x6d, 0x69 },
   [0x0841] = { 0x79, 0x6d, 0x69 },
   [0x0843] = { 0x18, 0x10, 0x10 },
   [0x0844] = { 0x2c, 0x20, 0x20 },
   [0x0846] = { 0x71, 0x65, 0x61 },
   [0x0847] = { 0x71, 0x65, 0x61 },
   [0x0850] = { 0x79, 0x6d, 0x69 },
   [0x0852] = { 0x79, 0x6d, 0x69 },
   [0x0854] = { 0x71, 0x65, 0x61 },
   [0x0860] = { 0x79, 0x6d, 0x69 },
   [0x0862] = { 0x38, 0x28, 0x28 },
   [0x0864] = { 0x71, 0x65, 0x61 },
   [0x0870] = { 0x79, 0x6d, 0x69 },
   [0x0872] = { 0x79, 0x6d, 0x69 },
   [0x0874] = { 0x71, 0x65, 0x61 },
   [0x0880] = { 0x79, 0x6d, 0x69 },
   [0x0882] = { 0x79, 0x6d, 0x69 },
   [0x0884] = { 0x71, 0x65, 0x61 },
   [0x0890] = { 0x79, 0x6d, 0x69 },
   [0x0891] = { 0x79, 0x6d, 0x69 },
   [0x0893] = { 0x71, 0x65, 0x61 },
   [0x0894] = { 0x79, 0x6d, 0x69 },
   [0x0896] = { 0x41, 0x34, 0x30 },
   [0x0897] = { 0x71, 0x65, 0x61 },
   [0x08a0] = { 0x79, 0x6d, 0x69 },
   [0x08a2] = { 0x49, 0x3c, 0x38 },
   [0x08a4] = { 0x41, 0x34, 0x30 },
   [0x08b0] = { 0x86, 0x79, 0x75 },
   [0x08b2] = { 0x86, 0x79, 0x75 },
   [0x08b4] = { 0x69, 0x59, 0x55 },
   [0x08c0] = { 0x86, 0x79, 0x75 },
   [0x08c2] = { 0x86, 0x79, 0x75 },
   [0x08c4] = { 0x71, 0x65, 0x61 },
   [0x08d0] = { 0x86, 0x79, 0x75 },
   [0x08d2] = { 0x86, 0x79, 0x75 },
   [0x08d4] = { 0x71, 0x65, 0x61 },
   [0x0900] = { 0x55, 0x45, 0x45 },
   [0x0902] = { 0x45, 0x2c, 0x1c },
   [0x0904] = { 0x71, 0x65, 0x61 },
   [0x0910] = { 0x55, 0x45, 0x45 },
   [0x0912] = { 0x45, 0x2c, 0x1c },
   [0x0914] = { 0x71, 0x65, 0x61 },
   [0x0920] = { 0x55, 0x45, 0x45 },
   [0x0922] = { 0x45, 0x2c, 0x1c },
   [0x0924] = { 0x71, 0x65, 0x61 },
   [0x0930] = { 0x55, 0x45, 0x45 },
   [0x0932] = { 0x7d, 0x55, 0x18 },
   [0x0934] = { 0x71, 0x65, 0x61 },
   [0x0940] = { 0x55, 0x45, 0x45 },
   [0x0941] = { 0x55, 0x45, 0x45 },
   [0x0943] = { 0x7d, 0x55, 0x18 },
   [0x0944] = { 0x38, 0x28, 0x28 },
   [0x0946] = { 0x71, 0x65, 0x61 },
   [0x0947] = { 0x71, 0x65, 0x61 },
   [0x0950] = { 0x55, 0x45, 0x45 },
   [0x0952] = { 0x7d, 0x55, 0x18 },
   [0x0954] = { 0x71, 0x65, 0x61 },
   [0x0960] = { 0x55, 0x45, 0x45 },
   [0x0962] = { 0x41, 0x34, 0x30 },
   [0x0964] = { 0x71, 0x65, 0x61 },
   [0x0970] = { 0x55, 0x45, 0x45 },
   [0x0972] = { 0x38, 0x28, 0x28 },
   [0x0974] = { 0x71, 0x65, 0x61 },
   [0x0980] = { 0x49, 0x3c, 0x38 },
   [0x0982] = { 0x38, 0x28, 0x28 },
   [0x0984] = { 0x71, 0x65, 0x61 },
   [0x0990] = { 0x49, 0x3c, 0x38 },
   [0x0991] = { 0x49, 0x3c, 0x38 },
   [0x0993] = { 0x24, 0x18, 0x18 },
   [0x0994] = { 0x49, 0x24, 0x00 },
   [0x0996] = { 0x41, 0x34, 0x30 },
   [0x0997] = { 0x71, 0x65, 0x61 },
   [0x09a0] = { 0x49, 0x3c, 0x38 },
   [0x09a2] = { 0x38, 0x28, 0x28 },
   [0x09a4] = { 0x41, 0x34, 0x30 },
   [0x09b0] = { 0x2c, 0x20, 0x20 },
   [0x09b2] = { 0x7d, 0x55, 0x18 },
   [0x09b4] = { 0x69, 0x59, 0x55 },
   [0x09c0] = { 0x2c, 0x20, 0x20 },
   [0x09c2] = { 0x7d, 0x55, 0x18 },
   [0x09c4] = { 0x71, 0x65, 0x61 },
   [0x09d0] = { 0x2c, 0x20, 0x20 },
   [0x09d2] = { 0x7d, 0x55, 0x18 },
   [0x09d4] = { 0x71, 0x65, 0x61 },
};


PUDAPI Pud_Color
pud_minimap_tile_to_color(Pud_Era  era,
                          uint16_t tile)
{
   switch (era)
     {
      case PUD_ERA_FOREST:
         if (tile >= ARRAY_SIZE(_forest_colors)) goto error;
         return color_make(_forest_colors[tile][0],
                           _forest_colors[tile][1],
                           _forest_colors[tile][2],
                           0xff);

      case PUD_ERA_WINTER:
         if (tile >= ARRAY_SIZE(_winter_colors)) goto error;
         return color_make(_winter_colors[tile][0],
                           _winter_colors[tile][1],
                           _winter_colors[tile][2],
                           0xff);


      case PUD_ERA_WASTELAND:
         if (tile >= ARRAY_SIZE(_wasteland_colors)) goto error;
         return color_make(_wasteland_colors[tile][0],
                           _wasteland_colors[tile][1],
                           _wasteland_colors[tile][2],
                           0xff);

      case PUD_ERA_SWAMP:
         if (tile >= ARRAY_SIZE(_swamp_colors)) goto error;
         return color_make(_swamp_colors[tile][0],
                           _swamp_colors[tile][1],
                           _swamp_colors[tile][2],
                           0xff);
     }

error:
   ERR("Unhandled tile [0x%04x] for era %s", tile, pud_era_to_string(era));
   return color_make(0xff, 0x00, 0xff, 0xff); // Flashy to be seen (debug)
}
