/* ===== MAIN CONTAINER ===== */
.maps-main {
  max-width: 1400px;
  margin: 2.5rem auto 0; /* Reduced from 5rem to 2.5rem for better spacing */
  padding: 0 2rem 2rem;
  position: relative;
  z-index: 10;
}

/* ===== GAME TABS WITH STATS ===== */
.game-tabs-container {
  margin: 0.5rem auto 1rem;
  max-width: 1000px;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.game-tabs-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, #ffd700, #ffed4e, #ff6b35);
}

.game-tabs-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.game-tabs {
  display: flex;
  gap: 0.4rem;
  justify-content: center;
  flex: 1;
}

.game-tab {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  padding: 0.4rem 0.8rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  color: rgba(255, 255, 255, 0.7);
  font-family: 'Cinzel', serif;
  font-size: 0.7rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  overflow: hidden;
  min-width: 100px;
  justify-content: center;
}

.game-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.2), transparent);
  transition: left 0.6s;
}

.game-tab:hover {
  background: rgba(255, 215, 0, 0.08);
  border-color: rgba(255, 215, 0, 0.2);
  color: #ffd700;
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 3px 10px rgba(255, 215, 0, 0.2);
}

.game-tab:hover::before {
  left: 100%;
}

.game-tab.active {
  background: linear-gradient(45deg, rgba(255, 215, 0, 0.15), rgba(255, 237, 78, 0.15));
  border-color: #ffd700;
  color: #ffd700;
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 3px 10px rgba(255, 215, 0, 0.3);
}

.game-tab i {
  font-size: 0.9rem;
}

.game-tab span {
  font-size: 0.7rem;
}

/* Hero stats next to tabs */
.hero-stats {
  display: flex;
  gap: 0.8rem;
  flex-shrink: 0;
}

.hero-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.2rem;
  padding: 0.4rem 0.6rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  min-width: 70px;
  transition: all 0.3s ease;
}

.hero-stat:hover {
  background: rgba(255, 215, 0, 0.1);
  border-color: rgba(255, 215, 0, 0.3);
  transform: translateY(-1px);
}

.hero-stat-value {
  font-size: 1rem;
  font-weight: 700;
  color: #ffd700;
}

.hero-stat-label {
  font-size: 0.6rem;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
}

/* Game specific styling */
.game-tab[data-game="war1"] {
  border-color: rgba(139, 69, 19, 0.5);
}

.game-tab[data-game="war1"]:hover,
.game-tab[data-game="war1"].active {
  border-color: #8b4513;
  color: #daa520;
  background: linear-gradient(45deg, rgba(139, 69, 19, 0.2), rgba(218, 165, 32, 0.2));
}

.game-tab[data-game="war3"] {
  border-color: rgba(138, 43, 226, 0.5);
}

.game-tab[data-game="war3"]:hover,
.game-tab[data-game="war3"].active {
  border-color: #8a2be2;
  color: #da70d6;
  background: linear-gradient(45deg, rgba(138, 43, 226, 0.2), rgba(218, 112, 214, 0.2));
}

/* ===== MINIMAL SECTIONS ===== */
.minimal-section {
  text-align: center;
  padding: 3rem 2rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin: 2rem auto;
  max-width: 600px;
}

.game-title {
  font-family: 'Cinzel', serif;
  font-size: 2.5rem;
  font-weight: 700;
  color: #ffd700;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
  background: linear-gradient(45deg, #ffd700, #ffed4e, #ffd700);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: goldShimmer 3s ease-in-out infinite;
}

@keyframes goldShimmer {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Responsive design for tabs */
@media (max-width: 768px) {
  .game-tabs-wrapper {
    flex-direction: column;
    gap: 1rem;
  }
  
  .game-tabs {
    width: 100%;
    justify-content: center;
  }
  
  .hero-stats {
    justify-content: center;
  }
  
  .game-tab {
    padding: 0.6rem 1rem;
    font-size: 0.8rem;
  }
  
  .game-tab span {
    font-size: 0.8rem;
  }
}

/* ===== GAME CONTENT SECTIONS ===== */
.game-content {
  display: none;
  animation: fadeIn 0.6s ease;
}

.game-content.active {
  display: block;
}

/* WC II Section (existing) */
.war2-section {
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(255, 215, 0, 0.1);
  padding: 2rem;
  margin: 1.5rem auto;
  max-width: 1400px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  position: relative;
  overflow: hidden;
}

/* WC I Section */
.war1-section {
  background: rgba(139, 69, 19, 0.08);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(218, 165, 32, 0.2);
  padding: 2rem;
  margin: 1.5rem auto;
  max-width: 1400px;
  box-shadow: 0 8px 32px rgba(139, 69, 19, 0.3);
  position: relative;
  overflow: hidden;
}

.war1-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #8b4513, #daa520, #cd853f);
}

/* WC1 Scenarios Grid */
.war1-scenarios-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 15px;
}

/* WC1 Scenario Card */
.war1-scenario-card {
  background: rgba(139, 69, 19, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(218, 165, 32, 0.2);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  cursor: pointer;
  transform-origin: center;
}

.war1-scenario-card:hover {
  background: rgba(218, 165, 32, 0.08);
  border-color: rgba(218, 165, 32, 0.4);
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 12px 25px rgba(139, 69, 19, 0.4);
}

.war1-scenario-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(218, 165, 32, 0.1), transparent);
  transition: left 0.6s;
  z-index: 1;
}

.war1-scenario-card:hover::before {
  left: 100%;
}

/* WC1 Scenario Image */
.war1-scenario-image-container {
  position: relative;
  width: 100%;
  height: 140px;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 12px 12px 0 0;
}

.war1-scenario-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.war1-scenario-card:hover .war1-scenario-image {
  transform: scale(1.05);
}

/* WC1 Fullscreen Button */
.war1-fullscreen-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(0, 0, 0, 0.8);
  color: #daa520;
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 10;
  font-size: 1rem;
  opacity: 0;
  transform: scale(0.8);
  backdrop-filter: blur(5px);
  border: 2px solid rgba(218, 165, 32, 0.3);
}

.war1-scenario-card:hover .war1-fullscreen-btn {
  opacity: 1;
  transform: scale(1);
}

.war1-fullscreen-btn:hover {
  background: rgba(218, 165, 32, 0.9);
  color: #000;
  transform: scale(1.15);
  box-shadow: 0 4px 15px rgba(218, 165, 32, 0.4);
}

/* WC1 Scenario Info */
.war1-scenario-info {
  padding: 1.2rem;
  z-index: 2;
  position: relative;
}

.war1-scenario-title {
  font-family: 'Cinzel', serif;
  font-size: 1.1rem;
  font-weight: 600;
  color: #daa520;
  margin: 0 0 0.5rem 0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.war1-scenario-category {
  display: inline-block;
  background: rgba(218, 165, 32, 0.2);
  color: #daa520;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid rgba(218, 165, 32, 0.3);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.75rem;
}

/* WC1 Rating Section */
.war1-rating-section {
  margin-top: 1rem;
  padding-top: 0.75rem;
  border-top: 1px solid rgba(218, 165, 32, 0.2);
}

.war1-rating-display {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
}

.war1-stars {
  display: flex;
  gap: 0.1rem;
}

.war1-stars i {
  color: #daa520;
  font-size: 0.9rem;
}

.war1-rating-value {
  font-weight: 600;
  color: #daa520;
  font-size: 0.9rem;
}

.war1-reviews-btn {
  background: none;
  border: none;
  color: rgba(218, 165, 32, 0.8);
  font-size: 0.8rem;
  cursor: pointer;
  text-decoration: underline;
  padding: 0;
  transition: color 0.2s ease;
}

.war1-reviews-btn:hover {
  color: #daa520;
}

.war1-rate-btn {
  background: rgba(218, 165, 32, 0.1);
  border: 1px solid rgba(218, 165, 32, 0.3);
  color: #daa520;
  padding: 0.4rem 0.8rem;
  border-radius: 6px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.war1-rate-btn:hover {
  background: rgba(218, 165, 32, 0.2);
  border-color: #daa520;
  transform: translateY(-1px);
}

/* WC1 Modal Overlays */
.war1-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
  z-index: 10000;
  display: none;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.war1-modal-overlay.show {
  opacity: 1;
}

.war1-modal-overlay .modal-content {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border: 2px solid #daa520;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.war1-modal-overlay.show .modal-content {
  transform: scale(1);
}

.war1-modal-overlay .modal-header {
  background: linear-gradient(135deg, #daa520 0%, #b8860b 100%);
  color: #1a1a1a;
  padding: 1rem 1.5rem;
  border-radius: 10px 10px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.war1-modal-overlay .modal-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.war1-modal-overlay .modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #1a1a1a;
  cursor: pointer;
  padding: 0;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.2s ease;
}

.war1-modal-overlay .modal-close:hover {
  background: rgba(0, 0, 0, 0.1);
}

.war1-modal-overlay .modal-body {
  padding: 1.5rem;
}

/* Rating Form Styles */
.rating-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.rating-stars-input {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.rating-label {
  font-weight: 600;
  color: #daa520;
}

.stars-input {
  display: flex;
  gap: 0.3rem;
}

.rating-star {
  font-size: 1.5rem;
  color: #555;
  cursor: pointer;
  transition: all 0.2s ease;
}

.rating-star:hover,
.rating-star.hover {
  color: #daa520;
  transform: scale(1.1);
}

.rating-star.fas {
  color: #daa520;
}

.rating-comment {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.rating-comment label {
  font-weight: 600;
  color: #daa520;
}

.rating-comment textarea {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(218, 165, 32, 0.3);
  border-radius: 6px;
  padding: 0.75rem;
  color: white;
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

.rating-comment textarea:focus {
  outline: none;
  border-color: #daa520;
  box-shadow: 0 0 0 2px rgba(218, 165, 32, 0.2);
}

.rating-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 0.5rem;
}

.btn-cancel,
.btn-submit {
  padding: 0.6rem 1.2rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  border: none;
}

.btn-cancel {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-cancel:hover {
  background: rgba(255, 255, 255, 0.2);
}

.btn-submit {
  background: #daa520;
  color: #1a1a1a;
  border: 1px solid #daa520;
}

.btn-submit:hover:not(:disabled) {
  background: #b8860b;
  transform: translateY(-1px);
}

.btn-submit:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Reviews Modal Styles */
.reviews-summary {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(218, 165, 32, 0.2);
}

.average-rating {
  display: flex;
  align-items: center;
  gap: 1rem;
  justify-content: center;
}

.average-rating .rating-value {
  font-size: 2rem;
  font-weight: 700;
  color: #daa520;
}

.average-rating .rating-stars {
  display: flex;
  gap: 0.2rem;
}

.average-rating .rating-stars i {
  font-size: 1.2rem;
  color: #daa520;
}

.average-rating .review-count {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

.reviews-list {
  max-height: 300px;
  overflow-y: auto;
}

.review-item {
  background: rgba(218, 165, 32, 0.05);
  border: 1px solid rgba(218, 165, 32, 0.1);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 0.75rem;
}

.review-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
}

.reviewer-name {
  font-weight: 600;
  color: #daa520;
}

.review-rating {
  display: flex;
  gap: 0.1rem;
}

.review-rating i {
  font-size: 0.8rem;
  color: #daa520;
}

.review-date {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.8rem;
  margin-left: auto;
}

.review-comment {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
  font-style: italic;
}

.no-reviews {
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  padding: 2rem;
  font-style: italic;
}

/* Category specific colors */
.war1-scenario-category.forest {
  background: rgba(34, 139, 34, 0.2);
  color: #22c55e;
  border-color: rgba(34, 139, 34, 0.3);
}

.war1-scenario-category.swamp {
  background: rgba(128, 128, 0, 0.2);
  color: #a3a332;
  border-color: rgba(128, 128, 0, 0.3);
}

.war1-scenario-category.dungeon {
  background: rgba(128, 0, 128, 0.2);
  color: #c084fc;
  border-color: rgba(128, 0, 128, 0.3);
}

/* WC1 Loading State */
.war1-scenarios-grid .loading {
  grid-column: 1 / -1;
  text-align: center;
  padding: 3rem;
  color: rgba(218, 165, 32, 0.8);
  font-size: 1.1rem;
  font-style: italic;
}

/* WC1 Empty State */
.war1-no-scenarios {
  grid-column: 1 / -1;
  text-align: center;
  padding: 3rem;
  color: rgba(255, 255, 255, 0.6);
  background: rgba(139, 69, 19, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(218, 165, 32, 0.1);
}

.war1-no-scenarios i {
  font-size: 3rem;
  color: #daa520;
  margin-bottom: 1rem;
  display: block;
}

.war1-no-scenarios h3 {
  color: #daa520;
  margin: 0 0 0.5rem 0;
  font-family: 'Cinzel', serif;
}

/* Responsive Design for WC1 */
@media (max-width: 768px) {
  .war1-scenarios-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
  }
  
  .war1-scenario-info {
    padding: 1rem;
  }
  
  .war1-scenario-title {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .war1-scenarios-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
} 
