const User = require('../models/User');
const jwt = require('jsonwebtoken');

exports.handleOAuthCallback = async (req, res, next) => {
  console.log('OAuth Callback Debug:', {
    sessionID: req.sessionID,
    hasUser: !!req.user,
    userID: req.user?._id,
    username: req.user?.username,
    isUsernameDefined: req.user?.isUsernameDefined,
    isElectron: req.session.isElectron,
    oauthState: req.session.oauthState,
    session: req.session
  });

  const user = req.user;
  const isElectron = req.session.isElectron;
  const oauthState = req.session.oauthState;

  if (!user) {
    console.error('OAuth callback received but user is not set in the request');
    
    if (isElectron) {
      return res.redirect(`warcraftarena://auth/callback?error=auth_failed&state=${oauthState || ''}`);
    }
    return res.redirect('/views/login.html?error=auth_failed');
  }

  try {
    // Ensure user ID is valid
    if (!user._id) {
      console.error('User object is missing _id');
      
      if (isElectron) {
        return res.redirect(`warcraftarena://auth/callback?error=invalid_user&state=${oauthState || ''}`);
      }
      return res.redirect('/views/login.html?error=invalid_user');
    }

    // Add debug checkpoint for user identity
    console.log('User authenticated:', {
      id: user._id,
      username: user.username,
      email: user.email,
      provider: user.provider,
      isElectron: isElectron
    });

    if (isElectron) {
      // Handle Electron OAuth flow
      try {
        // Generate JWT token for Electron app
        const token = jwt.sign(
          { 
            userId: user._id,
            username: user.username,
            isElectron: true,
            iat: Math.floor(Date.now() / 1000)
          },
          process.env.JWT_SECRET || 'your-secret-key',
          { expiresIn: '24h' }
        );

        // Update last login time
        user.lastLogin = new Date();
        await user.save();

        console.log('Electron OAuth successful, redirecting with token');
        
        // Redirect back to Electron app with token
        const callbackUrl = `warcraftarena://auth/callback?token=${token}&userId=${user._id}&state=${oauthState || ''}`;
        return res.redirect(callbackUrl);
        
      } catch (error) {
        console.error('Electron OAuth token generation failed:', error);
        return res.redirect(`warcraftarena://auth/callback?error=token_generation_failed&state=${oauthState || ''}`);
      }
    } else {
      // Handle web OAuth flow (existing logic)
      // Make sure passport data is properly set
      req.session.passport = req.session.passport || {};
      req.session.passport.user = user._id;
      
      // Set authentication flags directly
      req.session.isAuthenticated = true;
      req.session.userId = user._id.toString();
      req.session.userRole = user.role || 'user';
      req.session.loginTime = new Date().toISOString();

      // Clear electron flags from session
      delete req.session.isElectron;
      delete req.session.oauthState;

      // Force session save before redirect (CRITICAL)
      req.session.save(saveErr => {
        if (saveErr) {
          console.error('Error saving session:', saveErr);
          return next(saveErr);
        }
        
        console.log('Session saved successfully:', {
          sessionID: req.sessionID,
          userId: req.session.userId,
          isAuthenticated: req.session.isAuthenticated
        });
        
        // Handle first login scenario
        if (!user.isUsernameDefined) {
          console.log('Redirecting user to username setup page');
          return res.redirect('/views/setup-username.html');
        }

        // Update last login time
        user.lastLogin = new Date();
        user.save().then(() => {
          console.log('User login successful, redirecting to home');
          // Use absolute path to ensure proper redirection
          res.redirect('/views/index.html');
        }).catch(saveErr => {
          console.error('Error saving user last login:', saveErr);
          // Still redirect even if saving last login fails
          res.redirect('/views/index.html');
        });
      });
    }
  } catch (err) {
    console.error('OAuth callback error:', err);
    
    if (isElectron) {
      return res.redirect(`warcraftarena://auth/callback?error=server_error&state=${oauthState || ''}`);
    }
    next(err);
  }
};

exports.setupUsername = async (req, res, next) => {
  let { username } = req.body;
  const user = req.user;

  if (!user) {
    return res.status(401).json({ error: 'Not authenticated' });
  }

  if (!username || !/^[a-zA-Z0-9_-]{3,20}$/.test(username)) {
    return res.status(400).json({ error: 'Invalid username format' });
  }

  // Convert to uppercase
  username = username.toUpperCase();

  try {
    // Check if username is already taken by another user
    const existingUser = await User.findOne({ username: username });
    if (existingUser && existingUser._id.toString() !== user._id.toString()) {
      return res.status(400).json({ error: 'Username already taken' });
    }

    // Set the username and mark as defined
    user.username = username;
    user.isUsernameDefined = true;

    // Save with validation
    try {
      await user.save();

      // Update session if needed
      if (req.session) {
        req.session.touch();
      }

      res.json({ success: true, message: 'Username set successfully' });
    } catch (validationError) {
      console.error('Username validation error:', validationError);
      return res.status(400).json({
        error: validationError.message || 'Username validation failed',
        details: validationError.errors
      });
    }
  } catch (err) {
    console.error('Username setup error:', err);
    next(err);
  }
};

exports.logout = (req, res, next) => {
  req.logout(err => {
    if (err) return next(err);
    res.redirect('/');
  });
};
